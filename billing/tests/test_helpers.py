import re

from billing.components.core.dtos.deployment_billing import (
    DeploymentBillingConfig,
)
from billing.components.core.dtos.user_billing import (
    Diagnosis,
    InsuranceCarrier,
    UserBilling,
    UserBillingFile,
)
from sdk.deployment.dtos.deployment import Features
from sdk.deployment.models.deployment import Deployment


def sample_user_billing() -> dict:
    return {
        UserBilling.STATUS: UserBilling.BillingStatus.PENDING.value,
        UserBilling.INSURANCE_CARRIERS: [
            {
                InsuranceCarrier.ORDER: 1,
                InsuranceCarrier.GROUP_NAME: "testGroupName1",
                InsuranceCarrier.PAYER_ID: "testPayer1",
            },
            {
                InsuranceCarrier.ORDER: 2,
                InsuranceCarrier.GROUP_NAME: "testGroupName2",
                InsuranceCarrier.PAYER_ID: "testPayer2",
            },
        ],
        UserBilling.BILLING_PROVIDER_NAME: "testBillingProviderName1",
        UserBilling.DIAGNOSIS: {
            Diagnosis.ORDER: 1,
            Diagnosis.ICD_CODE: "ICD1",
            Diagnosis.DESCRIPTION: "test description",
        },
        UserBilling.CREATE_DT: "2022-11-22T14:50:59.039Z",
    }


def sample_user_billing_not_enrolled() -> dict:
    return {
        UserBilling.STATUS: UserBilling.BillingStatus.PENDING.value,
        UserBilling.FILE: {
            UserBillingFile.FILE_ID: "64ba818550451d5f115e2bbe",
            UserBillingFile.NAME: "temp_name",
        },
    }


def simplify_export_response_keys(response, pattern, module_name):
    result = {}
    if not response.json:
        return result

    for key, value in response.json.items():
        module_values = {}
        for module_key, module_value in value.items():
            match = re.search(pattern, module_key)
            if match:
                module_values[module_name] = module_value
            else:
                module_values[module_key] = module_value

        result[key] = module_values
    return result


def set_submission_calculation_type(deployment_id: str, use_calendar_calculation=True):
    deployment = Deployment.objects.filter(mongoId=deployment_id).first()
    deployment.features = deployment.features or {}
    deployment.features[Features.CUSTOM_APP_CONFIG] = deployment.features.get(Features.CUSTOM_APP_CONFIG, {})
    deployment.features[Features.CUSTOM_APP_CONFIG]["billing"] = deployment.features[Features.CUSTOM_APP_CONFIG].get(
        "billing", {}
    )
    deployment.features[Features.CUSTOM_APP_CONFIG]["billing"][DeploymentBillingConfig.USE_CALENDAR_CALCULATION] = (
        use_calendar_calculation
    )
    deployment.save()


def equal_csv_content(content, expected_content):
    old = "\r\n"
    new = "\n"
    if isinstance(content, bytes):
        old = b"\r\n"
        new = b"\n"
    content = content.replace(old, new)
    expected_content = expected_content.replace(old, new)
    return content == expected_content
