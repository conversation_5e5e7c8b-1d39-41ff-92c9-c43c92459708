from dataclasses import field

from billing.components.core.config.config import BillingConfig
from billing.components.billing_integration.config.config import BillingIntegrationConfig
from huma_plugins.tests.plugin_test_case import TestServer
from huma_plugins.tests.shared import PL<PERSON><PERSON><PERSON>_CONFIG_PATH
from sdk import convertibleclass
from sdk.common.utils.convertible import required_field
from sdk.phoenix.config.server_config import PhoenixServerConfig
from sdk.tests.extension_test_case import ExtensionTestCase


@convertibleclass
class BillingTestServer(TestServer):
    billing: BillingConfig = field(default_factory=BillingConfig)
    billing_integration: BillingIntegrationConfig = field(default_factory=BillingIntegrationConfig)


@convertibleclass
class ExtensionServerConfig(PhoenixServerConfig):
    server: BillingTestServer = required_field()


class BillingTestCase(ExtensionTestCase):
    config_class = ExtensionServerConfig
    config_file_path = PLUGIN_CONFIG_PATH
