import json
import logging
from datetime import datetime

from celery.schedules import crontab
from dateutil.relativedelta import relativedelta

from billing.components.export.use_case.billing_general_export import BillingGeneralExportableUseCase
from billing.components.export.use_case.billing_monthly_export import BillingMonthlyExportableUseCase
from billing.components.portal.dtos.billing_portal_models import CPTGeneralExport, CPTMonthlyExport
from billing.components.portal.repository.billing_portal_repository import BillingPortalRepository
from huma_plugins.components.export.dtos.export_models import ExportParameters
from huma_plugins.components.export.use_case.export_request_objects import ExportRequestObject, ExportUsersRequestObject
from huma_plugins.components.export.use_case.export_use_cases import ExportDeploymentUseCase
from sdk.authorization.repository import AuthorizationRepository
from sdk.celery.app import celery_app
from sdk.common.exceptions.exceptions import ObjectDoesNotExist
from sdk.common.utils import inject
from sdk.deployment.service.deployment_service import DeploymentService
from sdk.phoenix.config.server_config import PhoenixServerConfig

logger = logging.getLogger(__name__)


@celery_app.on_after_finalize.connect
def setup_periodic_billing_tasks(sender, **kwargs):
    sender.add_periodic_task(
        crontab(hour=6, minute=0),
        generate_billing_cpt_codes_report.s(),
        name="Event executor for daily 'monthly billing' cpt codes generation",
    )


@celery_app.task
def generate_billing_cpt_codes_report(year: int = None, month: int = None, deployment_id: str = None):
    logger.info("Generating CPT codes report - task started")
    cpt_export_repo: BillingPortalRepository = inject.instance(BillingPortalRepository)
    server_config = inject.instance(PhoenixServerConfig)
    config = server_config.server.billingPortal
    if not config or not config.tags:
        logger.info("No tags provided for billing report generation. Discarding the task.")
        return

    if deployment_id is None:
        tags_string = config.tags or ""
        tags = tags_string.split(",")
        if not tags:
            logger.info("No tags provided for billing report generation. Discarding the task.")
            return

        deployment_service = DeploymentService()
        deployment_ids = deployment_service.retrieve_deployment_ids_by_tags(tags)
    else:
        deployment_ids = [deployment_id]

    for deployment_id in deployment_ids:
        try:
            general_billings, monthly_billings = export_monthly_billing(deployment_id, _get_system_user_id())
            logger.info(
                f"Monthly CPT export for deployment: {deployment_id} with {len(general_billings)} general billings and {len(monthly_billings)} monthly billings"
            )
            for billing in general_billings:
                cpt_export_repo.add_user_general_cpt_code(billing)
            for billing in monthly_billings:
                cpt_export_repo.add_user_monthly_cpt_code(billing)
        except Exception as e:
            logger.error(f"Failed to export monthly billing for deployment {deployment_id}: {e}")


def export_monthly_billing(
    deployment_id: str, system_user_id: str = None, year: int = None, month: int = None
) -> tuple[list[CPTGeneralExport], list[CPTMonthlyExport]]:
    export_date_format = "%Y-%m-%dT%H:%M:%SZ"
    # todo: follow the calendar monthly cycle if it is set in the deployment config

    if year is not None and month is not None:
        start_date = datetime(year=year, month=month, day=1)
        end_date = datetime(year=year, month=month, day=1) + relativedelta(months=1) - relativedelta(seconds=1)
    else:
        now = datetime.now()
        start_date = datetime(year=now.year, month=now.month, day=1)
        # end_date should be the last date of the month for the report to
        # be valid, but we keep it the current date to store the progress.
        end_date = datetime(year=now.year, month=now.month, day=now.day) - relativedelta(seconds=1)

    export_params = {
        ExportParameters.DEPLOYMENT_ID: deployment_id,
        ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        ExportParameters.FROM_DATE: start_date.strftime(export_date_format),
        ExportParameters.MODULE_NAMES: [
            BillingGeneralExportableUseCase.MODULE_NAME,
            BillingMonthlyExportableUseCase.MODULE_NAME,
        ],
        ExportParameters.SINGLE_FILE_RESPONSE: True,
        ExportParameters.TO_DATE: end_date.strftime(export_date_format),
        ExportRequestObject.REQUESTER_ID: system_user_id or _get_system_user_id(),
        ExportRequestObject.VIEW: ExportRequestObject.DataViewOption.MODULE_CONFIG.value,
        ExportRequestObject.INCLUDE_USER_META_DATA: False,
    }

    request_object = ExportUsersRequestObject.from_dict(export_params)
    use_case = ExportDeploymentUseCase()

    logger.info(
        "Export monthly billing: Exporting monthly billing for deployment: {} with parameters {}".format(
            deployment_id, export_params
        )
    )

    response_object = use_case.execute(request_object)
    with open(response_object.filePath, "rb") as f:
        billing_export = json.load(f)

    report_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)

    if not billing_export or "billing" not in billing_export:
        logger.info(f"Export monthly billing: Invalid billing export for deployment {deployment_id}.")
        return [], []
    monthly_key = _get_billing_key(billing_export, deployment_id, "monthly")
    monthly_billings = billing_export["billing"][monthly_key] if monthly_key else []
    monthly_export = [CPTMonthlyExport.from_export_dict({**b, "reportDate": report_date}) for b in monthly_billings]

    logger.info(monthly_billings)

    general_key = _get_billing_key(billing_export, deployment_id, "general")
    general_billings = billing_export["billing"][general_key] if general_key else []
    general_export = [CPTGeneralExport.from_export_dict({**b, "reportDate": report_date}) for b in general_billings]
    return general_export, monthly_export


def _get_billing_key(billing_export: dict, deployment_id, billing_type: str) -> str | None:
    keys = [key for key in billing_export["billing"] if key.lower().startswith(billing_type.lower())]
    if len(keys) > 1:
        logger.info(
            f"Export monthly billing: Invalid {billing_type} billing export for deployment {deployment_id}. Keys: {keys}"
        )
        return None
    elif not keys:
        logger.info(f"Export monthly billing: No {billing_type} billing export for deployment {deployment_id} today.")
        return None

    return keys[0]


def _get_system_user_id():
    repo = inject.instance(AuthorizationRepository)
    system_user = repo.retrieve_user(check_super_admin=True)

    if not system_user:
        raise ObjectDoesNotExist("System user not found")

    return system_user.id
