import logging

from billing.components.portal.repository.billing_portal_repository import BillingPortalRepository
from billing.components.portal.router.billing_portal_requests import (
    AddClientDeploymentRequestObject,
    UpdateClientDeploymentRequestObject,
)
from sdk.common.usecase.request_object import RequestObject
from sdk.common.usecase.response_object import ResultIdResponseObject
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams

logger = logging.getLogger(__name__)


class DeploymentBaseUseCase(UseCase):
    @autoparams()
    def __init__(self, billing_portal_repository: BillingPortalRepository):
        self.repo = billing_portal_repository

    def process_request(self, request_object: RequestObject):
        raise NotImplementedError("This method should be overridden in subclasses.")


class AddClientDeploymentUseCase(DeploymentBaseUseCase):
    def process_request(self, request_object: AddClientDeploymentRequestObject):
        client_id = request_object.clientId
        deployment_id = request_object.deploymentId
        deployment_id = self.repo.add_deployment_to_client(client_id, deployment_id)

        return ResultIdResponseObject(id=str(deployment_id))


class UpdateClientDeploymentUseCase(DeploymentBaseUseCase):
    def process_request(self, request_object: UpdateClientDeploymentRequestObject):
        client_id = request_object.clientId
        deployment_id = request_object.deploymentId
        self.repo.update_deployment_client(client_id, deployment_id)

        return


class DeleteClientDeploymentUseCase(DeploymentBaseUseCase):
    def process_request(self, request_object: UpdateClientDeploymentRequestObject):
        client_id = request_object.clientId
        deployment_id = request_object.deploymentId
        self.repo.delete_deployment_client(client_id, deployment_id)

        return ResultIdResponseObject(id=str(deployment_id))
