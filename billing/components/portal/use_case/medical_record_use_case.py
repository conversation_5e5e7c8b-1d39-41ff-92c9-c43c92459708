import calendar
from datetime import date, datetime

from billing.components.portal.dtos.billing_portal_models import BillingRecordDTO
from billing.components.portal.router.billing_portal_requests import (
    ClientMedicalRecordRequestObject,
    PatientMedicalRecordRequestObject,
)
from billing.components.portal.router.billing_portal_response_objects import (
    ClientMedicalRecordResponseObject,
    MedicalRecordEntry,
    PatientMedicalRecordResponseObject,
)
from billing.components.portal.service.service import BillingPortalService
from sdk.authorization.services.authorization import AuthorizationService
from sdk.common.usecase.use_case import UseCase
from sdk.deployment.dtos.consent import ConsentLogDTO
from sdk.deployment.service.deployment_service import DeploymentService
from sdk.module_result.dtos.user_note import UserNoteDTO
from sdk.module_result.modules.heart_rate.primitives import HeartRateDTO
from sdk.module_result.modules.oxygen_saturation import OxygenSaturationDTO
from sdk.module_result.service.module_result_service import ModuleResultService


class GetClientMedicalRecordsUseCase(UseCase):
    def __init__(self):
        self.service = BillingPortalService()

    def process_request(self, request_object: ClientMedicalRecordRequestObject) -> ClientMedicalRecordResponseObject:
        records: list[BillingRecordDTO] = self.service.retrieve_billing_records(
            client_id=request_object.clientId,
            report_end_date=request_object.reportEndDate,
        )
        user_ids = [record.patientId for record in records]
        users = self._user_names(user_ids)

        patients = []
        for record in records:
            patients.append(
                MedicalRecordEntry(
                    id=record.patientId,
                    givenName=users.get(record.patientId, {}).get("givenName", ""),
                    familyName=users.get(record.patientId, {}).get("familyName", ""),
                    numberOfCalls=record.numberOfCalls,
                    monitoringMinutes=record.monitoringMinutes,
                    diagnosis=record.diagnosisType,
                )
            )
        return ClientMedicalRecordResponseObject.from_dict(
            {
                "patients": patients,
            }
        )

    def _user_names(self, user_ids: list[str]) -> dict[str, dict[str, str]]:
        users = self.service.retrieve_users(user_ids)
        return {
            user.id: {
                "givenName": user.givenName,
                "familyName": user.familyName,
            }
            for user in users
        }


class GetPatientMedicalRecordsUseCase(UseCase):
    def __init__(self):
        self.service = BillingPortalService()

    def process_request(self, request_object: PatientMedicalRecordRequestObject) -> PatientMedicalRecordResponseObject:
        start_date_time, end_date_time = self._month_time_range(request_object.reportEndDate)
        user_id = request_object.userId

        notes: list[UserNoteDTO] = self.service.retrieve_notes(user_id, start_date_time, end_date_time)
        module_result_service = ModuleResultService()

        vital_results = []
        for p in [HeartRateDTO.get_primitive_name(), OxygenSaturationDTO.get_primitive_name()]:
            aggregated_vital_result = module_result_service.retrieve_date_range_aggregated_results(
                user_id=user_id,
                primitive_name=p,
                start_date=start_date_time,
                end_date=end_date_time,
            )
            if aggregated_vital_result["count"] > 0:
                vital_results.append(
                    {
                        "primitive": p,
                        "count": aggregated_vital_result["count"],
                        "average": aggregated_vital_result["average"],
                        "min": aggregated_vital_result["min"],
                        "max": aggregated_vital_result["max"],
                    }
                )

        consents: list[ConsentLogDTO] = DeploymentService().retrieve_consent_logs(
            user_id=user_id, end_date_time=end_date_time, limit=1
        )
        consent = consents[0] if consents else ConsentLogDTO()

        billing_record: BillingRecordDTO = self.service.retrieve_billing_record_by_user_id(
            user_id, request_object.reportEndDate
        )
        user = AuthorizationService().retrieve_simple_user_profile(user_id)
        return PatientMedicalRecordResponseObject.from_dict(
            {
                PatientMedicalRecordResponseObject.USER_ID: user.id,
                PatientMedicalRecordResponseObject.GIVEN_NAME: user.givenName,
                PatientMedicalRecordResponseObject.FAMILY_NAME: user.familyName,
                PatientMedicalRecordResponseObject.DIAGNOSIS: billing_record.diagnosisType,
                PatientMedicalRecordResponseObject.PROVIDER: billing_record.provider,
                PatientMedicalRecordResponseObject.START_DATE: start_date_time.strftime("%Y-%m-%d"),
                PatientMedicalRecordResponseObject.END_DATE: end_date_time.strftime("%Y-%m-%d"),
                PatientMedicalRecordResponseObject.CONSENT_DATE: (
                    consent.createDateTime.strftime("%Y-%m-%d") if consent.createDateTime else None
                ),
                PatientMedicalRecordResponseObject.CONSENT_ID: consent.id,
                PatientMedicalRecordResponseObject.DEVICE_TYPE: billing_record.deviceType,
                PatientMedicalRecordResponseObject.DEVICE_CONNECTION_DATE: billing_record.deviceConnectionDate,
                PatientMedicalRecordResponseObject.DEVICE_MODEL: billing_record.deviceModel,
                PatientMedicalRecordResponseObject.MONITORING_MINUTES: billing_record.monitoringMinutes,
                PatientMedicalRecordResponseObject.NUMBER_OF_CALLS: billing_record.numberOfCalls,
                PatientMedicalRecordResponseObject.VITALS: vital_results,
                PatientMedicalRecordResponseObject.CONSENT_LOGS: consents,
                PatientMedicalRecordResponseObject.NOTES: notes,
            },
            use_validator_field=False,
        )

    @staticmethod
    def _month_time_range(dt: date):
        start = datetime(dt.year, dt.month, 1, 0, 0, 0)
        last_day = calendar.monthrange(dt.year, dt.month)[1]
        end = datetime(dt.year, dt.month, last_day, 23, 59, 59)
        return start, end
