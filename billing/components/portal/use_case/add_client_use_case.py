import json
import logging

from billing.components.portal.dtos.billing_portal_models import ClientDTO
from billing.components.portal.repository.billing_portal_repository import BillingPortalRepository
from billing.components.portal.router.billing_portal_requests import (
    AddClientRequestObject,
    UpdateClientRequestObject,
)
from sdk.common.usecase.response_object import ResultIdResponseObject
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams

logger = logging.getLogger(__name__)


class AddClientUseCase(UseCase):
    @autoparams()
    def __init__(self, billing_portal_repository: BillingPortalRepository):
        self.repo = billing_portal_repository

    def process_request(self, request_object: AddClientRequestObject):
        payment_formula = request_object.paymentFormula

        try:
            payment_formula_dict = json.loads(payment_formula)
            logger.info(f"Parsed payment formula as JSON: {payment_formula_dict}")
        except json.JSONDecodeError:
            payment_formula_dict = {"formula": payment_formula, "rate": request_object.rate}
            logger.info(f"Using payment formula as string: {payment_formula_dict}")

        client = ClientDTO.from_dict(
            {ClientDTO.NAME: request_object.name, ClientDTO.PAYMENT_FORMULA: payment_formula_dict}
        )
        client_id = self.repo.create_client(client)
        logger.info(f"Created new client: {client.name} with ID: {client_id}")

        return ResultIdResponseObject(id=str(client_id))


class UpdateClientUseCase(UseCase):
    @autoparams()
    def __init__(self, billing_portal_repository: BillingPortalRepository):
        self.repo = billing_portal_repository

    def process_request(self, request_object: UpdateClientRequestObject):
        payment_formula = request_object.paymentFormula

        try:
            payment_formula_dict = json.loads(payment_formula)
            logger.info(f"Parsed payment formula as JSON: {payment_formula_dict}")
        except json.JSONDecodeError:
            payment_formula_dict = {"formula": payment_formula, "rate": request_object.rate}
            logger.info(f"Using payment formula as string: {payment_formula_dict}")

        client = ClientDTO.from_dict(
            {
                ClientDTO.ID: request_object.id,
                ClientDTO.NAME: request_object.name,
                ClientDTO.PAYMENT_FORMULA: payment_formula_dict,
            }
        )
        client_id = self.repo.update_client(client)
        logger.info(f"Updated client: {client.name} with ID: {client_id}")

        return ResultIdResponseObject(id=str(client.id))
