import datetime
import io
from copy import deepcopy

import openpyxl
from openpyxl.utils import get_column_letter

from billing.components.core.dtos.user_billing import Diagnosis, UserBilling
from billing.components.portal.dtos.billing_portal_models import (
    BillingRecordDTO,
    ClientDTO,
    CptChargeUnitDTO,
    CptCodeEnum,
)
from billing.components.portal.repository.billing_portal_repository import BillingPortalRepository
from billing.components.portal.router.billing_portal_requests import GetClientReportRequestObject
from billing.components.portal.router.billing_portal_response_objects import GetClientReportResponseObject
from sdk.authorization.services.authorization import AuthorizationService
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams

NOT_ELIGIBLE = "not_eligible"


class GetClientReportUseCase(UseCase):
    @autoparams()
    def __init__(self, repo: BillingPortalRepository):
        self.repo = repo

    def process_request(self, request_object: GetClientReportRequestObject):
        client: ClientDTO = self.repo.retrieve_client(request_object.clientId)
        selected_date = request_object.reportEndDate

        context = self.get_client_report_view(client, selected_date)

        download_response = self.download_csv(context)
        file_name = f"{client.name}_report_{selected_date}.xlsx"
        return GetClientReportResponseObject(downloadResponse=download_response, filename=file_name)

    def download_csv(self, context):
        wb = self.create_cpt_report_csv(context)
        stream = io.BytesIO()
        wb.save(stream)
        stream.seek(0)
        return stream

    def get_client_report_view(self, client: ClientDTO, selected_date: str):
        if selected_date:
            if isinstance(selected_date, str):
                selected_date = datetime.datetime.strptime(selected_date, "%Y-%m-%d").date()
            reports = self.repo.retrieve_billing_records(report_end_date=selected_date, client_id=client.id)
        else:
            reports = []
        table_output = []
        summary = {
            NOT_ELIGIBLE: 0,
            "99453": 0,
            "99454": 0,
            "99457": 0,
            "99458": 0,
        }
        for report in reports:
            temp = self.get_table_data_from_billing_report(report)
            if temp:
                table_output.append(temp)
                total_unit_count = 0
                for cpt_code in CptCodeEnum:
                    unit = temp.get(cpt_code.value, 0)
                    total_unit_count += unit
                    summary[cpt_code.value] += unit

                if total_unit_count == 0:
                    summary[NOT_ELIGIBLE] += 1
            else:
                summary[NOT_ELIGIBLE] += 1
        summary["total_patients"] = len(reports) - summary[NOT_ELIGIBLE]
        context = {
            "client": client,
            "reports": table_output,
            "selected_date": selected_date,
            "summary": summary,
        }
        return context

    def get_table_data_from_billing_report(self, report: BillingRecordDTO) -> dict:
        patient = AuthorizationService().retrieve_simple_user_profile(user_id=report.patientId)
        temp = {
            "patient": patient.get_full_name(),
            "mrn": patient.componentsData.get("billing", {}).get("mrn", ""),
            "dob": patient.dateOfBirth.strftime("%-m/%-d/%y") if patient.dateOfBirth else "",
            "provider": patient.componentsData.get("billing", {}).get(UserBilling.BILLING_PROVIDER_NAME, ""),
            "icd_10": patient.componentsData.get("billing", {})
            .get(UserBilling.DIAGNOSIS, {})
            .get(Diagnosis.ICD_CODE, ""),
            "monthReading": report.monthReading,
            "deviceConnectionDate": report.deviceConnectionDate,
            "record": report,
        }

        if report.isEligible:
            units: list[CptChargeUnitDTO] = self.repo.get_cpt_charge_units_by_billing_record(report.id)
            unit_count = 0
            for unit in units:
                if unit.extraDetails:
                    temp = unit.extraDetails | temp
                temp[unit.code.value] = unit.units
                if unit.units > 0:
                    unit_count += unit.units
                temp[f"{unit.code}_dos"] = unit.dateOfService
            if unit_count > 0:
                return temp

        return dict()

    def create_cpt_report_csv(self, context) -> openpyxl.Workbook:
        summary = context["summary"]
        table_output = context["reports"]

        wb = openpyxl.Workbook()
        summary_sheet = wb.active
        summary_sheet.title = "Summary"
        summary_headers = ["Metric", "Value"]
        summary_data = [
            ["Total Patients", summary["total_patients"]],
            ["CPT 99453", summary["99453"]],
            ["CPT 99454", summary["99454"]],
            ["CPT 99457", summary["99457"]],
            ["CPT 99458", summary["99458"]],
        ]

        self.populate_sheet_based_on_2_2_array(summary_sheet, summary_headers, summary_data)

        header_to_field = {
            "Patient": "patient",
            "MRN": "mrn",
            "Date of Birth": "dob",
            "Provider": "provider",
            "Diagnosis Type": "icd_10",
            "Total Data Submissions": "total_data_submission",
            "Count of Data Submission Days": "count_of_submission_days",
            "99453/99454 Date of Service": "99454_dos",
            "99453 Units": "99453",
            "99454 Units": "99454",
            "Monitoring Time (min)": "monitoring_time",
            "Count of Interactive Calls": "count_of_interactive_calls",
            "99457/8 Date of Service": "99457_dos",
            "99457 Units": "99457",
            "99458 Units": "99458",
        }

        self.populate_sheet_based_on_mapping(wb.create_sheet(title="Eligible Reports"), header_to_field, table_output)

        if context.get("has_redox", False):
            reports_sheet = wb.create_sheet(title="Manual Entries")
            ehr_data = context.get("missing_records", []) + context.get("failed_records_logs", [])
            ehr_header_mapping = deepcopy(header_to_field)
            ehr_header_mapping["Error"] = "log"

            self.populate_sheet_based_on_mapping(reports_sheet, ehr_header_mapping, ehr_data)

        for sheet in wb.worksheets:
            for column in sheet.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)
                for cell in column:
                    if cell.value:
                        max_length = max(max_length, len(str(cell.value)))
                adjusted_width = max_length + 2
                sheet.column_dimensions[column_letter].width = adjusted_width
        return wb

    @staticmethod
    def clean_sheet_value(value):
        if isinstance(value, datetime.date):
            return value.strftime("%-m/%-d/%y")
        return value

    def populate_sheet_based_on_mapping(self, sheet, header_mapping, data):
        """
        Example:
            header_mapping = {
                "Patient": "patient",
                "MRN": "mrn",
            }
            data = [
                {"patient": "John Doe", "mrn": "12345"},
                {"patient": "Jane Smith", "mrn": "67890"},
            ]
            Output:
            +-----------------+---------+
            | Patient         | MRN     |
            +-----------------+---------+
            | John Doe        | 12345   |
            | Jane Smith      | 67890   |
            +-----------------+---------+
        """
        headers = header_mapping.keys()
        for col_idx, header in enumerate(headers, 1):
            cell = sheet.cell(row=1, column=col_idx)
            cell.value = header
            cell.font = openpyxl.styles.Font(bold=True)
        for row_idx, report_data in enumerate(data, 2):
            for col_idx, header in enumerate(headers, 1):
                header_name = header_mapping[header]
                value = report_data.get(header_name, "")
                sheet.cell(row=row_idx, column=col_idx).value = self.clean_sheet_value(value)

    def populate_sheet_based_on_2_2_array(self, sheet, headers, array):
        """
        Example:
            headers = ["Patient", "MRN"]
            array = [
                ["John Doe", "12345"],
                ["Jane Smith", "67890"],
            ]
            Output:
            +-----------------+---------+
            | Patient         | MRN     |
            +-----------------+---------+
            | John Doe        | 12345   |
            | Jane Smith      | 67890   |
            +-----------------+---------+
        """
        for col_idx, header in enumerate(headers, 1):
            cell = sheet.cell(row=1, column=col_idx)
            cell.value = header
            cell.font = openpyxl.styles.Font(bold=True)
        for row_idx, row_data in enumerate(array, 2):
            for col_idx, cell_value in enumerate(row_data, 1):
                sheet.cell(row=row_idx, column=col_idx).value = self.clean_sheet_value(cell_value)
