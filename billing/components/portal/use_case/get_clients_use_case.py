from billing.components.portal.dtos.billing_portal_models import ClientDTO
from billing.components.portal.repository.billing_portal_repository import BillingPortalRepository
from billing.components.portal.router.billing_portal_response_objects import (
    GetClientsResponseObject,
)
from sdk.authorization.use_cases.off_board_user_use_case import RequestObject
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams


class GetClientsUseCase(UseCase):
    @autoparams()
    def __init__(self, repo: BillingPortalRepository):
        self.repo = repo

    def process_request(self, request_object: RequestObject):
        clients = self.repo.retrieve_clients()
        return GetClientsResponseObject.from_dict(
            {"clients": [ClientDTO.from_dict(client.to_dict()) for client in clients]}
        )
