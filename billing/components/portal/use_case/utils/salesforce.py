from billing.components.portal.models import SalesForceFile
from sdk.storage.utils import download_file_id
from .csv_utils import CSVDictReader

SALES_FORCE_STATUS = "status"
SALES_FORCE_INSURANCE = "insurance"
SALES_FORCE_ICD_10 = "icd_10"
SALES_FORCE_PROVIDER = "Provider"
SALES_FORCE_STATUS_ACTIVE_RPM = "Active - RPM"
SALES_FORCE_MRN = "MRN"
SALES_FORCE_EMPTY_DICT = {
    SALES_FORCE_MRN: "",
    SALES_FORCE_PROVIDER: "",
    SALES_FORCE_ICD_10: "",
    SALES_FORCE_INSURANCE: "",
}


def read_sales_force_file():
    file = SalesForceFile.objects.all().order_by("-versionNumber").first()
    download_response = download_file_id(file.fileId)
    sales_force_csv = CSVDictReader(bytes=download_response.content)

    users_sf_data = {}
    for record in sales_force_csv:
        users_sf_data[record["Huma ID"]] = {
            SALES_FORCE_MRN: record["Patient MRN"],
            SALES_FORCE_PROVIDER: record["Patient Provider: Full Name"],
            SALES_FORCE_ICD_10: record["Primary ICD-10"],
            SALES_FORCE_INSURANCE: record["Patient Primary Insurance"],
            SALES_FORCE_STATUS: record["Patient Workflow"],
        }

    return users_sf_data
