import csv
import os
from io import BytesIO


class CSVDictReader:
    def __init__(self, file_path: str = None, bytes: BytesIO = None):
        self.file_path = file_path
        self.bytes = bytes
        self.header = []
        self.rows = []
        self._read_csv()

    def _read_csv(self):
        if self.file_path:
            self._read_csv_from_path()
        elif self.bytes:
            self._read_csv_from_bytes()
        else:
            raise IOError("No data source provided")

    def _read_csv_from_bytes(self):
        """
        Reads data from BytesIO object
        """
        self.bytes.seek(0)
        text_stream = self.bytes.read().decode("utf-8").splitlines()
        self._read_from_stream(text_stream)

    def _read_csv_from_path(self):
        if not os.path.exists(self.file_path):
            raise FileNotFoundError(f"The file {self.file_path} does not exist.")

        with open(self.file_path, mode="r", newline="", encoding="utf-8") as csvfile:
            self._read_from_stream(csvfile)

    def _read_from_stream(self, text_stream):
        reader = csv.reader(text_stream)
        self.header = next(reader)
        for row in reader:
            self.rows.append(row)

    def __iter__(self):
        for row in self.rows:
            yield {self.header[i]: row[i] for i in range(len(self.header))}

    def __len__(self):
        return len(self.rows)

    def __getitem__(self, index):
        if index < 0 or index >= len(self.rows):
            raise IndexError("Index out of range")
        return {self.header[i]: self.rows[index][i] for i in range(len(self.header))}

    def get_header(self):
        return self.header

    def add_dict_row(self, row: dict):
        if not isinstance(row, dict):
            raise ValueError("Row must be a dictionary.")
        if set(row.keys()) != set(self.header):
            raise ValueError("Row keys do not match the header.")
        self.rows.append([row[key] for key in self.header])

    def add_list_row(self, row: list):
        if not isinstance(row, list):
            raise ValueError("Row must be a list.")
        if len(row) != len(self.header):
            raise ValueError("Row length does not match the header length.")
        self.rows.append(row)
