import codecs
import csv
from datetime import date
from io import BytesIO

from billing.components.portal.dtos.billing_portal_models import InvoiceReport
from billing.components.portal.service.service import BillingPortalService
from sdk import convertibleclass
from sdk.common.usecase.request_object import RequestObject
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.convertible import default_field


@convertibleclass
class CollectAllClientsInvoicesRequestObject(RequestObject):
    reportEndDate: date = default_field()


class CollectAllClientsInvoices(UseCase):
    def __init__(self):
        self.service = BillingPortalService()

    def process_request(self, request_object: CollectAllClientsInvoicesRequestObject):
        request_end_date = request_object.reportEndDate
        csv_data = BytesIO()
        csv_text_wrapper = codecs.getwriter("utf-8")(csv_data)
        csv_writer = csv.writer(csv_text_wrapper)

        # Write header
        csv_writer.writerow(["Client", "Rate", "Unit", "Description", "Date"])

        if request_end_date:
            reports = self.service.retrieve_invoice_reports(request_end_date)
            self._write_reports(csv_writer, reports, request_end_date)
            filename = f"clients_invoices_{request_end_date.strftime('%Y-%m-%d')}.csv"
        else:
            dates = self.service.retrieve_distinct_invoice_dates()
            for date_item in dates:
                reports = self.service.retrieve_invoice_reports(date_item)
                self._write_reports(csv_writer, reports, date_item)
            filename = "all_clients_invoices.csv"

        csv_data.seek(0)
        return filename, csv_data

    @staticmethod
    def _write_reports(csv_writer, reports: list[InvoiceReport], report_date):
        for item in reports:
            client_name = item.client.name
            for entry in item.invoiceEntries:
                csv_writer.writerow(
                    [
                        client_name,
                        entry.rates,
                        entry.units,
                        entry.serviceDescription,
                        report_date.strftime("%Y-%m-%d"),
                    ]
                )
