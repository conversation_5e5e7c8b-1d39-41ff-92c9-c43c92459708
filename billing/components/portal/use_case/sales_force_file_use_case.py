import logging
import typing

from werkzeug.datastructures import FileStorage

from billing.components.portal.repository.billing_portal_repository import BillingPortalRepository
from billing.components.portal.router.billing_portal_requests import (
    AddSalesForceFileRequestObject,
    DownloadSalesForceFileRequestObject,
)
from billing.components.portal.router.billing_portal_response_objects import (
    AddSalesForceFileResponseObject,
    DownloadSalesForceFileResponseObject,
)
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams
from sdk.storage.use_case.storage_request_objects import DownloadFileRequestObjectV1, UploadFileRequestObjectV1
from sdk.storage.use_case.storage_response_objects import DownloadFileResponseObjectV1
from sdk.storage.use_case.storage_use_cases import DownloadFileUseCaseV1, UploadFileUseCaseV1

logger = logging.getLogger(__name__)


class AddSalesForceFileUseCase(UseCase):
    @autoparams()
    def __init__(self, repo: BillingPortalRepository):
        self.repo = repo

    def process_request(self, request_object: AddSalesForceFileRequestObject):
        file_data = request_object.fileData
        filename = request_object.fileName
        uploaded_at = request_object.uploadedAt
        user_id = request_object.userId

        uploaded_file_id = self.upload_file(filename, file_data, user_id=user_id)
        sales_force_file = self.repo.create_salesforce_file(uploaded_file_id=uploaded_file_id, uploaded_at=uploaded_at)

        logger.info(f"Created new SalesForceFile with ID: {sales_force_file.versionNumber}")

        return AddSalesForceFileResponseObject(
            id=sales_force_file.versionNumber, message="SalesForceFile uploaded successfully"
        )

    @staticmethod
    def upload_file(
        file_name: str,
        file: typing.Any,
        user_id: str = None,
    ):
        request_obj = UploadFileRequestObjectV1.from_dict(
            {
                UploadFileRequestObjectV1.FILE: FileStorage(filename=file_name, stream=file),
                UploadFileRequestObjectV1.FILE_NAME: file_name,
                UploadFileRequestObjectV1.USER_ID: user_id,
            }
        )
        rsp = UploadFileUseCaseV1().execute(request_obj)
        return rsp.id


class DownloadSalesForceFileUseCase(UseCase):
    @autoparams()
    def __init__(self, repo: BillingPortalRepository):
        self.repo = repo

    def process_request(self, request_object: DownloadSalesForceFileRequestObject):
        sales_force_file = self.repo.retrieve_salesforce_file(request_object.version)
        if not sales_force_file:
            raise ValueError("The requested SalesForceFile does not exist.")

        file_id = sales_force_file.fileId
        file_data: DownloadFileResponseObjectV1 = DownloadFileUseCaseV1().execute(
            DownloadFileRequestObjectV1(fileId=file_id)
        )

        return DownloadSalesForceFileResponseObject.from_dict(
            {
                DownloadSalesForceFileResponseObject.FILE_NAME: file_data.fileName,
                DownloadSalesForceFileResponseObject.FILE_DATA: file_data.content,
            }
        )
