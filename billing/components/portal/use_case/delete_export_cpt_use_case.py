import logging
from datetime import date, datetime

from dateutil.relativedelta import relativedelta

from billing.components.billing_portal.repository.billing_portal_repository import BillingPortalRepository
from billing.components.billing_portal.router.billing_portal_requests import DeleteExportRequestObject
from sdk.common.usecase.response_object import OkResponse
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams
from sdk.deployment.service.deployment_service import DeploymentService
from sdk.phoenix.config.server_config import PhoenixServerConfig

logger = logging.getLogger(__name__)


class DeleteExportUseCase(UseCase):
    @autoparams()
    def __init__(self, repo: BillingPortalRepository, config: PhoenixServerConfig = None):
        self.repo = repo
        self.config = config.server.billingPortal

    def process_request(self, request_object: DeleteExportRequestObject):
        if request_object.deploymentId is None:
            if not self.config or not self.config.tags:
                logger.info("No tags provided for billing report generation. Discarding the task.")
                return OkResponse(False)

            tags = self.config.tags.split(",")

            deployment_service = DeploymentService()
            deployment_ids = deployment_service.retrieve_deployment_ids_by_tags(tags)
        else:
            deployment_ids = [request_object.deploymentId]

        report_date = self._set_date(request_object.exportYear, request_object.exportMonth)

        logger.info(f"Removing CPT codes report for deployments: {deployment_ids} and report date: {report_date}")
        self.repo.delete_cpt_codes(deployment_ids=deployment_ids, report_date=report_date)

        return OkResponse(True)

    @staticmethod
    def _set_date(year: int, month: int) -> date:
        if year is not None and month is not None:
            end_date = datetime(year=year, month=month, day=1) + relativedelta(months=1) - relativedelta(seconds=1)
        else:
            now = datetime.now()
            end_date = datetime(year=now.year, month=now.month, day=now.day) - relativedelta(seconds=1)

        return end_date.date()
