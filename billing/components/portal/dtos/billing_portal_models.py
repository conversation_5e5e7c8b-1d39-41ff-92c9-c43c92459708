from datetime import date, datetime
from enum import Enum
from typing import Union

from sdk.authorization.dtos.user import UserDTO
from sdk.common.utils.convertible import convertibleclass, default_field, meta, required_field
from sdk.common.utils.huconvertible import HuConvertible
from sdk.common.utils.validators import default_date_meta
from sdk.deployment.dtos.deployment import DeploymentDTO


@convertibleclass
class ClientDTO:
    ID = "id"
    NAME = "name"
    PAYMENT_FORMULA = "paymentFormula"

    id: str = default_field()
    name: str = default_field()
    paymentFormula: dict = default_field()


def field_val_to_str(val: Union[str, date]) -> str | None:
    if isinstance(val, date):
        return val.strftime("%Y-%m-%d")
    if val is None:
        return None
    return val


def str_val_to_field(val: Union[str, date]) -> date | None:
    if isinstance(val, date):
        return val
    if val is None or val == "":
        return None
    if not isinstance(val, str):
        raise ValueError(f"Expected str or date, got {type(val)} - for value: {val}")
    return datetime.strptime(val, "%Y-%m-%d").date()


date_meta = meta(field_to_value=str_val_to_field, value_to_field=field_val_to_str)


class BillingRecordDTO(HuConvertible):
    id: str = default_field()
    deployment: DeploymentDTO = default_field()
    deploymentId: str = default_field()
    patient: UserDTO = default_field()
    patientId: str = default_field()
    provider: str = default_field()
    diagnosisType: str = default_field()
    deviceConnectionDate: str = default_field(metadata=date_meta)
    primaryInsurance: str = default_field()
    reportEndDate: str = default_field(metadata=date_meta)
    isEligible: bool = default_field()
    monitoringMinutes: int = default_field(default=0)
    numberOfCalls: int = default_field(default=0)
    monthReading: int = default_field(default=0)
    consentDate: str = default_field(metadata=date_meta)
    consentId: str = default_field()
    deviceType: str = default_field()
    deviceModel: str = default_field()

    @classmethod
    def from_dict(
        cls,
        d: dict,
        use_validator_field=True,
        ignored_fields: Union[list, tuple] = None,
        ignore_none: bool = False,
    ):
        if "deploymentId" not in d:
            d["deploymentId"] = d.pop("deployment")
        if "patientId" not in d:
            d["patientId"] = d.pop("patient")
        return super().from_dict(
            d,
            use_validator_field=use_validator_field,
            ignored_fields=ignored_fields,
            ignore_none=ignore_none,
        )


class CptCodeEnum(Enum):
    CPT_99453 = "99453"
    CPT_99454 = "99454"
    CPT_99457 = "99457"
    CPT_99458 = "99458"

    @classmethod
    def choices(cls):
        return [(item.value, item.name) for item in cls]


class CptChargeUnitDTO(HuConvertible):
    BILLING_RECORD = "billingRecord"
    BILLING_RECORD_ID = "billingRecordId"

    id: str = default_field()
    code: CptCodeEnum = default_field()
    units: int = default_field()
    dateOfService: str = default_field(metadata=date_meta)
    extraDetails: dict = default_field()
    billingRecord: BillingRecordDTO = default_field()
    billingRecordId: str = default_field()

    @classmethod
    def from_dict(
        cls,
        d: dict,
        use_validator_field=True,
        ignored_fields: Union[list, tuple] = None,
        ignore_none: bool = False,
    ):
        billing_record_id = d.pop(cls.BILLING_RECORD, None)
        if cls.BILLING_RECORD_ID not in d:
            d[cls.BILLING_RECORD_ID] = billing_record_id
        return super().from_dict(
            d,
            use_validator_field=use_validator_field,
            ignored_fields=ignored_fields,
            ignore_none=ignore_none,
        )


class CPTGeneralExport(HuConvertible):
    userId: str = default_field()
    user: UserDTO = default_field()
    deploymentId: str = default_field()
    reportDate: str = default_field(metadata=date_meta)
    billingFirstReading: str = default_field(metadata=date_meta)
    billingProviderName: str = default_field()
    billingDiagnosisDescription: str = default_field()
    billingDiagnosisICD10Code: str = default_field()
    billingDiagnosisOrder: int = default_field()
    primaryInsuranceCarrier: str = default_field()
    primaryInsuranceCarrierPayerId: str = default_field()
    secondaryInsuranceCarrier: str = default_field()
    secondaryInsuranceCarrierPayerId: str = default_field()
    cpt98975: int = default_field()
    cpt98976: int = default_field()
    cpt98980: int = default_field()
    cpt98981: int = default_field()
    cpt99453: int = default_field()
    cpt99454: int = default_field()
    cpt99457: int = default_field()
    cpt99458: int = default_field()
    monitoringTimeMinutes: int = default_field()
    numberOfCalls: int = default_field()
    numberOfSubmissionDays: int = default_field()
    numberOfSubmissions: int = default_field()

    @classmethod
    def from_dict(
        cls,
        d: dict,
        use_validator_field=True,
        ignored_fields: Union[list, tuple] = None,
        ignore_none: bool = False,
    ):
        if "userId" not in d:
            d["userId"] = d.pop("user")
        return super().from_dict(
            d,
            use_validator_field=use_validator_field,
            ignored_fields=ignored_fields,
            ignore_none=ignore_none,
        )

    def to_dict(
        self,
        include_none=True,
        ignored_fields: Union[list, tuple] = None,
        exclude_fields: Union[list, tuple] = None,
    ):
        if self.billingFirstReading == "":
            self.billingFirstReading = None
        return super().to_dict(
            include_none=include_none,
            ignored_fields=ignored_fields,
            exclude_fields=exclude_fields,
        )

    @classmethod
    def from_export_dict(cls, export_dict: dict):
        """export_dict structure is as follows:
        {
        "billing": {
          "billingFirstReading": "",
          "billingProviderName": "someName",
          "diagnosis": {
            "description": "diagnosis description",
            "icd10Code": "ICD",
            "order": 1
          },
          "primaryInsuranceCarrier": "",
          "primaryInsuranceCarrierPayerId": "",
          "secondaryInsuranceCarrier": "",
          "secondaryInsuranceCarrierPayerId": ""
        },
        "deploymentId": "5d386cc6ff885918d96edb2c",
        "moduleId": "billing",
        "report": {
          "cpt98975": 0,
          "cpt98976": 0,
          "cpt98980": 0,
          "cpt98981": 0,
          "cpt99453": 0,
          "cpt99454": 0,
          "cpt99457": 0,
          "cpt99458": 0,
          "monitoringTimeMins": 0,
          "numberOfCalls": 0,
          "numberOfSubmissionDays": 0,
          "numberOfSubmissions": 0
        },
        "user": {
          "biologicalSex": "",
          "dateOfBirth": "1988-02-20",
          "email": "<EMAIL>",
          "familyName": "test",
          "gender": "",
          "givenName": "test",
          "language": "",
          "phoneNumber": "+380999999999",
          "primaryAddress": "",
          "timezone": "UTC"
        },
        "userId": "5e8f0c74b50aa9656c34789b"
        }
        """
        billing = export_dict.get("billing", {})
        report = export_dict.get("report", {})

        return cls(
            userId=export_dict.get("userId"),
            deploymentId=export_dict.get("deploymentId"),
            reportDate=field_val_to_str(export_dict.get("reportDate")),
            billingFirstReading=field_val_to_str(billing.get("billingFirstReading")),
            billingProviderName=billing.get("billingProviderName"),
            billingDiagnosisDescription=billing.get("diagnosis", {}).get("description"),
            billingDiagnosisICD10Code=billing.get("diagnosis", {}).get("icd10Code"),
            billingDiagnosisOrder=billing.get("diagnosis", {}).get("order"),
            primaryInsuranceCarrier=billing.get("primaryInsuranceCarrier"),
            primaryInsuranceCarrierPayerId=billing.get("primaryInsuranceCarrierPayerId"),
            secondaryInsuranceCarrier=billing.get("secondaryInsuranceCarrier"),
            secondaryInsuranceCarrierPayerId=billing.get("secondaryInsuranceCarrierPayerId"),
            cpt98975=report.get("cpt98975", 0),
            cpt98976=report.get("cpt98976", 0),
            cpt98980=report.get("cpt98980", 0),
            cpt98981=report.get("cpt98981", 0),
            cpt99453=report.get("cpt99453", 0),
            cpt99454=report.get("cpt99454", 0),
            cpt99457=report.get("cpt99457", 0),
            cpt99458=report.get("cpt99458", 0),
            monitoringTimeMinutes=report.get("monitoringTimeMins", 0),
            numberOfCalls=report.get("numberOfCalls", 0),
            numberOfSubmissionDays=report.get("numberOfSubmissionDays", 0),
            numberOfSubmissions=report.get("numberOfSubmissions", 0),
        )


class CPT99454(HuConvertible):
    billingStartDate: str = default_field()
    billingEndDate: str = default_field()
    countOfSubmissions: int = default_field()
    countOfSubmissionDays: int = default_field()
    earliestBillingDate: str = default_field()


class CPTMonthlyExport(HuConvertible):
    """
      {
      "userId": "5e8f0c74b50aa9656c34789b",
      "deploymentId": "5d386cc6ff885918d96edb2c",
      "moduleId": "billing",
      "reportStartDate": "2022-06-01",
      "reportEndDate": "2022-06-30",
      "user": {
        "givenName": "test",
        "familyName": "test",
        "dateOfBirth": "1988-02-20"
      },
      "billing": {
        "cpt99453_4": {
          "billingProviderName": "",
          "billingProviderId": "",
          "billingProviderAbbreviation": ""
        },
        "cpt99457_8": {
          "billingProviderName": "",
          "billingProviderId": "",
          "billingProviderAbbreviation": ""
        },
        "icd10Code": "ICD",
        "description": "diagnosis description",
        "billingFirstReading": "2022-05-10",
        "primaryInsuranceCarrier": "",
        "secondaryInsuranceCarrier": "",
        "primaryInsuranceCarrierPayerId": "",
        "secondaryInsuranceCarrierPayerId": ""
      },
      "cptCodesDetails": {
        "cpt99453": {
          "billingStartDate": "2022-05-10",
          "billingEndDate": "2022-06-08",
          "countOfSubmissions": 21,
          "countOfSubmissionDays": 21,
          "earliestBillingDate": "2022-05-25"
        },
        "cpt99454": {
          "billingStartDate_0": "2022-05-10",
          "billingEndDate_0": "2022-06-08",
          "countOfSubmissions_0": 21,
          "countOfSubmissionDays_0": 21,
          "earliestBillingDate_0": "2022-05-25",
          "billingStartDate_1": "",
          "billingEndDate_1": "",
          "countOfSubmissions_1": "",
          "countOfSubmissionDays_1": "",
          "earliestBillingDate_1": ""
        },
        "cpt99457": {
          "billingStartDate": "2022-06-01",
          "billingEndDate": "2022-06-30",
          "totalBillable": 0
        },
        "cpt99458": {
          "billingStartDate": "2022-06-01",
          "billingEndDate": "2022-06-30",
          "totalBillable": 0
        },
        "monitoringTimeMins": 0,
        "numberOfCalls": 0
      }
    }
    """

    userId: str = default_field()
    user: UserDTO = default_field()
    deploymentId: str = default_field()
    moduleId: str = default_field()
    reportStartDate: str = default_field(metadata=date_meta)
    reportEndDate: str = default_field(metadata=date_meta)
    givenName: str = default_field()
    familyName: str = default_field()
    dateOfBirth: str = default_field(metadata=date_meta)
    monitoringTimeMins: int = default_field()
    numberOfCalls: int = default_field()
    cpt99457Units: int = default_field()
    cpt99458Units: int = default_field()
    cpt99457_8DateOfService: str = default_field(metadata=date_meta)

    cpt99453_4CountOfSubmissionDays: int = default_field()

    cpt99453BillingStartDate: str = default_field(metadata=date_meta)
    cpt99453BillingEndDate: str = default_field(metadata=date_meta)
    cpt99453CountOfSubmissions: int = default_field()
    cpt99453EarliestBillingDate: str = default_field(metadata=date_meta)
    cpt99454BillingStartDate: str = default_field(metadata=date_meta)
    cpt99454BillingEndDate: str = default_field(metadata=date_meta)
    cpt99454CountOfSubmissions: int = default_field()
    cpt99454_CountOfSubmissionDays: int = default_field()
    cpt99454_EarliestBillingDate: str = default_field(metadata=date_meta)

    cpt99454BillingStartDate0: str = default_field(metadata=date_meta)
    cpt99454BillingEndDate0: str = default_field(metadata=date_meta)
    cpt99454CountOfSubmission0: int = default_field()
    cpt99454CountOfSubmissionDays0: int = default_field()
    cpt99454EarliestBillingDate0: str = default_field(metadata=date_meta)

    cpt99454BillingStartDate1: str = default_field(metadata=date_meta)
    cpt99454BillingEndDate1: str = default_field(metadata=date_meta)
    cpt99454CountOfSubmission1: int = default_field()
    cpt99454CountOfSubmissionDays1: int = default_field()
    cpt99454EarliestBillingDate1: str = default_field(metadata=date_meta)

    @classmethod
    def from_dict(
        cls,
        d: dict,
        use_validator_field=True,
        ignored_fields: Union[list, tuple] = None,
        ignore_none: bool = False,
    ):
        if "userId" not in d:
            d["userId"] = d.pop("user")
        return super().from_dict(
            d,
            use_validator_field=use_validator_field,
            ignored_fields=ignored_fields,
            ignore_none=ignore_none,
        )

    def cpt99454(self, iteration: str = "") -> CPT99454:
        result = CPT99454.from_dict(
            {
                "billingStartDate": getattr(self, f"cpt99454BillingStartDate{iteration}", None),
                "billingEndDate": getattr(self, f"cpt99454BillingEndDate{iteration}", None),
                "countOfSubmissions": getattr(self, f"cpt99454_CountOfSubmission{iteration}", 0),
                "countOfSubmissionDays": getattr(self, f"cpt99454_CountOfSubmissionDays{iteration}", 0),
                "earliestBillingDate": getattr(self, f"cpt99454_EarliestBillingDate{iteration}", None),
            }
        )
        if not result.countOfSubmissions:
            result.countOfSubmissions = 0
        if not result.countOfSubmissionDays:
            result.countOfSubmissionDays = 0
        return result

    @classmethod
    def from_export_dict(cls, export_dict: dict):
        def safe_int(d: dict, key: str, default: int = 0) -> int:
            if key in d and not d.get(key):
                return default
            return int(d.get(key, default))

        user = export_dict.get("user", {})
        cpt_codes_details = export_dict.get("cptCodesDetails", {})
        cpt99453 = cpt_codes_details.get("cpt99453", {})
        cpt99454 = cpt_codes_details.get("cpt99454", {})
        cpt99457 = cpt_codes_details.get("cpt99457", {})
        cpt99458 = cpt_codes_details.get("cpt99458", {})

        return cls(
            userId=export_dict.get("userId"),
            deploymentId=export_dict.get("deploymentId"),
            moduleId=export_dict.get("moduleId"),
            reportStartDate=export_dict.get("reportStartDate"),
            reportEndDate=export_dict.get("reportEndDate"),
            givenName=user.get("givenName"),
            familyName=user.get("familyName"),
            dateOfBirth=user.get("dateOfBirth"),
            monitoringTimeMins=safe_int(cpt_codes_details, "monitoringTimeMins", 0),
            numberOfCalls=safe_int(cpt_codes_details, "numberOfCalls", 0),
            cpt99457Units=safe_int(cpt99457, "totalBillable", 0),
            cpt99458Units=safe_int(cpt99458, "totalBillable", 0),
            cpt99457_8DateOfService=cpt99458.get("billingEndDate"),
            cpt99453_4CountOfSubmissionDays=safe_int(cpt99453, "countOfSubmissionDays", 0),
            cpt99453BillingStartDate=cpt99453.get("billingStartDate"),
            cpt99453BillingEndDate=cpt99453.get("billingEndDate"),
            cpt99453CountOfSubmissions=safe_int(cpt99453, "countOfSubmissions", 0),
            cpt99453EarliestBillingDate=cpt99453.get("earliestBillingDate"),
            cpt99454BillingStartDate=cpt99454.get("billingStartDate"),
            cpt99454BillingEndDate=cpt99454.get("billingEndDate"),
            cpt99454CountOfSubmissions=safe_int(cpt99454, "countOfSubmissions", 0),
            cpt99454_CountOfSubmissionDays=safe_int(cpt99454, "countOfSubmissionDays", 0),
            cpt99454_EarliestBillingDate=cpt99454.get("earliestBillingDate"),
            cpt99454BillingStartDate0=cpt99454.get("billingStartDate_0"),
            cpt99454BillingEndDate0=cpt99454.get("billingEndDate_0"),
            cpt99454CountOfSubmission0=safe_int(cpt99454, "countOfSubmissions_0", 0),
            cpt99454CountOfSubmissionDays0=safe_int(cpt99454, "countOfSubmissionDays_0", 0),
            cpt99454EarliestBillingDate0=cpt99454.get("earliestBillingDate_0"),
            cpt99454BillingStartDate1=cpt99454.get("billingStartDate_1"),
            cpt99454BillingEndDate1=cpt99454.get("billingEndDate_1"),
            cpt99454CountOfSubmission1=safe_int(cpt99454, "countOfSubmissions_1", 0),
            cpt99454CountOfSubmissionDays1=safe_int(cpt99454, "countOfSubmissionDays_1", 0),
            cpt99454EarliestBillingDate1=cpt99454.get("earliestBillingDate_1"),
        )


@convertibleclass
class InvoiceDTO:
    startDate: date = required_field(metadata=default_date_meta())
    endDate: date = required_field(metadata=default_date_meta())
    extraDetails: str = default_field()
    client: ClientDTO = default_field()


@convertibleclass
class InvoiceEntryDTO:
    rates: int = default_field()
    units: int = default_field()
    serviceDescription: str = default_field()
    parentInvoice: InvoiceDTO = default_field()


@convertibleclass
class InvoiceReport:
    CLIENT = "client"
    INVOICE = "invoice"
    INVOICE_ENTRIES = "invoiceEntries"

    client: ClientDTO = default_field()
    invoice: InvoiceDTO = default_field()
    invoiceEntries: list[InvoiceEntryDTO] = default_field()


@convertibleclass
class SalesForceFileDTO:
    UPLOADED_AT = "uploadedAt"
    VERSION_NUMBER = "versionNumber"
    FILE_ID = "fileId"

    uploadedAt: datetime = default_field()
    versionNumber: int = default_field()
    fileId: str = default_field()
