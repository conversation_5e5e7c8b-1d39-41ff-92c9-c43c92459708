import logging
from typing import Optional, Union

from flask import Blueprint

from billing.components.portal.config.config import BillingPortalConfig
from billing.components.portal.di.components import bind_cpt_export_repository
from billing.components.portal.router.billing_portal_routers import api as billing_portal_route
from sdk.common.utils.inject import Binder
from sdk.phoenix.component_manager import PhoenixBaseComponent
from sdk.phoenix.config.server_config import PhoenixServerConfig

logger = logging.getLogger(__name__)


class BillingPortalComponent(PhoenixBaseComponent):
    config_class = BillingPortalConfig
    tag_name = "billingPortal"
    tasks = ["billing.components.portal"]

    def bind(self, binder: Binder, config: PhoenixServerConfig):
        super().bind(binder, config)

        bind_cpt_export_repository(binder)
        logger.debug("BillingPortalComponent BillingPortalRepository bound")

    @property
    def blueprint(self) -> Optional[Union[Blueprint, list[Blueprint]]]:
        blueprints = [billing_portal_route]
        return blueprints
