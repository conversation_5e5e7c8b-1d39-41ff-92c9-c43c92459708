<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Patient Medical Record</title>
    <style>
        tr {
            border: 1px solid black;
        }
        th, td {
            padding: 5px;
            text-align: left;
        }
    </style>
</head>
<body>
<table border="1" cellspacing="0" cellpadding="5" style="border-collapse: collapse; font-family: Arial; font-size: 14px;">
    <tr>
        <td><strong>Patient Name:</strong></td>
        <td colspan="2">{{ record.givenName }} {{ record.familyName }}</td>
        <td><strong>Huma ID</strong></td>
        <td>{{ record.userId }}</td>
        <td><strong>Diagnosis:</strong></td>
        <td colspan="2">{{ record.diagnosisType }}</td>
    </tr>
    <tr>
        <td><strong>RPM Provider:</strong></td>
        <td colspan="2">{{ record.provider }}</td>
        <td><strong>Report Start Date</strong></td>
        <td>{{ record.startDate }}</td>
        <td><strong>Report End Date</strong></td>
        <td colspan="2">{{ record.endDate }}</td>
    </tr>
    <tr>
        <td><em><strong>Consent Date</strong></em></td>
        <td>{{ record.consentDate }}</td>
        <td><em><strong>eConsent Captured</strong></em></td>
        <td colspan="5">{{ record.consentId }}</td>
    </tr>
    <tr>
        <td><strong><em>Device Type</em></strong></td>
        <td>{{ record.deviceType }}</td>
        <td><strong><em>Device Connected</em></strong></td>
        <td>{{ record.deviceConnectionDate }}</td>
        <td><strong>Model:</strong></td>
        <td colspan="3">{{ record.deviceModel }}</td>
    </tr>

    <tr><th colspan="8">Monitoring Summary</th></tr>

    <tr>
        <th colspan="4">Minutes Total</th>
        <th colspan="4">Interactive Call Count</th>
    </tr>
    <tr>
        <td colspan="4">{{ record.monitoringMinutes }}</td>
        <td colspan="4">{{ record.numberOfCalls }}</td>
    </tr>

    <tr><th colspan="8">Device Reading Summary</th></tr>
    <tr>
        <th>Type</th>
        <th>Total Submission Days</th>
        <th>Average</th>
        <th>Min</th>
        <th>Max</th>
        <th>Source</th>
        <td colspan="2"></td>
    </tr>
    {% for reading in record.vitals %}
    <tr>
        <td>{{ reading.primitive }}</td>
        <td>{{ reading.count }}</td>
        <td>{{ reading.average }}</td>
        <td>{{ reading.min }}</td>
        <td>{{ reading.max }}</td>
        <td>Fitbit</td>
        <td colspan="2"></td>
    </tr>
    {% endfor %}
    <tr><th colspan="8">Notes Summary</th></tr>
    <tr>
        <th>Date</th>
        <th>User</th>
        <th colspan="6">Details</th>
    </tr>
    {% for note in record.notes %}
    <tr>
        <td>{{ note.createDateTime }}</td>
        <td>{{ note.submitterName }}</td>
        <td colspan="6">{{ note.note }}</td>
    </tr>
    {% endfor %}
</table>
</body>
</html>
