import json
from datetime import date
from io import BytesIO
from pathlib import Path
from unittest.mock import MagicMock, patch

import i18n
from flask import url_for
from freezegun import freeze_time

from billing.components.portal.component import BillingPortalComponent
from billing.components.portal.models import (
    Billing<PERSON><PERSON>ord,
    Client,
    CptChargeUnit,
    Deployment,
    SalesForceFile,
)
from billing.components.portal.router.billing_portal_requests import AddClientRequestObject
from billing.components.portal.use_case.medical_record_use_case import (
    ClientMedicalRecordRequestObject,
    ClientMedicalRecordResponseObject,
    GetClientMedicalRecordsUseCase,
)
from billing.tests.billing_test_case import BillingTestCase
from huma_plugins.components.export.tests.IntegrationTests import enabled_modules
from huma_plugins.components.extended_module_result.component import ExtendedModuleResultComponent
from huma_plugins.tests.plugin_test_case import ExtensionServerConfig
from huma_plugins.tests.shared import PLUGIN_CONFIG_PATH
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.deployment.component import DeploymentComponent
from sdk.organization.component import OrganizationComponent
from sdk.storage.component import StorageComponentV1
from sdk.versioning.component import VersionComponent

ORGANIZATION_ID = "5fde855f12db509a2785da06"
DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
DUMMY_DEPLOYMENT_ID = "5d386cc6ff885918d96eeeee"
ORG_ADMIN_ID = "5e8f0c74b50aa9656c34789c"
USER_ID = "5e8f0c74b50aa9656c34789d"
CLIENT_ID = "5fde855f12db509a2785da12"


class BillingPortalIntegrationTest(BillingTestCase):
    config_file_path = PLUGIN_CONFIG_PATH
    db_migration_path = ""
    config_class = ExtensionServerConfig
    fixtures = [Path(__file__).parent.joinpath("fixtures").joinpath("organization_dump.json")]

    components = [
        AuthComponent(),
        VersionComponent(server_version="1.0.0", api_version="1.0.0"),
        AuthorizationComponent(),
        ExtendedModuleResultComponent(
            additional_modules=[m() for m in enabled_modules],
        ),
        StorageComponentV1(),
        DeploymentComponent(),
        OrganizationComponent(),
        BillingPortalComponent(),
    ]

    def setUp(self):
        super().setUp()
        self.client = self.flask_client
        self.user_headers = self.get_headers_for_token(USER_ID)
        self.user_headers["User-Agent"] = "test-agent"
        self.org_admin_headers = self.get_headers_for_token(ORG_ADMIN_ID)
        self.org_admin_headers["User-Agent"] = "web-agent"

    def tearDown(self):
        Client.objects.all().delete()
        SalesForceFile.objects.all().delete()
        Deployment.objects.all().delete()
        super().tearDown()

    @classmethod
    def tearDownClass(cls):
        i18n.translations.container = {}
        i18n.load_path.clear()

    def test_get_clients(self):
        endpoint_url = url_for("billing_portal_route.get_clients")
        response = self.client.get(
            endpoint_url,
            headers={**self.org_admin_headers, "X-ORG-ID": ORGANIZATION_ID},
        )
        self.assertEqual(200, response.status_code)

    def test_add_client(self):
        endpoint_url = url_for("billing_portal_route.add_client")
        payload = {
            AddClientRequestObject.NAME: "Test Client",
            AddClientRequestObject.PAYMENT_FORMULA: "EMBEDED_FURMULA_99454_20minutes",
        }

        response = self.client.post(
            endpoint_url,
            json=payload,
            headers={**self.org_admin_headers, "X-ORG-ID": ORGANIZATION_ID},
        )

        self.assertEqual(201, response.status_code)
        self.assertIn("id", response.json)

        client_id = response.json["id"]
        client = Client.objects.get(mongoId=client_id)
        self.assertEqual(client.name, payload[AddClientRequestObject.NAME])
        payment_formula = client.paymentFormula

        self.assertEqual(payment_formula["formula"], payload[AddClientRequestObject.PAYMENT_FORMULA])
        self.assertEqual(payment_formula["rate"], 0)

    def test_add_client_with_json_payment_formula(self):
        endpoint_url = url_for("billing_portal_route.add_client")
        payment_formula = {
            "formula": "EMBEDED_FURMULA_99454_20minutes",
            "rate": 100,
        }

        payload = {
            AddClientRequestObject.NAME: "Test Client",
            AddClientRequestObject.PAYMENT_FORMULA: json.dumps(payment_formula),
        }
        response = self.client.post(
            endpoint_url,
            json=payload,
            headers={**self.org_admin_headers, "X-ORG-ID": ORGANIZATION_ID},
        )
        self.assertEqual(201, response.status_code)
        self.assertIn("id", response.json)
        client_id = response.json["id"]
        client = Client.objects.get(mongoId=client_id)
        self.assertEqual(client.name, payload[AddClientRequestObject.NAME])
        self.assertEqual(payment_formula["formula"], client.paymentFormula["formula"])

    @patch("billing.components.portal.use_case.utils.salesforce.download_file_id")
    def test_generate_reports(
        self,
        mock_download_file_id,
    ):
        mock_download_file_id.return_value = MagicMock(content=open(Path(__file__).parent / "test_file.csv", "rb"))

        BillingRecord.objects.all().delete()
        with freeze_time("2023-06-13T10:00:00.000Z"):
            self._upload_sales_force_file()
            endpoint_url = url_for("billing_portal_route.generate_reports")
            params = {"monthYear": "2023-05"}
            response = self.client.get(
                endpoint_url,
                query_string=params,
                headers={**self.get_headers_for_token(ORG_ADMIN_ID), "X-ORG-ID": ORGANIZATION_ID},
            )
            self.assertEqual(200, response.status_code)
            self.assertEqual(4, CptChargeUnit.objects.count())
            self.assertEqual(1, BillingRecord.objects.count())

    def test_download_sales_force_file(self):
        # First upload a file to have something to download
        upload_response = self._upload_sales_force_file()
        self.assertEqual(200, upload_response.status_code)
        self.assertEqual(1, SalesForceFile.objects.count())

        # Get the version number from the response
        version_number = upload_response.json.get("id")
        self.assertIsNotNone(version_number)

        # Now test downloading the file
        endpoint_url = url_for("billing_portal_route.download_sales_force_file")

        with patch(
            "billing.components.portal.use_case.sales_force_file_use_case.DownloadFileUseCaseV1.execute"
        ) as mock_download_file:
            # Mock the download file response
            mock_download_file.return_value = MagicMock(
                fileName="test_file.csv",
                content=BytesIO(b"test file content"),
            )

            response = self.client.get(
                endpoint_url,
                headers={**self.org_admin_headers, "X-ORG-ID": ORGANIZATION_ID},
                query_string={"version": version_number},
            )

        self.assertEqual(200, response.status_code)
        self.assertEqual("text/csv; charset=utf-8", response.content_type)
        self.assertIn("attachment; filename=test_file.csv", response.headers.get("Content-Disposition", ""))

        # Test with non-existent version
        with patch(
            "billing.components.portal.use_case.sales_force_file_use_case.DownloadFileUseCaseV1.execute"
        ) as mock_download_file:
            response = self.client.get(
                endpoint_url,
                headers={**self.org_admin_headers, "X-ORG-ID": ORGANIZATION_ID},
                query_string={"version": 9999},
            )

        # Should return an error
        self.assertNotEqual(200, response.status_code)

    def test_download_specific_version_sales_force_file(self):
        """Test downloading a specific version of a SalesForce file when multiple versions exist."""
        # Upload first file (version 1)
        upload_response1 = self._upload_sales_force_file()
        self.assertEqual(200, upload_response1.status_code)
        version1 = upload_response1.json.get("id")

        # Upload second file (version 2)
        upload_response2 = self._upload_sales_force_file()
        self.assertEqual(200, upload_response2.status_code)
        version2 = upload_response2.json.get("id")

        # Verify we have two different versions
        self.assertNotEqual(version1, version2)
        self.assertEqual(2, SalesForceFile.objects.count())

        # Test downloading version 1
        endpoint_url = url_for("billing_portal_route.download_sales_force_file")

        with patch(
            "billing.components.portal.use_case.sales_force_file_use_case.DownloadFileUseCaseV1.execute"
        ) as mock_download_file:
            # Mock the download file response for version 1
            mock_download_file.return_value = MagicMock(
                fileName="version1_file.csv",
                content=BytesIO(b"version 1 content"),
            )

            response_v1 = self.client.get(
                endpoint_url,
                headers={**self.org_admin_headers, "X-ORG-ID": ORGANIZATION_ID},
                query_string={"version": version1},
            )

        self.assertEqual(200, response_v1.status_code)
        self.assertEqual("text/csv; charset=utf-8", response_v1.content_type)
        self.assertIn("attachment; filename=version1_file.csv", response_v1.headers.get("Content-Disposition", ""))

        # Test downloading version 2
        with patch(
            "billing.components.portal.use_case.sales_force_file_use_case.DownloadFileUseCaseV1.execute"
        ) as mock_download_file:
            # Mock the download file response for version 2
            mock_download_file.return_value = MagicMock(
                fileName="version2_file.csv",
                content=BytesIO(b"version 2 content"),
            )

            response_v2 = self.client.get(
                endpoint_url,
                headers={**self.org_admin_headers, "X-ORG-ID": ORGANIZATION_ID},
                query_string={"version": version2},
            )

        self.assertEqual(200, response_v2.status_code)
        self.assertEqual("text/csv; charset=utf-8", response_v2.content_type)
        self.assertIn("attachment; filename=version2_file.csv", response_v2.headers.get("Content-Disposition", ""))

    def test_add_sales_force_file(self):
        endpoint_url = url_for("billing_portal_route.add_sales_force_file")

        # Test with no file
        response_no_file = self.client.post(
            endpoint_url,
            headers={**self.get_headers_for_token(ORG_ADMIN_ID), "X-ORG-ID": ORGANIZATION_ID},
        )
        self.assertEqual(400, response_no_file.status_code)
        # Should contain an error message
        self.assertIn("No file part in the request", response_no_file.data.decode())

        # Test with an empty file
        response_empty_filename = self.client.post(
            endpoint_url,
            data={"file": (BytesIO(b""))},
            headers={**self.get_headers_for_token(ORG_ADMIN_ID), "X-ORG-ID": ORGANIZATION_ID},
            content_type="multipart/form-data",
        )
        self.assertEqual(400, response_empty_filename.status_code)
        # Should contain an error message
        self.assertIn("No file part in the request", response_empty_filename.data.decode())

        # Test with a valid file
        response = self._upload_sales_force_file()
        self.assertEqual(200, response.status_code)

        # Verify the response content
        self.assertIn("id", response.json)
        self.assertEqual("SalesForceFile uploaded successfully", response.json.get("message"))

        # Verify the SalesForceFile object was created with the correct attributes
        self.assertEqual(1, SalesForceFile.objects.count())
        sales_force_file = SalesForceFile.objects.first()
        self.assertEqual("mock_file_id", sales_force_file.fileId)
        # since we are preserving the database, the version number may not be 1
        # therefore; we check if it is at least 1
        first_version = sales_force_file.versionNumber
        self.assertGreaterEqual(first_version, 1)

        # Test uploading a second file - should create a new version
        response2 = self._upload_sales_force_file()
        self.assertEqual(200, response2.status_code)
        self.assertEqual(2, SalesForceFile.objects.count())
        self.assertEqual(first_version + 1, response2.json.get("id"))  # Version number should be increased

        # Test with an invalid file format
        with patch(
            "billing.components.portal.use_case.sales_force_file_use_case.UploadFileUseCaseV1.execute",
            side_effect=ValueError("Invalid file format"),
        ):
            response_invalid = self.client.post(
                endpoint_url,
                data={"file": (BytesIO(b"invalid content"), "invalid.txt")},
                headers={**self.get_headers_for_token(ORG_ADMIN_ID), "X-ORG-ID": ORGANIZATION_ID},
                content_type="multipart/form-data",
            )

        # Should return an error
        self.assertNotEqual(200, response_invalid.status_code)

    def _upload_sales_force_file(self, file_path=Path(__file__).parent / "test_file.csv"):
        endpoint_url = url_for("billing_portal_route.add_sales_force_file")
        with (
            open(Path(__file__).parent / file_path, "rb") as f,
            patch(
                "billing.components.portal.use_case.sales_force_file_use_case.UploadFileUseCaseV1.execute",
            ) as mock_upload_file,
        ):
            mock_upload_file.return_value = MagicMock(id="mock_file_id")
            response = self.client.post(
                endpoint_url,
                data={"file": (f, file_path), "versionNumber": "1.0", "uploadedAt": "2023-06-13T10:00:00.000Z"},
                headers={**self.get_headers_for_token(ORG_ADMIN_ID), "X-ORG-ID": ORGANIZATION_ID},
                content_type="multipart/form-data",
            )
        self.assertEqual(200, response.status_code)
        return response

    @staticmethod
    def _create_sample_client(
        client_id=CLIENT_ID, name="Test Client", payment_formula="EMBEDED_FURMULA_99454_20minutes"
    ):
        client = Client(
            mongoId=client_id,
            name=name,
            paymentFormula={
                "formula": payment_formula,
                "rate": 0,
            },
        )
        client.save()
        return client

    def test_add_client_deployment(self):
        self._create_sample_client()
        endpoint_url = url_for("billing_portal_route.add_deployment_client")
        payload = {
            "clientId": CLIENT_ID,
            "deploymentId": DUMMY_DEPLOYMENT_ID,
        }
        response = self.client.post(
            endpoint_url,
            json=payload,
            headers={**self.org_admin_headers, "X-ORG-ID": ORGANIZATION_ID},
        )
        self.assertEqual(201, response.status_code)
        self.assertIn("id", response.json)
        deployment_id = response.json["id"]
        client_deployment = Deployment.objects.get(mongoId=deployment_id, client__mongoId=payload["clientId"])
        self.assertIsNotNone(client_deployment)

    def test_update_client_deployment(self):
        self._create_sample_client()
        endpoint_url = url_for("billing_portal_route.update_deployment_client")

        # There is a client deployment to update, with deployment_id as DEPLOYMENT_ID
        payload = {
            "clientId": CLIENT_ID,
            "deploymentId": DEPLOYMENT_ID,
        }
        response = self.client.put(
            endpoint_url,
            json=payload,
            headers={**self.org_admin_headers, "X-ORG-ID": ORGANIZATION_ID},
        )
        self.assertEqual(200, response.status_code)

    def test_get_client_report(self):
        client_id = "6c386cc6ff885918d96eff2d"

        # Now test the get_client_report endpoint
        endpoint_url = url_for("billing_portal_route.get_client_report", client_id=client_id)
        response = self.client.get(
            endpoint_url,
            headers={**self.org_admin_headers, "X-ORG-ID": ORGANIZATION_ID},
            query_string={
                "reportEndDate": "2023-05-31",
            },
        )
        self.assertEqual(200, response.status_code)

    @patch("billing.components.portal.use_case.medical_record_use_case.BillingPortalService")
    def test_medical_record_use_case(self, mock_service_cls):
        mock_service = MagicMock()
        mock_service_cls.return_value = mock_service

        mock_record = MagicMock()
        mock_record.patientId = "user1"
        mock_record.numberOfCalls = 3
        mock_record.monitoringMinutes = 120
        mock_record.diagnosisType = "Hypertension"
        mock_service.retrieve_billing_records.return_value = [mock_record]

        mock_user = MagicMock()
        mock_user.id = "user1"
        mock_user.givenName = "John"
        mock_user.familyName = "Doe"
        mock_service.retrieve_users.return_value = [mock_user]

        use_case = GetClientMedicalRecordsUseCase()
        request = ClientMedicalRecordRequestObject(reportEndDate=date(2024, 6, 1), clientId="client123")

        response = use_case.process_request(request)

        self.assertIsInstance(response, ClientMedicalRecordResponseObject)
        self.assertEqual(len(response.patients), 1)
        patient = response.patients[0]
        self.assertEqual(patient.id, "user1")
        self.assertEqual(patient.givenName, "John")
        self.assertEqual(patient.familyName, "Doe")
        self.assertEqual(patient.numberOfCalls, 3)
        self.assertEqual(patient.monitoringMinutes, 120)
        self.assertEqual(patient.diagnosis, "Hypertension")

    def test_get_patient_medical_record_endpoint(self):
        endpoint_url = url_for("billing_portal_route.get_patient_medical_record")
        params = {"userId": USER_ID, "reportEndDate": "2023-05-31"}
        response = self.client.get(
            endpoint_url,
            headers={**self.org_admin_headers, "X-ORG-ID": ORGANIZATION_ID},
            query_string=params,
        )
        self.assertEqual(200, response.status_code)
