from pathlib import Path
from unittest.mock import patch

from freezegun import freeze_time

from billing.components.core.component import BillingComponent
from billing.components.core.helpers.module_result_helpers import PrimitiveSources
from billing.components.export.component import BillingExportComponent
from billing.components.portal.tasks import export_monthly_billing
from billing.tests.billing_test_case import BillingTestCase
from huma_plugins.components.export.tests.IntegrationTests import enabled_modules
from huma_plugins.components.extended_module_result.component import ExtendedModuleResultComponent
from huma_plugins.components.online_offline_call.component import OnlineOfflineCallComponent
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.deployment.component import DeploymentComponent
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.modules import BloodPressureModule
from sdk.module_result.modules.blood_pressure import BloodPressureDTO
from sdk.organization.component import OrganizationComponent
from sdk.storage.component import StorageComponentV1
from sdk.versioning.component import VersionComponent

DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
MANAGER_ID = "5e8f0c74b50aa9656c34789d"
USER_ID = "5e8f0c74b50aa9656c34789b"


class BillingExportMonthlyTaskTestCase(BillingTestCase):
    components = [
        AuthComponent(),
        AuthorizationComponent(),
        BillingComponent(),
        DeploymentComponent(),
        BillingExportComponent(),
        ExtendedModuleResultComponent(
            additional_modules=[m() for m in enabled_modules],
        ),
        StorageComponentV1(),
        OnlineOfflineCallComponent(),
        OrganizationComponent(),
        VersionComponent(server_version="1.0.0", api_version="1.0.0"),
    ]
    fixtures = [Path(__file__).parent.joinpath("fixtures/deployments_dump.json")]
    migration_path: str = str(Path(__file__).parent.parent.parent) + "/migrations"
    override_config = {"server.billingPortal.tags": "test"}

    def setUp(self):
        super().setUp()
        self._config = self.config_class.get(self.config_file_path, self.override_config)

    @patch("billing.components.core.use_case.billing_use_cases.CalendarService")
    @patch("sdk.module_result.use_cases.create_module_result_use_case.UpdateUserStatsEvent")
    @freeze_time("2022-07-01T10:00:00.000Z")
    def test_export_monthly_billing(self, mock_update_user_stats_event, mock_calendar_service):
        from sdk.common.adapter.event_bus_adapter import BaseEvent

        mock_update_user_stats_event.return_value = BaseEvent()
        for i in range(10, 31):
            self._submit_sample_module_results(start_date=f"2022-05-{i:02d}T00:00:00.039Z")
        general, monthly = export_monthly_billing(DEPLOYMENT_ID)
        self.assertEqual(len(general), 2)
        self.assertEqual(DEPLOYMENT_ID, general[0].deploymentId)
        self.assertEqual("2022-06-30", general[0].reportDate)

    def _submit_sample_module_results(
        self,
        user_id: str = USER_ID,
        deployment_id: str = DEPLOYMENT_ID,
        start_date: str = "2022-06-01T00:00:00.039Z",
    ):
        module_results = {
            PrimitiveDTO.USER_ID: user_id,
            PrimitiveDTO.SUBMITTER_ID: MANAGER_ID,
            PrimitiveDTO.DEPLOYMENT_ID: deployment_id,
            PrimitiveDTO.START_DATE_TIME: start_date,
            PrimitiveDTO.DEVICE_NAME: "device",
            PrimitiveDTO.SERVER: {"hostUrl": "local", "server": "1.21.0", "api": "V1"},
            PrimitiveDTO.SOURCE: f"{PrimitiveSources.HEALTH_KIT.value};other;possible;things",
            BloodPressureDTO.MODULE_ID: "BloodPressure",
            BloodPressureDTO.MODULE_CONFIG_ID: "5e94b2007773091c9a592650",
            BloodPressureDTO.DIASTOLIC_VALUE: 60,
            BloodPressureDTO.SYSTOLIC_VALUE: 120,
        }
        body = [{**module_results, "type": BloodPressureDTO.get_primitive_name()}]
        with freeze_time(start_date):
            rsp = self.flask_client.post(
                f"api/extensions/v1/user/{user_id}/module-result/{BloodPressureModule.moduleId}",
                headers=self.get_headers_for_token(MANAGER_ID),
                json=body,
            )
        self.assertEqual(201, rsp.status_code)
