{"deployment": [{"_id": {"$oid": "5d386cc6ff885918d96edb2c"}, "name": "Simple deployment", "status": "DRAFT", "color": "0x007AFF", "moduleConfigs": [], "learn": {"id": {"$oid": "5e8eeae1b707216625ca4202"}, "updateDateTime": {"$date": {"$numberLong": "1586433217042"}}, "createDateTime": {"$date": {"$numberLong": "1586433217042"}}}, "updateDateTime": {"$date": {"$numberLong": "1586433217042"}}, "createDateTime": {"$date": {"$numberLong": "1586433217042"}}}], "organization": [{"_id": {"$oid": "5fde855f12db509a2785da06"}, "name": "Simple organization", "enrollmentTarget": 3000, "studyCompletionTarget": 2800, "status": "DEPLOYED", "deploymentIds": ["5d386cc6ff885918d96edb2c"], "targetConsented": 0, "eulaUrl": "https://some_url.com/eulaUrl", "termAndConditionUrl": "https://example.com/termAndConditionUrl", "privacyPolicyUrl": "https://example.com/privacyPolicyUrl"}], "huma_auth_user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999999", "displayName": "test", "userAttributes": {"familyName": "test", "givenName": "test", "dob": "1988-02-20", "gender": "MALE"}, "createDateTime": {"$date": {"$numberInt": "1586422340"}}, "updateDateTime": {"$date": {"$numberInt": "1586422340"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789d"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999990", "displayName": "test", "userAttributes": {"familyName": "test", "givenName": "test", "dob": "1988-02-20", "gender": "MALE"}, "createDateTime": {"$date": {"$numberInt": "1586422340"}}, "updateDateTime": {"$date": {"$numberInt": "1586422340"}}}], "user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "phoneNumber": "+380999999999", "roles": [{"roleId": "OrganizationOwner", "resource": "organization/5fde855f12db509a2785da06", "userType": "Admin", "isActive": true}], "timezone": "UTC"}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789d"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "phoneNumber": "+380999999990", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "timezone": "Asia/Bangkok", "lastLoginDateTime": "2022-01-07T14:59:49.275750Z", "componentsData": {"billing": {"status": 1, "billingProviderName": "someName", "diagnosis": {"order": 1, "icd10Code": "ICD", "description": "diagnosis description"}}}}], "billing_portal_client": [{"_id": {"$oid": "6c386cc6ff885918d96eff2d"}, "name": "Simple client", "paymentFormula": {"FORMULA_BASIC": {"rate": 70, "description": "Data submission of at least 16 days and minimum of 20 monitoring minutes"}}}], "billing_client_deployment": [{"_id": {"$oid": "5d386cc6ff885918d96edb2c"}, "name": "Simple deployment", "client_id": "6c386cc6ff885918d96eff2d"}], "billing_record": [{"_id": {"$oid": "63f0c74b50aa9656c34789dd"}, "deployment_id": "5d386cc6ff885918d96edb2c", "patient_id": "5e8f0c74b50aa9656c34789d", "provider": "someName", "diagnosisType": "ICD", "deviceConnectionDate": "2023-04-12", "primaryInsurance": "<PERSON><PERSON><PERSON>", "reportEndDate": "2023-05-31", "isEligible": true, "monitoringMinutes": 120, "numberOfCalls": 2, "monthReading": 30, "consentDate": "2022-12-15", "consentId": "consent123", "deviceType": "BP Monitor", "deviceModel": "Omron X4"}], "billing_general_cpt_codes": [{"_id": {"$oid": "63f0c74b50aa9656c34789de"}, "mongoId": "63f0c74b50aa9656c34789de", "user_id": "5e8f0c74b50aa9656c34789d", "reportDate": "2023-05-31", "createDateTime": {"$date": "2023-06-01T10:00:00Z"}, "deploymentId": "5d386cc6ff885918d96edb2c", "billingFirstReading": "2023-05-01", "billingProviderName": "someName", "billingDiagnosisDescription": "diagnosis description", "billingDiagnosisICD10Code": "ICD", "billingDiagnosisOrder": 1, "primaryInsuranceCarrier": "<PERSON><PERSON><PERSON>", "primaryInsuranceCarrierPayerId": "AET123", "secondaryInsuranceCarrier": "BlueCross", "secondaryInsuranceCarrierPayerId": "BC456", "cpt98975": 0, "cpt98976": 0, "cpt98980": 0, "cpt98981": 0, "cpt99453": 1, "cpt99454": 1, "cpt99457": 1, "cpt99458": 1, "monitoringTimeMinutes": 120, "numberOfCalls": 2, "numberOfSubmissionDays": 20, "numberOfSubmissions": 30}], "billing_monthly_cpt_codes": [{"_id": {"$oid": "63f0c74b50aa9656c34789df"}, "mongoId": "63f0c74b50aa9656c34789df", "user_id": "5e8f0c74b50aa9656c34789d", "createDateTime": {"$date": "2023-06-01T10:00:00Z"}, "deploymentId": "5d386cc6ff885918d96edb2c", "moduleId": "module123", "reportStartDate": "2023-05-01", "reportEndDate": "2023-05-31", "givenName": "test", "familyName": "test", "dateOfBirth": "1988-02-20", "monitoringTimeMins": 120, "numberOfCalls": 2, "cpt99457Units": 1, "cpt99458Units": 1, "cpt99457_8DateOfService": "2023-05-31", "cpt99453_4CountOfSubmissionDays": 20, "cpt99453BillingStartDate": "2023-05-01", "cpt99453BillingEndDate": "2023-05-31", "cpt99453CountOfSubmissions": 30, "cpt99453EarliestBillingDate": "2023-05-01", "cpt99454BillingStartDate": "2023-05-01", "cpt99454BillingEndDate": "2023-05-31", "cpt99454CountOfSubmissions": 15, "cpt99454_CountOfSubmissionDays": 10, "cpt99454_EarliestBillingDate": "2023-05-01", "cpt99454BillingStartDate0": "2023-05-01", "cpt99454BillingEndDate0": "2023-05-15", "cpt99454CountOfSubmission0": 8, "cpt99454CountOfSubmissionDays0": 5, "cpt99454EarliestBillingDate0": "2023-05-01", "cpt99454BillingStartDate1": "2023-05-16", "cpt99454BillingEndDate1": "2023-05-31", "cpt99454CountOfSubmission1": 7, "cpt99454CountOfSubmissionDays1": 5, "cpt99454EarliestBillingDate1": "2023-05-16"}]}