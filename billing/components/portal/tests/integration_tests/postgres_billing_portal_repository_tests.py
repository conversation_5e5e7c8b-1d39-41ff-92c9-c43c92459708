from datetime import date
from pathlib import Path

from bson import ObjectId

from billing.components.portal.component import BillingPortalComponent
from billing.components.portal.models import (
    Client,
    Invoice,
    InvoiceEntry,
)
from billing.components.portal.repository.billing_portal_repository import BillingPortalRepository
from billing.tests.billing_test_case import BillingTestCase
from huma_plugins.components.export.tests.IntegrationTests import enabled_modules
from huma_plugins.components.extended_module_result.component import ExtendedModuleResultComponent
from huma_plugins.tests.plugin_test_case import ExtensionServerConfig
from huma_plugins.tests.shared import PLUGIN_CONFIG_PATH
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.common.utils import inject
from sdk.deployment.component import DeploymentComponent
from sdk.organization.component import OrganizationComponent


class PostgresBillingPortalRepositoryIntegrationTest(BillingTestCase):
    config_file_path = PLUGIN_CONFIG_PATH
    db_migration_path = ""
    config_class = ExtensionServerConfig
    fixtures = [Path(__file__).parent.joinpath("fixtures").joinpath("organization_dump.json")]

    components = [
        AuthComponent(),
        AuthorizationComponent(),
        DeploymentComponent(),
        OrganizationComponent(),
        ExtendedModuleResultComponent(
            additional_modules=[m() for m in enabled_modules],
        ),
        BillingPortalComponent(),
    ]

    def setUp(self):
        super().setUp()
        self.repo = inject.instance(BillingPortalRepository)

        Client.objects.all().delete()
        Invoice.objects.all().delete()
        InvoiceEntry.objects.all().delete()

        self.client1_id = ObjectId()
        self.client1 = Client(
            mongoId=self.client1_id,
            name="Test Client 1",
            paymentFormula={"formula": "test"},
        )
        self.client1.save()

        self.client2_id = ObjectId()
        self.client2 = Client(
            mongoId=self.client2_id,
            name="Test Client 2",
            paymentFormula={"formula": "test"},
        )
        self.client2.save()

        self.report_end_date = date(2023, 1, 31)
        self.invoice1 = Invoice(
            startDate=date(2023, 1, 1),
            endDate=self.report_end_date,
            client=self.client1,
            extraDetails="Test Invoice 1",
        )
        self.invoice1.save()

        self.invoice2 = Invoice(
            startDate=date(2023, 1, 1),
            endDate=self.report_end_date,
            client=self.client2,
            extraDetails="Test Invoice 2",
        )
        self.invoice2.save()

        self.entry1 = InvoiceEntry(
            serviceDescription="Service 1",
            units=10,
            rates=100,
            parentInvoice=self.invoice1,
        )
        self.entry1.save()

        self.entry2 = InvoiceEntry(
            serviceDescription="Service 2",
            units=5,
            rates=200,
            parentInvoice=self.invoice1,
        )
        self.entry2.save()

        self.entry3 = InvoiceEntry(
            serviceDescription="Service 1",
            units=15,
            rates=100,
            parentInvoice=self.invoice1,
        )
        self.entry3.save()

        self.entry4 = InvoiceEntry(
            serviceDescription="Service 3",
            units=20,
            rates=150,
            parentInvoice=self.invoice2,
        )
        self.entry4.save()

    def tearDown(self):
        # Clean up test data
        InvoiceEntry.objects.all().delete()
        Invoice.objects.all().delete()
        Client.objects.all().delete()
        super().tearDown()

    def test_retrieve_all_clients_invoices_with_entries(self):
        result = self.repo.retrieve_all_clients_invoices_with_entries(self.report_end_date)

        self.assertIsNotNone(result)
        self.assertEqual(2, len(result))  # Should return 2 clients

        # Find client1 and client2 in the results
        client1_report = next((r for r in result if r.client.id == str(self.client1_id)), None)
        client2_report = next((r for r in result if r.client.id == str(self.client2_id)), None)

        # Verify client1 report
        self.assertIsNotNone(client1_report)
        self.assertEqual(client1_report.client.name, "Test Client 1")
        self.assertEqual(client1_report.invoice.extraDetails, "Test Invoice 1")

        # Verify client1 entries - should have 2 entries after aggregation (rates 100 and 200)
        self.assertEqual(len(client1_report.invoiceEntries), 2)

        # Find entries by rate
        rate_100_entry = next((e for e in client1_report.invoiceEntries if e.rates == 100), None)
        rate_200_entry = next((e for e in client1_report.invoiceEntries if e.rates == 200), None)

        # Verify rate 100 entry - should have units 25 (10 + 15)
        self.assertIsNotNone(rate_100_entry)
        self.assertEqual(rate_100_entry.units, 25)
        self.assertEqual(rate_100_entry.serviceDescription, "Service 1")

        # Verify rate 200 entry
        self.assertIsNotNone(rate_200_entry)
        self.assertEqual(rate_200_entry.units, 5)
        self.assertEqual(rate_200_entry.serviceDescription, "Service 2")

        self.assertIsNotNone(client2_report)
        self.assertEqual(client2_report.client.name, "Test Client 2")
        self.assertEqual(client2_report.invoice.extraDetails, "Test Invoice 2")

        # Verify client2 entries - should have 1 entry
        self.assertEqual(len(client2_report.invoiceEntries), 1)
        self.assertEqual(client2_report.invoiceEntries[0].rates, 150)
        self.assertEqual(client2_report.invoiceEntries[0].units, 20)
        self.assertEqual(client2_report.invoiceEntries[0].serviceDescription, "Service 3")

    def test_retrieve_all_clients_invoices_with_entries_no_invoices(self):
        # Test with a date that has no invoices
        different_date = date(2022, 1, 1)

        result = self.repo.retrieve_all_clients_invoices_with_entries(different_date)

        self.assertIsNotNone(result)
        self.assertEqual(len(result), 2)  # Should still return 2 clients

        for client_report in result:
            self.assertIsNone(client_report.invoice)
            self.assertEqual(len(client_report.invoiceEntries), 0)
