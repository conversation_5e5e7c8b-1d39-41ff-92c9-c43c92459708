from copy import copy
from datetime import datetime, timed<PERSON>ta
from pathlib import Path

import i18n
from freezegun import freeze_time

from billing.components.portal.component import BillingPortalComponent
from billing.components.portal.dtos.billing_portal_models import CPTGeneralExport
from billing.components.portal.models import BillingGeneralCPTCodes
from billing.components.portal.repository.billing_portal_repository import BillingPortalRepository
from billing.tests.billing_test_case import BillingTestCase
from huma_plugins.components.export.tests.IntegrationTests import enabled_modules
from huma_plugins.components.extended_module_result.component import ExtendedModuleResultComponent
from huma_plugins.tests.plugin_test_case import ExtensionServerConfig
from huma_plugins.tests.shared import PLUGIN_CONFIG_PATH
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.common.utils import inject
from sdk.deployment.component import DeploymentComponent
from sdk.organization.component import OrganizationComponent


class PostgresCPTExportRepositoryIntegrationTest(BillingTestCase):
    config_file_path = PLUGIN_CONFIG_PATH
    db_migration_path = ""
    config_class = ExtensionServerConfig
    fixtures = [Path(__file__).parent.joinpath("fixtures").joinpath("organization_dump.json")]

    components = [
        AuthComponent(),
        AuthorizationComponent(),
        DeploymentComponent(),
        OrganizationComponent(),
        ExtendedModuleResultComponent(
            additional_modules=[m() for m in enabled_modules],
        ),
        BillingPortalComponent(),
    ]

    def setUp(self):
        super().setUp()
        self.repo = inject.instance(BillingPortalRepository)

        self.user_id = "5e8f0c74b50aa9656c34789d"
        self.deployment_id = "5d386cc6ff885918d96edb2c"
        self.report_date = datetime.now().strftime("%Y-%m-%d")
        self.cpt_export = CPTGeneralExport.from_dict(
            {
                "userId": self.user_id,
                "deploymentId": self.deployment_id,
                "reportDate": self.report_date,
                "billingFirstReading": datetime(2022, 1, 1),
                "billingProviderName": "Test Provider",
                "billingDiagnosisDescription": "Test Diagnosis",
                "billingDiagnosisICD10Code": "A00.0",
                "billingDiagnosisOrder": 1,
                "primaryInsuranceCarrier": "Test Insurance",
                "primaryInsuranceCarrierPayerId": "12345",
                "cpt98975": 0,
                "cpt98976": 0,
                "cpt98980": 0,
                "cpt98981": 0,
                "cpt99453": 1,
                "cpt99454": 2,
                "cpt99457": 0,
                "cpt99458": 0,
                "monitoringTimeMinutes": 30,
                "numberOfCalls": 2,
                "numberOfSubmissionDays": 10,
                "numberOfSubmissions": 20,
            }
        )

        self.repo.add_user_general_cpt_code(self.cpt_export)

    def tearDown(self):
        BillingGeneralCPTCodes.objects.all().delete()
        super().tearDown()

    @classmethod
    def tearDownClass(cls):
        i18n.translations.container = {}
        i18n.load_path.clear()

    def test_get_user_general_cpt_code_integration(self):
        result = self.repo.get_user_general_cpt_code(self.user_id, self.report_date)

        # Verify the result
        self.assertIsNotNone(result)
        self.assertEqual(result.userId, self.user_id)
        self.assertEqual(result.deploymentId, self.deployment_id)
        self.assertEqual(result.reportDate, self.report_date)
        self.assertEqual(result.billingProviderName, "Test Provider")
        self.assertEqual(result.billingDiagnosisDescription, "Test Diagnosis")
        self.assertEqual(result.billingDiagnosisICD10Code, "A00.0")
        self.assertEqual(result.billingDiagnosisOrder, 1)
        self.assertEqual(result.primaryInsuranceCarrier, "Test Insurance")
        self.assertEqual(result.primaryInsuranceCarrierPayerId, "12345")
        self.assertEqual(result.cpt99453, 1)
        self.assertEqual(result.cpt99454, 2)
        self.assertEqual(result.monitoringTimeMinutes, 30)
        self.assertEqual(result.numberOfCalls, 2)
        self.assertEqual(result.numberOfSubmissionDays, 10)
        self.assertEqual(result.numberOfSubmissions, 20)

    def test_get_user_general_cpt_code_not_found_integration(self):
        different_date = datetime(2022, 1, 1).date()

        result = self.repo.get_user_general_cpt_code(self.user_id, different_date)
        self.assertIsNone(result)

    @freeze_time("2023-05-15T10:00:00Z")
    def test_add_and_retrieve_user_general_cpt_code(self):
        older_report_date = "2023-05-01"
        new_cpt_export = copy(self.cpt_export)
        new_cpt_export.reportDate = older_report_date

        self.repo.add_user_general_cpt_code(new_cpt_export)

        result = self.repo.get_user_general_cpt_code(new_cpt_export.userId, older_report_date)

        self.assertIsNotNone(result)
        self.assertEqual(result.userId, new_cpt_export.userId)
        self.assertEqual(result.deploymentId, self.deployment_id)
        self.assertEqual(result.reportDate, older_report_date)
        self.assertEqual(result.billingFirstReading, self.cpt_export.billingFirstReading)
        self.assertEqual(result.billingProviderName, new_cpt_export.billingProviderName)
        self.assertEqual(result.cpt99453, new_cpt_export.cpt99453)
        self.assertEqual(result.cpt99454, new_cpt_export.cpt99454)
        self.assertEqual(result.monitoringTimeMinutes, new_cpt_export.monitoringTimeMinutes)
        self.assertEqual(result.numberOfCalls, new_cpt_export.numberOfCalls)
        self.assertEqual(result.numberOfSubmissionDays, new_cpt_export.numberOfSubmissionDays)
        self.assertEqual(result.numberOfSubmissions, new_cpt_export.numberOfSubmissions)

        BillingGeneralCPTCodes.objects.filter(user_id=new_cpt_export.userId, reportDate=older_report_date).delete()

    @freeze_time("2023-06-15T10:00:00Z")
    def test_get_latest_deployments_for_general_cpt_code(self):
        BillingGeneralCPTCodes.objects.all().delete()
        deployment_ids = ["deployment1", "deployment2", "deployment3"]

        current_date = datetime.now().date()
        first_day_of_month = current_date.replace(day=1)

        # Create data for the current month (should be included in results)
        for i, deployment_id in enumerate(deployment_ids):
            for day_offset in [5, 10, 15]:
                report_date = first_day_of_month + timedelta(days=day_offset)

                cpt_export = copy(self.cpt_export)
                cpt_export.deploymentId = deployment_id
                cpt_export.reportDate = report_date
                cpt_export.billingProviderName = f"Provider {i}"
                cpt_export.cpt99453 = i + 1
                cpt_export.cpt99454 = i + 2
                cpt_export.monitoringTimeMinutes = 30 + i * 5
                cpt_export.numberOfCalls = 2 + i

                self.repo.add_user_general_cpt_code(cpt_export)

        # Create data for the previous month (should not be included in results)
        previous_month_date = first_day_of_month - timedelta(days=15)
        previous_month_export = copy(self.cpt_export)
        previous_month_export.reportDate = previous_month_date
        previous_month_export.deploymentId = "prev_month_dep"
        previous_month_export.billingProviderName = "Previous Month Provider"

        self.repo.add_user_general_cpt_code(previous_month_export)

        # Call the method being tested
        result = self.repo.get_latest_deployments_for_general_cpt_code()

        # Verify the results
        self.assertIsNotNone(result)
        self.assertEqual(len(result), len(deployment_ids))

        # Check that all expected deploymentIds are in the result
        result_deployment_ids = [item.deploymentId for item in result]
        for deployment_id in deployment_ids:
            self.assertIn(deployment_id, result_deployment_ids)

        # Check that the previous month deployment is not in the result
        self.assertNotIn("previous_month_deployment", result_deployment_ids)

        # Verify that each deployment has the latest report date
        for deployment_id in deployment_ids:
            # Find the deployment in the result
            deployment_result = next(item for item in result if item.deploymentId == deployment_id)

            expected_latest_date = first_day_of_month + timedelta(days=15)
            self.assertEqual(
                deployment_result.maxReportDate.strftime("%Y-%m-%d"), expected_latest_date.strftime("%Y-%m-%d")
            )
