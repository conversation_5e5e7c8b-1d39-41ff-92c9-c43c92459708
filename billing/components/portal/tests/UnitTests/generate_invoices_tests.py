import unittest
from unittest.mock import MagicMock, patch

from billing.components.portal.use_case.generate_reports_use_case import (
    FORMULA_20_MINUTES_MONITORING,
    interpret_payment_formula,
)


class InterpretPaymentFormulaTestCase(unittest.TestCase):
    def setUp(self):
        self.formula = {FORMULA_20_MINUTES_MONITORING: {"description": "desc", "rate": 100, "max": 2}}
        self.invoice = MagicMock()
        self.report_end_date = MagicMock()

    @patch("billing.components.portal.use_case.generate_reports_use_case.InvoiceEntry")
    @patch("billing.components.portal.use_case.generate_reports_use_case.CptChargeUnit")
    def test_eligible_record_creates_invoice_entry(self, mock_cpt_charge_unit, mock_invoice_entry):
        record = MagicMock()
        record.isEligible = True
        record.monitoringMinutes = 40

        units_qs = MagicMock()
        units_qs.filter.return_value.first.return_value = True
        mock_cpt_charge_unit.objects.filter.return_value = units_qs

        interpret_payment_formula(self.formula, [record], self.invoice, self.report_end_date)

        mock_invoice_entry.objects.create.assert_called_once_with(
            serviceDescription="desc",
            units=2,
            rates=100,
            parentInvoice=self.invoice,
        )

    @patch("billing.components.portal.use_case.generate_reports_use_case.InvoiceEntry")
    @patch("billing.components.portal.use_case.generate_reports_use_case.CptChargeUnit")
    def test_ineligible_record_does_not_create_invoice_entry(self, mock_cpt_charge_unit, mock_invoice_entry):
        record = MagicMock()
        record.isEligible = False

        interpret_payment_formula(self.formula, [record], self.invoice, self.report_end_date)

        mock_invoice_entry.objects.create.assert_not_called()


if __name__ == "__main__":
    unittest.main()
