from datetime import date, datetime

from billing.components.portal.dtos.billing_portal_models import BillingRecordDTO
from billing.components.portal.repository.billing_portal_repository import BillingPortalRepository
from sdk.authorization.dtos.user import UserDTO
from sdk.authorization.repository import AuthorizationRepository
from sdk.common.utils.inject import autoparams
from sdk.module_result.dtos.user_note import UserNoteDTO
from sdk.module_result.service.observation_note import ObservationNoteService


class BillingPortalService:
    @autoparams()
    def __init__(self, billing_portal_repository: BillingPortalRepository, auth_repo: AuthorizationRepository):
        self.repo = billing_portal_repository
        self.auth_repo = auth_repo
        self.observation_note_service = ObservationNoteService()

    def retrieve_clients(self):
        return self.repo.retrieve_clients()

    def retrieve_distinct_invoice_dates(self):
        return self.repo.get_distinct_invoice_dates()

    def retrieve_invoice_reports(self, report_end_date):
        return self.repo.retrieve_all_clients_invoices_with_entries(report_end_date)

    def retrieve_billing_records(self, client_id, report_end_date):
        return self.repo.retrieve_billing_records(client_id=client_id, report_end_date=report_end_date)

    def retrieve_billing_record_by_user_id(self, user_id: str, report_end_date: date) -> BillingRecordDTO | None:
        return self.repo.retrieve_patient_billing_record(user_id=user_id, report_end_date=report_end_date)

    def retrieve_users(self, user_ids) -> list[UserDTO]:
        return self.auth_repo.retrieve_users_by_id_list(user_id_list=user_ids)

    def retrieve_notes(self, user_id: str, start_date: datetime, end_date: datetime) -> list[UserNoteDTO]:
        notes = self.observation_note_service.retrieve_user_notes(
            deployment_id=None,
            user_id=user_id,
            skip=0,
            limit=1000,
            start_date=start_date,
            end_date=end_date,
        )
        return notes[0] if notes else []
