# Generated by Django 5.1.9 on 2025-06-03 14:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("billing_portal", "0001_initial"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="invoice",
            name="unique_client_date_combo",
        ),
        migrations.RemoveIndex(
            model_name="invoice",
            name="idx_invoice_client_end_date",
        ),
        migrations.RemoveIndex(
            model_name="invoiceentry",
            name="idx_invoice_patient_parent",
        ),
        migrations.RenameField(
            model_name="invoice",
            old_name="invoice_end_date",
            new_name="endDate",
        ),
        migrations.RenameField(
            model_name="invoice",
            old_name="invoice_start_date",
            new_name="startDate",
        ),
        migrations.RenameField(
            model_name="invoiceentry",
            old_name="parent_invoice",
            new_name="parentInvoice",
        ),
        migrations.RenameField(
            model_name="invoiceentry",
            old_name="service_description",
            new_name="serviceDescription",
        ),
        migrations.RenameField(
            model_name="invoiceentry",
            old_name="unites",
            new_name="units",
        ),
        migrations.RemoveField(
            model_name="invoiceentry",
            name="patient",
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(fields=["client", "endDate"], name="idx_invoice_client_end_date"),
        ),
        migrations.AddIndex(
            model_name="invoiceentry",
            index=models.Index(fields=["parentInvoice"], name="idx_invoice_parent"),
        ),
        migrations.AddConstraint(
            model_name="invoice",
            constraint=models.UniqueConstraint(
                fields=("client", "startDate", "endDate"),
                name="unique_client_date_combo",
            ),
        ),
    ]
