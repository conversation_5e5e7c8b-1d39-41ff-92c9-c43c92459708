# Generated by Django 5.1.9 on 2025-06-03 14:05

import datetime
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("authorization", "0009_user_preferences"),
    ]

    operations = [
        migrations.CreateModel(
            name="Client",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("mongoId", models.CharField(default=None, max_length=24, unique=True)),
                ("name", models.CharField(max_length=255)),
                ("paymentFormula", models.JSONField()),
            ],
            options={
                "db_table": "billing_portal_client",
                "indexes": [models.Index(fields=["name"], name="idx_client_name")],
                "constraints": [
                    models.UniqueConstraint(fields=("name",), name="unique_client_name")
                ],
            },
        ),
        migrations.CreateModel(
            name="Deployment",
            fields=[
                ("id", models.<PERSON>Field(primary_key=True, serialize=False)),
                ("mongoId", models.<PERSON>r<PERSON><PERSON>(default=None, max_length=24, unique=True)),
                ("name", models.CharField(max_length=255)),
                (
                    "client",
                    models.ForeignKey(
                        db_column="clientId",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="billing_portal.client",
                        to_field="mongoId",
                    ),
                ),
            ],
            options={
                "db_table": "billing_client_deployment",
            },
        ),
        migrations.CreateModel(
            name="BillingRecord",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("mongoId", models.CharField(default=None, max_length=24, unique=True)),
                ("provider", models.CharField(max_length=255)),
                ("diagnosisType", models.CharField(max_length=255)),
                ("deviceConnectionDate", models.DateField(null=True)),
                ("primaryInsurance", models.CharField(max_length=255)),
                ("reportEndDate", models.DateField(null=True)),
                ("isEligible", models.BooleanField()),
                ("monitoringMinutes", models.IntegerField(default=0)),
                ("numberOfCalls", models.IntegerField(default=0)),
                ("monthReading", models.IntegerField(default=0)),
                ("consentDate", models.DateField(null=True)),
                ("consentId", models.CharField(max_length=255, null=True)),
                ("deviceType", models.CharField(max_length=255, null=True)),
                ("deviceModel", models.CharField(max_length=255, null=True)),
                (
                    "patient",
                    models.ForeignKey(
                        db_column="patientId",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="authorization.user",
                        to_field="mongoId",
                    ),
                ),
                (
                    "deployment",
                    models.ForeignKey(
                        db_column="deploymentId",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="billing_portal.deployment",
                        to_field="mongoId",
                    ),
                ),
            ],
            options={
                "db_table": "billing_record",
            },
        ),
        migrations.CreateModel(
            name="Invoice",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("invoice_start_date", models.DateField()),
                ("invoice_end_date", models.DateField()),
                ("extraDetails", models.TextField(null=True)),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="billing_portal.client",
                    ),
                ),
            ],
            options={
                "db_table": "billing_invoice",
            },
        ),
        migrations.CreateModel(
            name="InvoiceEntry",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("service_description", models.CharField(max_length=255)),
                ("unites", models.IntegerField()),
                ("rates", models.IntegerField()),
                (
                    "parent_invoice",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="billing_portal.invoice",
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="authorization.user",
                    ),
                ),
            ],
            options={
                "db_table": "billing_invoice_entry",
            },
        ),
        migrations.CreateModel(
            name="MedicalRecordDeviceReading",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("module", models.CharField(max_length=255)),
                ("average", models.FloatField()),
                ("min", models.FloatField()),
                ("max", models.FloatField()),
                ("source", models.CharField(max_length=255)),
                ("startDateTime", models.DateTimeField()),
                (
                    "patient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="authorization.user",
                    ),
                ),
            ],
            options={
                "db_table": "billing_medical_record_device_reading",
            },
        ),
        migrations.CreateModel(
            name="MedicalRecordNotes",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("dateTime", models.DateTimeField()),
                ("note", models.TextField()),
                ("clinicianGivenName", models.CharField(max_length=255)),
                ("clinicianFamilyName", models.CharField(max_length=255)),
                (
                    "billingRecord",
                    models.ForeignKey(
                        db_column="billingRecordId",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="billing_portal.billingrecord",
                        to_field="mongoId",
                    ),
                ),
            ],
            options={
                "db_table": "billing_medical_record_notes",
            },
        ),
        migrations.CreateModel(
            name="RedoxLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("logId", models.CharField(max_length=255)),
                ("timestamp", models.DateTimeField()),
                ("dataModel", models.CharField(max_length=255)),
                ("logType", models.CharField(max_length=255)),
                ("status", models.CharField(max_length=255)),
                ("firstName", models.CharField(max_length=255)),
                ("lastName", models.CharField(max_length=255)),
                ("dateTimeOfService", models.DateTimeField()),
                ("error", models.TextField(null=True)),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="billing_portal.client",
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="authorization.user",
                    ),
                ),
            ],
            options={
                "db_table": "billing_redox_log",
            },
        ),
        migrations.CreateModel(
            name="SalesForceFile",
            fields=[
                ("uploadedAt", models.DateTimeField(auto_now_add=True)),
                ("versionNumber", models.AutoField(primary_key=True, serialize=False)),
                ("fileId", models.CharField(default="None", max_length=24)),
            ],
            options={
                "db_table": "billing_sales_force_file",
                "ordering": ["-versionNumber"],
                "indexes": [
                    models.Index(
                        fields=["uploadedAt"], name="idx_sales_force_uploaded_at"
                    )
                ],
            },
        ),
        migrations.CreateModel(
            name="BillingGeneralCPTCodes",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("mongoId", models.CharField(default=None, max_length=24, unique=True)),
                ("reportDate", models.DateTimeField()),
                (
                    "createDateTime",
                    models.DateTimeField(default=datetime.datetime.utcnow),
                ),
                ("deploymentId", models.CharField(max_length=24)),
                ("billingFirstReading", models.DateTimeField(blank=True, null=True)),
                (
                    "billingProviderName",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "billingDiagnosisDescription",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "billingDiagnosisICD10Code",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("billingDiagnosisOrder", models.IntegerField(blank=True, null=True)),
                (
                    "primaryInsuranceCarrier",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "primaryInsuranceCarrierPayerId",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "secondaryInsuranceCarrier",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "secondaryInsuranceCarrierPayerId",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("cpt98975", models.IntegerField(default=0)),
                ("cpt98976", models.IntegerField(default=0)),
                ("cpt98980", models.IntegerField(default=0)),
                ("cpt98981", models.IntegerField(default=0)),
                ("cpt99453", models.IntegerField(default=0)),
                ("cpt99454", models.IntegerField(default=0)),
                ("cpt99457", models.IntegerField(default=0)),
                ("cpt99458", models.IntegerField(default=0)),
                ("monitoringTimeMinutes", models.IntegerField(default=0)),
                ("numberOfCalls", models.IntegerField(default=0)),
                ("numberOfSubmissionDays", models.IntegerField(default=0)),
                ("numberOfSubmissions", models.IntegerField(default=0)),
                (
                    "user",
                    models.ForeignKey(
                        db_column="userId",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="authorization.user",
                        to_field="mongoId",
                    ),
                ),
            ],
            options={
                "db_table": "billing_general_cpt_codes",
                "indexes": [
                    models.Index(
                        fields=["user", "reportDate"], name="idx_cpt_user_report_date"
                    ),
                    models.Index(
                        fields=["user", "deploymentId"],
                        name="idx_cpt_codes_user_deployment",
                    ),
                ],
                "constraints": [
                    models.UniqueConstraint(
                        fields=("user", "reportDate", "deploymentId"),
                        name="unique_billing_general_cpt_codes_user_report_date_deployment",
                    )
                ],
            },
        ),
        migrations.CreateModel(
            name="BillingMonthlyCPTCodes",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("mongoId", models.CharField(default=None, max_length=24, unique=True)),
                (
                    "createDateTime",
                    models.DateTimeField(default=datetime.datetime.utcnow),
                ),
                ("deploymentId", models.CharField(max_length=24)),
                ("moduleId", models.CharField(blank=True, max_length=255, null=True)),
                ("reportStartDate", models.DateField(blank=True, null=True)),
                ("reportEndDate", models.DateField(blank=True, null=True)),
                ("givenName", models.CharField(blank=True, max_length=255, null=True)),
                ("familyName", models.CharField(blank=True, max_length=255, null=True)),
                ("dateOfBirth", models.DateField(blank=True, null=True)),
                ("monitoringTimeMins", models.IntegerField(default=0)),
                ("numberOfCalls", models.IntegerField(default=0)),
                ("cpt99457Units", models.IntegerField(default=0)),
                ("cpt99458Units", models.IntegerField(default=0)),
                ("cpt99457_8DateOfService", models.DateField(blank=True, null=True)),
                ("cpt99453_4CountOfSubmissionDays", models.IntegerField(default=0)),
                ("cpt99453BillingStartDate", models.DateField(blank=True, null=True)),
                ("cpt99453BillingEndDate", models.DateField(blank=True, null=True)),
                ("cpt99453CountOfSubmissions", models.IntegerField(default=0)),
                (
                    "cpt99453EarliestBillingDate",
                    models.DateField(blank=True, null=True),
                ),
                ("cpt99454BillingStartDate", models.DateField(blank=True, null=True)),
                ("cpt99454BillingEndDate", models.DateField(blank=True, null=True)),
                ("cpt99454CountOfSubmissions", models.IntegerField(default=0)),
                ("cpt99454_CountOfSubmissionDays", models.IntegerField(default=0)),
                (
                    "cpt99454_EarliestBillingDate",
                    models.DateField(blank=True, null=True),
                ),
                ("cpt99454BillingStartDate0", models.DateField(blank=True, null=True)),
                ("cpt99454BillingEndDate0", models.DateField(blank=True, null=True)),
                ("cpt99454CountOfSubmission0", models.IntegerField(default=0)),
                ("cpt99454CountOfSubmissionDays0", models.IntegerField(default=0)),
                (
                    "cpt99454EarliestBillingDate0",
                    models.DateField(blank=True, null=True),
                ),
                ("cpt99454BillingStartDate1", models.DateField(blank=True, null=True)),
                ("cpt99454BillingEndDate1", models.DateField(blank=True, null=True)),
                ("cpt99454CountOfSubmission1", models.IntegerField(default=0)),
                ("cpt99454CountOfSubmissionDays1", models.IntegerField(default=0)),
                (
                    "cpt99454EarliestBillingDate1",
                    models.DateField(blank=True, null=True),
                ),
                (
                    "user",
                    models.ForeignKey(
                        db_column="userId",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="authorization.user",
                        to_field="mongoId",
                    ),
                ),
            ],
            options={
                "db_table": "billing_monthly_cpt_codes",
                "indexes": [
                    models.Index(
                        fields=["user", "reportStartDate", "reportEndDate"],
                        name="idx_monthly_cpt_user_report_dt",
                    ),
                    models.Index(
                        fields=["user", "deploymentId"],
                        name="idx_monthly_cpt_codes_user_dep",
                    ),
                ],
                "constraints": [
                    models.UniqueConstraint(
                        fields=(
                            "user",
                            "reportStartDate",
                            "reportEndDate",
                            "deploymentId",
                        ),
                        name="unique_billing_monthly_cpt_codes_user_report_date_deployment",
                    )
                ],
            },
        ),
        migrations.CreateModel(
            name="CptChargeUnit",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("mongoId", models.CharField(max_length=24, unique=True)),
                (
                    "code",
                    models.CharField(
                        choices=[
                            ("99453", "99453"),
                            ("99454", "99454"),
                            ("99457", "99457"),
                            ("99458", "99458"),
                        ],
                        max_length=20,
                    ),
                ),
                ("units", models.IntegerField(default=1)),
                ("dateOfService", models.DateField()),
                ("extraDetails", models.JSONField(null=True)),
                (
                    "billingRecord",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="billing_portal.billingrecord",
                    ),
                ),
                (
                    "dependantUnit",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="billing_portal.cptchargeunit",
                    ),
                ),
            ],
            options={
                "db_table": "billing_cpt_charge_unit",
                "indexes": [
                    models.Index(
                        fields=["dateOfService", "billingRecord"],
                        name="idx_cpt_unit_date_billing",
                    ),
                    models.Index(
                        fields=["code", "billingRecord"],
                        name="idx_cpt_unit_code_billing",
                    ),
                    models.Index(fields=["mongoId"], name="idx_cpt_unit_mongo_id"),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="deployment",
            index=models.Index(fields=["client"], name="idx_deployment_client"),
        ),
        migrations.AddIndex(
            model_name="deployment",
            index=models.Index(fields=["id"], name="idx_deployment_id"),
        ),
        migrations.AddIndex(
            model_name="billingrecord",
            index=models.Index(
                fields=["deployment"], name="billing_rec_deploym_1d04a8_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="billingrecord",
            index=models.Index(fields=["mongoId"], name="idx_billing_record_mongo_id"),
        ),
        migrations.AddIndex(
            model_name="billingrecord",
            index=models.Index(
                fields=["patient", "reportEndDate"], name="idx_billing_rec_patient_date"
            ),
        ),
        migrations.AddConstraint(
            model_name="billingrecord",
            constraint=models.UniqueConstraint(
                fields=("patient", "deployment", "reportEndDate"),
                name="unique_patient_client_combo",
            ),
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(
                fields=["client", "invoice_end_date"],
                name="idx_invoice_client_end_date",
            ),
        ),
        migrations.AddConstraint(
            model_name="invoice",
            constraint=models.UniqueConstraint(
                fields=("client", "invoice_start_date", "invoice_end_date"),
                name="unique_client_date_combo",
            ),
        ),
        migrations.AddIndex(
            model_name="invoiceentry",
            index=models.Index(
                fields=["patient", "parent_invoice"], name="idx_invoice_patient_parent"
            ),
        ),
        migrations.AddIndex(
            model_name="medicalrecorddevicereading",
            index=models.Index(
                fields=["patient", "startDateTime"], name="idx_med_rec_patient_start_dt"
            ),
        ),
        migrations.AddIndex(
            model_name="redoxlog",
            index=models.Index(
                fields=["client", "patient"], name="idx_redox_log_client_patient"
            ),
        ),
        migrations.AddIndex(
            model_name="redoxlog",
            index=models.Index(
                fields=["client", "patient", "dateTimeOfService"],
                name="idx_rdx_log_client_patient_dt",
            ),
        ),
        migrations.AddIndex(
            model_name="redoxlog",
            index=models.Index(fields=["logId"], name="idx_redox_log_log_id"),
        ),
    ]
