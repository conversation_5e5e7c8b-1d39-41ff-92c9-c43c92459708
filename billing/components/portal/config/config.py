from sdk.common.utils.convertible import convertibleclass, default_field
from sdk.phoenix.config.server_config import BasePhoenixConfig


@convertibleclass
class RedoxConfig:
    clientId: str = default_field()
    privateKey: str = default_field()
    environmentId: str = default_field()
    kid: str = default_field()
    clientName: str = default_field()


@convertibleclass
class BillingPortalConfig(BasePhoenixConfig):
    redoxConfig: RedoxConfig = default_field()
    tags: str = default_field()
