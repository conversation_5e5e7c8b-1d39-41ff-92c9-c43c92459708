from django.db import models

from .client import Client


class Deployment(models.Model):
    class Meta:
        db_table = "billing_client_deployment"
        app_label = "billing_portal"
        indexes = [
            models.Index(fields=["client"], name="idx_deployment_client"),
            models.Index(fields=["id"], name="idx_deployment_id"),
        ]
        constraints = []

    id = models.AutoField(primary_key=True)
    mongoId = models.CharField(max_length=24, unique=True, default=None)
    name = models.CharField(max_length=255)
    client = models.ForeignKey(Client, on_delete=models.CASCADE, to_field="mongoId", db_column="clientId")
