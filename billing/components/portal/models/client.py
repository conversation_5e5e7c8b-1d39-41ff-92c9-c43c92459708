from django.db import models


class Client(models.Model):
    class Meta:
        db_table = "billing_portal_client"
        app_label = "billing_portal"
        indexes = [models.Index(fields=["name"], name="idx_client_name")]
        constraints = [models.UniqueConstraint(fields=["name"], name="unique_client_name")]

    id = models.AutoField(primary_key=True)
    mongoId = models.CharField(max_length=24, unique=True, default=None)
    name = models.CharField(max_length=255, null=False)
    paymentFormula = models.JSONField(null=False)

    def __str__(self):
        return self.name

    def to_dict(self):
        return {"id": str(self.id), "name": self.name, "payment_formula": self.payment_formula}
