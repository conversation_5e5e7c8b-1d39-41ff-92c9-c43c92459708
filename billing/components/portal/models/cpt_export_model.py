from datetime import datetime

from django.db import models

from sdk.authorization.models import User


class BillingGeneralCPTCodes(models.Model):
    class Meta:
        db_table = "billing_general_cpt_codes"
        app_label = "billing_portal"
        indexes = [
            models.Index(fields=["user", "reportDate"], name="idx_cpt_user_report_date"),
            models.Index(fields=["user", "deploymentId"], name="idx_cpt_codes_user_deployment"),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=["user", "reportDate", "deploymentId"],
                name="unique_billing_general_cpt_codes_user_report_date_deployment",
            )
        ]

    id = models.AutoField(primary_key=True)
    mongoId = models.CharField(max_length=24, unique=True, default=None)
    user = models.ForeignKey(User, on_delete=models.CASCADE, to_field="mongoId", db_column="userId")
    reportDate = models.DateField()
    createDateTime = models.DateTimeField(default=datetime.utcnow)
    deploymentId = models.CharField(max_length=24)
    billingFirstReading = models.DateField(null=True, blank=True)
    billingProviderName = models.CharField(max_length=255, null=True, blank=True)
    billingDiagnosisDescription = models.CharField(max_length=255, null=True, blank=True)
    billingDiagnosisICD10Code = models.CharField(max_length=255, null=True, blank=True)
    billingDiagnosisOrder = models.IntegerField(null=True, blank=True)
    primaryInsuranceCarrier = models.CharField(max_length=255, null=True, blank=True)
    primaryInsuranceCarrierPayerId = models.CharField(max_length=255, null=True, blank=True)
    secondaryInsuranceCarrier = models.CharField(max_length=255, null=True, blank=True)
    secondaryInsuranceCarrierPayerId = models.CharField(max_length=255, null=True, blank=True)
    cpt98975 = models.IntegerField(default=0)
    cpt98976 = models.IntegerField(default=0)
    cpt98980 = models.IntegerField(default=0)
    cpt98981 = models.IntegerField(default=0)
    cpt99453 = models.IntegerField(default=0)
    cpt99454 = models.IntegerField(default=0)
    cpt99457 = models.IntegerField(default=0)
    cpt99458 = models.IntegerField(default=0)
    monitoringTimeMinutes = models.IntegerField(default=0)
    numberOfCalls = models.IntegerField(default=0)
    numberOfSubmissionDays = models.IntegerField(default=0)
    numberOfSubmissions = models.IntegerField(default=0)


class BillingMonthlyCPTCodes(models.Model):
    class Meta:
        db_table = "billing_monthly_cpt_codes"
        app_label = "billing_portal"
        indexes = [
            models.Index(
                fields=["user", "reportStartDate", "reportEndDate"],
                name="idx_monthly_cpt_user_report_dt",
            ),
            models.Index(fields=["user", "deploymentId"], name="idx_monthly_cpt_codes_user_dep"),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=["user", "reportStartDate", "reportEndDate", "deploymentId"],
                name="unique_billing_monthly_cpt_codes_user_report_date_deployment",
            )
        ]

    id = models.AutoField(primary_key=True)
    mongoId = models.CharField(max_length=24, unique=True, default=None)
    user = models.ForeignKey(User, on_delete=models.CASCADE, to_field="mongoId", db_column="userId")
    createDateTime = models.DateTimeField(default=datetime.utcnow)
    deploymentId = models.CharField(max_length=24)
    moduleId = models.CharField(max_length=255, null=True, blank=True)
    reportStartDate = models.DateField(null=True, blank=True)
    reportEndDate = models.DateField(null=True, blank=True)  # reportDate
    givenName = models.CharField(max_length=255, null=True, blank=True)
    familyName = models.CharField(max_length=255, null=True, blank=True)
    dateOfBirth = models.DateField(null=True, blank=True)
    monitoringTimeMins = models.IntegerField(default=0)
    numberOfCalls = models.IntegerField(default=0)
    cpt99457Units = models.IntegerField(default=0)
    cpt99458Units = models.IntegerField(default=0)
    cpt99457_8DateOfService = models.DateField(null=True, blank=True)

    cpt99453_4CountOfSubmissionDays = models.IntegerField(default=0)

    cpt99453BillingStartDate = models.DateField(null=True, blank=True)
    cpt99453BillingEndDate = models.DateField(null=True, blank=True)
    cpt99453CountOfSubmissions = models.IntegerField(default=0)
    cpt99453EarliestBillingDate = models.DateField(null=True, blank=True)

    cpt99454BillingStartDate = models.DateField(null=True, blank=True)
    cpt99454BillingEndDate = models.DateField(null=True, blank=True)
    cpt99454CountOfSubmissions = models.IntegerField(default=0)
    cpt99454_CountOfSubmissionDays = models.IntegerField(default=0)
    cpt99454_EarliestBillingDate = models.DateField(null=True, blank=True)

    cpt99454BillingStartDate0 = models.DateField(null=True, blank=True)
    cpt99454BillingEndDate0 = models.DateField(null=True, blank=True)
    cpt99454CountOfSubmission0 = models.IntegerField(default=0)
    cpt99454CountOfSubmissionDays0 = models.IntegerField(default=0)
    cpt99454EarliestBillingDate0 = models.DateField(null=True, blank=True)

    cpt99454BillingStartDate1 = models.DateField(null=True, blank=True)
    cpt99454BillingEndDate1 = models.DateField(null=True, blank=True)
    cpt99454CountOfSubmission1 = models.IntegerField(default=0)
    cpt99454CountOfSubmissionDays1 = models.IntegerField(default=0)
    cpt99454EarliestBillingDate1 = models.DateField(null=True, blank=True)
