from django.db import models

from sdk.authorization.models import User
from .client import Client


class RedoxLog(models.Model):
    class Meta:
        db_table = "billing_redox_log"
        app_label = "billing_portal"
        indexes = [
            models.Index(fields=["client", "patient"], name="idx_redox_log_client_patient"),
            models.Index(fields=["client", "patient", "dateTimeOfService"], name="idx_rdx_log_client_patient_dt"),
            models.Index(fields=["logId"], name="idx_redox_log_log_id"),
        ]

    logId = models.CharField(max_length=255, null=False)
    timestamp = models.DateTimeField()
    dataModel = models.CharField(max_length=255, null=False)
    logType = models.CharField(max_length=255, null=False)
    status = models.CharField(max_length=255, null=False)
    firstName = models.CharField(max_length=255, null=False)
    lastName = models.Char<PERSON>ield(max_length=255, null=False)
    patient = models.ForeignKey(User, on_delete=models.CASCADE, null=True)
    dateTimeOfService = models.DateTimeField()
    error = models.TextField(null=True)
    client = models.ForeignKey(Client, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.logId} {self.timestamp} - {self.status} - {self.firstName} - {self.patient}"
