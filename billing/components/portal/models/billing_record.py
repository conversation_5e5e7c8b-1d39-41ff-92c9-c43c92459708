from django.db import models
from django.db.models import CASCADE

from sdk.authorization.models import User
from .deployment import Deployment


class BillingRecord(models.Model):
    REPORT_END_DATE = "reportEndDate"

    id = models.AutoField(primary_key=True)
    mongoId = models.CharField(max_length=24, unique=True, default=None)
    deployment = models.ForeignKey(Deployment, on_delete=CASCADE, to_field="mongoId", db_column="deploymentId")
    patient = models.ForeignKey(User, on_delete=CASCADE, to_field="mongoId", db_column="patientId")
    provider = models.CharField(max_length=255, null=False)
    diagnosisType = models.CharField(max_length=255, null=False)
    deviceConnectionDate = models.DateField(null=True)
    primaryInsurance = models.CharField(max_length=255)
    reportEndDate = models.DateField(null=True)
    isEligible = models.BooleanField(null=False)
    monitoringMinutes = models.IntegerField(null=False, default=0)
    numberOfCalls = models.IntegerField(null=False, default=0)
    monthReading = models.IntegerField(null=False, default=0)
    consentDate = models.DateField(null=True)
    consentId = models.CharField(max_length=255, null=True)
    deviceType = models.CharField(max_length=255, null=True)
    deviceModel = models.CharField(max_length=255, null=True)

    class Meta:
        db_table = "billing_record"
        app_label = "billing_portal"
        indexes = [
            models.Index(fields=["deployment"]),
            models.Index(fields=["mongoId"], name="idx_billing_record_mongo_id"),
            models.Index(fields=["patient", "reportEndDate"], name="idx_billing_rec_patient_date"),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=["patient", "deployment", "reportEndDate"], name="unique_patient_client_combo"
            )
        ]

    def __str__(self):
        return f"{self.patient_id}@{self.deployment.client} - {self.reportEndDate}"

    def _is_eligible(self):
        eligible = not self._is_engage() and self._device_connection_before_report()
        return eligible

    def _is_engage(self):
        return self.primaryInsurance.lower() == "engage"

    def _device_connection_before_report(self):
        return self.deviceConnectionDate and self.deviceConnectionDate <= self.reportEndDate

    def save(self, *args, **kwargs):
        self.isEligible = bool(self._is_eligible())
        super().save(*args, **kwargs)
