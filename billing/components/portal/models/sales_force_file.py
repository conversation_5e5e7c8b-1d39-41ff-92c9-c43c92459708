from django.db import models


class SalesForceFile(models.Model):
    class Meta:
        db_table = "billing_sales_force_file"
        app_label = "billing_portal"
        indexes = [
            models.Index(fields=["uploadedAt"], name="idx_sales_force_uploaded_at"),
        ]
        ordering = ["-versionNumber"]

    uploadedAt = models.DateTimeField(auto_now_add=True)
    versionNumber = models.AutoField(primary_key=True)
    fileId = models.CharField(max_length=24, null=False, blank=False, default="None")
