from django.db import models

from .invoice import Invoice


class InvoiceEntry(models.Model):
    class Meta:
        db_table = "billing_invoice_entry"
        app_label = "billing_portal"
        indexes = [
            models.Index(fields=["parentInvoice"], name="idx_invoice_parent"),
        ]

    serviceDescription = models.CharField(max_length=255, null=False)
    units = models.IntegerField(null=False)
    rates = models.IntegerField(null=False)
    parentInvoice = models.ForeignKey(Invoice, on_delete=models.CASCADE)
