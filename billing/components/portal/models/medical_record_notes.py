from django.db import models

from .billing_record import BillingRecord


class MedicalRecordNotes(models.Model):
    # todo: remove and replace with Observation Notes
    class Meta:
        db_table = "billing_medical_record_notes"
        app_label = "billing_portal"

    billingRecord = models.ForeignKey(
        BillingRecord, on_delete=models.CASCADE, to_field="mongoId", db_column="billingRecordId"
    )
    dateTime = models.DateTimeField()
    note = models.TextField()
    clinicianGivenName = models.CharField(max_length=255)
    clinicianFamilyName = models.CharField(max_length=255)
