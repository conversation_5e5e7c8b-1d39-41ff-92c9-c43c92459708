from django.db import models
from django.db.models import ForeignKey

from .client import Client


class Invoice(models.Model):
    class Meta:
        db_table = "billing_invoice"
        app_label = "billing_portal"
        indexes = [
            models.Index(fields=["client", "endDate"], name="idx_invoice_client_end_date"),
        ]
        constraints = [
            models.UniqueConstraint(fields=["client", "startDate", "endDate"], name="unique_client_date_combo")
        ]

    startDate = models.DateField()
    endDate = models.DateField()
    client = ForeignKey(Client, on_delete=models.CASCADE)
    extraDetails = models.TextField(null=True)

    def __str__(self):
        return f"{self.client.name} - {self.endDate}"
