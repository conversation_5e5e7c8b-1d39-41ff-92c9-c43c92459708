import io

from billing.components.portal.dtos.billing_portal_models import ClientDTO
from sdk import convertibleclass
from sdk.common.usecase.response_object import ResponseObject
from sdk.common.utils.convertible import default_field
from sdk.deployment.dtos.consent import ConsentLogDTO
from sdk.module_result.dtos.user_note import UserNoteDTO


@convertibleclass
class GetClientsResponseObject(ResponseObject):
    clients: list[ClientDTO] = default_field()


@convertibleclass
class DownloadResponseObject(ResponseObject):
    FILE_NAME = "filename"
    FILE_DATA = "downloadResponse"

    downloadResponse: io.BytesIO = default_field()
    filename: str = default_field()


@convertibleclass
class DownloadClientsResponseObject(DownloadResponseObject):
    pass


@convertibleclass
class DownloadSalesForceFileResponseObject(DownloadResponseObject):
    pass


@convertibleclass
class GetClientReportResponseObject(DownloadClientsResponseObject):
    pass


@convertibleclass
class AddClientsResponseObject(ResponseObject):
    form: dict = default_field()
    message: str = default_field()


@convertibleclass
class GenerateReportsResponseObject(ResponseObject):
    message: str = default_field()


@convertibleclass
class AddSalesForceFileResponseObject(ResponseObject):
    id: int = default_field()
    message: str = default_field()


@convertibleclass
class MedicalRecordEntry:
    id: str = default_field()
    givenName: str = default_field()
    familyName: str = default_field()
    numberOfCalls: int = default_field()
    monitoringMinutes: int = default_field()
    diagnosis: str = default_field()


@convertibleclass
class ClientMedicalRecordResponseObject(ResponseObject):
    patients: list[MedicalRecordEntry] = default_field()


@convertibleclass
class AggregatedVital:
    primitive: str = default_field()
    count: int = default_field()
    average: float = default_field()
    min: float = default_field()
    max: float = default_field()


@convertibleclass
class PatientMedicalRecordResponseObject(ResponseObject):
    USER_ID = "userId"
    GIVEN_NAME = "givenName"
    FAMILY_NAME = "familyName"
    DIAGNOSIS = "diagnosis"
    PROVIDER = "provider"
    START_DATE = "startDate"
    END_DATE = "endDate"
    CONSENT_DATE = "consentDate"
    CONSENT_ID = "consentId"
    DEVICE_TYPE = "deviceType"
    DEVICE_CONNECTION_DATE = "deviceConnectionDate"
    DEVICE_MODEL = "deviceModel"
    MONITORING_MINUTES = "monitoringMinutes"
    NUMBER_OF_CALLS = "numberOfCalls"
    VITALS = "vitals"
    CONSENT_LOGS = "consentLogs"
    NOTES = "notes"

    userId: str = default_field()
    givenName: str = default_field()
    familyName: str = default_field()
    diagnosis: str = default_field()
    provider: str = default_field()
    startDate: str = default_field()
    endDate: str = default_field()
    consentDate: str = default_field()
    consentId: str = default_field()
    deviceType: str = default_field()
    deviceConnectionDate: str = default_field()
    deviceModel: str = default_field()
    monitoringMinutes: int = default_field()
    numberOfCalls: int = default_field()
    vitals: list[AggregatedVital] = default_field()
    consentLogs: list[ConsentLogDTO] = default_field()
    notes: list[UserNoteDTO] = default_field()
