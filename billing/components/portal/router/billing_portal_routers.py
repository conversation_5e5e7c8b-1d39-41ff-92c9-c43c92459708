from io import Bytes<PERSON>

from flask import g, render_template, request, send_file

from billing.components.portal.router.billing_portal_requests import (
    AddClientDeploymentRequestObject,
    AddClientRequestObject,
    AddSalesForceFileRequestObject,
    ClientMedicalRecordRequestObject,
    DownloadSalesForceFileRequestObject,
    GenerateExportRequestObject,
    GenerateReportsRequestObject,
    GetClientReportRequestObject,
    PatientMedicalRecordRequestObject,
    UpdateClientDeploymentRequestObject,
)
from billing.components.portal.router.billing_portal_response_objects import (
    AddSalesForceFileResponseObject,
    ClientMedicalRecordResponseObject,
    GetClientsResponseObject,
)
from billing.components.portal.tasks import generate_billing_cpt_codes_report
from billing.components.portal.use_case import (
    AddClientUseCase,
    GenerateReportsUseCase,
    GetClientReportUseCase,
    GetClientsUseCase,
)
from billing.components.portal.use_case.client_deployment_use_case import (
    AddClientDeploymentUseCase,
    DeleteClientDeploymentUseCase,
    UpdateClientDeploymentUseCase,
)
from billing.components.portal.use_case.collect_all_clients_invoices import (
    CollectAllClientsInvoices,
    CollectAllClientsInvoicesRequestObject,
)
from billing.components.portal.use_case.medical_record_use_case import (
    GetClientMedicalRecordsUseCase,
    GetPatientMedicalRecordsUseCase,
)
from billing.components.portal.use_case.sales_force_file_use_case import (
    AddSalesForceFileUseCase,
    DownloadSalesForceFileUseCase,
)
from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.usecase.request_object import RequestObject
from sdk.common.usecase.response_object import OkResponse, ResultIdResponseObject
from sdk.security import Access, ProtectedBlueprint

api = ProtectedBlueprint(
    "billing_portal_route", __name__, url_prefix="/api/v1/billing-portal", template_folder="../templates"
)


@api.get("/")
@api.output(GetClientsResponseObject.Schema, 200)
@api.requires(Access.ORG.EDIT)
def get_clients():
    """
    Get clients for the Billing Portal.
    Responsible for rendering the client's page.
    """
    request_object = RequestObject()
    return GetClientsUseCase().execute(request_object)


@api.post("/deployment-client/")
@api.requires(Access.ORG.EDIT)
@api.input(AddClientDeploymentRequestObject.Schema, location="json", arg_name="request_object")
@api.output(ResultIdResponseObject.Schema, 201)
def add_deployment_client(request_object: AddClientDeploymentRequestObject):
    """
    Add a deployment to a client for the Billing Portal.
    """
    return AddClientDeploymentUseCase().execute(request_object=request_object)


@api.put("/deployment-client/")
@api.requires(Access.ORG.EDIT)
@api.input(UpdateClientDeploymentRequestObject.Schema, location="json", arg_name="request_object")
@api.output(OkResponse.Schema, 200)
def update_deployment_client(request_object: UpdateClientDeploymentRequestObject):
    """
    Update a deployment for a client for the Billing Portal.
    """
    UpdateClientDeploymentUseCase().execute(request_object=request_object)

    return OkResponse()


@api.delete("/deployment-client/")
@api.requires(Access.ORG.EDIT)
@api.input(UpdateClientDeploymentRequestObject.Schema, location="json", arg_name="request_object")
@api.output(OkResponse.Schema, 200)
def delete_deployment_client(request_object: UpdateClientDeploymentRequestObject):
    """
    Delete a deployment for a client for the Billing Portal.
    """
    DeleteClientDeploymentUseCase().execute(request_object=request_object)

    return OkResponse()


@api.post("/clients")
@api.requires(Access.ORG.EDIT)
@api.input(AddClientRequestObject.Schema, location="json", arg_name="request_object")
@api.output(ResultIdResponseObject.Schema, 201)
def add_client(request_object: AddClientRequestObject):
    """
    Add a single client for the Billing Portal.
    """
    return AddClientUseCase().execute(request_object=request_object)


@api.get("/generate/billing_and_invoices/")
@api.requires(Access.ORG.EDIT)
@api.input(GenerateReportsRequestObject.Schema, location="query", arg_name="request_object")
@api.output(OkResponse.Schema, 200)
def generate_reports(request_object: GenerateReportsRequestObject):
    """
    Generate billing and invoice reports for the Billing Portal.
    """
    GenerateReportsUseCase().execute(request_object)
    return OkResponse()


@api.get("/import/salesforce/")
@api.requires(Access.ORG.EDIT)
@api.input(DownloadSalesForceFileRequestObject.Schema, location="query", arg_name="query_data")
def download_sales_force_file(query_data: DownloadSalesForceFileRequestObject):
    """
    Downloads the sales force file for the Billing Portal.
    """
    response = DownloadSalesForceFileUseCase().execute(query_data)
    return send_file(
        response.downloadResponse,
        mimetype="text/csv",
        as_attachment=True,
        download_name=response.filename,
    )


@api.post("/import/salesforce/")
@api.output(AddSalesForceFileResponseObject.Schema, 200)
@api.requires(Access.ORG.EDIT)
def add_sales_force_file():
    if "file" not in request.files:
        raise InvalidRequestException("No file part in the request")

    file = request.files["file"]
    file_name = file.filename
    file = BytesIO(file.read())

    if file_name == "":
        file_name = "salesforce_file.csv"

    request_object = AddSalesForceFileRequestObject.from_dict(
        {"fileName": file_name, "fileData": file, "userId": g.auth_user.id}
    )
    return AddSalesForceFileUseCase().execute(request_object)


@api.get("/cpt/<client_id>")
@api.requires(Access.ORG.EDIT)
@api.input(GetClientReportRequestObject.Schema, location="query", arg_name="request_object")
@api.doc(
    description="Download client report as Excel",
    responses={
        200: {
            "description": "XLSX file",
            "content": {
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {
                    "schema": {"type": "string", "format": "binary"}
                }
            },
        }
    },
)
def get_client_report(request_object: GetClientReportRequestObject, client_id: str):
    """
    Get client report for the Billing Portal.
    Responsible for rendering the client report page and downloading the client report data.
    """
    request_object.clientId = client_id
    response = GetClientReportUseCase().execute(request_object)
    return send_file(
        response.downloadResponse,
        mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        as_attachment=True,
        download_name=response.filename,
    )


@api.post("/call-export-task")
@api.input(GenerateExportRequestObject.Schema, location="query", arg_name="request_object")
@api.output(OkResponse.Schema, 202)
@api.requires(Access.ORG.EDIT)
def call_export_task(request_object: GenerateExportRequestObject):
    """
    Call the export task for the Billing Portal.
    This endpoint is used to trigger the export of billing data.
    Month and year are taken from the request query parameters.
    """
    generate_billing_cpt_codes_report.delay(
        year=request_object.exportYear, month=request_object.exportMonth, deployment_id=request_object.deploymentId
    )
    return {"message": "Export task has been triggered."}, 202


@api.get("/get-clients-invoices")
@api.input(CollectAllClientsInvoicesRequestObject.Schema, location="query")
@api.requires(Access.ORG.EDIT)
def get_clients_invoices(query_data: GenerateReportsRequestObject, **_):
    """
    Get all clients' invoices for the Billing Portal.
    This endpoint is used to retrieve all clients' invoices as a CSV file.
    If no report_end_date is provided, all reports from all dates will be included.
    If a report_end_date is provided, only reports for that date will be included.
    """
    use_case = CollectAllClientsInvoices()
    filename, csv_data = use_case.execute(query_data)

    return send_file(csv_data, mimetype="text/csv", as_attachment=True, download_name=filename)


@api.get("/client-medical-records")
@api.input(ClientMedicalRecordRequestObject.Schema, location="query", arg_name="query_data")
@api.output(ClientMedicalRecordResponseObject.Schema)
@api.requires(Access.ORG.EDIT)
def get_client_medical_record(query_data: ClientMedicalRecordRequestObject):
    """
    Get client medical records for the Billing Portal.
    This endpoint is used to retrieve client medical records.
    """
    return GetClientMedicalRecordsUseCase().execute(query_data)


@api.get("/patient-medical-records")
@api.input(PatientMedicalRecordRequestObject.Schema, location="query", arg_name="query_data")
@api.requires(Access.ORG.EDIT)
def get_patient_medical_record(query_data: PatientMedicalRecordRequestObject):
    """
    Get patient medical records for the Billing Portal.
    This endpoint is used to retrieve client medical records.
    """
    patient_record = GetPatientMedicalRecordsUseCase().execute(query_data)
    return render_template("patient_medical_record.html", record=patient_record)
