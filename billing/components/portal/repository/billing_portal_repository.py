from abc import ABC, abstractmethod
from datetime import date, datetime

from billing.components.portal.dtos.billing_portal_models import (
    BillingRecordDTO,
    CPTGeneralExport,
    CPTMonthlyExport,
    ClientDTO,
    CptChargeUnitDTO,
    SalesForceFileDTO,
)
from billing.components.portal.repository.models import LatestDeployment


class BillingPortalRepository(ABC):
    """This is the base repository class for keeping track of users' CPT codes."""

    @abstractmethod
    def add_user_general_cpt_code(self, billing: CPTGeneralExport):
        raise NotImplementedError

    @abstractmethod
    def get_user_general_cpt_code(self, user_id: str, report_date: date):
        raise NotImplementedError

    @abstractmethod
    def get_general_cpt_codes(self, report_date: datetime.date) -> list[CPTGeneralExport]:
        raise NotImplementedError

    @abstractmethod
    def get_monthly_cpt_codes(self, report_date: datetime.date) -> list[CPTMonthlyExport]:
        raise NotImplementedError

    @abstractmethod
    def get_latest_deployments_for_general_cpt_code(self) -> list[LatestDeployment]:
        """
        Queries the latest deploymentIds in the BillingGeneralCPTCodes table, for the current month.
        If a deploymentId has multiple reportDates, it will return the one with the latest reportDate.
        """
        raise NotImplementedError

    @abstractmethod
    def add_user_monthly_cpt_code(self, billing: CPTMonthlyExport):
        raise NotImplementedError

    @abstractmethod
    def get_cpt_charge_units_by_billing_record(self, billing_record_id: str) -> list[CptChargeUnitDTO]:
        raise NotImplementedError

    @abstractmethod
    def create_client(self, client: ClientDTO) -> str:
        raise NotImplementedError

    @abstractmethod
    def update_client(self, client: ClientDTO) -> str:
        raise NotImplementedError

    @abstractmethod
    def retrieve_clients(self) -> list[ClientDTO]:
        raise NotImplementedError

    @abstractmethod
    def retrieve_client(self, client_id: str) -> ClientDTO:
        raise NotImplementedError

    @abstractmethod
    def set_user_mrn(self, user_id: str, mrn: str):
        raise NotImplementedError

    @abstractmethod
    def create_billing_record(self, record: BillingRecordDTO) -> str:
        raise NotImplementedError

    @abstractmethod
    def retrieve_patient_billing_record(self, user_id: str, report_end_date: date) -> BillingRecordDTO | None:
        raise NotImplementedError

    @abstractmethod
    def retrieve_billing_records(
        self, report_end_date: date | None = None, client_id: str | None = None
    ) -> list[BillingRecordDTO]:
        raise NotImplementedError

    @abstractmethod
    def update_billing_record(self, record: BillingRecordDTO) -> str:
        raise NotImplementedError

    @abstractmethod
    def create_cpt_charge_unit(self, cpt_charge_unit: CptChargeUnitDTO) -> str:
        raise NotImplementedError

    @abstractmethod
    def add_deployment_to_client(self, client_id: str, deployment_id: str) -> str:
        raise NotImplementedError

    @abstractmethod
    def update_deployment_client(self, client_id: str, deployment_id: str):
        raise NotImplementedError

    @abstractmethod
    def delete_deployment_client(self, client_id, deployment_id):
        raise NotImplementedError

    @abstractmethod
    def get_report_end_dates_for_client(self, client_id: str) -> list[date]:
        raise NotImplementedError

    @abstractmethod
    def get_distinct_invoice_dates(self) -> list[date]:
        raise NotImplementedError

    @abstractmethod
    def retrieve_all_clients_invoices_with_entries(self, report_end_date: date) -> list[dict]:
        raise NotImplementedError

    @abstractmethod
    def create_salesforce_file(self, uploaded_file_id: str, uploaded_at: datetime) -> SalesForceFileDTO:
        raise NotImplementedError

    @abstractmethod
    def retrieve_salesforce_file(self, version: int) -> SalesForceFileDTO | None:
        raise NotImplementedError
