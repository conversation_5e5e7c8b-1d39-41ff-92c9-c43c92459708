from datetime import date, datetime

from bson import ObjectId
from django.db import models

from billing.components.portal.dtos.billing_portal_models import (
    BillingRecordDTO,
    CPTGeneralExport,
    CPTMonthlyExport,
    ClientDTO,
    CptChargeUnitDTO,
    InvoiceEntryDTO,
    InvoiceReport,
    SalesForceFileDTO,
)
from billing.components.portal.models import (
    BillingGeneralCPTCodes,
    BillingMonthlyCPTCodes,
    BillingRecord,
    Client,
    CptChargeUnit,
    Deployment,
    Invoice,
    SalesForceFile,
)
from billing.components.portal.repository.billing_portal_repository import (
    BillingPortalRepository,
)
from billing.components.portal.repository.models import LatestDeployment
from sdk.authorization.models import User
from sdk.common.utils.validators import model_to_dict


class PostgresBillingPortalRepository(BillingPortalRepository):
    def add_user_general_cpt_code(self, billing: CPTGeneralExport):
        billing_dict = self._prepare_billing_cpt_code_dict(billing)
        billing = BillingGeneralCPTCodes(**billing_dict)
        billing.save()

    def get_user_general_cpt_code(self, user_id: str, report_date: date):
        try:
            billing = BillingGeneralCPTCodes.objects.get(user_id=user_id, reportDate=report_date)
            billing_dict = model_to_dict(billing)
            billing_dict["userId"] = billing_dict.pop("user")
            return CPTGeneralExport.from_dict(billing_dict)
        except BillingGeneralCPTCodes.DoesNotExist:
            return None

    def get_general_cpt_codes(self, report_date: datetime.date) -> list[CPTGeneralExport]:
        return [
            CPTGeneralExport.from_dict(model_to_dict(b))
            for b in BillingGeneralCPTCodes.objects.filter(reportDate=report_date)
        ]

    def get_monthly_cpt_codes(self, report_date: datetime.date) -> list[CPTMonthlyExport]:
        return [
            CPTMonthlyExport.from_dict(model_to_dict(b))
            for b in BillingMonthlyCPTCodes.objects.filter(reportEndDate=report_date)
        ]

    def get_latest_deployments_for_general_cpt_code(self) -> list[LatestDeployment]:
        first_day_of_current_month = datetime.now().replace(day=1)
        latest_deployments = (
            BillingGeneralCPTCodes.objects.filter(reportDate__gte=first_day_of_current_month)
            .values("deploymentId")
            .annotate(maxReportDate=models.Max("reportDate"))
            .order_by("-maxReportDate")
        )
        return [LatestDeployment.from_dict(d) for d in latest_deployments]

    def add_user_monthly_cpt_code(self, billing: CPTMonthlyExport):
        billing_dict = self._prepare_billing_cpt_code_dict(billing)
        billing = BillingMonthlyCPTCodes(**billing_dict)
        billing.save()

    @staticmethod
    def _prepare_billing_cpt_code_dict(billing: CPTGeneralExport | CPTMonthlyExport) -> dict:
        user_id = billing.userId
        try:
            user = User.objects.get(mongoId=user_id)
        except User.DoesNotExist:
            raise ValueError(f"User with id {user_id} does not exist.")
        billing_dict = billing.to_dict(include_none=False)
        billing_dict["user"] = user
        billing_dict.pop("userId")
        billing_dict["mongoId"] = ObjectId()
        return billing_dict

    def get_cpt_charge_units_by_billing_record(self, billing_record_id: str) -> list[CptChargeUnitDTO]:
        cpt_charge_units = CptChargeUnit.objects.filter(billingRecord__mongoId=billing_record_id)
        return [
            CptChargeUnitDTO.from_dict({**model_to_dict(unit), CptChargeUnitDTO.BILLING_RECORD: billing_record_id})
            for unit in cpt_charge_units
        ]

    def create_client(self, client: ClientDTO) -> str:
        client_dict = client.to_dict(include_none=False)
        client_dict["mongoId"] = ObjectId()
        new_client = Client(**client_dict)
        new_client.save()
        return str(new_client.mongoId)

    def update_client(self, client: ClientDTO) -> str:
        try:
            client_record = Client.objects.get(mongoId=client.id)
        except Client.DoesNotExist:
            raise ValueError(f"Client with id {client.id} does not exist.")
        client_dict = client.to_dict(include_none=False)
        client_dict.pop("mongoId")
        for key, value in client_dict.items():
            setattr(client_record, key, value)
        client_record.save()

    def retrieve_clients(self) -> list[ClientDTO]:
        clients = Client.objects.all()
        return [ClientDTO.from_dict(model_to_dict(client)) for client in clients]

    def retrieve_client(self, client_id: str) -> ClientDTO:
        try:
            client = Client.objects.get(mongoId=client_id)
            return ClientDTO.from_dict(model_to_dict(client))
        except Client.DoesNotExist:
            raise ValueError(f"Client with id {client_id} does not exist.")

    def set_user_mrn(self, user_id: str, mrn: str):
        try:
            user = User.objects.get(mongoId=user_id)
        except User.DoesNotExist:
            raise ValueError(f"User with id {user_id} does not exist.")
        if not user.componentsData or not user.componentsData.get("billing"):
            raise ValueError(f"User {user_id} does not have billing component data.")
        user.componentsData["billing"]["mrn"] = mrn
        user.save()

    def create_billing_record(self, record: BillingRecordDTO) -> str:
        record_dict = record.to_dict(include_none=False)
        record_dict["patient"] = User.objects.get(mongoId=record_dict["patientId"])
        record_dict["deployment"] = Deployment.objects.get(mongoId=record_dict["deploymentId"])
        record_dict.pop("patientId")
        record_dict.pop("deploymentId")
        record_dict["mongoId"] = ObjectId()
        billing_record = BillingRecord(**record_dict)
        billing_record.save()
        return str(billing_record.mongoId)

    def retrieve_patient_billing_record(self, user_id: str, report_end_date: date) -> BillingRecordDTO | None:
        try:
            entity = BillingRecord.objects.filter(patient_id=user_id, reportEndDate=report_end_date).first()
            record = model_to_dict(entity)
            return BillingRecordDTO.from_dict(record)
        except BillingRecord.DoesNotExist:
            return None
        except ValueError:
            # todo: handle the case in the tests where deployment, patient, or client does not exist
            return None

    def retrieve_billing_records(
        self, report_end_date: date | None = None, client_id: str | None = None
    ) -> list[BillingRecordDTO]:
        query = BillingRecord.objects.all()
        if report_end_date:
            query = query.filter(reportEndDate=report_end_date)
        if client_id:
            query = query.filter(deployment__client__mongoId=client_id)
        records = query.order_by("-reportEndDate")
        return [BillingRecordDTO.from_dict(model_to_dict(record)) for record in records]

    def update_billing_record(self, record: BillingRecordDTO) -> str:
        record_dict = record.to_dict(include_none=False)
        record_dict["patient"] = User.objects.get(mongoId=record_dict["patientId"])
        record_dict["deployment"] = Deployment.objects.get(mongoId=record_dict["deploymentId"])
        record_dict.pop("patientId")
        record_dict.pop("deploymentId")
        mongo_id = record_dict.pop("id")
        billing_record = BillingRecord.objects.get(mongoId=mongo_id)
        for key, value in record_dict.items():
            setattr(billing_record, key, value)
        billing_record.save()
        return str(billing_record.mongoId)

    def create_cpt_charge_unit(self, cpt_charge_unit: CptChargeUnitDTO) -> str:
        cpt_charge_unit_dict = cpt_charge_unit.to_dict(include_none=False)
        cpt_charge_unit_dict["billingRecord"] = BillingRecord.objects.get(
            mongoId=cpt_charge_unit_dict["billingRecordId"]
        )
        cpt_charge_unit_dict.pop("billingRecordId")
        cpt_charge_unit_dict["mongoId"] = ObjectId()
        cpt_charge_unit = CptChargeUnit(**cpt_charge_unit_dict)
        cpt_charge_unit.save()
        return str(cpt_charge_unit.mongoId)

    def add_deployment_to_client(self, client_id: str, deployment_id: str) -> str:
        try:
            client = Client.objects.get(mongoId=client_id)
        except Client.DoesNotExist:
            raise ValueError(f"Client with id {client_id} does not exist.")
        deployment = Deployment(mongoId=ObjectId(deployment_id), client=client)
        deployment.save()
        return str(deployment.mongoId)

    def update_deployment_client(self, client_id: str, deployment_id: str):
        try:
            deployment = Deployment.objects.get(mongoId=deployment_id)
        except Deployment.DoesNotExist:
            raise ValueError(f"Deployment with id {deployment_id} does not exist.")
        try:
            client = Client.objects.get(mongoId=client_id)
        except Client.DoesNotExist:
            raise ValueError(f"Client with id {client_id} does not exist.")
        deployment.client = client
        deployment.save()

    def delete_deployment_client(self, client_id, deployment_id):
        Deployment.objects.filter(mongoId=deployment_id, client__mongoId=client_id).delete()

    def get_report_end_dates_for_client(self, client_id: str) -> list[date]:
        dates = (
            BillingRecord.objects.filter(deployment__client__mongoId=client_id)
            .values_list(BillingRecord.REPORT_END_DATE, flat=True)
            .distinct()
            .order_by(f"-{BillingRecord.REPORT_END_DATE}")
        )
        return [d for d in dates if isinstance(d, date)]

    def get_distinct_invoice_dates(self) -> list[date]:
        return list(Invoice.objects.values_list("endDate", flat=True).distinct())

    def retrieve_all_clients_invoices_with_entries(self, report_end_date: date) -> list[InvoiceReport]:
        clients = self.retrieve_clients()
        result = []

        invoices = Invoice.objects.filter(endDate=report_end_date).prefetch_related("invoiceentry_set")

        client_invoice_map = {str(invoice.client.mongoId): invoice for invoice in invoices}

        invoice_entries_map = {}
        for invoice in invoices:
            entries = list(
                invoice.invoiceentry_set.all()
                .values(
                    "rates",
                    "serviceDescription",
                )
                .annotate(units=models.Sum("units"))
            )
            invoice_entries_map[invoice.id] = [InvoiceEntryDTO.from_dict(entry) for entry in entries]

        for client in clients:
            invoice = client_invoice_map.get(client.id)
            entries = invoice_entries_map.get(invoice.id, []) if invoice else []
            invoice_report = InvoiceReport(client=client, invoice=invoice, invoiceEntries=entries)
            result.append(invoice_report)

        return result

    def create_salesforce_file(self, uploaded_file_id: str, uploaded_at: datetime) -> SalesForceFileDTO:
        file = SalesForceFile.objects.create(uploadedAt=uploaded_at, fileId=uploaded_file_id)
        return SalesForceFileDTO.from_dict(
            {
                SalesForceFileDTO.FILE_ID: file.fileId,
                SalesForceFileDTO.UPLOADED_AT: file.uploadedAt,
                SalesForceFileDTO.VERSION_NUMBER: file.versionNumber,
            }
        )

    def retrieve_salesforce_file(self, version: int = None) -> SalesForceFileDTO | None:
        sales_force_file = (
            SalesForceFile.objects.get(versionNumber=version)
            if version
            else SalesForceFile.objects.latest("versionNumber")
        )
        if not sales_force_file:
            return None
        return SalesForceFileDTO.from_dict(
            {
                SalesForceFileDTO.FILE_ID: sales_force_file.fileId,
                SalesForceFileDTO.UPLOADED_AT: sales_force_file.uploadedAt,
                SalesForceFileDTO.VERSION_NUMBER: sales_force_file.versionNumber,
            }
        )
