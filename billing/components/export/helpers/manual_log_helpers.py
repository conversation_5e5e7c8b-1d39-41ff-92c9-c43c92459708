from datetime import datetime
from typing import Optional

import i18n
import pytz as pytz

from billing.components.core.dtos.billing_models import BillingMonitoringLogDTO
from billing.components.export.models.billing_models import (
    BillingManualLogExportItem,
)
from sdk.authorization.dtos.user import UserDTO

ENGLISH_LANG = "en"


class ManualLogReportFormatter:
    DIRECT_FIELDS = {
        BillingManualLogExportItem.LAST_MODIFIED_BY_ID,
        BillingManualLogExportItem.INITIAL_CREATE_DATE_TIME,
    }

    INDIRECT_IMMUTABLE_MAPPING = {
        BillingManualLogExportItem.INITIAL_CREATED_BY_ID: BillingMonitoringLogDTO.CREATED_BY_ID,
        BillingManualLogExportItem.UPDATE_DATE_TIME: BillingMonitoringLogDTO.CREATE_DATE_TIME,
        BillingManualLogExportItem.INVALIDATION_REASON: BillingMonitoringLogDTO.ADDENDUM,
        BillingManualLogExportItem.MONITORING_ID: BillingMonitoringLogDTO.ORIGINAL_LOG_ID,
        BillingManualLogExportItem.MONITORING_START_DATE_TIME: BillingMonitoringLogDTO.START_DATE_TIME,
        BillingManualLogExportItem.MONITORING_END_DATE_TIME: BillingMonitoringLogDTO.END_DATE_TIME,
    }

    INDIRECT_CALCULATED_FIELDS = {
        BillingManualLogExportItem.MONITORING_CATEGORY,
        BillingManualLogExportItem.VALIDATION,
        BillingManualLogExportItem.MONITORING_DURATION,
        BillingManualLogExportItem.INITIAL_CREATED_BY_FULL_NAME,
        BillingManualLogExportItem.UPDATED_BY_FULL_NAME,
    }

    @classmethod
    def reformat(
        cls,
        log: BillingMonitoringLogDTO,
        clinician: Optional[UserDTO],
        latest_updater: Optional[UserDTO],
        requester: UserDTO,
    ):
        requester_tz = pytz.timezone(requester.timezone or "UTC")
        data = {}
        cls._add_direct_fields(data, log)
        cls._add_immutable_indirect_fields(data, log)
        cls._add_calculated_fields(data, log, clinician, latest_updater, requester)
        data = cls._values_2_str(data, requester_tz)

        return BillingManualLogExportItem.from_dict(data).to_dict()

    @classmethod
    def _values_2_str(cls, data, tz):
        if isinstance(data, dict):
            for key in data:
                data[key] = cls._values_2_str(data[key], tz)
        elif isinstance(data, datetime):
            data = str(pytz.utc.localize(data).astimezone(tz).replace(tzinfo=None))
        elif not isinstance(data, str):
            data = str(data)
        return data

    @classmethod
    def _add_direct_fields(cls, data: dict, log: BillingMonitoringLogDTO):
        for key in cls.DIRECT_FIELDS:
            data[key] = getattr(log, key) or ""

    @classmethod
    def _add_immutable_indirect_fields(cls, data: dict, log: BillingMonitoringLogDTO):
        for key in cls.INDIRECT_IMMUTABLE_MAPPING:
            data[key] = getattr(log, cls.INDIRECT_IMMUTABLE_MAPPING[key]) or ""

    @classmethod
    def _add_calculated_fields(
        cls,
        data: dict,
        log: BillingMonitoringLogDTO,
        clinician: Optional[UserDTO],
        latest_updater: Optional[UserDTO],
        requester: UserDTO,
    ):
        log_status = log.status
        time_spent = log.timeSpent
        report_status = None
        if log_status == BillingMonitoringLogDTO.BillingMonitoringLogStatus.ACTIVE.value:
            report_status = BillingManualLogExportItem.BillingMonitoringLogStatus.ACTIVE.value
        elif log_status == BillingMonitoringLogDTO.BillingMonitoringLogStatus.EDITED.value:
            report_status = BillingManualLogExportItem.BillingMonitoringLogStatus.EDITED.value
        elif log_status == BillingMonitoringLogDTO.BillingMonitoringLogStatus.DELETED.value:
            report_status = BillingManualLogExportItem.BillingMonitoringLogStatus.DELETED.value

        data[BillingManualLogExportItem.VALIDATION] = report_status

        data[BillingManualLogExportItem.MONITORING_DURATION] = int(round((time_spent or 0) // 60, 2))
        data[BillingManualLogExportItem.INITIAL_CREATED_BY_FULL_NAME] = (
            f"{clinician.givenName} {clinician.familyName}" if clinician else ""
        )
        data[BillingManualLogExportItem.UPDATED_BY_FULL_NAME] = (
            f"{latest_updater.givenName} {latest_updater.familyName}" if latest_updater else ""
        )
        data[BillingManualLogExportItem.MONITORING_CATEGORY] = i18n.t(
            log.action.value,
            locale=requester.language or ENGLISH_LANG,
        )
