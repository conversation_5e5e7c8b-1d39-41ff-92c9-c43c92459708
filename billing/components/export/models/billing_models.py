import datetime
from enum import Enum

from billing.components.core.dtos.user_billing import (
    Diagnosis,
    InsuranceCarrier,
)
from sdk import convertibleclass
from sdk.common.utils.convertible import default_field
from sdk.common.utils.validators import default_date_meta


@convertibleclass
class BillingGeneralReportUser:
    GIVEN_NAME = "givenName"
    FAMILY_NAME = "familyName"
    GENDER = "gender"
    BIOLOGICAL_SEX = "biologicalSex"
    DATE_OF_BIRTH = "dateOfBirth"
    EMAIL = "email"
    PHONE_NUMBER = "phoneNumber"
    PRIMARY_ADDRESS = "primaryAddress"
    TIMEZONE = "timezone"
    LANGUAGE = "language"

    givenName: str = default_field()
    familyName: str = default_field()
    gender: str = default_field()
    biologicalSex: str = default_field()
    dateOfBirth: str = default_field()
    email: str = default_field()
    phoneNumber: str = default_field()
    primaryAddress: str = default_field()
    timezone: str = default_field()
    language: str = default_field()


@convertibleclass
class BillingMonthlyReportUser:
    GIVEN_NAME = "givenName"
    FAMILY_NAME = "familyName"
    DATE_OF_BIRTH = "dateOfBirth"

    givenName: str = default_field()
    familyName: str = default_field()
    dateOfBirth: str = default_field()


@convertibleclass
class BillingGeneralReportCPTCodes:
    NUMBER_OF_SUBMISSIONS = "numberOfSubmissions"
    NUMBER_OF_SUBMISSIONS_DAYS = "numberOfSubmissionDays"
    CPT_99453 = "cpt99453"
    CPT_99454 = "cpt99454"
    CPT_99457 = "cpt99457"
    CPT_99458 = "cpt99458"
    CPT_98975 = "cpt98975"
    CPT_98976 = "cpt98976"
    CPT_98980 = "cpt98980"
    CPT_98981 = "cpt98981"
    MONITORING_TIME_MINS = "monitoringTimeMins"
    NUMBER_OF_CALLS = "numberOfCalls"

    numberOfSubmissions: int = default_field()
    numberOfSubmissionDays: int = default_field()
    cpt99453: int = default_field()
    cpt99454: int = default_field()
    cpt99457: int = default_field()
    cpt99458: int = default_field()
    cpt98975: int = default_field()
    cpt98976: int = default_field()
    cpt98980: int = default_field()
    cpt98981: int = default_field()
    monitoringTimeMins: float = default_field()
    numberOfCalls: int = default_field()


@convertibleclass
class MonthlyReportSubmissionCptCodeMapping2:
    BILLING_START_DATE_0 = "billingStartDate_0"
    BILLING_END_DATE_0 = "billingEndDate_0"
    SUBMISSIONS_0 = "countOfSubmissions_0"
    SUBMISSIONS_DAY_0 = "countOfSubmissionDays_0"
    EARLIEST_BILLING_DATE_0 = "earliestBillingDate_0"

    BILLING_START_DATE_1 = "billingStartDate_1"
    BILLING_END_DATE_1 = "billingEndDate_1"
    SUBMISSIONS_1 = "countOfSubmissions_1"
    SUBMISSIONS_DAY_1 = "countOfSubmissionDays_1"
    EARLIEST_BILLING_DATE_1 = "earliestBillingDate_1"
    TOTAL_BILLABLE = "totalBillable"

    billingStartDate_0: str = default_field()
    billingEndDate_0: str = default_field()
    countOfSubmissions_0: int = default_field()
    countOfSubmissionDays_0: int = default_field()
    earliestBillingDate_0: str = default_field()

    billingStartDate_1: str = default_field()
    billingEndDate_1: str = default_field()
    countOfSubmissions_1: int = default_field()
    countOfSubmissionDays_1: int = default_field()
    earliestBillingDate_1: str = default_field()


@convertibleclass
class MonthlyReportSubmissionCptCodeMapping1:
    BILLING_START_DATE = "billingStartDate"
    BILLING_END_DATE = "billingEndDate"
    SUBMISSIONS = "countOfSubmissions"
    SUBMISSIONS_DAY = "countOfSubmissionDays"
    EARLIEST_BILLING_DATE = "earliestBillingDate"
    TOTAL_BILLABLE = "totalBillable"

    billingStartDate: str = default_field()
    billingEndDate: str = default_field()
    countOfSubmissions: int = default_field()
    countOfSubmissionDays: int = default_field()
    earliestBillingDate: str = default_field()


@convertibleclass
class CPTCodeDetails:
    BILLING_START_DATE = "billingStartDate"
    BILLING_END_DATE = "billingEndDate"

    billingStartDate: datetime.date = default_field(metadata=default_date_meta())
    billingEndDate: datetime.date = default_field(metadata=default_date_meta())


@convertibleclass
class TimeTrackingCPTCodeDetails(CPTCodeDetails):
    TOTAL_BILLABLE = "totalBillable"

    totalBillable: int = default_field()


@convertibleclass
class BillingMonthlyReportCPTCodes:
    CPT_99453 = "cpt99453"
    CPT_99454 = "cpt99454"
    CPT_98975 = "cpt98975"
    CPT_98976 = "cpt98976"
    CPT_99457 = "cpt99457"
    CPT_99458 = "cpt99458"
    CPT_98980 = "cpt98980"
    CPT_98981 = "cpt98981"
    MONITORING_TIME_MINS = "monitoringTimeMins"
    NUMBER_OF_CALLS = "numberOfCalls"

    cpt99453: dict = default_field()
    cpt99454: dict = default_field()
    cpt98975: dict = default_field()
    cpt98976: dict = default_field()
    cpt99457: TimeTrackingCPTCodeDetails = default_field()
    cpt99458: TimeTrackingCPTCodeDetails = default_field()
    cpt98980: TimeTrackingCPTCodeDetails = default_field()
    cpt98981: TimeTrackingCPTCodeDetails = default_field()
    monitoringTimeMins: int = default_field()
    numberOfCalls: int = default_field()


@convertibleclass
class BillingGeneralReportUserBilling:
    PRIMARY_INSURANCE_CARRIER = "primaryInsuranceCarrier"
    SECONDARY_INSURANCE_CARRIER = "secondaryInsuranceCarrier"
    PRIMARY_INSURANCE_CARRIER_PAYER_ID = "primaryInsuranceCarrierPayerId"
    SECONDARY_INSURANCE_CARRIER_PAYER_ID = "secondaryInsuranceCarrierPayerId"
    BILLING_PROVIDER_NAME = "billingProviderName"
    DIAGNOSIS = "diagnosis"
    BILLING_FIRST_READING = "billingFirstReading"

    primaryInsuranceCarrier: str = default_field()
    secondaryInsuranceCarrier: str = default_field()
    primaryInsuranceCarrierPayerId: str = default_field()
    secondaryInsuranceCarrierPayerId: str = default_field()
    billingProviderName: str = default_field()
    diagnosis: Diagnosis = default_field()
    billingFirstReading: str = default_field()


@convertibleclass
class UserBaseBilling:
    INSURANCE_CARRIERS = "insuranceCarriers"
    BILLING_PROVIDER_NAME = "billingProviderName"
    DIAGNOSIS = "diagnosis"

    insuranceCarriers: list[InsuranceCarrier] = default_field()
    billingProviderName: str = default_field()
    diagnosis: Diagnosis = default_field()


@convertibleclass
class BillingProvider:
    NAME = "billingProviderName"
    ID = "billingProviderId"
    ABBREVIATION = "billingProviderAbbreviation"

    billingProviderName: str = default_field()
    billingProviderId: str = default_field()
    billingProviderAbbreviation: str = default_field()


@convertibleclass
class EHRBillingProvider(BillingProvider):
    LOCATION_DEPARTMENT = "billingProviderLocationDepartment"
    LOCATION_ABBREVIATION = "billingProviderLocationAbbreviation"
    PLACE_OF_SERVICE_ABBREVIATION = "billingProviderPlaceOfServiceAbbreviation"

    billingProviderLocationDepartment: str = default_field()
    billingProviderLocationAbbreviation: str = default_field()
    billingProviderPlaceOfServiceAbbreviation: str = default_field()


@convertibleclass
class BillingMonthlyReportUserBilling:
    CPT_99453_4 = "cpt99453_4"
    CPT_99457_8 = "cpt99457_8"
    CPT_98975_6 = "cpt98975_6"
    CPT_98980_1 = "cpt98980_1"

    ICD_CODE = "icd10Code"
    DESCRIPTION = "description"

    BILLING_FIRST_READING = "billingFirstReading"
    PRIMARY_INSURANCE_CARRIER = "primaryInsuranceCarrier"
    SECONDARY_INSURANCE_CARRIER = "secondaryInsuranceCarrier"
    PRIMARY_INSURANCE_CARRIER_PAYER_ID = "primaryInsuranceCarrierPayerId"
    SECONDARY_INSURANCE_CARRIER_PAYER_ID = "secondaryInsuranceCarrierPayerId"

    cpt99453_4: BillingProvider = default_field()
    cpt99457_8: BillingProvider = default_field()
    cpt98975_6: BillingProvider = default_field()
    cpt98980_1: BillingProvider = default_field()

    icd10Code: str = default_field()
    description: str = default_field()
    billingFirstReading: str = default_field()

    primaryInsuranceCarrier: str = default_field()
    secondaryInsuranceCarrier: str = default_field()
    primaryInsuranceCarrierPayerId: str = default_field()
    secondaryInsuranceCarrierPayerId: str = default_field()


@convertibleclass
class ExportBillingLogUser:
    GIVEN_NAME = "givenName"
    FAMILY_NAME = "familyName"
    DATE_OF_BIRTH = "dateOfBirth"

    givenName: str = default_field()
    familyName: str = default_field()
    dateOfBirth: str = default_field()


@convertibleclass
class ExportBillingLogClinician:
    ID = "id"
    GIVEN_NAME = "givenName"
    FAMILY_NAME = "familyName"

    id: str = default_field()
    givenName: str = default_field()
    familyName: str = default_field()


class BillingManualLogReportBaseFields:
    USER_ID = "userId"
    MODULE_ID = "moduleId"
    DEPLOYMENT_ID = "deploymentID"
    REPORT_START_DATE = "reportStartDate"
    REPORT_END_DATE = "reportEndDate"


@convertibleclass
class BillingManualLogExportItem:
    class BillingMonitoringLogStatus(Enum):
        ACTIVE = "valid"
        EDITED = "invalid due to editing"
        DELETED = "invalid due to deletion"

    INITIAL_CREATE_DATE_TIME = "initialCreateDateTime"
    UPDATE_DATE_TIME = "updateDateTime"
    MONITORING_ID = "monitoringId"
    VALIDATION = "validation"
    INVALIDATION_REASON = "invalidationReason"
    MONITORING_START_DATE_TIME = "monitoringStartDateTime"
    MONITORING_END_DATE_TIME = "monitoringEndDateTime"
    MONITORING_DURATION = "monitoringDuration"
    MONITORING_CATEGORY = "monitoringCategory"
    INITIAL_CREATED_BY_ID = "initialCreatedById"
    INITIAL_CREATED_BY_FULL_NAME = "initialCreatedByFullname"
    LAST_MODIFIED_BY_ID = "lastModifiedById"
    UPDATED_BY_FULL_NAME = "updatedByFullName"

    initialCreateDateTime: str = default_field()
    updateDateTime: str = default_field()
    monitoringId: str = default_field()
    validation: str = default_field()
    invalidationReason: str = default_field()
    monitoringStartDateTime: str = default_field()
    monitoringEndDateTime: str = default_field()
    monitoringDuration: str = default_field()
    monitoringCategory: str = default_field()
    initialCreatedById: str = default_field()
    initialCreatedByFullname: str = default_field()
    lastModifiedById: str = default_field()
    updatedByFullName: str = default_field()


@convertibleclass
class ExportBillingAutomatedLog:
    REPORT_START_DATE = "reportStartDate"
    REPORT_END_DATE = "reportEndDate"
    USER_ID = "userId"
    USER = "user"
    MONITORING_ID = "monitoringId"
    START_DATE_TIME = "startDateTime"
    END_DATE_TIME = "endDateTime"
    DURATION = "duration"
    CLINICIAN = "clinician"
    MODULE_ID = "moduleId"

    reportStartDate: str = default_field()
    reportEndDate: str = default_field()
    userId: str = default_field()
    user: ExportBillingLogUser = default_field()
    monitoringId: str = default_field()
    startDateTime: str = default_field()
    endDateTime: str = default_field()
    duration: str = default_field()
    clinician: ExportBillingLogClinician = default_field()
    moduleId: str = default_field()
