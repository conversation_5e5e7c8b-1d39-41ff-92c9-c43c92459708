import calendar
import csv
import datetime
import io
from copy import copy
from datetime import timedelta
from functools import cached_property
from math import floor
from typing import Optional

from dateutil.relativedelta import relativedelta

from billing.components.core.dtos.deployment_billing import (
    ProductType,
    BILLING_FEATURES_KEY,
    DeploymentBillingFileType,
    BillingProviderFile,
)
from billing.components.core.dtos.user_billing import Diagnosis
from billing.components.core.helpers.billing_profile_helpers import (
    get_user_billing_provider_before_end_date,
    get_diagnosis,
)
from billing.components.core.helpers.export_billing_submission_helpers import (
    get_billable_period_count_for_cpt_3,
    get_billable_period_count_for_cpt_4_1x,
    get_billable_period_count_for_cpt_4_2x,
    get_user_first_submission,
    generate_periodical_result_for_submission_cpt_codes,
    get_calendar_submission_period,
    get_start_date_of_first_calendar_billable_or_current_period,
    get_start_date_of_first_billable_or_current_period,
)
from billing.components.core.helpers.export_billing_time_tracking_helpers import (
    get_time_spent,
    get_number_of_interactive_sessions,
    remove_user_billing_data_overlapping,
)
from billing.components.core.validators import FileDownload
from billing.components.export.models.billing_models import (
    BillingMonthlyReportUser,
    BillingMonthlyReportUserBilling,
    MonthlyReportSubmissionCptCodeMapping2,
    MonthlyReportSubmissionCptCodeMapping1,
    BillingMonthlyReportCPTCodes,
    BillingProvider,
    BillingGeneralReportCPTCodes,
    UserBaseBilling,
    TimeTrackingCPTCodeDetails,
    EHRBillingProvider,
)
from billing.components.export.use_case.base_billing_exportable_use_case import (
    BaseBillingExportableUseCase,
)
from huma_plugins.components.export.helpers.convertion_helpers import (
    ExportData,
    convert_id_fields_to_string,
)
from huma_plugins.components.export.use_case.exportable.exportable_request_objects import (
    ExportableRequestObject,
)
from sdk.authorization.dtos.user import UserDTO
from sdk.common.exceptions.exceptions import (
    BucketFileDoesNotExist,
    FileNotFoundException,
)
from sdk.common.utils.common_functions_utils import deep_get
from sdk.common.utils.validators import (
    utc_date_to_str,
    utc_str_to_date,
)
from sdk.module_result.dtos.primitives import PrimitiveDTO

DateRange = tuple[datetime.date, datetime.date]

CPT_MAPPING = {
    1: {
        ProductType.RPM: BillingMonthlyReportCPTCodes.CPT_99453,
        ProductType.RTM: BillingMonthlyReportCPTCodes.CPT_98975,
    },
    2: {
        ProductType.RPM: BillingMonthlyReportCPTCodes.CPT_99454,
        ProductType.RTM: BillingMonthlyReportCPTCodes.CPT_98976,
    },
    3: {
        ProductType.RPM: BillingMonthlyReportCPTCodes.CPT_99457,
        ProductType.RTM: BillingMonthlyReportCPTCodes.CPT_98980,
    },
    4: {
        ProductType.RPM: BillingMonthlyReportCPTCodes.CPT_99458,
        ProductType.RTM: BillingMonthlyReportCPTCodes.CPT_98981,
    },
}

CPT_2_MODEL_MAPPER_CLASSIC = {
    1: MonthlyReportSubmissionCptCodeMapping1,
    2: MonthlyReportSubmissionCptCodeMapping2,
    3: TimeTrackingCPTCodeDetails,
    4: TimeTrackingCPTCodeDetails,
}

CPT_2_MODEL_MAPPER_CALENDAR = copy(CPT_2_MODEL_MAPPER_CLASSIC)
CPT_2_MODEL_MAPPER_CALENDAR[2] = CPT_2_MODEL_MAPPER_CALENDAR[1]


class BillingMonthlyExportableUseCase(BaseBillingExportableUseCase):
    class BillingMonthlyReportSections:
        REPORT_START_DATE = "reportStartDate"
        REPORT_END_DATE = "reportEndDate"
        USER_PROFILE_DATA = "user"
        USER_BILLING_DATA = "billing"
        CPT_CODES_DETAILS = "cptCodesDetails"

    MODULE_NAME = "MonthlyBilling"

    @cached_property
    def billing_providers(self) -> dict[str, BillingProvider]:
        providers = {}
        file_id = self._get_billing_provider_file_id()
        if not file_id:
            return {}

        rows = self._read_csv_file(file_id)
        if not rows:
            return providers

        for row in rows:
            provider_name = row.get(BillingProviderFile.NAME.value)
            providers[provider_name] = self._build_provider_data(row)
        return providers

    def _build_provider_data(self, row: dict) -> BillingProvider:
        return BillingProvider.from_dict(
            {
                BillingProvider.NAME: row.get(BillingProviderFile.NAME.value),
                BillingProvider.ID: row.get(BillingProviderFile.ID.value),
                BillingProvider.ABBREVIATION: row.get(BillingProviderFile.ABBREVIATION.value),
            }
        )

    def _get_cpt_code_by_product_type(self, mapping_number: int, product_type=None) -> int:
        return CPT_MAPPING[mapping_number][product_type or self.product_type]

    def get_raw_result(self) -> ExportData:
        if not (self._module_exists() and self._billing_enabled()):
            return {}

        from_date, to_date = self._extract_dates()
        users = self._fetch_users_data()

        result = {
            f"{self.MODULE_NAME}_{from_date}_{to_date}": self.get_formatted_monthly_billing_data(
                users, from_date, to_date
            )
        }
        return result

    def get_formatted_monthly_billing_data(self, users, from_date, to_date):
        return self._get_monthly_billing_data(users, from_date, to_date)

    def _add_user_profile_data(self, user_data: dict, user: UserDTO) -> None:
        user_data[self.BillingMonthlyReportSections.USER_PROFILE_DATA] = BillingMonthlyReportUser.from_dict(
            user.to_dict()
        ).to_dict()

    def _add_base_details(self, user_data: dict, start_date: datetime.date, end_date: datetime.date) -> None:
        user_data[self.BillingMonthlyReportSections.REPORT_START_DATE] = str(start_date)
        user_data[self.BillingMonthlyReportSections.REPORT_END_DATE] = str(end_date)

    def _add_user_cpt_codes_data(
        self,
        user_data: dict,
        user: UserDTO,
        start_date: datetime.date,
        end_date: datetime.date,
        first_bp_start_date: Optional[datetime.date],
    ) -> None:
        user_cpt_codes_details = {}
        self._add_submission_cpt_codes_details(
            user_id=user.id,
            first_bp_start_date=first_bp_start_date,
            from_date=start_date,
            to_date=end_date,
            cpt_details=user_cpt_codes_details,
        )

        self._add_time_tracking_cpt_codes_details(
            user_id=user.id,
            from_date=start_date,
            to_date=end_date,
            user_cpt_codes_details=user_cpt_codes_details,
        )

        user_data[self.BillingMonthlyReportSections.CPT_CODES_DETAILS] = user_cpt_codes_details

    def _add_user_billing_data(
        self,
        user_data: dict,
        deployment_id: str,
        user: UserDTO,
        initial_submission_dt: Optional[datetime],
    ) -> None:
        billing_data = {
            BillingMonthlyReportUserBilling.BILLING_FIRST_READING: (
                str(initial_submission_dt.date()) if initial_submission_dt else ""
            ),
            **self._extract_latest_carriers_data(user),
        }

        if diagnosis := get_diagnosis(user_id=user.id, deployment_id=deployment_id):
            billing_data[Diagnosis.ICD_CODE] = diagnosis.icd10Code
            billing_data[Diagnosis.DESCRIPTION] = diagnosis.description

        user_data[self.BillingMonthlyReportSections.USER_BILLING_DATA] = BillingMonthlyReportUserBilling.from_dict(
            billing_data
        ).to_dict()

    @staticmethod
    def _extract_latest_carriers_data(user: UserDTO) -> dict:
        billing_data = user.componentsData.get(BILLING_FEATURES_KEY, {}) if user.componentsData else {}
        carriers_data = {
            BillingMonthlyReportUserBilling.PRIMARY_INSURANCE_CARRIER: "",
            BillingMonthlyReportUserBilling.PRIMARY_INSURANCE_CARRIER_PAYER_ID: "",
            BillingMonthlyReportUserBilling.SECONDARY_INSURANCE_CARRIER: "",
            BillingMonthlyReportUserBilling.SECONDARY_INSURANCE_CARRIER_PAYER_ID: "",
        }

        billing_obj = UserBaseBilling.from_dict(billing_data)

        primary_carrier_idx, secondary_carrier_idx = 0, 1

        if carriers := billing_obj.insuranceCarriers:
            if first_carrier := deep_get(carriers, str(primary_carrier_idx)):
                carriers_data[BillingMonthlyReportUserBilling.PRIMARY_INSURANCE_CARRIER] = first_carrier.groupName
                carriers_data[BillingMonthlyReportUserBilling.PRIMARY_INSURANCE_CARRIER_PAYER_ID] = (
                    first_carrier.payerId
                )

            if second_carrier := deep_get(carriers, str(secondary_carrier_idx)):
                carriers_data[BillingMonthlyReportUserBilling.SECONDARY_INSURANCE_CARRIER] = second_carrier.groupName
                carriers_data[BillingMonthlyReportUserBilling.SECONDARY_INSURANCE_CARRIER_PAYER_ID] = (
                    second_carrier.payerId
                )

        return carriers_data

    def _get_initial_submission_billable_period_details(
        self, user: UserDTO, to_date: datetime.date
    ) -> tuple[datetime.datetime | None, datetime.date | None]:
        submission = get_user_first_submission(user_id=user.id)
        submission_dt = submission.startDateTime if submission else None
        if not submission_dt:
            return None, None

        if self.billing_config.useCalendarCalculation:
            first_bp_start_date = get_start_date_of_first_calendar_billable_or_current_period(
                user_id=user.id,
                check_upper_bound=to_date,
            )
        else:
            first_bp_start_date = get_start_date_of_first_billable_or_current_period(
                user_id=user.id, check_upper_bound=to_date, return_current_period=False
            )
        return submission_dt, first_bp_start_date

    def _get_monthly_billing_data(
        self,
        users: list[UserDTO],
        from_date: datetime.date,
        to_date: datetime.date,
    ) -> list:
        total_results = []
        deployment_id = self.request_object.deploymentId
        month_ranges = self.get_month_ranges(from_date, to_date)
        user_ids = {user.id for user in users}
        move_logs = self._get_users_move_logs(list(user_ids))
        for user in users:
            user_results = []
            if to_date >= datetime.datetime.utcnow().date():
                remove_user_billing_data_overlapping(user_id=user.id)
            (
                initial_submission_date,
                first_bp_start_date,
            ) = self._get_initial_submission_billable_period_details(user, to_date)
            if not initial_submission_date:
                continue

            user_move_logs = move_logs.get(user.id, [])
            month_deployment_map = self._get_deployment_ids_for_time_ranges(user_move_logs, deployment_id, month_ranges)

            for start_date, end_date in month_ranges:
                cur_deployment_id = month_deployment_map.get((start_date, end_date))
                user_data = {}
                self._add_base_details(user_data, start_date, end_date)
                self._add_user_profile_data(user_data, user)
                self._add_user_billing_data(user_data, cur_deployment_id, user, initial_submission_date)
                self._add_user_cpt_codes_data(
                    user_data,
                    user,
                    start_date,
                    end_date,
                    first_bp_start_date,
                )
                self._prune_unneeded_cpt_codes(user_data)
                if not self._user_result_has_cpt_data(user_data):
                    continue
                self._fix_cpt_codes_detail_structure(user_data)
                user_results.append(
                    convert_id_fields_to_string(
                        {
                            PrimitiveDTO.USER_ID: user.id,
                            PrimitiveDTO.DEPLOYMENT_ID: cur_deployment_id,
                            PrimitiveDTO.MODULE_ID: BILLING_FEATURES_KEY,
                            **user_data,
                        }
                    )
                )
            user_results = self._add_billing_provider_details_to_result(
                result=user_results,
                user_id=user.id,
                end_date=to_date,
                initial_submission_start_datetime=initial_submission_date,
            )
            total_results.extend(user_results)
        self._reformat_fields(total_results)
        return total_results

    def _prune_unneeded_cpt_codes(self, result: dict) -> None:
        cpt_codes_details = result.get(self.BillingMonthlyReportSections.CPT_CODES_DETAILS, {})

        for p_type in ProductType:
            if p_type is self.product_type:
                continue
            for cpt_code_num in range(1, 5):
                cpt = self._get_cpt_code_by_product_type(cpt_code_num, p_type)
                if cpt_codes_details.get(cpt):
                    cpt_codes_details.pop(cpt)

    def _fix_cpt_codes_detail_structure(self, result: dict) -> None:
        is_calendar_type = self.billing_config.useCalendarCalculation
        cpt_codes_details = result.get(self.BillingMonthlyReportSections.CPT_CODES_DETAILS, {})

        for cpt_code_num in range(1, 5):
            cpt_code = self._get_cpt_code_by_product_type(cpt_code_num)
            pre_cpt_data = cpt_codes_details[cpt_code] or {}
            model_mapper = CPT_2_MODEL_MAPPER_CALENDAR if is_calendar_type else CPT_2_MODEL_MAPPER_CLASSIC
            cpt_codes_details[cpt_code] = {
                **model_mapper[cpt_code_num].from_dict({}).to_dict(),
                **pre_cpt_data,
            }

    def _user_result_has_cpt_data(self, result: dict) -> bool:
        cpt_codes_details = result.get(self.BillingMonthlyReportSections.CPT_CODES_DETAILS, {})
        has_submission_codes = cpt_codes_details.get(self._get_cpt_code_by_product_type(1)) or cpt_codes_details.get(
            self._get_cpt_code_by_product_type(2)
        )

        has_time_tracking_codes = self._has_user_time_tracking_data(cpt_codes_details)

        return bool(has_time_tracking_codes or has_submission_codes)

    def _add_billing_provider_details_to_result(
        self,
        result: list,
        user_id: str,
        end_date: datetime.date,
        initial_submission_start_datetime: Optional[datetime],
    ) -> list:
        billing_providers_logs = get_user_billing_provider_before_end_date(user_id=user_id, to_date=end_date)
        for billing_cycle in result:
            report_cycle_end_date = utc_str_to_date(billing_cycle[self.BillingMonthlyReportSections.REPORT_END_DATE])
            submissions_provider = self._get_submission_codes_billing_provider(
                billing_providers_logs=billing_providers_logs,
                end_date=report_cycle_end_date,
                initial_submission_start_datetime=initial_submission_start_datetime,
                billing_cycle=billing_cycle,
            )
            time_tracking_provider = self._get_time_tracking_codes_billing_providers(
                billing_providers_logs=billing_providers_logs,
                end_date=report_cycle_end_date,
                billing_cycle=billing_cycle,
            )
            user_billing_data = billing_cycle[self.BillingMonthlyReportSections.USER_BILLING_DATA]
            self._add_billing_provider_details_to_cpt_codes(
                user_billing_data,
                submissions_provider,
                time_tracking_provider,
            )

        return result

    def _add_billing_provider_details_to_cpt_codes(
        self,
        user_billing_data: dict,
        submissions_provider: BillingProvider | None,
        time_tracking_provider: BillingProvider | None,
    ) -> None:
        (
            submission_cpt,
            time_tracking_cpt,
            unneeded_cpt_codes,
        ) = self._divide_codes_to_keep_or_remove()
        user_billing_data[submission_cpt] = submissions_provider.to_dict()
        user_billing_data[time_tracking_cpt] = time_tracking_provider.to_dict()
        for cpt in unneeded_cpt_codes:
            user_billing_data.pop(cpt, None)

    def _divide_codes_to_keep_or_remove(self):
        if self.product_type is ProductType.RPM:
            return (
                BillingMonthlyReportUserBilling.CPT_99453_4,
                BillingMonthlyReportUserBilling.CPT_99457_8,
                [
                    BillingMonthlyReportUserBilling.CPT_98975_6,
                    BillingMonthlyReportUserBilling.CPT_98980_1,
                ],
            )
        return (
            BillingMonthlyReportUserBilling.CPT_98975_6,
            BillingMonthlyReportUserBilling.CPT_98980_1,
            [
                BillingMonthlyReportUserBilling.CPT_99453_4,
                BillingMonthlyReportUserBilling.CPT_99457_8,
            ],
        )

    def _get_submission_codes_billing_provider(
        self,
        billing_providers_logs: list,
        end_date: datetime.date,
        initial_submission_start_datetime: datetime,
        billing_cycle: dict,
    ) -> BillingProvider | None:
        cpt_details = billing_cycle.get(self.BillingMonthlyReportSections.CPT_CODES_DETAILS)
        if not any(
            [
                cpt_details.get(BillingGeneralReportCPTCodes.CPT_99453),
                cpt_details.get(BillingGeneralReportCPTCodes.CPT_99454),
                cpt_details.get(BillingGeneralReportCPTCodes.CPT_98975),
                cpt_details.get(BillingGeneralReportCPTCodes.CPT_98976),
            ]
        ):
            return None

        if self.billing_config.useCalendarCalculation:
            start_date = utc_str_to_date(billing_cycle[self.BillingMonthlyReportSections.REPORT_START_DATE])
            (
                provider_id,
                provider_name,
            ) = self.get_billing_provider_for_calendar_submission_codes(
                billing_providers_logs,
                start_date,
            )
        else:
            (
                provider_id,
                provider_name,
            ) = self.get_billing_provider_for_classic_submission_codes(
                billing_providers_logs,
                end_date,
                initial_submission_start_datetime,
            )
        if provider := self.billing_providers.get(provider_name):
            return provider

        return BillingProvider.from_dict(
            {
                BillingProvider.NAME: provider_name,
                BillingProvider.ID: provider_id,
            }
        )

    def get_billing_provider_for_calendar_submission_codes(
        self,
        billing_providers_logs: list,
        start_date: datetime.date,
    ) -> tuple[None, None] | tuple[str, str]:
        _, nearest_dos = get_calendar_submission_period(start_date)
        return self.get_closest_billing_provider_details_to_dos(
            billing_providers_logs=billing_providers_logs,
            to_date=nearest_dos,
        )

    def get_billing_provider_for_classic_submission_codes(
        self,
        billing_providers_logs: list,
        end_date: datetime.date,
        initial_submission_start_datetime: datetime,
    ) -> tuple[None, None] | tuple[str, str]:
        all_submissions_date_of_services = self.calculate_all_dos_from_date_till_now(
            initial_submission_date=initial_submission_start_datetime,
            to_date=end_date,
        )
        closest_submission_dos_before_end_of_billing_cycle = (
            self._get_closest_submission_dos_before_end_of_billing_cycle(
                all_submissions_date_of_services=all_submissions_date_of_services,
                to_date=end_date,
            )
        )
        return self.get_closest_billing_provider_details_to_dos(
            billing_providers_logs=billing_providers_logs,
            to_date=closest_submission_dos_before_end_of_billing_cycle,
        )

    def _get_time_tracking_codes_billing_providers(
        self, billing_providers_logs: list, end_date: datetime.date, billing_cycle: dict
    ) -> BillingProvider | None:
        cpt_details = billing_cycle.get(self.BillingMonthlyReportSections.CPT_CODES_DETAILS)
        if not any(
            [
                cpt_details.get(BillingGeneralReportCPTCodes.CPT_99457),
                cpt_details.get(BillingGeneralReportCPTCodes.CPT_99458),
                cpt_details.get(BillingGeneralReportCPTCodes.CPT_98980),
                cpt_details.get(BillingGeneralReportCPTCodes.CPT_98981),
            ]
        ):
            return None

        provider_id, provider_name = self.get_closest_billing_provider_details_to_dos(
            billing_providers_logs=billing_providers_logs,
            to_date=end_date,
        )
        if provider := self.billing_providers.get(provider_name):
            return provider

        return BillingProvider.from_dict(
            {
                BillingProvider.NAME: provider_name,
                BillingProvider.ID: provider_id,
            }
        )

    @staticmethod
    def get_closest_billing_provider_details_to_dos(
        billing_providers_logs: list,
        to_date: datetime.date | None,
    ) -> tuple[str, str] | tuple[None, None]:
        if to_date is None:
            return None, None

        for log in billing_providers_logs:
            if log.createDateTime.date() <= to_date:
                return (
                    log.billingProviderId,
                    log.billingProviderName,
                )
        return None, None

    @staticmethod
    def _get_closest_submission_dos_before_end_of_billing_cycle(
        all_submissions_date_of_services: list, to_date: datetime.date
    ) -> Optional[datetime.date]:
        if all_submissions_date_of_services and to_date:
            billing_cycle_end_date = utc_str_to_date(to_date)
            for submission_date_of_service in reversed(all_submissions_date_of_services):
                if submission_date_of_service <= billing_cycle_end_date:
                    return submission_date_of_service
        return None

    @staticmethod
    def get_month_ranges(from_date, to_date):
        month_ranges = []
        if to_date < from_date:
            return month_ranges

        first_day_checker = from_date.replace(day=1)
        while first_day_checker <= to_date:
            last_day_checker = min(
                (first_day_checker + relativedelta(months=1) - timedelta(days=1)),
                to_date,
            )
            month_ranges.append((first_day_checker, last_day_checker))
            first_day_checker += relativedelta(months=1)

        month_ranges[0] = (from_date, month_ranges[0][1])

        return month_ranges

    @staticmethod
    def calculate_all_dos_from_date_till_now(initial_submission_date: datetime, to_date: datetime.date):
        first_dos = initial_submission_date.date() + timedelta(days=29)
        all_dos = []
        check_dos = first_dos
        while check_dos < to_date:
            all_dos.append(check_dos)
            check_dos += timedelta(days=30)
        return all_dos

    def get_cpt_code_99453_export_data(
        self,
        user_id: str,
        first_bp_start_date: datetime.date | None,
        from_date: datetime.date,
        to_date: datetime.date,
    ):
        if not first_bp_start_date:
            return {}

        if self.billing_config.useCalendarCalculation:
            start_date, dos = get_calendar_submission_period(first_bp_start_date)
        else:
            start_date = first_bp_start_date
            dos = first_bp_start_date + timedelta(days=29)

        if not (from_date <= dos <= to_date):
            return {}

        result = generate_periodical_result_for_submission_cpt_codes(
            user_id,
            start_date,
            dos,
        )

        response = dict()
        if result[0]:
            response = {
                MonthlyReportSubmissionCptCodeMapping1.BILLING_START_DATE: result[0],
                MonthlyReportSubmissionCptCodeMapping1.BILLING_END_DATE: result[1],
                MonthlyReportSubmissionCptCodeMapping1.SUBMISSIONS: result[2],
                MonthlyReportSubmissionCptCodeMapping1.SUBMISSIONS_DAY: result[3],
                MonthlyReportSubmissionCptCodeMapping1.EARLIEST_BILLING_DATE: result[4],
            }

        return response

    def get_cpt_code_99454_export_data(
        self,
        user_id: str,
        first_bp_start_date: Optional[datetime.date],
        from_date: datetime.date,
        to_date: datetime.date,
    ):
        args = (user_id, first_bp_start_date, from_date, to_date)
        if self.billing_config.useCalendarCalculation:
            return self.get_cpt_code_99454_export_data_calendar_calculation(*args)
        return self.get_cpt_code_99454_export_data_classic(*args)

    def get_cpt_code_99454_export_data_calendar_calculation(
        self,
        user_id: str,
        first_bp_start_date: Optional[datetime.date],
        from_date: datetime.date,
        to_date: datetime.date,
    ):
        if not first_bp_start_date or first_bp_start_date > to_date:
            return {}

        start, dos = get_calendar_submission_period(from_date)

        if not first_bp_start_date < dos <= to_date:
            return {}

        result = generate_periodical_result_for_submission_cpt_codes(user_id, start, dos)
        response = dict()
        if result[2]:
            response = {
                MonthlyReportSubmissionCptCodeMapping1.BILLING_START_DATE: result[0],
                MonthlyReportSubmissionCptCodeMapping1.BILLING_END_DATE: result[1],
                MonthlyReportSubmissionCptCodeMapping1.SUBMISSIONS: result[2],
                MonthlyReportSubmissionCptCodeMapping1.SUBMISSIONS_DAY: result[3],
                MonthlyReportSubmissionCptCodeMapping1.EARLIEST_BILLING_DATE: result[4],
            }
        return response

    def get_cpt_code_99454_export_data_classic(
        self,
        user_id: str,
        first_bp_start_date: Optional[datetime.date],
        from_date: datetime.date,
        to_date: datetime.date,
    ):
        if not first_bp_start_date:
            return {}

        total_passed_periods = max((from_date - first_bp_start_date).days // 30, 0)

        previous_period_start_date = first_bp_start_date + timedelta(30 * total_passed_periods)
        previous_period_dos = previous_period_start_date + timedelta(days=29)

        primary_report_data = (None, None, None, None, None)
        secondary_report_data = (None, None, None, None, None)

        if from_date <= previous_period_dos <= to_date:
            primary_report_data = generate_periodical_result_for_submission_cpt_codes(
                user_id, previous_period_start_date, previous_period_dos
            )

        secondary_report_start_date = previous_period_start_date + timedelta(days=30)
        secondary_report_dos = secondary_report_start_date + timedelta(days=29)

        if from_date <= secondary_report_dos <= to_date:
            secondary_report_data = generate_periodical_result_for_submission_cpt_codes(
                user_id,
                secondary_report_start_date,
                secondary_report_dos,
            )

        response = dict()

        if primary_report_data[2]:
            response = {
                MonthlyReportSubmissionCptCodeMapping2.BILLING_START_DATE_0: primary_report_data[0],
                MonthlyReportSubmissionCptCodeMapping2.BILLING_END_DATE_0: primary_report_data[1],
                MonthlyReportSubmissionCptCodeMapping2.SUBMISSIONS_0: primary_report_data[2],
                MonthlyReportSubmissionCptCodeMapping2.SUBMISSIONS_DAY_0: primary_report_data[3],
                MonthlyReportSubmissionCptCodeMapping2.EARLIEST_BILLING_DATE_0: primary_report_data[4],
            }

        if secondary_report_data[2]:
            response = {
                **response,
                MonthlyReportSubmissionCptCodeMapping2.BILLING_START_DATE_1: secondary_report_data[0],
                MonthlyReportSubmissionCptCodeMapping2.BILLING_END_DATE_1: secondary_report_data[1],
                MonthlyReportSubmissionCptCodeMapping2.SUBMISSIONS_1: secondary_report_data[2],
                MonthlyReportSubmissionCptCodeMapping2.SUBMISSIONS_DAY_1: secondary_report_data[3],
                MonthlyReportSubmissionCptCodeMapping2.EARLIEST_BILLING_DATE_1: secondary_report_data[4],
            }

        return response

    def get_cpt_code_99457_export_data(self, user_id: str, from_date: datetime.date, to_date: datetime.date):
        if not self.is_time_tracking_date_of_service_in_range(from_date, to_date):
            return {}
        cpt_code_result = dict()
        billing_start_date = datetime.datetime(from_date.year, from_date.month, 1)
        billing_end_date = datetime.datetime(
            to_date.year,
            to_date.month,
            day=calendar.monthrange(from_date.year, from_date.month)[1],
        )
        cpt_code_result[TimeTrackingCPTCodeDetails.BILLING_START_DATE] = utc_date_to_str(billing_start_date)
        cpt_code_result[TimeTrackingCPTCodeDetails.BILLING_END_DATE] = utc_date_to_str(billing_end_date)
        cpt_code_result[TimeTrackingCPTCodeDetails.TOTAL_BILLABLE] = get_billable_period_count_for_cpt_3(
            user_id=user_id, from_date=from_date, to_date=to_date
        )
        return TimeTrackingCPTCodeDetails.to_dict(TimeTrackingCPTCodeDetails.from_dict(cpt_code_result))

    def get_cpt_code_99458_export_data(self, user_id: str, from_date: datetime.date, to_date: datetime.date):
        if not self.is_time_tracking_date_of_service_in_range(from_date, to_date):
            return {}
        cpt_code_result = dict()
        cpt_code_result[TimeTrackingCPTCodeDetails.BILLING_START_DATE] = utc_date_to_str(from_date)
        cpt_code_result[TimeTrackingCPTCodeDetails.BILLING_END_DATE] = utc_date_to_str(to_date)
        cpt_code_result[TimeTrackingCPTCodeDetails.TOTAL_BILLABLE] = get_billable_period_count_for_cpt_4_1x(
            user_id=user_id, from_date=from_date, to_date=to_date
        ) + get_billable_period_count_for_cpt_4_2x(user_id=user_id, from_date=from_date, to_date=to_date)

        return TimeTrackingCPTCodeDetails.to_dict(TimeTrackingCPTCodeDetails.from_dict(cpt_code_result))

    @staticmethod
    def is_time_tracking_date_of_service_in_range(from_date: datetime.date, to_date: datetime.date):
        date_of_service = datetime.datetime(
            from_date.year,
            from_date.month,
            day=calendar.monthrange(from_date.year, from_date.month)[1],
        ).date()
        return utc_str_to_date(from_date) < date_of_service <= utc_str_to_date(to_date)

    @staticmethod
    def _has_user_time_tracking_data(user_cpt_codes_details: dict):
        time_spent_in_seconds = user_cpt_codes_details.get(BillingMonthlyReportCPTCodes.MONITORING_TIME_MINS, 0)
        enough_time = time_spent_in_seconds != 0
        has_calls = user_cpt_codes_details.get(BillingMonthlyReportCPTCodes.NUMBER_OF_CALLS, 0) != 0
        return enough_time or has_calls

    def _add_submission_cpt_codes_details(
        self,
        user_id,
        first_bp_start_date,
        from_date,
        to_date,
        cpt_details,
    ) -> None:
        cpt_details[self._get_cpt_code_by_product_type(1)] = self.get_cpt_code_99453_export_data(
            user_id=user_id,
            first_bp_start_date=first_bp_start_date,
            from_date=from_date,
            to_date=to_date,
        )

        cpt_details[self._get_cpt_code_by_product_type(2)] = self.get_cpt_code_99454_export_data(
            user_id=user_id,
            first_bp_start_date=first_bp_start_date,
            from_date=from_date,
            to_date=to_date,
        )

    def _add_time_tracking_cpt_codes_details(
        self,
        user_id,
        from_date,
        to_date,
        user_cpt_codes_details,
    ) -> None:
        user_cpt_codes_details[self._get_cpt_code_by_product_type(3)] = self.get_cpt_code_99457_export_data(
            user_id=user_id, from_date=from_date, to_date=to_date
        )
        user_cpt_codes_details[self._get_cpt_code_by_product_type(4)] = self.get_cpt_code_99458_export_data(
            user_id=user_id, from_date=from_date, to_date=to_date
        )

        if not self.is_time_tracking_date_of_service_in_range(from_date, to_date):
            return None

        time_spent_in_seconds = get_time_spent(user_id=user_id, start_date=from_date, end_date=to_date)
        user_cpt_codes_details[BillingMonthlyReportCPTCodes.MONITORING_TIME_MINS] = floor(time_spent_in_seconds / 60)
        user_cpt_codes_details[BillingMonthlyReportCPTCodes.NUMBER_OF_CALLS] = get_number_of_interactive_sessions(
            user_id=user_id, from_date=from_date, to_date=to_date
        )

    @staticmethod
    def _get_deployment_ids_for_time_ranges(
        move_history: list, deployment_id: str, ranges: list[DateRange]
    ) -> dict[DateRange, str]:
        """Maps each time range to the deployment id that was active in that range"""
        if not move_history:
            return {range_: deployment_id for range_ in ranges}

        moves_descending = sorted(move_history, key=lambda x: x.createDateTime)

        deployment_mapping = {}
        for range_ in ranges:
            from_date, to_date = range_[0], range_[1]
            cur_deployment_id = None
            for move in moves_descending:
                move_date = move.createDateTime.date()
                if move_date < from_date:
                    cur_deployment_id = move.targetResourceId
                elif from_date <= move_date <= to_date:
                    cur_deployment_id = move.targetResourceId
                elif move_date > to_date:
                    cur_deployment_id = move.resourceId
                    break
            deployment_mapping[range_] = cur_deployment_id

        return deployment_mapping

    def _get_billing_provider_file_id(self) -> str | None:
        files = self.billing_config.files
        if not files:
            return None

        for file in files:
            if file.type == DeploymentBillingFileType.BILLING_PROVIDERS:
                return file.fileId

        return None

    @staticmethod
    def _read_csv_file(file_id) -> list[dict]:
        try:
            file = FileDownload(file_id)
            with io.TextIOWrapper(file.content, "utf-8") as text_file:
                return list(csv.DictReader(text_file, delimiter=",", restval=None))
        except (BucketFileDoesNotExist, FileNotFoundException):
            return []


class BillingMonthlyEHRExportableUseCase(BillingMonthlyExportableUseCase):
    MODULE_NAME = "EHRMonthlyBilling"

    def get_module(self, request_object: ExportableRequestObject, module_name: str) -> bool:
        """Module is not exported if not explicitly requested"""
        if self.is_exporting_own_data():
            return False

        return request_object.moduleNames and module_name in request_object.moduleNames

    def get_formatted_monthly_billing_data(self, users, from_date, to_date):
        results = self._get_monthly_billing_data(users, from_date, to_date)
        if results:
            for user_result in results:
                if not user_result:
                    continue

                self._populate_missing_fields(user_result)
        return results

    def _populate_missing_fields(self, user_data: dict):
        cpt_details = user_data.get(
            self.BillingMonthlyReportSections.CPT_CODES_DETAILS,
            {},
        )
        for code, value in cpt_details.items():
            if not value:
                continue

            if not isinstance(value, dict):
                continue

            self._populate_fields_for_mapping_1(code, value)
            self._populate_fields_for_mapping_2(code, value)

    @staticmethod
    def _populate_fields_for_mapping_1(code: str, value: dict):
        if code not in (
            BillingMonthlyReportCPTCodes.CPT_99453,
            BillingMonthlyReportCPTCodes.CPT_98975,
        ):
            return

        submission_days = value.get(MonthlyReportSubmissionCptCodeMapping1.SUBMISSIONS_DAY, 0)
        total_billable = 0
        if isinstance(submission_days, int) and submission_days >= 16:
            total_billable = 1

        value[MonthlyReportSubmissionCptCodeMapping2.TOTAL_BILLABLE] = total_billable

    @staticmethod
    def _populate_fields_for_mapping_2(code: str, value: dict):
        if code not in (
            BillingMonthlyReportCPTCodes.CPT_99454,
            BillingMonthlyReportCPTCodes.CPT_98976,
        ):
            return

        total_billable = 0
        if submission_days := value.get(MonthlyReportSubmissionCptCodeMapping1.SUBMISSIONS_DAY, 0):
            if isinstance(submission_days, int) and submission_days >= 16:
                total_billable += 1

            value[MonthlyReportSubmissionCptCodeMapping2.TOTAL_BILLABLE] = total_billable
            return

        submission_days_0 = value.get(MonthlyReportSubmissionCptCodeMapping2.SUBMISSIONS_DAY_0, 0)
        if isinstance(submission_days_0, int) and submission_days_0 >= 16:
            total_billable += 1

        submission_days_1 = value.get(MonthlyReportSubmissionCptCodeMapping2.SUBMISSIONS_DAY_1, 0)
        if isinstance(submission_days_1, int) and submission_days_1 >= 16:
            total_billable += 1

        value[MonthlyReportSubmissionCptCodeMapping2.TOTAL_BILLABLE] = total_billable

    def _build_provider_data(self, row: dict) -> EHRBillingProvider:
        return EHRBillingProvider.from_dict(
            {
                EHRBillingProvider.NAME: row.get(BillingProviderFile.NAME.value),
                EHRBillingProvider.ID: row.get(BillingProviderFile.ID.value),
                EHRBillingProvider.ABBREVIATION: row.get(BillingProviderFile.ABBREVIATION.value),
                EHRBillingProvider.LOCATION_DEPARTMENT: row.get(BillingProviderFile.LOCATION_DEPARTMENT.value),
                EHRBillingProvider.LOCATION_ABBREVIATION: row.get(BillingProviderFile.LOCATION_ABBREVIATION.value),
                EHRBillingProvider.PLACE_OF_SERVICE_ABBREVIATION: row.get(
                    BillingProviderFile.PLACE_OF_SERVICE_ABBREVIATION.value
                ),
            }
        )
