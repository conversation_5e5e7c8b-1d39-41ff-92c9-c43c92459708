from collections import defaultdict
from datetime import date

import pytz

from billing.components.core.dtos.billing_models import (
    BillingRemoteTimeTrackingDTO,
)
from billing.components.core.dtos.deployment_billing import BILLING_FEATURES_KEY
from billing.components.core.repository.billing_repository import (
    BillingRemoteTimeTrackingRepository,
)
from billing.components.export.models.billing_models import (
    ExportBillingAutomatedLog,
)
from billing.components.export.use_case.base_billing_exportable_use_case import (
    BaseBillingExportableUseCase,
)
from huma_plugins.components.export.helpers.convertion_helpers import (
    ExportData,
    convert_id_fields_to_string,
)
from sdk.authorization.dtos.user import UserDTO
from sdk.common.utils.common_functions_utils import find
from sdk.common.utils.date_utils import localize_from_utc
from sdk.common.utils.inject import autoparams


class BillingAutomatedLogExportableUseCase(BaseBillingExportableUseCase):
    MODULE_NAME = "AutomatedLogBilling"

    def get_raw_result(self) -> ExportData:
        if not self._module_exists() or not self._billing_enabled():
            return {}

        user_ids = self._fetch_user_ids()
        billing_logs = self._get_automated_logs(user_ids)
        if not billing_logs:
            return {}

        from_date, to_date = self._extract_dates()
        exportable_logs = self._logs_to_exportable_model(billing_logs, from_date, to_date)
        return {f"{self.MODULE_NAME}_{from_date}_{to_date}": exportable_logs}

    @autoparams("billing_repo")
    def _get_automated_logs(
        self, user_ids: list[str], billing_repo: BillingRemoteTimeTrackingRepository
    ) -> list[BillingRemoteTimeTrackingDTO]:
        return billing_repo.retrieve_users_automated_logs_from_to(
            user_ids=user_ids,
            start_date=self._convert_dt_to_utc(self.request_object.fromDate),
            end_date=self._convert_dt_to_utc(self.request_object.toDate),
        )

    def _logs_to_exportable_model(
        self,
        billing_logs: list[BillingRemoteTimeTrackingDTO],
        from_date: date,
        to_date: date,
    ) -> list[dict]:
        logs, clinicians, patients = self._get_data_from_logs(billing_logs)

        logs_to_export = []
        for patient in patients:
            if not (patient_logs := logs.get(patient.id)):
                continue

            logs_data = self._generate_logs_data(patient, clinicians, patient_logs, from_date, to_date)
            logs_to_export.extend(logs_data)

        return logs_to_export

    def _get_data_from_logs(
        self, billing_logs: list[BillingRemoteTimeTrackingDTO]
    ) -> tuple[dict[str : list[BillingRemoteTimeTrackingDTO]], list[UserDTO], list[UserDTO]]:
        logs = defaultdict(list)
        clinician_ids = set()

        for log in billing_logs:
            user_id = str(log.userId)
            clinician_ids.add(str(log.clinicianId))
            logs[user_id].append(log)

        clinicians = self._auth_repo.retrieve_simple_user_profiles_by_ids(clinician_ids, force_fetch_all=False)
        patients = self._auth_repo.retrieve_simple_user_profiles_by_ids(set(logs.keys()), force_fetch_all=False)
        return logs, clinicians, patients

    def _generate_logs_data(
        self,
        patient: UserDTO,
        clinicians: list[UserDTO],
        patient_logs: list[BillingRemoteTimeTrackingDTO],
        start_date: date,
        end_date: date,
    ) -> list[dict]:
        requester_tz = pytz.timezone(self.request_object.requester.timezone or "UTC")

        logs_to_export = []
        for billing_log in patient_logs:
            duration = billing_log.effectiveDuration
            duration_to_str = str(round((duration or 0) / 60, 2))
            clinician = find(lambda x: x.id == billing_log.clinicianId, clinicians)
            billing_dict = self._convert_dt_to_user_tz(billing_log, requester_tz).to_dict()

            billing_dict.update(
                {
                    ExportBillingAutomatedLog.REPORT_START_DATE: str(start_date),
                    ExportBillingAutomatedLog.REPORT_END_DATE: str(end_date),
                    ExportBillingAutomatedLog.USER: patient.to_dict(),
                    ExportBillingAutomatedLog.CLINICIAN: clinician.to_dict() if clinician else {},
                    ExportBillingAutomatedLog.MODULE_ID: BILLING_FEATURES_KEY,
                    ExportBillingAutomatedLog.MONITORING_ID: billing_log.id,
                    ExportBillingAutomatedLog.DURATION: duration_to_str,
                }
            )

            billing_dict = convert_id_fields_to_string(billing_dict)
            export_log = ExportBillingAutomatedLog.from_dict(billing_dict)
            logs_to_export.append(export_log.to_dict())

        return logs_to_export

    @staticmethod
    def _convert_dt_to_user_tz(log: BillingRemoteTimeTrackingDTO, tz) -> BillingRemoteTimeTrackingDTO:
        log.startDateTime = localize_from_utc(log.startDateTime, tz, remove_tzinfo=True).strftime("%Y-%m-%d %H:%M:%S")
        log.endDateTime = localize_from_utc(log.endDateTime, tz, remove_tzinfo=True).strftime("%Y-%m-%d %H:%M:%S")
        return log
