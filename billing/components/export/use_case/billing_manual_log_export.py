from datetime import date, datetime

from billing.components.core.dtos.billing_models import BillingMonitoringLogDTO
from billing.components.core.dtos.deployment_billing import BILLING_FEATURES_KEY
from billing.components.core.helpers.export_billing_manual_log_helpers import (
    retrieve_user_monitoring_logs_from_to,
)
from billing.components.export.helpers.manual_log_helpers import (
    ManualLogReportFormatter,
)
from billing.components.export.models.billing_models import (
    BillingManualLogReportBaseFields,
    ExportBillingLogUser,
)
from billing.components.export.use_case.base_billing_exportable_use_case import (
    BaseBillingExportableUseCase,
)
from huma_plugins.components.export.helpers.convertion_helpers import (
    ExportData,
    convert_id_fields_to_string,
)
from sdk.authorization.dtos.user import UserDTO


class BillingManualLogExportableUseCase(BaseBillingExportableUseCase):
    MODULE_NAME = "ManualLogBilling"

    class BillingManualLogReportSections:
        USER_DATA = "user"
        LOG_DATA = "log"

    def get_raw_result(self) -> ExportData:
        if not (self._module_exists() and self._billing_enabled()):
            return {}

        from_date, to_date = self._extract_dates()
        user_logs = self._get_manual_logs_per_user(
            self.request_object.fromDate,
            self.request_object.toDate,
            self._fetch_user_ids(),
        )
        if not user_logs:
            return {}

        clinicians = self._fetch_clinicians_data_from_user_logs(user_logs)
        logs_results = self._generate_results(
            user_logs,
            self._auth_repo.retrieve_simple_user_profiles_by_ids(set(user_logs.keys())),
            clinicians,
            from_date,
            to_date,
        )

        return {f"{self.MODULE_NAME}_{from_date}_{to_date}": logs_results} if logs_results else {}

    def _fetch_clinicians_data_from_user_logs(self, user_logs: dict[str : list[dict]]) -> dict[str:UserDTO]:
        clinician_ids = set()
        for user in user_logs:
            for log in user_logs[user]:
                clinician_ids = clinician_ids.union(
                    {
                        str(log.createdById),
                        str(log.lastModifiedById),
                    }
                )

        clinicians = self._auth_repo.retrieve_simple_user_profiles_by_ids(ids=clinician_ids, force_fetch_all=False)
        return {clinician.id: clinician for clinician in clinicians}

    def _generate_results(
        self,
        logs: dict[str:list],
        users: list[UserDTO],
        clinician_data: dict[str:UserDTO],
        start_date: date,
        end_date: date,
    ) -> list:
        results = []
        for user in users:
            if not (user_logs := logs.get(user.id)):
                continue

            logs_data = self._generate_log_data(user_logs, clinician_data, self.request_object.requester)

            user_data = self._generate_user_specific_data(user)

            user_results = [
                convert_id_fields_to_string(
                    {
                        BillingManualLogReportBaseFields.USER_ID: user.id,
                        BillingManualLogReportBaseFields.DEPLOYMENT_ID: self.request_object.deploymentId,
                        BillingManualLogReportBaseFields.MODULE_ID: BILLING_FEATURES_KEY,
                        BillingManualLogReportBaseFields.REPORT_START_DATE: str(start_date),
                        BillingManualLogReportBaseFields.REPORT_END_DATE: str(end_date),
                        self.BillingManualLogReportSections.USER_DATA: user_data,
                        self.BillingManualLogReportSections.LOG_DATA: log,
                    }
                )
                for log in logs_data
            ]

            results.extend(user_results)

        return results

    def _get_manual_logs_per_user(
        self,
        from_dt: datetime,
        to_dt: datetime,
        user_ids: list[str],
        deployment_id: str = None,
    ) -> dict:
        if not user_ids:
            return {}

        return retrieve_user_monitoring_logs_from_to(
            deployment_id=deployment_id,
            from_dt=self._convert_dt_to_utc(from_dt),
            to_dt=self._convert_dt_to_utc(to_dt),
            user_ids=user_ids,
        )

    @staticmethod
    def _generate_log_data(
        logs: list[BillingMonitoringLogDTO],
        clinicians: dict[str:UserDTO],
        requester: UserDTO,
    ) -> list:
        return [
            ManualLogReportFormatter.reformat(
                log,
                clinicians.get(str(log.createdById)),
                clinicians.get(str(log.lastModifiedById)),
                requester,
            )
            for log in logs
        ]

    @staticmethod
    def _generate_user_specific_data(user: UserDTO) -> dict:
        return ExportBillingLogUser.from_dict(user.to_dict()).to_dict()
