from datetime import datetime
from math import floor
from typing import Union

from billing.components.core.dtos.deployment_billing import BILLING_FEATURES_KEY
from billing.components.core.dtos.user_billing import UserBilling
from billing.components.core.helpers import export_billing_submission_helpers
from billing.components.core.helpers.export_billing_submission_helpers import (
    get_billable_period_counts_for_user,
    get_user_first_submission,
)
from billing.components.core.helpers.export_billing_time_tracking_helpers import (
    get_time_spent,
    get_number_of_interactive_sessions,
    remove_user_billing_data_overlapping,
)
from billing.components.export.models.billing_models import (
    BillingGeneralReportUser,
    BillingGeneralReportCPTCodes,
    BillingGeneralReportUserBilling,
    UserBaseBilling,
)
from billing.components.export.use_case.base_billing_exportable_use_case import (
    BaseBillingExportableUseCase,
)
from billing.components.export.use_case.billing_monthly_export import (
    BillingMonthlyExportableUseCase,
    BillingMonthlyEHRExportableUseCase,
)
from huma_plugins.components.export.helpers.convertion_helpers import (
    ExportData,
    convert_id_fields_to_string,
)
from sdk.authorization.dtos.user import UserDTO
from sdk.common.utils.common_functions_utils import deep_get
from sdk.module_result.dtos.primitives import PrimitiveDTO


class BillingGeneralExportableUseCase(BaseBillingExportableUseCase):
    class BillingGeneralReportSections:
        USER_PROFILE_DATA = "user"
        USER_BILLING_DATA = "billing"
        USER_CPT_CODES_DATA = "report"

    MODULE_NAME = "GeneralBilling"

    def get_raw_result(self) -> ExportData:
        if not (self._module_exists() and self._billing_enabled()):
            return {}

        from_date, to_date = self._extract_dates()
        users = self._fetch_users_data()
        general_billing_data = self.get_general_billing_data(users, from_date, to_date)
        if not general_billing_data:
            return {}

        return {f"{self.MODULE_NAME}_{from_date}_{to_date}": general_billing_data}

    def _add_user_profile_data(self, user_data: dict, user: UserDTO):
        user_data[self.BillingGeneralReportSections.USER_PROFILE_DATA] = BillingGeneralReportUser.from_dict(
            user.to_dict()
        ).to_dict()

    def _add_user_billing_data(self, user_data: dict, user: UserDTO):
        billing_data = user.componentsData.get(BILLING_FEATURES_KEY, {}) if user.componentsData else {}
        billing_obj = UserBaseBilling.from_dict(billing_data)

        primary_carrier_idx, secondary_carrier_idx = 0, 1

        if carriers := billing_obj.insuranceCarriers:
            if first_carrier := deep_get(carriers, str(primary_carrier_idx)):
                billing_data[BillingGeneralReportUserBilling.PRIMARY_INSURANCE_CARRIER] = first_carrier.groupName
                billing_data[BillingGeneralReportUserBilling.PRIMARY_INSURANCE_CARRIER_PAYER_ID] = first_carrier.payerId

            if second_carrier := deep_get(carriers, str(secondary_carrier_idx)):
                billing_data[BillingGeneralReportUserBilling.SECONDARY_INSURANCE_CARRIER] = second_carrier.groupName
                billing_data[BillingGeneralReportUserBilling.SECONDARY_INSURANCE_CARRIER_PAYER_ID] = (
                    second_carrier.payerId
                )

        if first_submission := get_user_first_submission(user_id=user.id):
            billing_data[BillingGeneralReportUserBilling.BILLING_FIRST_READING] = str(
                first_submission.startDateTime.date()
            )

        if not billing_obj.diagnosis:
            billing_data[BillingGeneralReportUserBilling.DIAGNOSIS] = {}

        user_data[self.BillingGeneralReportSections.USER_BILLING_DATA] = BillingGeneralReportUserBilling.to_dict(
            BillingGeneralReportUserBilling.from_dict({**billing_data, **UserBilling.to_dict(billing_data)})
        )

    def _add_cpt_codes_data(
        self,
        from_date: Union[datetime.date, None],
        to_date: Union[datetime.date, None],
        user_data: dict,
        user: UserDTO,
    ):
        user_id = user.id
        use_calendar_calculation = self.billing_config.useCalendarCalculation
        user_data[self.BillingGeneralReportSections.USER_CPT_CODES_DATA] = {
            BillingGeneralReportCPTCodes.NUMBER_OF_SUBMISSIONS: export_billing_submission_helpers.get_user_total_submission_count(
                user_id=user_id,
                from_date=from_date,
                to_date=to_date,
            ),
            BillingGeneralReportCPTCodes.NUMBER_OF_SUBMISSIONS_DAYS: export_billing_submission_helpers.get_user_total_submission_days_count(
                user_id=user_id,
                from_date=from_date,
                to_date=to_date,
            ),
            **get_billable_period_counts_for_user(
                user_id=user_id,
                product_type=self.product_type,
                from_date=from_date,
                to_date=to_date,
                use_calendar=use_calendar_calculation,
            ),
            BillingGeneralReportCPTCodes.MONITORING_TIME_MINS: floor(
                get_time_spent(user_id=user_id, start_date=from_date, end_date=to_date) / 60
            ),
            BillingGeneralReportCPTCodes.NUMBER_OF_CALLS: get_number_of_interactive_sessions(
                user_id=user_id, from_date=from_date, to_date=to_date
            ),
        }

    def _check_overlapping(self, user_id: str):
        modules = self.request_object.moduleNames or []
        if (
            BillingMonthlyExportableUseCase.MODULE_NAME not in modules
            or BillingMonthlyEHRExportableUseCase.MODULE_NAME not in modules
        ):
            remove_user_billing_data_overlapping(user_id=user_id)

    def get_general_billing_data(
        self,
        users: list[UserDTO],
        from_date: datetime.date,
        to_date: datetime.date,
    ) -> list:
        result = []
        deployment_id = self.request_object.deploymentId
        for user in users:
            user_data = {}
            self._add_user_profile_data(user_data, user)
            self._add_user_billing_data(user_data, user)
            if to_date >= datetime.utcnow().date():
                self._check_overlapping(user_id=user.id)
            self._add_cpt_codes_data(from_date, to_date, user_data, user)

            result.append(
                convert_id_fields_to_string(
                    {
                        PrimitiveDTO.USER_ID: user.id,
                        PrimitiveDTO.DEPLOYMENT_ID: deployment_id,
                        PrimitiveDTO.MODULE_ID: BILLING_FEATURES_KEY,
                        **user_data,
                    }
                )
            )

        self._reformat_fields(result)
        return result
