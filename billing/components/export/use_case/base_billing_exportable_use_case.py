from abc import abstractmethod
from collections import defaultdict
from datetime import date, datetime
from functools import cached_property
from typing import Optional, Tuple, Union

import pytz

from billing.components.core.dtos.deployment_billing import ProductType
from billing.components.core.helpers.alerts_helpers import (
    get_billing_product_type,
    get_deployment_billing_object,
)
from huma_plugins.components.export.helpers.convertion_helpers import ExportData
from huma_plugins.components.export.use_case.exportable.exportable_request_objects import ExportableRequestObject
from huma_plugins.components.export.use_case.exportable.exportable_use_case import ExportableUseCase
from sdk.authorization.dtos.user import UserDTO
from sdk.authorization.dtos.user_move_history import UserMoveHistoryDTO
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.authorization.repository.user_move_history_repository import UserMoveHistoryRepository
from sdk.common.utils.date_utils import localize_to_utc
from sdk.common.utils.inject import autoparams


class BaseBillingExportableUseCase(ExportableUseCase):
    MODULE_NAME = None

    request_object: ExportableRequestObject

    @autoparams()
    def __init__(self, *args, auth_repo: AuthorizationRepository, **kwargs):
        self._auth_repo = auth_repo
        super().__init__(*args, **kwargs)

    def get_module(self, request_object: ExportableRequestObject, module_name: str) -> bool:
        if self.is_exporting_own_data():
            return False

        return bool(
            (
                not request_object.moduleNames
                and request_object.externalModuleNames
                and module_name in request_object.externalModuleNames
            )
            or (request_object.moduleNames and module_name in request_object.moduleNames)
        )

    def filter_based_on_consent(self, *args, **kwargs):
        """Filtering by e/consents are omitted for all billing exports"""
        pass

    @cached_property
    def deployment(self):
        return self._deployment_repo.retrieve_deployment(deployment_id=self.request_object.deploymentId)

    @cached_property
    def billing_config(self):
        return get_deployment_billing_object(self.deployment)

    def _billing_enabled(self) -> bool:
        return self.billing_config and self.billing_config.enabled

    @cached_property
    def product_type(self):
        return self.billing_config.productType if self.billing_config else None

    def _extract_deployment_product_type(self) -> Optional[ProductType]:
        return get_billing_product_type(self.deployment)

    def _module_exists(self) -> bool:
        return bool(self.get_module(self.request_object, self.MODULE_NAME))

    def _extract_dates(self) -> Tuple[date, date]:
        return self.request_object.fromDate.date(), self.request_object.toDate.date()

    def _fetch_users_data(self) -> list[UserDTO]:
        user_ids = self._fetch_user_ids()
        return self._auth_repo.retrieve_simple_user_profiles_by_ids(set(user_ids))

    def _fetch_user_ids(self) -> list[str]:
        deployment_users = self._auth_repo.retrieve_user_ids_in_deployment(
            self.request_object.deploymentId, only_active=True
        )
        if self.request_object.userIds:
            return self._exclude_moved_users(self.request_object.userIds, deployment_users)
        return deployment_users

    def _reformat_fields(self, result: Union[dict, list]):
        if isinstance(result, list):
            for item in result:
                self._reformat_fields(item)
            return

        if not isinstance(result, dict):
            return

        for key, val in result.items():
            if isinstance(val, dict):
                self._reformat_fields(val)

            if val is None:
                result[key] = ""

    def _convert_dt_to_utc(self, dt: datetime):
        tz = self.request_object.requester.timezone
        if not tz or tz == str(pytz.UTC):
            return dt
        dt = dt.replace(tzinfo=None)
        return localize_to_utc(dt, tz)

    @staticmethod
    def _exclude_moved_users(user_ids: list[str], deployment_users: list[str]) -> list[str]:
        """Excludes IDs of inactive users in current deployment."""
        return list(set(user_ids).intersection(deployment_users))

    @autoparams("move_repo")
    def _get_users_move_logs(
        self, user_ids: list[str], move_repo: UserMoveHistoryRepository
    ) -> dict[str, list[UserMoveHistoryDTO]]:
        history = move_repo.retrieve_history(user_ids)
        result = defaultdict(list)
        for move in history:
            result[move.userId].append(move)

        return result

    @abstractmethod
    def get_raw_result(self) -> ExportData:
        raise NotImplementedError
