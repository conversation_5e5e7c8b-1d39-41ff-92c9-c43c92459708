import logging

from flask import Blueprint
from flask_limiter import Limiter

from billing.components.export.config.config import BillingExportServerConfig
from billing.components.export.router.export_routers import (
    api as extended_export_route,
)
from billing.components.export.use_case.billing_automated_log_export import (
    BillingAutomatedLogExportableUseCase,
)
from billing.components.export.use_case.billing_general_export import (
    BillingGeneralExportableUseCase,
)
from billing.components.export.use_case.billing_manual_log_export import (
    BillingManualLogExportableUseCase,
)
from billing.components.export.use_case.billing_monthly_export import (
    BillingMonthlyEHRExportableUseCase,
    BillingMonthlyExportableUseCase,
)
from huma_plugins.components.export.component import ExportComponent
from huma_plugins.components.export.use_case.exportable.exportable_use_case import (
    ExportableUseCase,
)

logger = logging.getLogger(__name__)


class BillingExportComponent(ExportComponent):
    config_class = BillingExportServerConfig

    @property
    def blueprint(self) -> Blueprint | list[Blueprint] | None:
        # reusing original blueprints, but import is needed to register the changes
        router_path = "billing.components.export.router.export_routers"
        __import__(router_path)
        return [*super().blueprint, extended_export_route]

    def setup_auth(self):
        super(ExportComponent, self).setup_auth()

    def setup_rate_limiter(self, limiter: Limiter):
        limiter_config = self.config.rateLimit
        if limiter_config and limiter_config.enable and limiter_config.default:
            logger.info("Setting up rate limiter for rpm export routes")
            limiter.limit(limiter_config.default)(extended_export_route)

    @property
    def external_exportable_use_cases(self) -> list[ExportableUseCase]:
        return super().external_exportable_use_cases + [
            BillingGeneralExportableUseCase,
            BillingMonthlyExportableUseCase,
            BillingMonthlyEHRExportableUseCase,
            BillingManualLogExportableUseCase,
            BillingAutomatedLogExportableUseCase,
        ]
