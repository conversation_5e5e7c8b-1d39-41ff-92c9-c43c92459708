from datetime import datetime, <PERSON><PERSON><PERSON>
from unittest import TestCase
from unittest.mock import MagicMock, patch

from freezegun import freeze_time

from billing.components.core.repository.billing_repository import (
    BillingRemoteTimeTrackingRepository,
)
from billing.components.export.tests.UnitTests.test_samples import (
    get_sample_automated_log,
    sample_clinician,
    sample_user,
)
from billing.components.export.use_case.billing_automated_log_export import (
    BillingAutomatedLogExportableUseCase,
)
from huma_plugins.components.export.repository.export_repository import ExportRepository
from huma_plugins.components.export.use_case.exportable.exportable_request_objects import (
    ExportableRequestObject,
)
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.common.adapter.file_storage_adapter import FileStorageAdapter
from sdk.common.utils import inject
from sdk.deployment.repository.deployment_repository import DeploymentRepository
from sdk.phoenix.config.server_config import PhoenixServerConfig

USER_ID = "5fe0b3bb2896c6d525461087"
CLINICIAN_ID = "5fe0b3bb2896c6d525461089"
BILLING = BillingAutomatedLogExportableUseCase.MODULE_NAME


class BillingAutomatedLogExportableUseCaseTests(TestCase):
    def setUp(self):
        self.billing_repo = MagicMock()
        self.deployment_repo = MagicMock()
        self.auth_repo = MagicMock()

        def bind(binder):
            binder.bind(BillingRemoteTimeTrackingRepository, self.billing_repo)
            binder.bind(DeploymentRepository, self.deployment_repo)
            binder.bind(AuthorizationRepository, self.auth_repo)
            binder.bind(ExportRepository, MagicMock())
            binder.bind(FileStorageAdapter, MagicMock())
            binder.bind(PhoenixServerConfig, MagicMock())

        inject.clear_and_configure(bind)
        self.use_case = BillingAutomatedLogExportableUseCase()
        self.use_case.request_object = ExportableRequestObject()
        self.req_obj = self.use_case.request_object
        self.req_obj.userIds = [USER_ID]
        self.req_obj.moduleNames = [BILLING]

    @patch.object(BillingAutomatedLogExportableUseCase, "_billing_enabled")
    def test_failed_to_export_log_when_billing_disabled(self, billing_enabled):
        billing_enabled.return_value = False
        result = self.use_case.get_raw_result()
        self.assertEqual(result, {})

    @freeze_time("2023-10-05")
    @patch.object(BillingAutomatedLogExportableUseCase, "_billing_enabled")
    @patch.object(BillingAutomatedLogExportableUseCase, "_get_users_move_logs")
    def test_successful_get_raw_result_for_automated_logs(self, move_logs, billing_enabled):
        billing_enabled.return_value = True
        move_logs.return_value = {}
        self.req_obj.fromDate = datetime.utcnow() - timedelta(days=3)
        self.req_obj.toDate = datetime.utcnow()
        self.req_obj.requester.timezone = "UTC"
        self.billing_repo.retrieve_users_automated_logs_from_to.return_value = get_sample_automated_log()
        mock_retrieve_profiles = MagicMock()
        mock_retrieve_profiles.side_effect = [
            sample_clinician,
            sample_user,
        ]
        self.auth_repo.retrieve_simple_user_profiles_by_ids = mock_retrieve_profiles

        result = self.use_case.get_raw_result()
        expected = {
            "AutomatedLogBilling_2023-10-02_2023-10-05": [
                {
                    "reportStartDate": "2023-10-02",
                    "reportEndDate": "2023-10-05",
                    "userId": USER_ID,
                    "user": {
                        "givenName": "John",
                        "familyName": "Smith",
                        "dateOfBirth": None,
                    },
                    "monitoringId": "654128fd83a863ae18af9d45",
                    "startDateTime": "2023-10-03 14:50:22",
                    "endDateTime": "2023-10-03 14:50:59",
                    "duration": "0.62",
                    "clinician": {
                        "id": CLINICIAN_ID,
                        "givenName": "Tom",
                        "familyName": "Parker",
                    },
                    "moduleId": "billing",
                }
            ]
        }
        self.assertEqual(result, expected)
