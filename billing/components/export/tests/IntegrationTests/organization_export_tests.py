import json
from pathlib import Path

from flask import url_for

from billing.components.core.component import BillingComponent
from billing.components.core.helpers.module_result_helpers import (
    PrimitiveSources,
)
from billing.components.export.component import BillingExportComponent
from billing.tests.billing_test_case import BillingTestCase
from huma_plugins.components.export.dtos.export_models import ExportParameters
from huma_plugins.components.extended_authorization.component import (
    ExtendedAuthorizationComponent,
)
from huma_plugins.components.extended_authorization.component_options import (
    authorization_options,
)
from huma_plugins.components.extended_module_result.component import (
    ExtendedModuleResultComponent,
)
from huma_plugins.components.online_offline_call.component import (
    OnlineOfflineCallComponent,
)
from sdk.auth.component import AuthComponent
from sdk.deployment.component import DeploymentComponent
from sdk.deployment.dtos.status import EnableStatus
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.modules import BloodPressureModule
from sdk.module_result.modules.blood_pressure import BloodPressureDTO
from sdk.organization.component import OrganizationComponent
from sdk.storage.component import StorageComponentV1
from sdk.versioning.component import VersionComponent

DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
USER_ID = "5e8f0c74b50aa9656c34789b"
MANAGER_ID = "63ce44193d6527f1c3ceafe3"
SUPER_ADMIN_ID = "5e8f0c74b50aa9656c34789a"
ORG_ID = "63b67ff40d4b124707c3d29b"


class OrganizationReportExportTestCase(BillingTestCase):
    components = [
        AuthComponent(),
        ExtendedAuthorizationComponent(
            component_options=authorization_options,
        ),
        BillingComponent(),
        DeploymentComponent(),
        OrganizationComponent(),
        ExtendedModuleResultComponent(
            additional_modules=[BloodPressureModule()],
        ),
        BillingExportComponent(),
        OnlineOfflineCallComponent(),
        StorageComponentV1(),
        VersionComponent(server_version="1.0.0", api_version="1.0.0"),
    ]
    fixtures = [
        Path(__file__).parent.joinpath("fixtures/deployments_dump.json"),
    ]

    def setUp(self):
        super().setUp()
        self.export_url = "/api/extensions/v1/export/"

    def test_sample_export(self):
        rsp = self.flask_client.post(
            self.export_url,
            headers=self.get_headers_for_token(MANAGER_ID),
            json=self._export_payload(),
        )
        self._assert_json_content_is_equal(rsp, "org_sample_export_json.json")

    def test_export_is_not_affected_by_consent_or_econsent(self):
        self._enable_consent("Consent")
        self._enable_consent("EConsent")
        self._submit_sample_module_results()

        rsp = self.flask_client.post(
            self.export_url,
            headers=self.get_headers_for_token(MANAGER_ID),
            json=self._export_payload(),
        )

        self.assertIn(USER_ID, rsp.json)

    @staticmethod
    def _export_payload(org_id: str = ORG_ID, **kwargs):
        return {
            ExportParameters.FROM_DATE: "2022-01-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-07-01T00:00:00.039Z",
            ExportParameters.SINGLE_FILE_RESPONSE: True,
            ExportParameters.DEIDENTIFIED: False,
            ExportParameters.INCLUDE_USER_META_DATA: False,
            ExportParameters.ORGANIZATION_ID: org_id,
            **kwargs,
        }

    def _assert_json_content_is_equal(self, response, sample_file):
        sample_file_path = Path(__file__).parent.joinpath(f"fixtures/{sample_file}")
        with open(sample_file_path) as sample:
            file_data = sample.read()
            sample_file_json = json.loads(file_data)
            self.assertEqual(sample_file_json, json.loads(response.data))

    def _enable_consent(self, onboarding_id="Consent"):
        url = url_for(
            "deployment_route.create_onboarding_module_config",
            deployment_id=DEPLOYMENT_ID,
        )
        body = {
            "onboardingId": onboarding_id,
            "status": EnableStatus.ENABLED.value,
            "order": 1,
        }
        rsp = self.flask_client.post(url, headers=self.get_headers_for_token(SUPER_ADMIN_ID), json=body)
        self.assertEqual(201, rsp.status_code)

    def _submit_sample_module_results(self, user_id: str = USER_ID, start_date: str = "2022-06-01T00:00:00.039Z"):
        module_results = {
            PrimitiveDTO.USER_ID: user_id,
            PrimitiveDTO.SUBMITTER_ID: MANAGER_ID,
            PrimitiveDTO.DEPLOYMENT_ID: DEPLOYMENT_ID,
            PrimitiveDTO.CREATE_DATE_TIME: "2022-06-01T00:00:00.039Z",
            PrimitiveDTO.START_DATE_TIME: start_date,
            PrimitiveDTO.DEVICE_NAME: "device",
            PrimitiveDTO.SERVER: {"hostUrl": "local", "server": "1.21.0", "api": "V1"},
            PrimitiveDTO.SOURCE: f"{PrimitiveSources.HEALTH_KIT.value};other;possible;things",
            BloodPressureDTO.MODULE_ID: "BloodPressure",
            BloodPressureDTO.MODULE_CONFIG_ID: "5e94b2007773091c9a592650",
            BloodPressureDTO.DIASTOLIC_VALUE: 60,
            BloodPressureDTO.SYSTOLIC_VALUE: 120,
        }
        body = [{**module_results, "type": BloodPressureDTO.get_primitive_name()}]
        rsp = self.flask_client.post(
            f"api/extensions/v1/user/{user_id}/module-result/{BloodPressureModule.moduleId}",
            headers=self.get_headers_for_token(MANAGER_ID),
            json=body,
        )
        self.assertEqual(201, rsp.status_code)
