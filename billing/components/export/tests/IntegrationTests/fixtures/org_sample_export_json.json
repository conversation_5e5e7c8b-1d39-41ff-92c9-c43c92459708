{"5e8f0c74b50aa9656c34789b": {"GeneralBilling_2022-01-01_2023-07-01": [{"userId": "5e8f0c74b50aa9656c34789b", "deploymentId": "5d386cc6ff885918d96edb2c", "moduleId": "billing", "user": {"givenName": "test", "familyName": "test", "gender": "", "biologicalSex": "", "dateOfBirth": "1988-02-20", "email": "<EMAIL>", "phoneNumber": "+380999999999", "primaryAddress": "", "timezone": "UTC", "language": ""}, "billing": {"primaryInsuranceCarrier": "", "secondaryInsuranceCarrier": "", "primaryInsuranceCarrierPayerId": "", "secondaryInsuranceCarrierPayerId": "", "billingProviderName": "someName", "diagnosis": {"order": 1, "icd10Code": "ICD", "description": "diagnosis description"}, "billingFirstReading": ""}, "report": {"numberOfSubmissions": 0, "numberOfSubmissionDays": 0, "cpt99453": 0, "cpt99454": 0, "cpt99457": 0, "cpt99458": 0, "cpt98975": 0, "cpt98976": 0, "cpt98980": 0, "cpt98981": 0, "monitoringTimeMins": 0, "numberOfCalls": 3}}], "ManualLogBilling_2022-01-01_2023-07-01": [{"userId": "5e8f0c74b50aa9656c34789b", "deploymentID": "5d386cc6ff885918d96edb2c", "moduleId": "billing", "reportStartDate": "2022-01-01", "reportEndDate": "2023-07-01", "user": {"givenName": "test", "familyName": "test", "dateOfBirth": "1988-02-20"}, "log": {"initialCreateDateTime": "2023-06-01 00:12:00.039000", "updateDateTime": "2023-06-15 00:00:00", "monitoringId": "648a5480e8c83e2af28b7aa0", "validation": "valid", "invalidationReason": "", "monitoringStartDateTime": "2023-06-01 00:12:00.039000", "monitoringEndDateTime": "2023-06-01 00:12:31.039000", "monitoringDuration": "0", "monitoringCategory": "Attempted to contact patient", "initialCreatedById": "5e8f0c74b50aa9656c34789d", "initialCreatedByFullname": "manager manager", "lastModifiedById": "5e8f0c74b50aa9656c34789d", "updatedByFullName": "manager manager"}}]}, "5e8f0c74b50aa9656c34789c": {"GeneralBilling_2022-01-01_2023-07-01": [{"userId": "5e8f0c74b50aa9656c34789c", "deploymentId": "5d386cc6ff885918d96edb2c", "moduleId": "billing", "user": {"givenName": "test", "familyName": "test", "gender": "", "biologicalSex": "", "dateOfBirth": "1988-02-20", "email": "<EMAIL>", "phoneNumber": "+380999999899", "primaryAddress": "", "timezone": "UTC", "language": ""}, "billing": {"primaryInsuranceCarrier": "", "secondaryInsuranceCarrier": "", "primaryInsuranceCarrierPayerId": "", "secondaryInsuranceCarrierPayerId": "", "billingProviderName": "someName", "diagnosis": {"order": 1, "icd10Code": "ICD", "description": "diagnosis description"}, "billingFirstReading": ""}, "report": {"numberOfSubmissions": 0, "numberOfSubmissionDays": 0, "cpt99453": 0, "cpt99454": 0, "cpt99457": 0, "cpt99458": 0, "cpt98975": 0, "cpt98976": 0, "cpt98980": 0, "cpt98981": 0, "monitoringTimeMins": 0, "numberOfCalls": 0}}]}}