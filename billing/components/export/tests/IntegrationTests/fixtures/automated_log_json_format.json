{"5e8f0c74b50aa9656c34789b": {"AutomatedLogBilling_2023-06-01_2023-09-24": [{"reportStartDate": "2023-06-01", "reportEndDate": "2023-09-24", "userId": "5e8f0c74b50aa9656c34789b", "user": {"givenName": "test", "familyName": "test", "dateOfBirth": "1988-02-20"}, "monitoringId": "654128fd83a863ae18af9d45", "startDateTime": "2023-08-22 14:50:22", "endDateTime": "2023-08-22 14:50:59", "duration": "0.62", "clinician": {"id": "5e8f0c74b50aa9656c34789d", "givenName": "manager", "familyName": "manager"}, "moduleId": "billing"}, {"reportStartDate": "2023-06-01", "reportEndDate": "2023-09-24", "userId": "5e8f0c74b50aa9656c34789b", "user": {"givenName": "test", "familyName": "test", "dateOfBirth": "1988-02-20"}, "monitoringId": "654128fd83a863ae18af9d46", "startDateTime": "2023-09-22 14:51:10", "endDateTime": "2023-09-22 14:51:50", "duration": "0.67", "clinician": {"id": "5e8f0c74b50aa9656c34789d", "givenName": "manager", "familyName": "manager"}, "moduleId": "billing"}]}, "5e8f0c74b50aa9656c34789c": {"AutomatedLogBilling_2023-06-01_2023-09-24": [{"reportStartDate": "2023-06-01", "reportEndDate": "2023-09-24", "userId": "5e8f0c74b50aa9656c34789c", "user": {"givenName": "test", "familyName": "test", "dateOfBirth": "1988-02-20"}, "monitoringId": "654128fd83a863ae18af9d47", "startDateTime": "2023-09-14 14:51:10", "endDateTime": "2023-09-14 14:51:35", "duration": "0.42", "clinician": {"id": "5e8f0c74b50aa9656c34789e", "givenName": "manager3", "familyName": "manager3"}, "moduleId": "billing"}]}}