{"deployment": [{"_id": {"$oid": "5d386cc6ff885918d96edb2c"}, "name": "X", "status": "DRAFT", "color": "0x007AFF", "code": "AU15", "privacyPolicyUrl": "https://storage.googleapis.com/hu-deployment-static-content/discovernow/Huma%20App%20Privacy%20Notice%20-%20Discover%20Now.pdf", "eulaUrl": "https://medopad.com/medopad_Ltd/EULA.pdf", "termAndConditionUrl": "https://medopad.com/medopad_Ltd/EULA.pdf", "profile": {}, "contactUsURL": "https://huma.com/contact", "onboardingConfigs": [], "roles": [], "learn": {}, "moduleConfigs": [{"id": {"$oid": "5e94b2007773091c9a592650"}, "about": "string", "configBody": {}, "moduleId": "BloodPressure", "moduleName": "string", "schedule": {"isoDuration": "P1W", "timesPerDuration": 1, "timesOfDay": ["BEFORE_DINNER"]}, "status": "ENABLED"}], "keyActionsEnabled": true, "keyActions": [], "surgeryDetails": {}, "features": {"messaging": {"enabled": true, "messages": ["text_message", "hu_sample_message"]}, "customAppConfig": {"billing": {"enabled": true, "productType": "RPM", "files": [{"type": "insuranceCarriers", "name": "insurance_carrier_test_file.csv", "uploadDateTime": "2023-02-10T15:21:09.090Z", "fileId": "63e660e27ab4034a57aa8e55"}, {"type": "billingProviders", "name": "BillingProviders.csv", "uploadDateTime": "2023-02-10T15:21:09.091Z", "fileId": "63e660e37ab4034a57aa8e56"}, {"type": "icd10Codes", "name": "ICD-10-codes.csv", "uploadDateTime": "2023-02-10T15:21:09.091Z", "fileId": "63e660e4985eb95f05b21e70"}]}}}, "updateDateTime": {"$date": {"$numberLong": "1586433217042"}}, "createDateTime": {"$date": {"$numberLong": "1586433217042"}}, "localizations": {"en": {"hu_sample_message": "Some EN translated message"}}}, {"_id": {"$oid": "5d386cc6ff885918d96edb2b"}, "name": "X", "status": "DEPLOYED", "color": "0x007AFF", "code": "AU15", "privacyPolicyUrl": "https://storage.googleapis.com/hu-deployment-static-content/discovernow/Huma%20App%20Privacy%20Notice%20-%20Discover%20Now.pdf", "eulaUrl": "https://medopad.com/medopad_Ltd/EULA.pdf", "termAndConditionUrl": "https://medopad.com/medopad_Ltd/EULA.pdf", "profile": {}, "contactUsURL": "https://huma.com/contact", "onboardingConfigs": [], "roles": [], "learn": {}, "moduleConfigs": [{"id": {"$oid": "5e94b2007773091c9a592650"}, "about": "string", "configBody": {}, "moduleId": "BloodPressure", "moduleName": "string", "schedule": {"isoDuration": "P1W", "timesPerDuration": 1, "timesOfDay": ["BEFORE_DINNER"]}, "status": "ENABLED"}, {"id": {"$oid": "5e94b2007773091c9a592673"}, "about": "string", "configBody": {}, "moduleId": "Step", "moduleName": "string", "status": "ENABLED"}], "keyActionsEnabled": true, "keyActions": [], "surgeryDetails": {}, "features": {"messaging": {"enabled": true, "messages": ["text_message", "hu_sample_message"]}, "customAppConfig": {"billing": {"enabled": true, "productType": "RTM", "files": [{"type": "insuranceCarriers", "name": "insurance_carrier_test_file.csv", "uploadDateTime": "2023-02-10T15:21:09.090Z", "fileId": "63e660e27ab4034a57aa8e55"}, {"type": "billingProviders", "name": "BillingProviders.csv", "uploadDateTime": "2023-02-10T15:21:09.091Z", "fileId": "63e660e37ab4034a57aa8e56"}, {"type": "icd10Codes", "name": "ICD-10-codes.csv", "uploadDateTime": "2023-02-10T15:21:09.091Z", "fileId": "63e660e4985eb95f05b21e70"}]}}}, "updateDateTime": {"$date": {"$numberLong": "1586433217042"}}, "createDateTime": {"$date": {"$numberLong": "1586433217042"}}, "localizations": {"en": {"hu_sample_message": "Some EN translated message"}}}], "huma_auth_user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789a"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789d"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999544", "displayName": "manager", "userAttributes": {"familyName": "manager", "givenName": "manager", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789e"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999566", "displayName": "manager", "userAttributes": {"familyName": "manager", "givenName": "manager", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "5b8f0c74b50aa9656c34789b"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+480999999999", "displayName": "manager", "userAttributes": {"familyName": "manager", "givenName": "manager", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "63ce44193d6527f1c3ceafe3"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999545", "displayName": "administator", "userAttributes": {"familyName": "administator", "givenName": "administator", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c347890"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999540", "displayName": "managerrtm", "userAttributes": {"familyName": "managerrtm", "givenName": "managerrtm", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "64de06d743c2b2d1a11edf41"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380501111111", "displayName": "managerrtm", "userAttributes": {"familyName": "managerrtm", "givenName": "managerrtm", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999999", "displayName": "test", "userAttributes": {"familyName": "test", "givenName": "test", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}}], "user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789a"}, "email": "<EMAIL>", "roles": [{"roleId": "SuperAdmin", "resource": "deployment/*", "userType": "User", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "phoneNumber": "+380999999999", "nhsId": "nhs_id", "dateOfBirth": "1988-02-20", "boardingStatus": {"status": 0, "updateDateTime": {"$date": "2021-02-09T14:08:05.997Z"}}, "componentsData": {"billing": {"status": 1, "billingProviderName": "someName", "billingProviderId": "tempId", "diagnosis": {"order": 1, "icd10Code": "ICD", "description": "diagnosis description"}}}, "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}, "surgeryDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "5b8f0c74b50aa9656c34789b"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "phoneNumber": "+480999999999", "nhsId": "nhs_id", "dateOfBirth": "1988-02-20", "boardingStatus": {"status": 0, "updateDateTime": {"$date": "2021-02-09T14:08:05.997Z"}}, "componentsData": {"billing": {"status": 1, "billingProviderName": "someName", "billingProviderId": "tempId", "diagnosis": {"order": 1, "icd10Code": "ICD", "description": "diagnosis description"}}}, "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2b", "userType": "User", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}, "surgeryDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "phoneNumber": "+380999999899", "nhsId": "nhs_id", "dateOfBirth": "1988-02-20", "boardingStatus": {"status": 0, "updateDateTime": {"$date": "2021-02-09T14:08:05.997Z"}}, "componentsData": {"billing": {"status": 1, "billingProviderName": "someName", "billingProviderId": "tempId", "diagnosis": {"order": 1, "icd10Code": "ICD", "description": "diagnosis description"}}}, "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}, "surgeryDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789d"}, "givenName": "manager", "familyName": "manager", "email": "<EMAIL>", "phoneNumber": "+380999999555", "boardingStatus": {"status": 0, "updateDateTime": {"$date": "2023-02-09T14:08:05.997Z"}}, "roles": [{"roleId": "Admin", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "Manager", "isActive": true}, {"roleId": "Admin", "resource": "deployment/5d386cc6ff885918d96edb2b", "userType": "Manager"}], "timezone": "UTC"}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789e"}, "givenName": "manager3", "familyName": "manager3", "email": "<EMAIL>", "phoneNumber": "+380999999556", "boardingStatus": {"status": 0, "updateDateTime": {"$date": "2023-02-09T14:08:05.997Z"}}, "roles": [{"roleId": "Admin", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "Manager", "isActive": true}], "timezone": "UTC"}, {"_id": {"$oid": "63ce44193d6527f1c3ceafe3"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "timezone": "UTC", "boardingStatus": {"status": 0, "updateDateTime": {"$date": "2023-02-09T14:08:05.997Z"}}, "roles": [{"roleId": "Manager", "resource": "organization/63b67ff40d4b124707c3d29b", "userType": "Manager", "isActive": true}]}, {"_id": {"$oid": "5e8f0c74b50aa9656c347890"}, "givenName": "managerrtm", "familyName": "managerrtm", "email": "<EMAIL>", "phoneNumber": "+380999999557", "roles": [{"roleId": "Clinician", "resource": "deployment/5d386cc6ff885918d96edb2b", "userType": "Manager", "isActive": true}], "timezone": "UTC"}, {"_id": {"$oid": "64de06d743c2b2d1a11edf41"}, "givenName": "managerrtm", "familyName": "managerrtm", "email": "<EMAIL>", "phoneNumber": "+380501111111", "roles": [{"roleId": "Clinician", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "Manager", "isActive": true}], "timezone": "UTC"}], "video_call": [{"_id": {"$oid": "5f0496ab82a630a9725336c2"}, "_cls": "MongoVideoCall", "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "managerId": {"$oid": "64de06d743c2b2d1a11edf41"}, "roomStatus": "completed", "startDateTime": {"$date": "2022-08-04T14:08:03.997Z"}, "endDateTime": {"$date": "2022-08-04T14:08:05.997Z"}, "updateDateTime": {"$date": "2022-08-04T14:08:03.997Z"}, "createDateTime": {"$date": "2022-08-04T14:08:03.997Z"}, "duration": {"$numberInt": "2"}, "status": "ANSWERED"}, {"_id": {"$oid": "5f0496ab82a630a9725336c1"}, "_cls": "MongoVideoCall", "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "managerId": {"$oid": "64de06d743c2b2d1a11edf41"}, "roomStatus": "completed", "startDateTime": {"$date": "2023-02-09T14:08:03.997Z"}, "endDateTime": {"$date": "2023-02-09T14:08:05.997Z"}, "updateDateTime": {"$date": "2023-02-09T14:08:03.997Z"}, "createDateTime": {"$date": "2023-02-09T14:08:03.997Z"}, "duration": {"$numberInt": "2"}, "status": "ANSWERED"}, {"_id": {"$oid": "5f0496ab82a630a9725336d1"}, "_cls": "MongoVideoCall", "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "managerId": {"$oid": "64de06d743c2b2d1a11edf41"}, "roomStatus": "completed", "startDateTime": {"$date": "2023-03-09T14:08:03.997Z"}, "endDateTime": {"$date": "2023-03-09T14:08:05.997Z"}, "updateDateTime": {"$date": "2023-03-09T14:08:03.997Z"}, "createDateTime": {"$date": "2023-03-09T14:08:03.997Z"}, "duration": {"$numberInt": "2"}, "status": "ANSWERED"}], "organization": [{"_id": {"$oid": "63b67ff40d4b124707c3d29b"}, "termAndConditionUrl": "https://goole.com", "privacyPolicyUrl": "https://goole.com", "eulaUrl": "https://goole.com", "name": "rpmdevbillingorganization", "status": "DRAFT", "enrollmentTarget": 0, "studyCompletionTarget": 0, "viewType": "RPM", "dashboardId": "OrganizationOverview", "features": {"newRolesSupport": true}, "deploymentIds": ["5d386cc6ff885918d96edb2c"], "targetConsented": 0}], "weight": [{"_id": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "moduleId": "Weight", "moduleConfigId": {"$oid": "5e94b2007773091c9a592660"}, "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "version": 0, "deviceName": "iOS", "startDateTime": {"$date": "2020-10-07T17:12:00.000Z"}, "createDateTime": {"$date": "2020-10-07T14:34:24.851Z"}, "submitterId": {"$oid": "5e8f0c74b50aa9656c34789c"}, "value": 99}], "billing_profile_log_provider": [{"_id": {"$oid": "5f7dd1f0e03d4a97e8007a94"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "createDateTime": {"$date": "2023-02-28T14:08:03.997Z"}, "billingProviderName": "New Billing Provider", "billingProviderId": "New Billing Provider Id"}, {"_id": {"$oid": "5f7dd1f0e03d4a97e8007a93"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "createDateTime": {"$date": "2023-01-01T14:08:03.997Z"}, "billingProviderName": "Old Billing Provider", "billingProviderId": "Old Billing Provider Id"}], "billing_profile_log_diagnosis": [{"_id": {"$oid": "64c1031e0be76f924d301cc7"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "createDateTime": {"$date": "2023-01-01T14:08:03.997Z"}, "order": 1, "description": "diagnosis description", "icd10Code": "ICD"}, {"_id": {"$oid": "64c1031e0be76f924d311cc7"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789c"}, "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "createDateTime": {"$date": "2023-01-01T14:08:03.997Z"}, "order": 1, "description": "diagnosis description", "icd10Code": "ICD"}], "billing_monitoring_log": [{"_id": {"$oid": "648a5480e8c83e2af28b7aa0"}, "originalLogId": {"$oid": "648a5480e8c83e2af28b7aa0"}, "_cls": "MongoBillingMonitoringLogDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "createdById": {"$oid": "5e8f0c74b50aa9656c34789d"}, "lastModifiedById": {"$oid": "5e8f0c74b50aa9656c34789d"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "timeTrackingId": {"$oid": "648a5480e8c83e2af28b7a9f"}, "status": 0, "action": "hu_billing_log_action_1", "timeSpent": 31, "initialCreateDateTime": {"$date": "2023-06-01T00:12:00.039Z"}, "startDateTime": {"$date": "2023-06-01T00:12:00.039Z"}, "endDateTime": {"$date": "2023-06-01T00:12:31.039Z"}, "createDateTime": {"$date": "2023-06-15T00:00:00.000Z"}, "updateDateTime": {"$date": "2023-06-15T00:00:00.000Z"}}, {"_id": {"$oid": "648a5480e8c83e2af28b7aa1"}, "_cls": "MongoBillingMonitoringLogDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "createdById": {"$oid": "5e8f0c74b50aa9656c34789d"}, "lastModifiedById": {"$oid": "5e8f0c74b50aa9656c34789d"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789d"}, "timeTrackingId": {"$oid": "648a5480e8c83e2af28b7a9f"}, "status": 1, "action": "hu_billing_log_action_1", "timeSpent": 31, "initialCreateDateTime": {"$date": "2023-06-01T00:12:00.039Z"}, "startDateTime": {"$date": "2023-06-01T00:12:00.039Z"}, "endDateTime": {"$date": "2023-06-01T00:12:31.039Z"}, "createDateTime": {"$date": "2023-06-01T00:12:00.039Z"}, "updateDateTime": {"$date": "2023-06-01T00:12:00.039Z"}}, {"_id": {"$oid": "648a5480e8c83e2af28b7aa2"}, "_cls": "MongoBillingMonitoringLogDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "createdById": {"$oid": "5e8f0c74b50aa9656c34789d"}, "lastModifiedById": {"$oid": "5e8f0c74b50aa9656c34789d"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789d"}, "timeTrackingId": {"$oid": "648a5480e8c83e2af28b7a9f"}, "status": 1, "action": "hu_billing_log_action_1", "timeSpent": 31, "initialCreateDateTime": {"$date": "2023-06-01T00:12:00.039Z"}, "startDateTime": {"$date": "2023-06-01T00:12:00.039Z"}, "endDateTime": {"$date": "2023-06-01T00:12:31.039Z"}, "createDateTime": {"$date": "2023-06-02T00:12:00.039Z"}, "updateDateTime": {"$date": "2023-06-02T00:12:00.039Z"}}, {"_id": {"$oid": "648a5480e8c83e2af28b7aa3"}, "_cls": "MongoBillingMonitoringLogDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "createdById": {"$oid": "5e8f0c74b50aa9656c34789d"}, "lastModifiedById": {"$oid": "5e8f0c74b50aa9656c34789d"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789d"}, "timeTrackingId": {"$oid": "648a5480e8c83e2af28b7a9f"}, "status": 0, "action": "hu_billing_log_action_1", "timeSpent": 31, "initialCreateDateTime": {"$date": "2023-06-01T00:12:00.039Z"}, "startDateTime": {"$date": "2023-06-01T00:12:00.039Z"}, "endDateTime": {"$date": "2023-06-01T00:12:31.039Z"}, "createDateTime": {"$date": "2023-06-03T00:12:00.039Z"}, "updateDateTime": {"$date": "2023-06-03T00:12:00.039Z"}}, {"_id": {"$oid": "648a5480e8c83e2af28b7aa4"}, "_cls": "MongoBillingMonitoringLogDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "createdById": {"$oid": "5e8f0c74b50aa9656c34789d"}, "lastModifiedById": {"$oid": "5e8f0c74b50aa9656c34789d"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789e"}, "timeTrackingId": {"$oid": "648a5480e8c83e2af28b7a9f"}, "status": 1, "action": "hu_billing_log_action_1", "timeSpent": 31, "initialCreateDateTime": {"$date": "2023-06-02T00:12:00.039Z"}, "startDateTime": {"$date": "2023-06-01T00:12:00.039Z"}, "endDateTime": {"$date": "2023-06-01T00:12:31.039Z"}, "createDateTime": {"$date": "2023-06-02T00:12:00.039Z"}, "updateDateTime": {"$date": "2023-06-02T00:12:00.039Z"}}, {"_id": {"$oid": "648a5480e8c83e2af28b7aa5"}, "_cls": "MongoBillingMonitoringLogDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "createdById": {"$oid": "5e8f0c74b50aa9656c34789d"}, "lastModifiedById": {"$oid": "5e8f0c74b50aa9656c34789d"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789e"}, "timeTrackingId": {"$oid": "648a5480e8c83e2af28b7a9f"}, "status": 0, "action": "hu_billing_log_action_1", "timeSpent": 31, "initialCreateDateTime": {"$date": "2023-06-02T00:12:00.039Z"}, "startDateTime": {"$date": "2023-06-01T00:12:00.039Z"}, "endDateTime": {"$date": "2023-06-01T00:12:31.039Z"}, "createDateTime": {"$date": "2023-06-02T00:15:00.039Z"}, "updateDateTime": {"$date": "2023-06-02T00:15:00.039Z"}}], "billing_remote_time_tracking": [{"_id": {"$oid": "654128fd83a863ae18af9d45"}, "_cls": "MongoBillingRemoteTimeTrackingDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "clinicianId": {"$oid": "5e8f0c74b50aa9656c34789d"}, "startDateTime": {"$date": "2023-08-22T14:50:22.039Z"}, "endDateTime": {"$date": "2023-08-22T14:50:59.039Z"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "effectiveStartDateTime": {"$date": "2023-08-22T14:50:22.039Z"}, "effectiveEndDateTime": {"$date": "2023-08-22T14:50:59.039Z"}, "effectiveDuration": 37, "createDateTime": {"$date": "2023-10-31T16:19:09.106Z"}}, {"_id": {"$oid": "654128fd83a863ae18af9d46"}, "_cls": "MongoBillingRemoteTimeTrackingDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "clinicianId": {"$oid": "5e8f0c74b50aa9656c34789d"}, "startDateTime": {"$date": "2023-09-22T14:51:10.039Z"}, "endDateTime": {"$date": "2023-09-22T14:51:50.039Z"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "effectiveStartDateTime": {"$date": "2023-09-22T14:51:10.039Z"}, "effectiveEndDateTime": {"$date": "2023-09-22T14:51:50.039Z"}, "effectiveDuration": 40, "createDateTime": {"$date": "2023-10-31T16:20:09.106Z"}}, {"_id": {"$oid": "654128fd83a863ae18af9d47"}, "_cls": "MongoBillingRemoteTimeTrackingDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "clinicianId": {"$oid": "5e8f0c74b50aa9656c34789e"}, "startDateTime": {"$date": "2023-09-14T14:51:10.039Z"}, "endDateTime": {"$date": "2023-09-14T14:51:35.039Z"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789c"}, "effectiveStartDateTime": {"$date": "2023-09-14T14:51:10.039Z"}, "effectiveEndDateTime": {"$date": "2023-09-14T14:51:35.039Z"}, "effectiveDuration": 25, "createDateTime": {"$date": "2023-09-25T16:20:09.106Z"}}]}