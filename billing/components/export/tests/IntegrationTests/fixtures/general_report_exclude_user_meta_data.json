{"5e8f0c74b50aa9656c34789b": {"GeneralBilling": [{"userId": "5e8f0c74b50aa9656c34789b", "deploymentId": "5d386cc6ff885918d96edb2c", "moduleId": "billing", "user": {"givenName": "test", "familyName": "test", "gender": "", "biologicalSex": "", "dateOfBirth": "1988-02-20", "email": "<EMAIL>", "phoneNumber": "+380999999999", "primaryAddress": "", "timezone": "UTC", "language": ""}, "billing": {"primaryInsuranceCarrier": "", "secondaryInsuranceCarrier": "", "primaryInsuranceCarrierPayerId": "", "secondaryInsuranceCarrierPayerId": "", "billingProviderName": "someName", "diagnosis": {"order": 1, "icd10Code": "ICD", "description": "diagnosis description"}, "billingFirstReading": "2022-06-01"}, "report": {"numberOfSubmissions": 1, "numberOfSubmissionDays": 1, "cpt99453": 0, "cpt99454": 0, "cpt99457": 0, "cpt99458": 0, "cpt98975": 0, "cpt98976": 0, "cpt98980": 0, "cpt98981": 0, "monitoringTimeMins": 0, "numberOfCalls": 0}}]}, "5e8f0c74b50aa9656c34789c": {"GeneralBilling": [{"userId": "5e8f0c74b50aa9656c34789c", "deploymentId": "5d386cc6ff885918d96edb2c", "moduleId": "billing", "user": {"givenName": "test", "familyName": "test", "gender": "", "biologicalSex": "", "dateOfBirth": "1988-02-20", "email": "<EMAIL>", "phoneNumber": "+380999999899", "primaryAddress": "", "timezone": "UTC", "language": ""}, "billing": {"primaryInsuranceCarrier": "", "secondaryInsuranceCarrier": "", "primaryInsuranceCarrierPayerId": "", "secondaryInsuranceCarrierPayerId": "", "billingProviderName": "someName", "diagnosis": {"order": 1, "icd10Code": "ICD", "description": "diagnosis description"}, "billingFirstReading": ""}, "report": {"numberOfSubmissions": 0, "numberOfSubmissionDays": 0, "cpt99453": 0, "cpt99454": 0, "cpt99457": 0, "cpt99458": 0, "cpt98975": 0, "cpt98976": 0, "cpt98980": 0, "cpt98981": 0, "monitoringTimeMins": 0, "numberOfCalls": 0}}]}}