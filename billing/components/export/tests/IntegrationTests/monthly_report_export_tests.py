import json
from datetime import datetime
from pathlib import Path
from unittest.mock import patch

from freezegun import freeze_time

from billing.components.billing_integration.tasks import (
    execute_integration_billing,
)
from billing.components.billing_integration.tests.IntegrationTests.sample_data import (
    sample_generic_ehr,
    sample_integration,
)
from billing.components.core.component import BillingComponent
from billing.components.core.helpers.module_result_helpers import (
    PrimitiveSources,
)
from billing.components.core.models import BillingSubmission
from billing.components.core.router.billing_requests import (
    CreateBillingRequestObject,
)
from billing.components.export.component import BillingExportComponent
from billing.components.export.models.billing_models import (
    BillingMonthlyReportCPTCodes,
    BillingMonthlyReportUserBilling,
    BillingProvider,
    TimeTrackingCPTCodeDetails,
)
from billing.components.export.use_case.billing_monthly_export import (
    BillingMonthlyEHRExportableUseCase,
    BillingMonthlyExportableUseCase,
)
from billing.tests.billing_test_case import BillingTestCase
from billing.tests.test_helpers import (
    equal_csv_content,
    set_submission_calculation_type,
    simplify_export_response_keys,
)
from huma_plugins.components.export.dtos.export_models import ExportParameters
from huma_plugins.components.export.tests.IntegrationTests import enabled_modules
from huma_plugins.components.export.use_case.export_request_objects import (
    ExportRequestObject,
)
from huma_plugins.components.extended_authorization.component import (
    ExtendedAuthorizationComponent,
)
from huma_plugins.components.extended_authorization.component_options import (
    authorization_options,
)
from huma_plugins.components.extended_module_result.component import (
    ExtendedModuleResultComponent,
)
from huma_plugins.components.integration.dtos.generic_ehr import GenericEHRDTO
from huma_plugins.components.online_offline_call.component import (
    OnlineOfflineCallComponent,
)
from sdk.auth.component import AuthComponent
from sdk.deployment.component import DeploymentComponent
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.modules import BloodPressureModule, StepModule
from sdk.module_result.modules.blood_pressure import BloodPressureDTO
from sdk.module_result.modules.step import StepDTO
from sdk.organization.component import OrganizationComponent
from sdk.storage.component import StorageComponentV1
from sdk.versioning.component import VersionComponent

DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
RTM_DEPLOYMENT_ID = "5d386cc6ff885918d96edb2b"
MANAGER_ID = "5e8f0c74b50aa9656c34789d"
CLINICIAN_ID = "64de06d743c2b2d1a11edf41"
RTM_CLINICIAN_ID = "5e8f0c74b50aa9656c347890"
USER_ID = "5e8f0c74b50aa9656c34789b"
USER_ID_2 = "5e8f0c74b50aa9656c34789c"
RTM_USER_ID = "5b8f0c74b50aa9656c34789b"
RTM_MANAGER_ID = "5e8f0c74b50aa9656c347890"

USE_CASE_PATH = "billing.components.export.use_case.billing_monthly_export"
PATTERN_FILE_NAME_DATE = r"MonthlyBilling_\d{4}-\d{2}-\d{2}_\d{4}-\d{2}-\d{2}"
EXPORT_TYPE = BillingMonthlyExportableUseCase.MODULE_NAME
CPT_SECTION = BillingMonthlyExportableUseCase.BillingMonthlyReportSections.CPT_CODES_DETAILS
BILLING_DATA_SECTION = BillingMonthlyExportableUseCase.BillingMonthlyReportSections.USER_BILLING_DATA
CALENDAR_CALC_DATES = ("2024-01-03T00:00:00.039Z", "2024-03-31T23:59:59.99Z")
DT_FORMAT = "%Y-%m-%dT%H:%M:%S.%fZ"


class MonthlyReportExportTestCase(BillingTestCase):
    components = [
        AuthComponent(),
        ExtendedAuthorizationComponent(
            component_options=authorization_options,
        ),
        BillingComponent(),
        DeploymentComponent(),
        OrganizationComponent(),
        ExtendedModuleResultComponent(
            additional_modules=[m() for m in enabled_modules],
        ),
        BillingExportComponent(),
        StorageComponentV1(),
        OnlineOfflineCallComponent(),
        VersionComponent(server_version="1.0.0", api_version="1.0.0"),
    ]
    fixtures = [
        Path(__file__).parent.joinpath("fixtures/deployments_dump.json"),
    ]

    def setUp(self):
        super().setUp()
        self.export_url = f"/api/extensions/v1/export/deployment/{DEPLOYMENT_ID}"
        self.user_export_url = f"/api/extensions/v1/export/user/{USER_ID}"
        self.add_time_tracking_url = "/api/extensions/v1/billing"
        self.maxDiff = None

    def tearDown(self):
        super().tearDown()
        BillingSubmission.objects.all().delete()

    @freeze_time("2022-06-30T10:00:00.000Z")
    def test_export_monthly_report_in_json_format(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }
        self._submit_sample_module_results()
        rsp = self.send_export_request(payload, replace_keys=False)
        self._assert_json_content_is_equal(rsp, "monthly_report_json_format_sample.json")

    @freeze_time("2022-06-30T10:00:00.000Z")
    def test_export_monthly_report_to_financial_report(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }
        self._submit_sample_module_results()
        rsp = self.send_export_request(payload, replace_keys=False)
        data = rsp.data.decode("utf-8")
        billing = json.loads(data)

        generic_ehr = GenericEHRDTO.from_dict(sample_generic_ehr)
        sample_integration.generic_ehr = generic_ehr

        execute_integration_billing(
            sample_integration.to_dict(include_none=False),
            billing,
            sample_integration.deploymentIds[0],
        )

    def test_export_monthly_report_with_cpt_status_99457_1(self):
        """
        99457-1 is a CPT code that is used for the first 20 minutes of time tracking
        """
        self._submit_sample_module_results()
        for i in range(20, 41):
            self._add_time_tracking(f"2023-02-06T14:{i}:01.039Z", f"2023-02-06T14:{i}:59.039Z")
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-02-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-02-28T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }
        rsp = self.send_export_request(payload, replace_keys=False)
        json_data = json.loads(rsp.data)
        self.assertEqual(
            1,
            json_data[USER_ID][
                f"{BillingMonthlyExportableUseCase.MODULE_NAME}_{payload[ExportParameters.FROM_DATE][:10]}_{payload[ExportParameters.TO_DATE][:10]}"
            ][0][BillingMonthlyExportableUseCase.BillingMonthlyReportSections.CPT_CODES_DETAILS][
                BillingMonthlyReportCPTCodes.CPT_99457
            ][
                TimeTrackingCPTCodeDetails.TOTAL_BILLABLE
            ],
        )
        self.assertEqual(
            20,
            json_data[USER_ID][
                f"{BillingMonthlyExportableUseCase.MODULE_NAME}_{payload[ExportParameters.FROM_DATE][:10]}_{payload[ExportParameters.TO_DATE][:10]}"
            ][0][BillingMonthlyExportableUseCase.BillingMonthlyReportSections.CPT_CODES_DETAILS][
                BillingMonthlyReportCPTCodes.MONITORING_TIME_MINS
            ],
        )
        self.assertEqual(
            1,
            json_data[USER_ID][
                f"{BillingMonthlyExportableUseCase.MODULE_NAME}_{payload[ExportParameters.FROM_DATE][:10]}_{payload[ExportParameters.TO_DATE][:10]}"
            ][0][BillingMonthlyExportableUseCase.BillingMonthlyReportSections.CPT_CODES_DETAILS][
                BillingMonthlyReportCPTCodes.NUMBER_OF_CALLS
            ],
        )

    def test_export_monthly_report_with_cpt_status_99458_1(self):
        self._submit_sample_module_results()
        for i in range(10, 52):
            self._add_time_tracking(f"2023-02-06T14:{i}:01.039Z", f"2023-02-06T14:{i}:59.039Z")
        self._add_time_tracking("2023-03-01T14:01:01.039Z", "2023-03-01T14:01:59.039Z")
        self._add_time_tracking("2023-01-31T14:01:01.039Z", "2023-01-31T14:01:59.039Z")
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-02-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-02-28T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }
        rsp = self.send_export_request(payload, replace_keys=False)
        json_data = json.loads(rsp.data)
        self.assertEqual(
            1,
            json_data[USER_ID][
                f"{BillingMonthlyExportableUseCase.MODULE_NAME}_{payload[ExportParameters.FROM_DATE][:10]}_{payload[ExportParameters.TO_DATE][:10]}"
            ][0][BillingMonthlyExportableUseCase.BillingMonthlyReportSections.CPT_CODES_DETAILS][
                BillingMonthlyReportCPTCodes.CPT_99457
            ][
                TimeTrackingCPTCodeDetails.TOTAL_BILLABLE
            ],
        )
        self.assertEqual(
            1,
            json_data[USER_ID][
                f"{BillingMonthlyExportableUseCase.MODULE_NAME}_{payload[ExportParameters.FROM_DATE][:10]}_{payload[ExportParameters.TO_DATE][:10]}"
            ][0][BillingMonthlyExportableUseCase.BillingMonthlyReportSections.CPT_CODES_DETAILS][
                BillingMonthlyReportCPTCodes.CPT_99458
            ][
                TimeTrackingCPTCodeDetails.TOTAL_BILLABLE
            ],
        )
        self.assertEqual(
            40,
            json_data[USER_ID][
                f"{BillingMonthlyExportableUseCase.MODULE_NAME}_{payload[ExportParameters.FROM_DATE][:10]}_{payload[ExportParameters.TO_DATE][:10]}"
            ][0][BillingMonthlyExportableUseCase.BillingMonthlyReportSections.CPT_CODES_DETAILS][
                BillingMonthlyReportCPTCodes.MONITORING_TIME_MINS
            ],
        )
        self.assertEqual(
            1,
            json_data[USER_ID][
                f"{BillingMonthlyExportableUseCase.MODULE_NAME}_{payload[ExportParameters.FROM_DATE][:10]}_{payload[ExportParameters.TO_DATE][:10]}"
            ][0][BillingMonthlyExportableUseCase.BillingMonthlyReportSections.CPT_CODES_DETAILS][
                BillingMonthlyReportCPTCodes.NUMBER_OF_CALLS
            ],
        )

    def test_export_monthly_report_with_cpt_status_99458_2x(self):
        self._submit_sample_module_results()

        for i in range(10, 31):
            for j in range(10, 13):
                self._add_time_tracking(f"2023-02-06T{j}:{i}:01.039Z", f"2023-02-06T{j}:{i}:59.039Z")
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-02-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-02-28T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }
        report = self.send_export_request(payload)
        cpt_details = report[USER_ID][EXPORT_TYPE][0][CPT_SECTION]
        self.assertEqual(
            1,
            cpt_details[BillingMonthlyReportCPTCodes.CPT_99457][TimeTrackingCPTCodeDetails.TOTAL_BILLABLE],
        )
        self.assertEqual(
            2,
            cpt_details[BillingMonthlyReportCPTCodes.CPT_99458][TimeTrackingCPTCodeDetails.TOTAL_BILLABLE],
        )
        self.assertEqual(60, cpt_details[BillingMonthlyReportCPTCodes.MONITORING_TIME_MINS])
        self.assertEqual(1, cpt_details[BillingMonthlyReportCPTCodes.NUMBER_OF_CALLS])

    def test_export_monthly_report_when_date_of_service_is_not_met(self):
        self._submit_sample_module_results()

        for i in range(20, 41):
            self._add_time_tracking(f"2023-02-06T14:{i}:01.039Z", f"2023-02-06T14:{i}:59.039Z")
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-02-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-02-26T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }
        rsp = self.send_export_request(payload, replace_keys=False)
        json_data = json.loads(rsp.data)
        self.assertFalse(json_data)

    def test_export_monthly_report_when_date_of_service_is_not_met_for_second_period(
        self,
    ):
        self._submit_sample_module_results()

        for i in range(20, 41):
            self._add_time_tracking(f"2023-02-06T14:{i}:01.039Z", f"2023-02-06T14:{i}:59.039Z")
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-02-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-03-15T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }
        report = self.send_export_request(payload)
        cpt_details = report[USER_ID][EXPORT_TYPE][0][CPT_SECTION]
        self.assertEqual(
            1,
            cpt_details[BillingMonthlyReportCPTCodes.CPT_99457][TimeTrackingCPTCodeDetails.TOTAL_BILLABLE],
        )
        self.assertEqual(20, cpt_details[BillingMonthlyReportCPTCodes.MONITORING_TIME_MINS])
        self.assertEqual(1, cpt_details[BillingMonthlyReportCPTCodes.NUMBER_OF_CALLS])
        self.assertEqual(1, len(report[USER_ID][EXPORT_TYPE]))

    @freeze_time("2022-06-30T10:00:00.000Z")
    def test_export_monthly_report_WHEN_there_is_no_data(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }
        rsp = self.send_export_request(payload, replace_keys=False)
        self.assertEqual({}, json.loads(rsp.data))

    @freeze_time("2022-08-07T10:00:00.000Z")
    def test_export_monthly_report_in_JSON_format_in_future(self):
        """SDK sets future submissions start date as today while sending RPM an event"""
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.FROM_DATE: "2022-07-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2022-08-31T00:00:00.039Z",
        }

        for i in range(20, 41):
            self._add_time_tracking(f"2022-08-05T14:{i}:01.039Z", f"2022-08-05T14:{i}:59.039Z")
        self._submit_sample_module_results(start_date="2022-07-01T00:00:00.039Z")
        self._submit_sample_module_results(start_date="2022-08-05T00:00:00.039Z")
        self._submit_sample_module_results(start_date="2022-08-07T10:01:00.000Z")

        rsp = self.send_export_request(payload, replace_keys=False)
        self._assert_json_content_is_equal(rsp, "monthly_report_json_format_in_future.json")

    @freeze_time("2022-08-07T10:00:00.000Z")
    def test_export_monthly_report_in_csv_format(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.CSV.value,
            ExportParameters.FROM_DATE: "2022-04-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2022-08-31T00:00:00.039Z",
        }
        self._add_time_tracking("2022-06-06T14:12:01.039Z", "2022-06-06T14:12:59.039Z")
        self._submit_sample_module_results(start_date="2022-07-11T00:00:00.039Z")
        self._submit_sample_module_results(start_date="2022-07-12T00:00:00.039Z")
        self._submit_sample_module_results(user_id=USER_ID_2, start_date="2022-07-16T00:00:00.039Z")
        rsp = self.send_export_request(payload, replace_keys=False)
        self._assert_csv_content_is_equal(rsp, "monthly_report_csv_format_sample.csv")

    @freeze_time("2022-06-30T10:00:00.000Z")
    def test_export_monthly_report_in_json_format_deidentified_data(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.DEIDENTIFIED: True,
        }
        self._submit_sample_module_results()
        rsp = self.send_export_request(payload, replace_keys=False)
        self._assert_json_content_is_equal(rsp, "monthly_json_format_deidentified_sample.json")

    @freeze_time("2022-06-30T10:00:00.000Z")
    def test_export_monthly_submission_report_WHEN_metadata_excluded(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.INCLUDE_USER_META_DATA: False,
        }
        self._submit_sample_module_results()
        rsp = self.send_export_request(payload, replace_keys=False)
        self._assert_json_content_is_equal(rsp, "monthly_report_export_exclude_user_meta_data.json")

    @freeze_time("2022-05-30T10:00:00.000Z")
    def test_export_monthly_submission_report_WHEN_two_cycles_are_in_one_month(self):
        @freeze_time("2022-05-31T20:00:00.000Z")
        def _submit_submission_in_next_cycle():
            self._submit_sample_module_results(start_date="2022-05-31T20:00:00.039Z")

        @freeze_time("2022-04-30T10:00:00.000Z")
        def _submit_submission_in_previous_cycles():
            self._submit_sample_module_results(start_date="2022-04-02T00:00:00.039Z")
            for i in range(10, 28):
                self._submit_sample_module_results(start_date=f"2022-04-{i}T20:00:00.039Z")

        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.INCLUDE_USER_META_DATA: False,
            ExportParameters.FROM_DATE: "2022-04-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2022-05-31T00:00:00.039Z",
        }

        _submit_submission_in_previous_cycles()

        for i in range(13, 28):
            self._submit_sample_module_results(start_date=f"2022-05-{i}T20:00:00.039Z")

        _submit_submission_in_next_cycle()

        report = self.send_export_request(payload)
        cpt_details = report[USER_ID][EXPORT_TYPE][0][CPT_SECTION]
        self.assertEqual("2022-04-02", cpt_details["cpt99453"]["billingStartDate"])
        self.assertEqual("2022-05-01", cpt_details["cpt99453"]["billingEndDate"])
        self.assertEqual(19, cpt_details["cpt99453"]["countOfSubmissions"])
        self.assertEqual(19, cpt_details["cpt99453"]["countOfSubmissionDays"])
        self.assertEqual("2022-04-24", cpt_details["cpt99453"]["earliestBillingDate"])
        self.assertEqual("2022-04-02", cpt_details["cpt99454"]["billingStartDate_0"])
        self.assertEqual("2022-05-01", cpt_details["cpt99454"]["billingEndDate_0"])
        self.assertEqual(19, cpt_details["cpt99454"]["countOfSubmissions_0"])
        self.assertEqual(19, cpt_details["cpt99454"]["countOfSubmissionDays_0"])
        self.assertEqual("2022-04-24", cpt_details["cpt99454"]["earliestBillingDate_0"])
        self.assertEqual("2022-05-02", cpt_details["cpt99454"]["billingStartDate_1"])
        self.assertEqual("2022-05-31", cpt_details["cpt99454"]["billingEndDate_1"])
        self.assertEqual(16, cpt_details["cpt99454"]["countOfSubmissions_1"])
        self.assertEqual(16, cpt_details["cpt99454"]["countOfSubmissionDays_1"])
        self.assertEqual("2022-05-31", cpt_details["cpt99454"]["earliestBillingDate_1"])

    @freeze_time("2024-01-31T10:00:00.000Z")
    def test_export_monthly_submission_report_WHEN_multiple_non_billable_periods(self):
        @freeze_time("2023-10-25T10:00:00.000Z")
        def _submit_submission_in_first_cycles():
            for i in range(12, 16):
                self._submit_sample_module_results(start_date=f"2023-10-{i}T20:00:00.039Z")

        @freeze_time("2023-11-27T10:00:00.000Z")
        def _submit_submission_in_second_cycles():
            for i in range(13, 17):
                self._submit_sample_module_results(start_date=f"2023-11-{i}T20:00:00.039Z")

        @freeze_time("2023-12-30T10:00:00.000Z")
        def _submit_submission_in_third_cycles():
            for i in range(14, 30):
                self._submit_sample_module_results(start_date=f"2023-12-{i}T20:00:00.039Z")

        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.INCLUDE_USER_META_DATA: False,
            ExportParameters.FROM_DATE: "2023-11-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2024-01-30T00:00:00.039Z",
        }
        _submit_submission_in_first_cycles()
        _submit_submission_in_second_cycles()
        _submit_submission_in_third_cycles()

        rsp = self.send_export_request(payload)
        user_data = rsp[USER_ID][EXPORT_TYPE]
        self.assertEqual(1, len(user_data))

        cpt_data = user_data[0][CPT_SECTION]
        self.assertEqual("2023-12-11", cpt_data["cpt99453"]["billingStartDate"])
        self.assertEqual("2024-01-09", cpt_data["cpt99453"]["billingEndDate"])
        self.assertEqual("2023-12-29", cpt_data["cpt99453"]["earliestBillingDate"])

        self.assertEqual("2023-12-11", cpt_data["cpt99454"]["billingStartDate_0"])
        self.assertEqual("2024-01-09", cpt_data["cpt99454"]["billingEndDate_0"])
        self.assertEqual("2023-12-29", cpt_data["cpt99454"]["earliestBillingDate_0"])

    @freeze_time("2024-01-31T10:00:00.000Z")
    def test_export_monthly_submission_report_WHEN_there_are_multiple_billable_periods(
        self,
    ):
        @freeze_time("2023-10-29T10:00:00.000Z")
        def _submit_submission_in_first_cycles():
            for i in range(12, 28):
                self._submit_sample_module_results(start_date=f"2023-10-{i}T20:00:00.039Z")

        @freeze_time("2023-11-27T10:00:00.000Z")
        def _submit_submission_in_second_cycles():
            for i in range(13, 17):
                self._submit_sample_module_results(start_date=f"2023-11-{i}T20:00:00.039Z")

        @freeze_time("2023-12-30T10:00:00.000Z")
        def _submit_submission_in_third_cycles():
            for i in range(14, 30):
                self._submit_sample_module_results(start_date=f"2023-12-{i}T20:00:00.039Z")

        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.INCLUDE_USER_META_DATA: False,
            ExportParameters.FROM_DATE: "2023-11-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2024-01-30T00:00:00.039Z",
        }
        _submit_submission_in_first_cycles()
        _submit_submission_in_second_cycles()
        _submit_submission_in_third_cycles()

        rsp = self.send_export_request(payload)
        user_data = rsp[USER_ID][EXPORT_TYPE]

        self.assertEqual(3, len(user_data))
        cpt_data_1 = user_data[0][CPT_SECTION]
        cpt_data_2 = user_data[1][CPT_SECTION]
        cpt_data_3 = user_data[2][CPT_SECTION]

        self.assertEqual("2023-10-12", cpt_data_1["cpt99453"]["billingStartDate"])
        self.assertEqual("2023-11-10", cpt_data_1["cpt99453"]["billingEndDate"])
        self.assertEqual("2023-10-27", cpt_data_1["cpt99453"]["earliestBillingDate"])
        self.assertEqual(16, cpt_data_1["cpt99453"]["countOfSubmissions"])
        self.assertEqual("2023-10-12", cpt_data_1["cpt99454"]["billingStartDate_0"])
        self.assertEqual("2023-11-10", cpt_data_1["cpt99454"]["billingEndDate_0"])
        self.assertEqual("2023-10-27", cpt_data_1["cpt99454"]["earliestBillingDate_0"])
        self.assertEqual(16, cpt_data_1["cpt99454"]["countOfSubmissions_0"])

        self.assertEqual("", cpt_data_2["cpt99453"]["billingStartDate"])
        self.assertEqual("2023-11-11", cpt_data_2["cpt99454"]["billingStartDate_0"])
        self.assertEqual("2023-12-10", cpt_data_2["cpt99454"]["billingEndDate_0"])
        self.assertEqual("", cpt_data_2["cpt99454"]["earliestBillingDate_0"])
        self.assertEqual(4, cpt_data_2["cpt99454"]["countOfSubmissions_0"])

        self.assertEqual("", cpt_data_3["cpt99453"]["billingStartDate"])
        self.assertEqual("2023-12-11", cpt_data_3["cpt99454"]["billingStartDate_0"])
        self.assertEqual("2024-01-09", cpt_data_3["cpt99454"]["billingEndDate_0"])
        self.assertEqual("2023-12-29", cpt_data_3["cpt99454"]["earliestBillingDate_0"])
        self.assertEqual(16, cpt_data_3["cpt99454"]["countOfSubmissions_0"])

    @freeze_time("2024-01-31T10:00:00.000Z")
    def test_export_monthly_submission_report_WHEN_there_are_edge_counted_submissions_in_periods(self):
        @freeze_time("2023-09-29T10:00:00.000Z")
        def _submit_submission_in_first_cycles():
            # 14 submission days
            for i in range(10, 24):
                self._submit_sample_module_results(start_date=f"2023-09-{i}T20:00:00.039Z")

        @freeze_time("2023-10-27T10:00:00.000Z")
        def _submit_submission_in_second_cycles():
            # 15 submission days
            for i in range(11, 26):
                self._submit_sample_module_results(start_date=f"2023-10-{i}T20:00:00.039Z")

        @freeze_time("2023-11-30T10:00:00.000Z")
        def _submit_submission_in_third_cycles():
            # 16 submission days
            for i in range(12, 28):
                self._submit_sample_module_results(start_date=f"2023-11-{i}T20:00:00.039Z")

        @freeze_time("2023-12-30T10:00:00.000Z")
        def _submit_submission_in_fourth_cycles():
            # 17 submission days
            for i in range(13, 30):
                self._submit_sample_module_results(start_date=f"2023-12-{i}T20:00:00.039Z")

        _submit_submission_in_first_cycles()
        _submit_submission_in_second_cycles()
        _submit_submission_in_third_cycles()
        _submit_submission_in_fourth_cycles()

        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.INCLUDE_USER_META_DATA: False,
            ExportParameters.FROM_DATE: "2023-09-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2024-01-30T00:00:00.039Z",
        }

        rsp = self.send_export_request(payload)
        user_data = rsp[USER_ID][EXPORT_TYPE]
        self.assertEqual(2, len(user_data))

        cpt_data_for_period_1 = user_data[0][CPT_SECTION]
        cpt_data_for_period_2 = user_data[1][CPT_SECTION]

        self.assertEqual(16, cpt_data_for_period_1["cpt99454"]["countOfSubmissions_0"])
        self.assertEqual(17, cpt_data_for_period_2["cpt99454"]["countOfSubmissions_0"])

    @freeze_time("2022-06-07T10:00:00.000Z")
    def test_export_monthly_submission_report_WHEN_two_periods_have_record(self):
        @freeze_time("2022-04-30T10:00:00.000Z")
        def _submit_submission_in_previous_cycles():
            self._submit_sample_module_results(start_date="2022-04-10T00:00:00.039Z")
            for i in range(10, 28):
                self._submit_sample_module_results(start_date=f"2022-04-{i}T20:00:00.039Z")

        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.INCLUDE_USER_META_DATA: False,
            ExportParameters.FROM_DATE: "2022-04-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2022-06-30T00:00:00.039Z",
        }

        _submit_submission_in_previous_cycles()

        for i in range(13, 29):
            self._submit_sample_module_results(start_date=f"2022-05-{i}T00:00:00.039Z")

        report = self.send_export_request(payload)
        cpt_details_1 = report[USER_ID][EXPORT_TYPE][0][CPT_SECTION]
        cpt_details_2 = report[USER_ID][EXPORT_TYPE][1][CPT_SECTION]
        self.assertEqual("2022-04-10", cpt_details_1["cpt99453"]["billingStartDate"])
        self.assertEqual("2022-05-09", cpt_details_1["cpt99453"]["billingEndDate"])
        self.assertEqual(19, cpt_details_1["cpt99453"]["countOfSubmissions"])
        self.assertEqual(18, cpt_details_1["cpt99453"]["countOfSubmissionDays"])
        self.assertEqual("2022-04-25", cpt_details_1["cpt99453"]["earliestBillingDate"])
        self.assertEqual(
            {
                "billingStartDate_0": "2022-04-10",
                "billingEndDate_0": "2022-05-09",
                "countOfSubmissions_0": 19,
                "countOfSubmissionDays_0": 18,
                "earliestBillingDate_0": "2022-04-25",
                "billingStartDate_1": "",
                "billingEndDate_1": "",
                "countOfSubmissions_1": "",
                "countOfSubmissionDays_1": "",
                "earliestBillingDate_1": "",
            },
            cpt_details_1["cpt99454"],
        )
        self.assertEqual(
            {
                "billingEndDate": "",
                "billingStartDate": "",
                "countOfSubmissionDays": "",
                "countOfSubmissions": "",
                "earliestBillingDate": "",
            },
            cpt_details_2.get("cpt99453"),
        )
        self.assertEqual("2022-05-10", cpt_details_2["cpt99454"]["billingStartDate_0"])
        self.assertEqual("2022-06-08", cpt_details_2["cpt99454"]["billingEndDate_0"])
        self.assertEqual(16, cpt_details_2["cpt99454"]["countOfSubmissions_0"])
        self.assertEqual(16, cpt_details_2["cpt99454"]["countOfSubmissionDays_0"])
        self.assertEqual("2022-05-28", cpt_details_2["cpt99454"]["earliestBillingDate_0"])

    @freeze_time("2022-07-07T10:00:00.000Z")
    def test_export_for_calendar_period_calculations(self):
        self._set_calendar_calculation_type()
        # add 1 submission in April
        april = "2022-03-31T10:00:00.000Z"
        with freeze_time(april):
            self._submit_sample_module_results(start_date=april)

        # add 16 submission in May
        with freeze_time("2022-05-30T10:00:00.000Z"):
            for day in range(10, 26):
                start_date = f"2022-05-{day}T20:00:00.039Z"
                self._submit_sample_module_results(start_date=start_date)

        report = self.send_export_request(self._export_payload())
        cpt_details = report[USER_ID][EXPORT_TYPE][0][CPT_SECTION]
        # Only 1 period is Completed - that has 16 submissions
        self.assertEqual(1, len(report[USER_ID][EXPORT_TYPE]))
        self.assertEqual(16, cpt_details["cpt99453"]["countOfSubmissionDays"])
        self.assertEqual(16, cpt_details["cpt99454"]["countOfSubmissionDays"])

    @freeze_time("2022-06-30T10:00:00.000Z")
    def test_export_for_calendar_calculation_WHEN_there_is_no_data(self):
        self._set_calendar_calculation_type()
        report = self.send_export_request(self._export_payload())
        self.assertEqual({}, report)

    @freeze_time("2024-03-31T10:00:00.000Z")
    def test_export_for_calendar_period_calculations__billable_empty_in_progress(self):
        self._set_calendar_calculation_type()
        # add 16 submission in January
        end_of_jan = "2024-01-30T10:00:00.000Z"
        with freeze_time(end_of_jan):
            for day in range(3, 19):
                january = datetime(2024, 1, day).strftime(DT_FORMAT)
                self._submit_sample_module_results(start_date=january)

        # 0 submission in February
        # add 1 submission in March
        march = "2024-03-15T10:00:00.000Z"
        with freeze_time(march):
            self._submit_sample_module_results(start_date=march)

        payload = self._export_payload(*CALENDAR_CALC_DATES)
        report = self.send_export_request(payload)

        # only 2 periods are visible, February is empty
        self.assertEqual(2, len(report[USER_ID][EXPORT_TYPE]))

        cpt_period_1 = report[USER_ID][EXPORT_TYPE][0][CPT_SECTION]
        self.assertEqual(16, cpt_period_1["cpt99453"]["countOfSubmissions"])
        self.assertEqual("2024-01-30", cpt_period_1["cpt99453"]["billingEndDate"])
        cpt_period_2 = report[USER_ID][EXPORT_TYPE][1][CPT_SECTION]
        self.assertEqual("", cpt_period_2["cpt99453"]["countOfSubmissions"])
        self.assertEqual("", cpt_period_2["cpt99453"]["billingEndDate"])
        self.assertEqual(1, cpt_period_2["cpt99454"]["countOfSubmissions"])
        self.assertEqual("2024-03-31", cpt_period_2["cpt99454"]["billingEndDate"])

    @freeze_time("2024-03-31T10:00:00.000Z")
    def test_export_for_calendar_period_calculations__three_periods_incomplete(self):
        self._set_calendar_calculation_type()
        # add 1 submission in January
        january = "2024-01-15T10:00:00.000Z"
        with freeze_time(january):
            self._submit_sample_module_results(start_date=january)
        # add 1 submission in February
        february = "2024-02-15T10:00:00.000Z"
        with freeze_time(february):
            self._submit_sample_module_results(start_date=february)
        # add 1 submission in March
        march = "2024-03-15T10:00:00.000Z"
        with freeze_time(march):
            self._submit_sample_module_results(start_date=march)

        payload = self._export_payload(*CALENDAR_CALC_DATES)
        report = self.send_export_request(payload)

        self.assertEqual(1, len(report[USER_ID][EXPORT_TYPE]))
        cpt_period_1 = report[USER_ID][EXPORT_TYPE][0][CPT_SECTION]
        self.assertEqual(1, cpt_period_1["cpt99453"]["countOfSubmissions"])
        self.assertEqual("2024-03-31", cpt_period_1["cpt99453"]["billingEndDate"])
        self.assertEqual(1, cpt_period_1["cpt99454"]["countOfSubmissions"])
        self.assertEqual(1, cpt_period_1["cpt99454"]["countOfSubmissions"])

    @freeze_time("2024-01-31T10:00:00.000Z")
    def test_export_for_calendar_period_calculations__first_period_completed_others_not_started(
        self,
    ):
        self._set_calendar_calculation_type()
        # add 16 submission in January
        end_of_jan = "2024-01-30T10:00:00.000Z"
        with freeze_time(end_of_jan):
            for day in range(3, 19):
                january = datetime(2024, 1, day).strftime(DT_FORMAT)
                self._submit_sample_module_results(start_date=january)

        # 0 submission in February, 0 submission in March

        payload = self._export_payload(CALENDAR_CALC_DATES[0], end_of_jan)
        report = self.send_export_request(payload)

        self.assertEqual(1, len(report[USER_ID][EXPORT_TYPE]))
        cpt_period_1 = report[USER_ID][EXPORT_TYPE][0][CPT_SECTION]
        self.assertEqual(16, cpt_period_1["cpt99453"]["countOfSubmissions"])
        self.assertEqual("2024-01-30", cpt_period_1["cpt99453"]["billingEndDate"])

    @freeze_time("2024-03-29T10:00:00.000Z")
    def test_export_for_calendar_period_calculations__only_first_period_completed(self):
        self._set_calendar_calculation_type()
        # add 16 submission in January
        with freeze_time("2024-01-30T10:00:00.000Z"):
            for day in range(5, 21):
                january = datetime(2024, 1, day).strftime(DT_FORMAT)
                self._submit_sample_module_results(start_date=january)

        # 0 submission in February, 0 submission in March

        payload = self._export_payload(*CALENDAR_CALC_DATES)
        report = self.send_export_request(payload)

        self.assertEqual(1, len(report[USER_ID][EXPORT_TYPE]))
        period_1 = report[USER_ID][EXPORT_TYPE][0]
        cpt_period_1 = period_1[CPT_SECTION]
        self.assertEqual(16, cpt_period_1["cpt99453"]["countOfSubmissions"])
        self.assertEqual("2024-01-30", cpt_period_1["cpt99453"]["billingEndDate"])
        # February is empty
        self.assertNotIn("2024-02-01", period_1)
        self.assertNotIn("2024-02-29", period_1)

    @freeze_time("2024-02-29T10:00:00.000Z")
    def test_export_for_calendar_period_calculations__new_submissions_after_first_period_completed(
        self,
    ):
        self._set_calendar_calculation_type()
        # add 16 submission in January
        with freeze_time("2024-01-30T10:00:00.000Z"):
            for day in range(5, 21):
                january = datetime(2024, 1, day).strftime(DT_FORMAT)
                self._submit_sample_module_results(start_date=january)

        # add 1 submission in February
        february = "2024-02-15T10:00:00.000Z"
        with freeze_time(february):
            self._submit_sample_module_results(start_date=february)

        payload = self._export_payload(*CALENDAR_CALC_DATES)
        report = self.send_export_request(payload)

        self.assertEqual(2, len(report[USER_ID][EXPORT_TYPE]))
        cpt_period_2 = report[USER_ID][EXPORT_TYPE][1][CPT_SECTION]
        self.assertEqual(1, cpt_period_2["cpt99454"]["countOfSubmissions"])
        self.assertEqual("2024-02-29", cpt_period_2["cpt99454"]["billingEndDate"])
        # 53 is blank
        self.assertEqual("", cpt_period_2["cpt99453"]["countOfSubmissions"])
        self.assertEqual("", cpt_period_2["cpt99453"]["billingEndDate"])

    @freeze_time("2024-03-29T10:00:00.000Z")
    def test_export_for_calendar_period_calculations_LEAP_year_EDGE_of_month(self):
        self._set_calendar_calculation_type()
        # 16 submission in February
        with freeze_time("2024-02-29T10:00:00.000Z"):
            for day in range(14, 30):
                february = datetime(2024, 2, day).strftime(DT_FORMAT)
                self._submit_sample_module_results(start_date=february)

        # 16 submission in March
        with freeze_time("2024-03-25T10:00:00.000Z"):
            for day in range(1, 17):
                march = datetime(2024, 3, day).strftime(DT_FORMAT)
                self._submit_sample_module_results(start_date=march)

        payload = self._export_payload(*CALENDAR_CALC_DATES)
        report = self.send_export_request(payload)

        self.assertEqual(2, len(report[USER_ID][EXPORT_TYPE]))
        cpt_period_1 = report[USER_ID][EXPORT_TYPE][0][CPT_SECTION]
        cpt_period_2 = report[USER_ID][EXPORT_TYPE][1][CPT_SECTION]
        self.assertEqual(16, cpt_period_1["cpt99453"]["countOfSubmissions"])
        self.assertEqual("2024-02-29", cpt_period_1["cpt99453"]["billingEndDate"])
        self.assertEqual(16, cpt_period_2["cpt99454"]["countOfSubmissions"])
        self.assertEqual("2024-03-31", cpt_period_2["cpt99454"]["billingEndDate"])

    @freeze_time("2024-02-28T10:00:00.000Z")
    def test_export_for_calendar_period_calculations_first_period_in_progress(self):
        self._set_calendar_calculation_type()
        # 8 submission in February
        end_of_feb = "2024-02-29T10:00:00.000Z"
        with freeze_time(end_of_feb):
            for day in range(1, 9):
                february = datetime(2024, 2, day).strftime(DT_FORMAT)
                self._submit_sample_module_results(start_date=february)

        payload = self._export_payload(CALENDAR_CALC_DATES[0], end_of_feb)
        report = self.send_export_request(payload)

        self.assertEqual(1, len(report[USER_ID][EXPORT_TYPE]))
        cpt_period_1 = report[USER_ID][EXPORT_TYPE][0][CPT_SECTION]
        self.assertEqual(8, cpt_period_1["cpt99453"]["countOfSubmissions"])
        self.assertEqual(8, cpt_period_1["cpt99454"]["countOfSubmissions"])
        self.assertEqual("2024-02-29", cpt_period_1["cpt99453"]["billingEndDate"])
        self.assertEqual("2024-02-29", cpt_period_1["cpt99454"]["billingEndDate"])

    @freeze_time("2022-12-31T10:00:00.000Z")
    def test_export_for_calendar_calc_in_december(self):
        self._set_calendar_calculation_type()
        # 16 submissions before the 30th of December
        end_of_dec = "2022-12-29T10:00:00.000Z"
        with freeze_time(end_of_dec):
            for day in range(1, 17):
                dec = datetime(2022, 12, day).strftime(DT_FORMAT)
                self._submit_sample_module_results(start_date=dec)

        # 1 submission on the 31st of December
        last_day_of_dec = "2022-12-31T10:00:00.000Z"
        with freeze_time(last_day_of_dec):
            self._submit_sample_module_results(start_date=last_day_of_dec)

        payload = self._export_payload("2022-12-1T05:00:00.000Z", last_day_of_dec)
        report = self.send_export_request(payload)

        self.assertEqual(1, len(report[USER_ID][EXPORT_TYPE]))
        cpt_period_1 = report[USER_ID][EXPORT_TYPE][0][CPT_SECTION]
        self.assertEqual(16, cpt_period_1["cpt99453"]["countOfSubmissions"])
        self.assertEqual(16, cpt_period_1["cpt99454"]["countOfSubmissions"])
        self.assertEqual("2022-12-01", cpt_period_1["cpt99453"]["billingStartDate"])
        self.assertEqual("2022-12-30", cpt_period_1["cpt99453"]["billingEndDate"])

    @freeze_time("2023-01-31T10:00:00.000Z")
    def test_export_for_calendar_calc_in_january(self):
        self._set_calendar_calculation_type()

        # 1 submission on the 31st of December
        last_day_of_dec = "2022-12-31T10:00:00.000Z"
        with freeze_time(last_day_of_dec):
            self._submit_sample_module_results(start_date=last_day_of_dec)

        # 15 submissions in January
        with freeze_time("2023-01-29T10:00:00.000Z"):
            for day in range(1, 16):
                dec = datetime(2023, 1, day).strftime(DT_FORMAT)
                self._submit_sample_module_results(start_date=dec)

        payload = self._export_payload("2022-12-1T05:00:00.000Z", "2023-01-31T10:00:00.000Z")
        report = self.send_export_request(payload)

        self.assertEqual(1, len(report[USER_ID][EXPORT_TYPE]))
        cpt_period_1 = report[USER_ID][EXPORT_TYPE][0][CPT_SECTION]
        self.assertEqual(16, cpt_period_1["cpt99453"]["countOfSubmissions"])
        self.assertEqual(16, cpt_period_1["cpt99454"]["countOfSubmissions"])
        self.assertEqual("2023-01-15", cpt_period_1["cpt99453"]["earliestBillingDate"])
        self.assertEqual("2022-12-31", cpt_period_1["cpt99453"]["billingStartDate"])
        self.assertEqual("2023-01-29", cpt_period_1["cpt99453"]["billingEndDate"])

    @freeze_time("2024-01-30T10:00:00.000Z")
    def test_export_for_calendar_calc_in_january_leap_year(self):
        self._set_calendar_calculation_type()

        # 1 submission on the 31st of December
        last_day_of_dec = "2023-12-31T10:00:00.000Z"
        with freeze_time(last_day_of_dec):
            self._submit_sample_module_results(start_date=last_day_of_dec)

        # 15 submissions in January
        with freeze_time("2024-01-29T10:00:00.000Z"):
            for day in range(1, 16):
                dec = datetime(2024, 1, day).strftime(DT_FORMAT)
                self._submit_sample_module_results(start_date=dec)

        payload = self._export_payload("2023-12-1T05:00:00.000Z", "2024-01-31T10:00:00.000Z")
        report = self.send_export_request(payload)

        self.assertEqual(1, len(report[USER_ID][EXPORT_TYPE]))
        cpt_period_1 = report[USER_ID][EXPORT_TYPE][0][CPT_SECTION]
        self.assertEqual(15, cpt_period_1["cpt99453"]["countOfSubmissions"])
        self.assertEqual(15, cpt_period_1["cpt99454"]["countOfSubmissions"])
        self.assertEqual("", cpt_period_1["cpt99453"]["earliestBillingDate"])
        self.assertEqual("2024-01-01", cpt_period_1["cpt99453"]["billingStartDate"])
        self.assertEqual("2024-01-30", cpt_period_1["cpt99453"]["billingEndDate"])

    @freeze_time("2023-02-28T10:00:00.000Z")
    def test_export_for_calendar_calc_in_february(self):
        self._set_calendar_calculation_type()

        # 1 submission on the 30th of January
        last_day_of_dec = "2023-01-30T10:00:00.000Z"
        with freeze_time(last_day_of_dec):
            self._submit_sample_module_results(start_date=last_day_of_dec)

        # 1 submission on the 31st of January
        last_day_of_dec = "2023-01-31T10:00:00.000Z"
        with freeze_time(last_day_of_dec):
            self._submit_sample_module_results(start_date=last_day_of_dec)

        # 14 submissions in February
        with freeze_time("2023-02-21T10:00:00.000Z"):
            for day in range(1, 15):
                dec = datetime(2023, 2, day).strftime(DT_FORMAT)
                self._submit_sample_module_results(start_date=dec)

        payload = self._export_payload("2023-02-01T05:00:00.000Z", "2023-02-28T10:00:00.000Z")
        report = self.send_export_request(payload)

        self.assertEqual(1, len(report[USER_ID][EXPORT_TYPE]))
        cpt_period_1 = report[USER_ID][EXPORT_TYPE][0][CPT_SECTION]
        self.assertEqual(16, cpt_period_1["cpt99453"]["countOfSubmissions"])
        self.assertEqual(16, cpt_period_1["cpt99454"]["countOfSubmissions"])
        self.assertEqual("2023-02-14", cpt_period_1["cpt99453"]["earliestBillingDate"])
        self.assertEqual("2023-01-30", cpt_period_1["cpt99453"]["billingStartDate"])
        self.assertEqual("2023-02-28", cpt_period_1["cpt99453"]["billingEndDate"])

    @freeze_time("2024-02-29T10:00:00.000Z")
    def test_export_for_calendar_calc_in_february_leap_year(self):
        self._set_calendar_calculation_type()

        # 1 submission on the 30th of January
        last_day_of_dec = "2024-01-30T10:00:00.000Z"
        with freeze_time(last_day_of_dec):
            self._submit_sample_module_results(start_date=last_day_of_dec)

        # 1 submission on the 31st of January
        last_day_of_dec = "2024-01-31T10:00:00.000Z"
        with freeze_time(last_day_of_dec):
            self._submit_sample_module_results(start_date=last_day_of_dec)

        # 14 submissions in February
        with freeze_time("2024-02-21T10:00:00.000Z"):
            for day in range(1, 15):
                dec = datetime(2024, 2, day).strftime(DT_FORMAT)
                self._submit_sample_module_results(start_date=dec)

        payload = self._export_payload("2024-01-01T05:00:00.000Z", "2024-02-29T10:00:00.000Z")
        report = self.send_export_request(payload)

        self.assertEqual(1, len(report[USER_ID][EXPORT_TYPE]))
        cpt_period_1 = report[USER_ID][EXPORT_TYPE][0][CPT_SECTION]
        self.assertEqual(15, cpt_period_1["cpt99453"]["countOfSubmissions"])
        self.assertEqual(15, cpt_period_1["cpt99454"]["countOfSubmissions"])
        self.assertEqual("", cpt_period_1["cpt99453"]["earliestBillingDate"])
        self.assertEqual("2024-01-31", cpt_period_1["cpt99453"]["billingStartDate"])
        self.assertEqual("2024-02-29", cpt_period_1["cpt99453"]["billingEndDate"])

    @freeze_time("2023-02-28T10:00:00.000Z")
    def test_export_for_calendar_calc_in_february_dos_out_of_report_time(self):
        self._set_calendar_calculation_type()

        # 16 submissions in February
        with freeze_time("2023-02-21T10:00:00.000Z"):
            for day in range(1, 17):
                dec = datetime(2023, 2, day).strftime(DT_FORMAT)
                self._submit_sample_module_results(start_date=dec)

        payload = self._export_payload("2023-02-01T05:00:00.000Z", "2023-02-27T10:00:00.000Z")
        report = self.send_export_request(payload)
        self.assertEmpty(report)

    @freeze_time("2023-01-30T10:00:00.000Z")
    def test_export_monthly_report_billing_provider(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-01-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-02-28T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.USER_IDS: [USER_ID, USER_ID_2],
        }
        self._submit_sample_module_results(start_date="2023-01-10T00:00:00.039Z")
        self._submit_sample_module_results(user_id=USER_ID_2, start_date="2023-01-10T00:00:00.039Z")
        report = self.send_export_request(payload)
        billing_data_user_1 = report[USER_ID][EXPORT_TYPE][0][BILLING_DATA_SECTION]
        billing_data_user_2 = report[USER_ID_2][EXPORT_TYPE][0][BILLING_DATA_SECTION]
        self.assertEqual(
            "New Billing Provider",
            billing_data_user_1[BillingMonthlyReportUserBilling.CPT_99457_8][BillingProvider.NAME],
        )
        self.assertEqual(
            "Old Billing Provider",
            billing_data_user_1[BillingMonthlyReportUserBilling.CPT_99453_4][BillingProvider.NAME],
        )
        self.assertEqual(
            "",
            billing_data_user_2[BillingMonthlyReportUserBilling.CPT_99457_8][BillingProvider.NAME],
        )
        self.assertEqual(
            "",
            billing_data_user_2[BillingMonthlyReportUserBilling.CPT_99453_4][BillingProvider.NAME],
        )

    def test_export_monthly_report_billing_provider_for_two_period(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-01-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-02-28T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.USER_IDS: [USER_ID],
        }
        self._submit_sample_module_results(start_date="2023-01-10T00:00:00.039Z")
        self._add_time_tracking("2023-01-06T17:12:01.039Z", "2023-01-06T17:12:59.039Z")
        self._add_time_tracking("2023-01-06T17:13:01.039Z", "2023-01-06T17:13:59.039Z")

        report = self.send_export_request(payload)
        billing_data_period_1 = report[USER_ID][EXPORT_TYPE][0][BILLING_DATA_SECTION]
        billing_data_period_2 = report[USER_ID][EXPORT_TYPE][1][BILLING_DATA_SECTION]
        self.assertEqual(
            "Old Billing Provider",
            billing_data_period_1[BillingMonthlyReportUserBilling.CPT_99457_8][BillingProvider.NAME],
        )
        self.assertEqual(
            "New Billing Provider",
            billing_data_period_2[BillingMonthlyReportUserBilling.CPT_99457_8][BillingProvider.NAME],
        )
        self.assertEqual(
            "Old Billing Provider Id",
            billing_data_period_1[BillingMonthlyReportUserBilling.CPT_99457_8][BillingProvider.ID],
        )
        self.assertEqual(
            "New Billing Provider Id",
            billing_data_period_2[BillingMonthlyReportUserBilling.CPT_99457_8][BillingProvider.ID],
        )

    @freeze_time("2023-01-29T10:00:00.000Z")
    def test_export_monthly_report_billing_provider_for_calendar_calculation(self):
        self._set_calendar_calculation_type()
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-01-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-02-28T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.USER_IDS: [USER_ID],
        }

        self._submit_sample_module_results(start_date="2023-01-10T00:00:00.039Z")
        report = self.send_export_request(payload)

        billing_data_period_1 = report[USER_ID][EXPORT_TYPE][0][BILLING_DATA_SECTION]
        self.assertEqual(
            "Old Billing Provider",
            billing_data_period_1["cpt99453_4"][BillingProvider.NAME],
        )
        self.assertEqual(
            "Old Billing Provider Id",
            billing_data_period_1["cpt99453_4"][BillingProvider.ID],
        )

    def test_time_tracking_cpt_codes_for_rtm(self):
        self._submit_sample_RTM_module_results(
            user_id=RTM_USER_ID,
            deployment_id=RTM_DEPLOYMENT_ID,
            start_date="2023-01-01T14:12:30.039Z",
        )
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-01-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-02-28T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.USER_IDS: [RTM_USER_ID],
        }
        body = {
            CreateBillingRequestObject.START_DATE_TIME: "2023-01-01T14:12:01.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2023-01-01T14:12:59.039Z",
        }
        rsp = self.flask_client.post(
            f"{self.add_time_tracking_url}/user/{RTM_USER_ID}",
            headers=self.get_headers_for_token(RTM_CLINICIAN_ID),
            json=body,
        )
        self.assertEqual(201, rsp.status_code)
        body = {
            CreateBillingRequestObject.START_DATE_TIME: "2023-01-01T14:15:01.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2023-01-01T14:15:59.039Z",
        }
        rsp = self.flask_client.post(
            f"{self.add_time_tracking_url}/user/{RTM_USER_ID}",
            headers=self.get_headers_for_token(RTM_CLINICIAN_ID),
            json=body,
        )
        self.assertEqual(201, rsp.status_code)
        rsp = self.flask_client.post(
            f"/api/extensions/v1/export/deployment/{RTM_DEPLOYMENT_ID}",
            headers=self.get_headers_for_token(RTM_CLINICIAN_ID),
            json=payload,
        )
        json_data = json.loads(rsp.data)
        cpt_codes_details = json_data[RTM_USER_ID][
            f"{BillingMonthlyExportableUseCase.MODULE_NAME}_{payload[ExportParameters.FROM_DATE][:10]}_{payload[ExportParameters.TO_DATE][:10]}"
        ][0][BillingMonthlyExportableUseCase.BillingMonthlyReportSections.CPT_CODES_DETAILS]
        self.assertIn(BillingMonthlyReportCPTCodes.CPT_98980, cpt_codes_details)
        self.assertIn(BillingMonthlyReportCPTCodes.CPT_98981, cpt_codes_details)

        self.assertNotIn(BillingMonthlyReportCPTCodes.CPT_99457, cpt_codes_details)
        self.assertNotIn(BillingMonthlyReportCPTCodes.CPT_99458, cpt_codes_details)

    @freeze_time("2023-01-30T10:00:00.000Z")
    def test_submission_cpt_codes_for_rtm(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-01-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-02-28T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.USER_IDS: [RTM_USER_ID],
        }

        self._submit_sample_RTM_module_results()

        rsp = self.flask_client.post(
            f"/api/extensions/v1/export/deployment/{RTM_DEPLOYMENT_ID}",
            headers=self.get_headers_for_token(RTM_CLINICIAN_ID),
            json=payload,
        )
        json_data = json.loads(rsp.data)
        cpt_codes_details = json_data[RTM_USER_ID][
            f"{BillingMonthlyExportableUseCase.MODULE_NAME}_{payload[ExportParameters.FROM_DATE][:10]}_{payload[ExportParameters.TO_DATE][:10]}"
        ][0][BillingMonthlyExportableUseCase.BillingMonthlyReportSections.CPT_CODES_DETAILS]
        self.assertIn(BillingMonthlyReportCPTCodes.CPT_98975, cpt_codes_details)
        self.assertIn(BillingMonthlyReportCPTCodes.CPT_98976, cpt_codes_details)

        self.assertNotIn(BillingMonthlyReportCPTCodes.CPT_99453, cpt_codes_details)
        self.assertNotIn(BillingMonthlyReportCPTCodes.CPT_99454, cpt_codes_details)

    @patch(f"{USE_CASE_PATH}.BillingMonthlyExportableUseCase.filter_data_based_on_consent_and_econsent")
    def test_billing_export_not_called_on_user_own_request(self, mocked_use_case_filter):
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-01-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-02-28T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.USER_IDS: [USER_ID],
        }
        self._submit_sample_module_results(start_date="2023-01-10T00:00:00.039Z")
        self.flask_client.post(
            self.user_export_url,
            headers=self.get_headers_for_token(USER_ID),
            json=payload,
        )
        mocked_use_case_filter.assert_not_called()

        self.send_export_request(payload)
        mocked_use_case_filter.assert_called_once()

    @freeze_time("2024-03-31T10:00:00.000Z")
    def test_ehr_export(self):
        # add 16 submission in January
        end_of_jan = "2024-01-31T10:00:00.000Z"
        with freeze_time(end_of_jan):
            for day in range(3, 19):
                january = datetime(2024, 1, day).strftime(DT_FORMAT)
                self._submit_sample_module_results(start_date=january)

        # 0 submission in February
        # add 1 submission in March
        march = "2024-03-15T10:00:00.000Z"
        with freeze_time(march):
            self._submit_sample_module_results(start_date=march)

        payload = {
            **self._export_payload(*CALENDAR_CALC_DATES),
            ExportParameters.MODULE_NAMES: [
                BillingMonthlyExportableUseCase.MODULE_NAME,
                BillingMonthlyEHRExportableUseCase.MODULE_NAME,
            ],
        }
        rsp = self.send_export_request(payload, replace_keys=False)
        self.assertEqual(200, rsp.status_code)
        key = f"{BillingMonthlyExportableUseCase.MODULE_NAME}_{payload[ExportParameters.FROM_DATE][:10]}_{payload[ExportParameters.TO_DATE][:10]}"
        export = rsp.json[USER_ID][key][0]
        export_ehr = rsp.json[USER_ID][f"EHR{key}"][0]
        self.assertNotIn("totalBillable", export["cptCodesDetails"]["cpt99453"])
        self.assertNotIn("totalBillable", export["cptCodesDetails"]["cpt99454"])

        self.assertEqual(1, export_ehr["cptCodesDetails"]["cpt99453"]["totalBillable"])
        self.assertEqual(1, export_ehr["cptCodesDetails"]["cpt99454"]["totalBillable"])

    @freeze_time("2024-03-31T10:00:00.000Z")
    def test_ehr_export_for_calendar_calculation(self):
        self._set_calendar_calculation_type()
        # add 16 submission in January
        end_of_jan = "2024-01-30T10:00:00.000Z"
        with freeze_time(end_of_jan):
            for day in range(3, 19):
                january = datetime(2024, 1, day).strftime(DT_FORMAT)
                self._submit_sample_module_results(start_date=january)

        # 0 submission in February
        # add 1 submission in March
        march = "2024-03-15T10:00:00.000Z"
        with freeze_time(march):
            self._submit_sample_module_results(start_date=march)

        payload = {
            **self._export_payload(*CALENDAR_CALC_DATES),
            ExportParameters.MODULE_NAMES: [
                BillingMonthlyExportableUseCase.MODULE_NAME,
                BillingMonthlyEHRExportableUseCase.MODULE_NAME,
            ],
        }
        rsp = self.send_export_request(payload, replace_keys=False)
        self.assertEqual(200, rsp.status_code)
        key = f"{BillingMonthlyExportableUseCase.MODULE_NAME}_{payload[ExportParameters.FROM_DATE][:10]}_{payload[ExportParameters.TO_DATE][:10]}"
        export = rsp.json[USER_ID][key][0]
        export_ehr = rsp.json[USER_ID][f"EHR{key}"][0]
        self.assertNotIn("totalBillable", export["cptCodesDetails"]["cpt99453"])
        self.assertNotIn("totalBillable", export["cptCodesDetails"]["cpt99454"])

        self.assertEqual(1, export_ehr["cptCodesDetails"]["cpt99453"]["totalBillable"])
        self.assertEqual(1, export_ehr["cptCodesDetails"]["cpt99454"]["totalBillable"])

    @freeze_time("2024-03-31T10:00:00.000Z")
    def test_ehr_export_not_exported_for_all_modules_export(self):
        # add 16 submission in January
        end_of_jan = "2024-01-31T10:00:00.000Z"
        with freeze_time(end_of_jan):
            for day in range(3, 19):
                january = datetime(2024, 1, day).strftime(DT_FORMAT)
                self._submit_sample_module_results(start_date=january)

        payload = {
            **self._export_payload(*CALENDAR_CALC_DATES),
            ExportParameters.MODULE_NAMES: [],
        }
        rsp = self.send_export_request(payload, replace_keys=False)
        self.assertEqual(200, rsp.status_code)
        key = f"EHR{BillingMonthlyExportableUseCase.MODULE_NAME}_{payload[ExportParameters.FROM_DATE][:10]}_{payload[ExportParameters.TO_DATE][:10]}"
        self.assertNotIn(key, rsp.json[USER_ID])

    def send_export_request(self, payload: dict, replace_keys: bool = True):
        rsp = self.flask_client.post(
            self.export_url,
            headers=self.get_headers_for_token(MANAGER_ID),
            json=payload,
        )
        if not replace_keys:
            return rsp
        self.assertEqual(200, rsp.status_code)
        return self._simplify_export_response_keys(rsp)

    def send_add_time_tracking_request(self, payload: dict):
        return self.flask_client.post(
            f"{self.add_time_tracking_url}/user/{USER_ID}",
            headers=self.get_headers_for_token(CLINICIAN_ID),
            json=payload,
        )

    def _add_time_tracking(self, start_date_time, end_date_time):
        body = {
            CreateBillingRequestObject.START_DATE_TIME: start_date_time,
            CreateBillingRequestObject.END_DATE_TIME: end_date_time,
        }
        rsp = self.send_add_time_tracking_request(body)
        self.assertEqual(201, rsp.status_code)

    @staticmethod
    def _export_payload(start="2022-04-01T00:00:00.039Z", end="2022-07-01T00:00:00.039Z"):
        return {
            ExportParameters.FROM_DATE: start,
            ExportParameters.TO_DATE: end,
            ExportParameters.MODULE_NAMES: [BillingMonthlyExportableUseCase.MODULE_NAME],
            ExportParameters.SINGLE_FILE_RESPONSE: True,
            ExportRequestObject.REQUESTER_ID: MANAGER_ID,
            ExportRequestObject.EXCLUDE_FIELDS: ["user.contactEmail"],
        }

    def _assert_json_content_is_equal(self, response, sample_file):
        sample_file_path = Path(__file__).parent.joinpath(f"fixtures/{sample_file}")
        # uncomment to update sample files
        # with open(sample_file_path, "w") as sample:
        #     if isinstance(response, dict):
        #         json.dump(response, sample)
        #     else:
        #         json.dump(json.loads(response.data), sample)
        with open(sample_file_path) as sample:
            file_data = sample.read()
            sample_file_json = json.loads(file_data)
            self.assertEqual(sample_file_json, json.loads(response.data))

    def _assert_csv_content_is_equal(self, response, sample_file):
        sample_file_path = Path(__file__).parent.joinpath(f"fixtures/{sample_file}")
        # uncomment to update the sample file
        # with open(sample_file_path, "wb") as sample:
        #     sample.write(response.data)
        with open(sample_file_path, "rb") as sample:
            self.assertTrue(equal_csv_content(sample.read(), response.data))

    def _submit_sample_module_results(
        self,
        user_id: str = USER_ID,
        deployment_id: str = DEPLOYMENT_ID,
        start_date: str = "2022-06-01T00:00:00.039Z",
    ):
        module_results = {
            PrimitiveDTO.USER_ID: user_id,
            PrimitiveDTO.SUBMITTER_ID: MANAGER_ID,
            PrimitiveDTO.DEPLOYMENT_ID: deployment_id,
            PrimitiveDTO.START_DATE_TIME: start_date,
            PrimitiveDTO.DEVICE_NAME: "device",
            PrimitiveDTO.SERVER: {"hostUrl": "local", "server": "1.21.0", "api": "V1"},
            PrimitiveDTO.SOURCE: f"{PrimitiveSources.HEALTH_KIT.value};other;possible;things",
            BloodPressureDTO.MODULE_ID: "BloodPressure",
            BloodPressureDTO.MODULE_CONFIG_ID: "5e94b2007773091c9a592650",
            BloodPressureDTO.DIASTOLIC_VALUE: 60,
            BloodPressureDTO.SYSTOLIC_VALUE: 120,
        }
        body = [{**module_results, "type": BloodPressureDTO.get_primitive_name()}]
        rsp = self.flask_client.post(
            f"api/extensions/v1/user/{user_id}/module-result/{BloodPressureModule.moduleId}",
            headers=self.get_headers_for_token(MANAGER_ID),
            json=body,
        )
        self.assertEqual(201, rsp.status_code)

    def _submit_sample_RTM_module_results(
        self,
        user_id: str = RTM_USER_ID,
        deployment_id: str = RTM_DEPLOYMENT_ID,
        start_date: str = "2023-01-10T00:00:00.039Z",
    ):
        module_results = {
            PrimitiveDTO.USER_ID: RTM_USER_ID,
            PrimitiveDTO.SUBMITTER_ID: RTM_CLINICIAN_ID,
            PrimitiveDTO.DEPLOYMENT_ID: RTM_DEPLOYMENT_ID,
            PrimitiveDTO.CREATE_DATE_TIME: "2022-06-01T00:00:00.039Z",
            PrimitiveDTO.START_DATE_TIME: start_date,
            PrimitiveDTO.DEVICE_NAME: "device",
            PrimitiveDTO.SERVER: {"hostUrl": "local", "server": "1.21.0", "api": "V1"},
            PrimitiveDTO.SOURCE: f"{PrimitiveSources.HEALTH_KIT.value};other;possible;things",
            StepDTO.MODULE_ID: "Step",
            StepDTO.MODULE_CONFIG_ID: "5e94b2007773091c9a592673",
            StepDTO.VALUE: 60,
        }
        body = [{**module_results, "type": StepDTO.get_primitive_name()}]
        rsp = self.flask_client.post(
            f"api/extensions/v1/user/{user_id}/module-result/{StepModule.moduleId}",
            headers=self.get_headers_for_token(RTM_CLINICIAN_ID),
            json=body,
        )
        self.assertEqual(201, rsp.status_code)

    def _set_calendar_calculation_type(self, use_calendar_calculation=True):
        set_submission_calculation_type(DEPLOYMENT_ID, use_calendar_calculation)

    @staticmethod
    def _simplify_export_response_keys(response):
        return simplify_export_response_keys(response, PATTERN_FILE_NAME_DATE, EXPORT_TYPE)
