import json
from pathlib import Path

from freezegun import freeze_time

from billing.components.core.component import BillingComponent
from billing.components.export.component import BillingExportComponent
from billing.components.export.models.billing_models import (
    BillingManualLogExportItem,
)
from billing.components.export.use_case.billing_manual_log_export import (
    BillingManualLogExportableUseCase,
)
from billing.tests.billing_test_case import BillingTestCase
from billing.tests.test_helpers import equal_csv_content
from huma_plugins.components.export.dtos.export_models import ExportParameters
from huma_plugins.components.export.tests.IntegrationTests import enabled_modules
from huma_plugins.components.extended_authorization.component import (
    ExtendedAuthorizationComponent,
)
from huma_plugins.components.extended_authorization.component_options import (
    authorization_options,
)
from huma_plugins.components.extended_module_result.component import (
    ExtendedModuleResultComponent,
)
from huma_plugins.components.online_offline_call.component import (
    OnlineOfflineCallComponent,
)
from sdk.auth.component import AuthComponent
from sdk.deployment.component import DeploymentComponent
from sdk.organization.component import OrganizationComponent
from sdk.versioning.component import VersionComponent

MANAGER_ID = "5e8f0c74b50aa9656c34789d"
USER_ID = "5e8f0c74b50aa9656c34789b"
USER_ID_2 = "5e8f0c74b50aa9656c34789c"
USER_ID_3_ORDERING = "5e8f0c74b50aa9656c34789d"
USER_ID_4_ORDERING = "5e8f0c74b50aa9656c34789e"
DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
TEMP_TT_ID = "5ff86cc6ff885918d96edb2c"


class ManualMonitoringLogReportExportTestCase(BillingTestCase):
    components = [
        AuthComponent(),
        ExtendedAuthorizationComponent(
            component_options=authorization_options,
        ),
        BillingComponent(),
        DeploymentComponent(),
        OrganizationComponent(),
        ExtendedModuleResultComponent(
            additional_modules=[m() for m in enabled_modules],
        ),
        BillingExportComponent(),
        OnlineOfflineCallComponent(),
        VersionComponent(server_version="1.0.0", api_version="1.0.0"),
    ]
    fixtures = [
        Path(__file__).parent.joinpath("fixtures/deployments_dump.json"),
    ]

    def setUp(self):
        super().setUp()
        self.base_billing_url = "/api/extensions/v1/billing"
        self.log_submission_url = f"{self.base_billing_url}/user/{'{user_id}'}/log"
        self.export_url = f"/api/extensions/v1/export/deployment/{DEPLOYMENT_ID}"

    @freeze_time("2023-06-15")
    def test_export_sample_data_JSON(self):
        payload = {
            **self._export_base_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.FROM_DATE: "2023-04-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-07-04T00:00:00.039Z",
            ExportParameters.USER_IDS: [USER_ID, USER_ID_2],
        }

        rsp = self._export_logs(payload)
        self._assert_json_content_is_equal(rsp.json, "sample_manual_log_export.json")

    @freeze_time("2023-06-15")
    def test_export_sample_data_CSV(self):
        payload = {
            **self._export_base_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.CSV.value,
            ExportParameters.FROM_DATE: "2023-04-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-07-04T00:00:00.039Z",
            ExportParameters.USER_IDS: [USER_ID, USER_ID_2],
        }

        rsp = self._export_logs(payload)
        self._assert_csv_content_is_equal(rsp, "sample_manual_log_export.csv")

    def test_export_record_orderings(self):
        payload = {
            **self._export_base_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.FROM_DATE: "2023-04-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-07-04T00:00:00.039Z",
            ExportParameters.USER_IDS: [USER_ID_3_ORDERING, USER_ID_4_ORDERING],
        }
        results = self._export_logs(payload).json
        data = self._extract_logs(results)

        for usr, logs in data.items():
            for idx, log in enumerate(logs):
                if idx == len(logs) - 1:
                    break
                self.assertGreaterEqual(
                    logs[idx + 1][BillingManualLogExportItem.INITIAL_CREATE_DATE_TIME],
                    log[BillingManualLogExportItem.INITIAL_CREATE_DATE_TIME],
                )
                self.assertGreaterEqual(
                    logs[idx + 1][BillingManualLogExportItem.UPDATE_DATE_TIME],
                    log[BillingManualLogExportItem.UPDATE_DATE_TIME],
                )

    @staticmethod
    def _extract_logs(data: dict) -> dict:
        usr_logs = {}
        for usr, details in data.items():
            for k in details:
                usr_logs[usr] = [item.get("log") for item in details[k]]

        return usr_logs

    def _assert_json_content_is_equal(self, response, sample_file):
        sample_file_path = Path(__file__).parent.joinpath(f"fixtures/{sample_file}")
        with open(sample_file_path) as sample:
            file_data = sample.read()
            sample_file_json = json.loads(file_data)
            if isinstance(response, dict):
                self.assertEqual(sample_file_json, response)
            else:
                self.assertEqual(sample_file_json, json.loads(response.data))

    def _assert_csv_content_is_equal(self, response, sample_file):
        sample_file_path = Path(__file__).parent.joinpath(f"fixtures/{sample_file}")
        with open(sample_file_path, "rb") as sample:
            self.assertTrue(equal_csv_content(response.data, sample.read()))

    def _export_logs(self, payload: dict):
        return self.flask_client.post(
            self.export_url,
            headers=self.get_headers_for_token(MANAGER_ID),
            json=payload,
        )

    @staticmethod
    def _export_base_payload():
        return {
            ExportParameters.FROM_DATE: "2023-04-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-07-01T00:00:00.039Z",
            ExportParameters.MODULE_NAMES: [BillingManualLogExportableUseCase.MODULE_NAME],
            ExportParameters.SINGLE_FILE_RESPONSE: True,
            ExportParameters.TRANSLATE_PRIMITIVES: False,
            ExportParameters.DEIDENTIFIED: False,
            ExportParameters.INCLUDE_USER_META_DATA: False,
        }
