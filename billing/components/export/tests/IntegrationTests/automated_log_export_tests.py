import json
import logging
from pathlib import Path

from flask import url_for

from billing.components.core.component import BillingComponent
from billing.components.export.component import BillingExportComponent
from billing.components.export.models.billing_models import (
    ExportBillingLogUser,
)
from billing.components.export.use_case.billing_automated_log_export import (
    BillingAutomatedLogExportableUseCase,
)
from billing.tests.billing_test_case import BillingTestCase
from billing.tests.test_helpers import equal_csv_content
from huma_plugins.components.export.dtos.export_models import ExportParameters
from huma_plugins.components.extended_authorization.component import (
    ExtendedAuthorizationComponent,
)
from huma_plugins.components.extended_authorization.component_options import (
    authorization_options,
)
from sdk.auth.component import AuthComponent
from sdk.authorization.models import User
from sdk.deployment.component import DeploymentComponent
from sdk.organization.component import OrganizationComponent

DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
MANAGER_ID = "5e8f0c74b50aa9656c34789d"
USER_ID = "5e8f0c74b50aa9656c34789c"

logger = logging.getLogger(__name__)


class AutomatedLogExportTestCase(BillingTestCase):
    components = [
        AuthComponent(),
        ExtendedAuthorizationComponent(
            component_options=authorization_options,
        ),
        BillingComponent(),
        DeploymentComponent(),
        OrganizationComponent(),
        BillingExportComponent(),
    ]
    fixtures = [
        Path(__file__).parent.joinpath("fixtures/deployments_dump.json"),
    ]

    def test_export_automated_logs_JSON(self):
        payload = self._export_base_payload()
        rsp = self._send_export_request(payload)
        self._assert_json_content_is_equal(rsp.json, "automated_log_json_format.json")

    def test_export_automated_logs_CSV(self):
        payload = {
            **self._export_base_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.CSV.value,
        }
        rsp = self._send_export_request(payload)
        self._assert_csv_content_is_equal(rsp, "automated_log_csv_format.csv")

    def test_export_automated_log_de_identified_data_JSON(self):
        payload = {
            **self._export_base_payload(),
            ExportParameters.USER_IDS: [USER_ID],
            ExportParameters.DEIDENTIFIED: True,
        }
        rsp = self._send_export_request(payload)
        user_data = list(list(rsp.json.values())[0].values())[0][0]["user"]
        self.assertEqual(user_data[ExportBillingLogUser.GIVEN_NAME], None)
        self.assertEqual(user_data[ExportBillingLogUser.FAMILY_NAME], None)
        self.assertEqual(len(rsp.json), 1)

    def test_export_automated_log_for_not_active_user(self):
        payload = {
            **self._export_base_payload(),
            ExportParameters.USER_IDS: [USER_ID],
        }
        self._deactivate_user(USER_ID)
        rsp = self._send_export_request(payload)
        self.assertNotIn(USER_ID, rsp.json)

    @staticmethod
    def _export_base_payload():
        return {
            ExportParameters.FROM_DATE: "2023-06-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-09-24T00:00:00.039Z",
            ExportParameters.MODULE_NAMES: [BillingAutomatedLogExportableUseCase.MODULE_NAME],
            ExportParameters.SINGLE_FILE_RESPONSE: True,
            ExportParameters.TRANSLATE_PRIMITIVES: False,
            ExportParameters.DEIDENTIFIED: False,
            ExportParameters.INCLUDE_USER_META_DATA: False,
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }

    def _send_export_request(self, payload: dict):
        url = url_for("billing_export_route.export_deployment", deployment_id=DEPLOYMENT_ID)
        rsp = self.flask_client.post(
            url,
            headers=self.get_headers_for_token(MANAGER_ID),
            json=payload,
        )
        self.assertEqual(200, rsp.status_code)
        return rsp

    def _assert_json_content_is_equal(self, response, sample_file):
        sample_file_path = Path(__file__).parent.joinpath(f"fixtures/{sample_file}")
        with open(sample_file_path) as sample:
            file_data = sample.read()
            sample_file_json = json.loads(file_data)
            if isinstance(response, dict):
                self.assertEqual(sample_file_json, response)
            else:
                self.assertEqual(sample_file_json, json.loads(response.data))

    def _assert_csv_content_is_equal(self, response, sample_file):
        sample_file_path = Path(__file__).parent.joinpath(f"fixtures/{sample_file}")
        with open(sample_file_path, "rb") as sample:
            self.assertTrue(equal_csv_content(sample.read(), response.data))

    @staticmethod
    def _deactivate_user(user_id: str):
        user = User.objects.get(mongoId=user_id)
        user.roles[0]["isActive"] = False
        user.save()
