import json
from datetime import datetime
from pathlib import Path

from flask import url_for
from freezegun import freeze_time

from billing.components.core.component import BillingComponent
from billing.components.core.helpers.module_result_helpers import (
    PrimitiveSources,
)
from billing.components.core.router.billing_requests import (
    CreateBillingRequestObject,
)
from billing.components.export.component import BillingExportComponent
from billing.components.export.models.billing_models import (
    BillingGeneralReportCPTCodes,
)
from billing.components.export.use_case.billing_general_export import (
    BillingGeneralExportableUseCase,
)
from billing.tests.billing_test_case import BillingTestCase
from billing.tests.test_helpers import (
    equal_csv_content,
    set_submission_calculation_type,
    simplify_export_response_keys,
)
from huma_plugins.components.export.dtos.export_models import ExportParameters
from huma_plugins.components.export.tests.IntegrationTests import enabled_modules
from huma_plugins.components.export.use_case.export_request_objects import (
    ExportRequestObject,
)
from huma_plugins.components.extended_authorization.component import (
    ExtendedAuthorizationComponent,
)
from huma_plugins.components.extended_authorization.component_options import (
    authorization_options,
)
from huma_plugins.components.extended_module_result.component import (
    ExtendedModuleResultComponent,
)
from huma_plugins.components.online_offline_call.component import (
    OnlineOfflineCallComponent,
)
from sdk.auth.component import AuthComponent
from sdk.common.utils.validators import utc_str_field_to_val
from sdk.deployment.component import DeploymentComponent
from sdk.deployment.dtos.status import EnableStatus
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.modules import BloodPressureModule
from sdk.module_result.modules.blood_pressure import BloodPressureDTO
from sdk.organization.component import OrganizationComponent
from sdk.versioning.component import VersionComponent

DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
SUPER_ADMIN_ID = "5e8f0c74b50aa9656c34789a"
MANAGER_ID = "5e8f0c74b50aa9656c34789d"
CLINICIAN_ID = "64de06d743c2b2d1a11edf41"
USER_ID = "5e8f0c74b50aa9656c34789b"
USER_ID_2 = "5e8f0c74b50aa9656c34789c"
PATTERN_FILE_NAME_DATE = r"GeneralBilling_\d{4}-\d{2}-\d{2}_\d{4}-\d{2}-\d{2}"
EXPORT_TYPE = BillingGeneralExportableUseCase.MODULE_NAME
EXPORT_SECTION = BillingGeneralExportableUseCase.BillingGeneralReportSections.USER_CPT_CODES_DATA


class GeneralReportExportTestCase(BillingTestCase):
    components = [
        AuthComponent(),
        ExtendedAuthorizationComponent(
            component_options=authorization_options,
        ),
        BillingComponent(),
        DeploymentComponent(),
        OrganizationComponent(),
        ExtendedModuleResultComponent(
            additional_modules=[m() for m in enabled_modules],
        ),
        BillingExportComponent(),
        OnlineOfflineCallComponent(),
        VersionComponent(server_version="1.0.0", api_version="1.0.0"),
    ]
    fixtures = [
        Path(__file__).parent.joinpath("fixtures/deployments_dump.json"),
    ]

    def _submit_multiple_module_results(self, year=2022, month=1, day_from=2, day_to=18, user_id=USER_ID):
        for day in range(day_from, day_to):
            start_dt = utc_str_field_to_val(datetime(year, month, day))
            self._submit_sample_module_results(user_id=user_id, start_date=start_dt)

    def _set_calendar_calculation_type(self, use_calendar_calculation=True):
        set_submission_calculation_type(DEPLOYMENT_ID, use_calendar_calculation)

    @freeze_time("2022-06-30T10:00:00.000Z")
    def test_export_for_calendar_period_calculations(self):
        self._set_calendar_calculation_type()

        self._submit_multiple_module_results(month=6)

        export = self.send_export_request(self._export_payload())
        report_section = export[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]
        self.assertEqual(
            16,
            report_section.get(BillingGeneralReportCPTCodes.NUMBER_OF_SUBMISSIONS_DAYS),
        )
        self.assertEqual(1, report_section.get(BillingGeneralReportCPTCodes.CPT_99453))
        self.assertEqual(1, report_section.get(BillingGeneralReportCPTCodes.CPT_99454))

    @freeze_time("2022-07-02T10:00:00.000Z")
    def test_export_for_calendar_period_one_day_overflow_next_month(self):
        self._set_calendar_calculation_type()

        with freeze_time("2022-06-30T10:00:00.000Z"):
            self._submit_multiple_module_results(month=6, day_from=15, day_to=30)
        self._submit_sample_module_results(start_date="2022-07-01T00:00:00Z")

        export = self.send_export_request(self._export_payload())
        report_section = export[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]
        self.assertEqual(
            16,
            report_section.get(BillingGeneralReportCPTCodes.NUMBER_OF_SUBMISSIONS_DAYS),
        )
        self.assertEqual(0, report_section.get(BillingGeneralReportCPTCodes.CPT_99453))
        self.assertEqual(0, report_section.get(BillingGeneralReportCPTCodes.CPT_99454))

    @freeze_time("2022-07-10T10:00:00.000Z")
    def test_export_for_calendar_period_three_periods_in_completed(self):
        self._set_calendar_calculation_type()
        with freeze_time("2022-04-30T10:00:00.000Z"):
            self._submit_sample_module_results(start_date="2022-04-03T00:00:00.039Z")
        with freeze_time("2022-05-30T10:00:00.000Z"):
            self._submit_sample_module_results(start_date="2022-05-03T00:00:00.039Z")
        with freeze_time("2022-06-30T10:00:00.000Z"):
            self._submit_sample_module_results(start_date="2022-06-03T00:00:00.039Z")

        export = self.send_export_request(self._export_payload())
        report_section = export[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]

        self.assertEqual(3, report_section.get("numberOfSubmissionDays"))
        self.assertEqual(0, report_section.get(BillingGeneralReportCPTCodes.CPT_99453))
        self.assertEqual(0, report_section.get(BillingGeneralReportCPTCodes.CPT_99454))

    @freeze_time("2022-07-10T10:00:00.000Z")
    def test_export_for_calendar_period_zero_submissions(self):
        self._set_calendar_calculation_type()

        export = self.send_export_request(self._export_payload())
        report_section = export[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]

        self.assertEqual(0, report_section.get("numberOfSubmissionDays"))
        self.assertEqual(0, report_section.get(BillingGeneralReportCPTCodes.CPT_99453))
        self.assertEqual(0, report_section.get(BillingGeneralReportCPTCodes.CPT_99454))

        # 0 submissions in April
        # 16 submissions in May
        with freeze_time("2022-05-30T10:00:00.000Z"):
            self._submit_multiple_module_results(month=5, day_from=14, day_to=30)
        # 0 submissions in June

        export = self.send_export_request(self._export_payload())
        report_section = export[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]

        self.assertEqual(16, report_section.get("numberOfSubmissionDays"))
        self.assertEqual(1, report_section.get(BillingGeneralReportCPTCodes.CPT_99453))
        self.assertEqual(1, report_section.get(BillingGeneralReportCPTCodes.CPT_99454))

    def test_export_for_calendar_period__TWO_billable_1_non_billable_periods(self):
        self._set_calendar_calculation_type()

        # 16 submissions in April
        with freeze_time("2022-04-30T10:00:00.000Z"):
            self._submit_multiple_module_results(month=4, day_from=2, day_to=18)
        # 16 submissions in June
        with freeze_time("2022-06-30T10:00:00.000Z"):
            self._submit_multiple_module_results(month=6, day_from=2, day_to=18)

        export = self.send_export_request(self._export_payload())
        report_section = export[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]

        self.assertEqual(32, report_section.get("numberOfSubmissionDays"))
        self.assertEqual(1, report_section.get(BillingGeneralReportCPTCodes.CPT_99453))
        self.assertEqual(2, report_section.get(BillingGeneralReportCPTCodes.CPT_99454))

    @freeze_time("2022-07-10T10:00:00.000Z")
    def test_export_for_calendar_period_calculations_first_period_in_progress(self):
        self._set_calendar_calculation_type()
        # 8 submission in May
        end_of_may = "2022-05-31T10:00:00.000Z"
        with freeze_time(end_of_may):
            self._submit_multiple_module_results(month=5, day_from=1, day_to=9)

        export = self.send_export_request(self._export_payload())
        report_section = export[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]

        self.assertEqual(8, report_section.get("numberOfSubmissionDays"))
        self.assertEqual(0, report_section.get(BillingGeneralReportCPTCodes.CPT_99453))
        self.assertEqual(0, report_section.get(BillingGeneralReportCPTCodes.CPT_99454))

    @freeze_time("2022-07-31T10:00:00.000Z")
    def test_export_for_calendar_calculations_with_cpt_53_is_not_counted_for_second_bp(
        self,
    ):
        self._set_calendar_calculation_type()

        # cpt 53 is met in June
        with freeze_time("2022-06-30T10:00:00.000Z"):
            self._submit_multiple_module_results(month=6)
        self._submit_multiple_module_results(month=7)

        payload = self._export_payload()
        payload[ExportParameters.FROM_DATE] = "2022-07-1T10:00:00.000Z"
        payload[ExportParameters.TO_DATE] = "2022-07-31T10:00:00.000Z"
        export = self.send_export_request(payload)
        report_section = export[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]
        self.assertEqual(
            16,
            report_section.get(BillingGeneralReportCPTCodes.NUMBER_OF_SUBMISSIONS_DAYS),
        )
        self.assertEqual(0, report_section.get(BillingGeneralReportCPTCodes.CPT_99453))
        self.assertEqual(1, report_section.get(BillingGeneralReportCPTCodes.CPT_99454))

    @freeze_time("2022-12-31T10:00:00.000Z")
    def test_export_for_calendar_period_calculations_winter_month_december(self):
        self._set_calendar_calculation_type()

        # 15 submissions before 31st of December
        with freeze_time("2022-12-30T10:00:00.000Z"):
            self._submit_multiple_module_results(month=12, day_to=17)

        # 1 submission on the 31 of December
        with freeze_time("2022-12-31T10:00:00.000Z"):
            start = utc_str_field_to_val(datetime(2022, 12, 31))
            self._submit_sample_module_results(user_id=USER_ID, start_date=start)

        payload = self._export_payload()
        payload[ExportParameters.FROM_DATE] = "2022-12-01T00:00:00.039Z"
        payload[ExportParameters.TO_DATE] = "2022-12-31T00:00:00.039Z"
        export = self.send_export_request(payload)
        report_section = export[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]
        self.assertEqual(
            16,
            report_section.get(BillingGeneralReportCPTCodes.NUMBER_OF_SUBMISSIONS_DAYS),
        )
        self.assertEqual(0, report_section.get(BillingGeneralReportCPTCodes.CPT_99453))
        self.assertEqual(0, report_section.get(BillingGeneralReportCPTCodes.CPT_99454))

    @freeze_time("2023-01-31T10:00:00.000Z")
    def test_export_for_calendar_period_calculations_winter_month_january(self):
        self._set_calendar_calculation_type()

        # 1 submission on the 31 of December
        with freeze_time("2022-12-31T10:00:00.000Z"):
            start = utc_str_field_to_val(datetime(2022, 12, 31))
            self._submit_sample_module_results(user_id=USER_ID, start_date=start)

        # 15 submissions in January
        with freeze_time("2023-01-29T10:00:00.000Z"):
            self._submit_multiple_module_results(year=2023, month=1, day_to=17)

        payload = self._export_payload()
        payload[ExportParameters.FROM_DATE] = "2022-12-01T00:00:00.039Z"
        payload[ExportParameters.TO_DATE] = "2023-01-31T00:00:00.039Z"
        export = self.send_export_request(payload)
        report_section = export[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]
        self.assertEqual(
            16,
            report_section.get(BillingGeneralReportCPTCodes.NUMBER_OF_SUBMISSIONS_DAYS),
        )
        self.assertEqual(1, report_section.get(BillingGeneralReportCPTCodes.CPT_99453))
        self.assertEqual(1, report_section.get(BillingGeneralReportCPTCodes.CPT_99454))

    @freeze_time("2022-06-30T10:00:00.000Z")
    def test_export_for_calendar_period_calculations_winter_month_february(self):
        self._set_calendar_calculation_type()

        # one submission on the 31st of January
        with freeze_time("2022-01-31T10:00:00.000Z"):
            start = utc_str_field_to_val(datetime(2022, 1, 31))
            self._submit_sample_module_results(user_id=USER_ID, start_date=start)

        # 15 submissions in February
        with freeze_time("2022-02-28T10:00:00.000Z"):
            self._submit_multiple_module_results(month=2, day_to=17)

        payload = self._export_payload()
        payload[ExportParameters.FROM_DATE] = "2022-01-01T00:00:00.039Z"
        payload[ExportParameters.TO_DATE] = "2022-02-28T00:00:00.039Z"
        export = self.send_export_request(payload)
        report_section = export[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]
        self.assertEqual(
            16,
            report_section.get(BillingGeneralReportCPTCodes.NUMBER_OF_SUBMISSIONS_DAYS),
        )
        self.assertEqual(1, report_section.get(BillingGeneralReportCPTCodes.CPT_99453))
        self.assertEqual(1, report_section.get(BillingGeneralReportCPTCodes.CPT_99454))

    @freeze_time("2022-02-27T10:00:00.000Z")
    def test_export_for_calendar_period_calculations_dos_out_of_report_time(self):
        self._set_calendar_calculation_type()

        # one submission on the 31st of January
        with freeze_time("2022-01-31T10:00:00.000Z"):
            start = utc_str_field_to_val(datetime(2022, 1, 31))
            self._submit_sample_module_results(user_id=USER_ID, start_date=start)

        # 15 submissions in February
        with freeze_time("2022-02-27T10:00:00.000Z"):
            self._submit_multiple_module_results(month=2, day_to=17)

        payload = self._export_payload()
        payload[ExportParameters.FROM_DATE] = "2022-01-01T00:00:00.039Z"
        payload[ExportParameters.TO_DATE] = "2022-02-27T00:00:00.039Z"
        export = self.send_export_request(payload)
        report_section = export[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]
        self.assertEqual(
            16,
            report_section.get(BillingGeneralReportCPTCodes.NUMBER_OF_SUBMISSIONS_DAYS),
        )
        self.assertEqual(0, report_section.get(BillingGeneralReportCPTCodes.CPT_99453))
        self.assertEqual(0, report_section.get(BillingGeneralReportCPTCodes.CPT_99454))

    @freeze_time("2024-12-02T10:00:00.000Z")
    def test_export_for_calendar_period__first_billing_submission_is_later_then_export_time_frame(
        self,
    ):
        self._set_calendar_calculation_type()

        start = utc_str_field_to_val(datetime.now())
        self._submit_sample_module_results(user_id=USER_ID, start_date=start)

        payload = self._export_payload()
        payload[ExportParameters.FROM_DATE] = "2024-11-01T00:00:00.000Z"
        payload[ExportParameters.TO_DATE] = "2024-11-30T00:00:00.000Z"
        export = self.send_export_request(payload)
        report_section = export[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]
        self.assertEqual(
            0,
            report_section.get(BillingGeneralReportCPTCodes.NUMBER_OF_SUBMISSIONS_DAYS),
        )
        self.assertEqual(0, report_section.get(BillingGeneralReportCPTCodes.CPT_99453))
        self.assertEqual(0, report_section.get(BillingGeneralReportCPTCodes.CPT_99454))

    @freeze_time("2022-06-30T10:00:00.000Z")
    def test_export_general_report_in_json_format(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }
        self._submit_sample_module_results()
        rsp = self.send_export_request(payload, replace_keys=False)
        rsp = simplify_export_response_keys(rsp, PATTERN_FILE_NAME_DATE, EXPORT_TYPE)
        self._assert_json_content_is_equal(rsp, "general_report_json_format_sample.json")

    @freeze_time("2022-06-30T10:00:00.000Z")
    def test_export_general_report_with_number_of_calls_1(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-02-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-02-28T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }
        rsp = self.send_export_request(payload)
        report_section = rsp[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]
        self.assertEqual(1, report_section[BillingGeneralReportCPTCodes.NUMBER_OF_CALLS])

    def test_export_general_report_when_date_of_service_is_not_met(self):
        for i in range(20, 41):
            self._add_time_tracking(f"2023-02-06T14:{i}:01.039Z", f"2023-02-06T14:{i}:59.039Z")
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-02-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-02-25T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }

        rsp = self.send_export_request(payload)
        report_section = rsp[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]
        self.assertEqual(0, report_section[BillingGeneralReportCPTCodes.CPT_99457])
        self.assertEqual(20, report_section[BillingGeneralReportCPTCodes.MONITORING_TIME_MINS])
        self.assertEqual(1, report_section[BillingGeneralReportCPTCodes.NUMBER_OF_CALLS])

    @freeze_time("2022-08-07T10:00:00.000Z")
    def test_export_general_report_in_JSON_format_in_future(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.FROM_DATE: "2022-07-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2022-08-31T00:00:00.039Z",
        }

        for i in range(20, 41):
            self._add_time_tracking(f"2022-08-05T14:{i}:01.039Z", f"2022-08-05T14:{i}:59.039Z")
        self._submit_sample_module_results(start_date="2022-07-01T00:00:00.039Z")
        self._submit_sample_module_results(start_date="2022-08-05T00:00:00.039Z")
        self._submit_sample_module_results(start_date="2022-08-07T10:01:00.039Z")

        rsp = self.send_export_request(payload, replace_keys=False)
        self._assert_json_content_is_equal(rsp, "general_report_json_format_in_future.json")

    def test_export_general_report_with_cpt_status_99457_1(self):
        """
        99457 is the CPT code for first 20 minutes of monitoring
        """
        for i in range(20, 41):
            self._add_time_tracking(f"2023-02-06T14:{i}:01.039Z", f"2023-02-06T14:{i}:59.039Z")
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-02-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-02-28T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }
        rsp = self.send_export_request(payload)
        report_section = rsp[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]
        self.assertEqual(1, report_section[BillingGeneralReportCPTCodes.NUMBER_OF_CALLS])
        self.assertEqual(1, report_section[BillingGeneralReportCPTCodes.CPT_99457])
        self.assertEqual(20, report_section[BillingGeneralReportCPTCodes.MONITORING_TIME_MINS])
        # check if the rest are 0
        self.assertEqual(0, report_section[BillingGeneralReportCPTCodes.CPT_99458])
        self.assertEqual(12, len(report_section))
        self.assertEqual(3, len([k for k in report_section if report_section[k] != 0]))

    def test_export_general_report_when_date_of_service_of_second_period_is_not_met(
        self,
    ):
        for i in range(20, 41):
            self._add_time_tracking(f"2023-02-06T14:{i}:01.039Z", f"2023-02-06T14:{i}:59.039Z")
        for i in range(20, 41):
            self._add_time_tracking(f"2023-03-06T14:{i}:01.039Z", f"2023-03-06T14:{i}:59.039Z")
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-02-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-03-15T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }
        rsp = self.send_export_request(payload)
        report_section = rsp[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]
        self.assertEqual(1, report_section[BillingGeneralReportCPTCodes.CPT_99457])
        self.assertEqual(40, report_section[BillingGeneralReportCPTCodes.MONITORING_TIME_MINS])
        self.assertEqual(2, report_section[BillingGeneralReportCPTCodes.NUMBER_OF_CALLS])

    def test_export_general_report_with_two_period(self):
        for i in range(20, 41):
            self._add_time_tracking(f"2023-02-06T14:{i}:01.039Z", f"2023-02-06T14:{i}:59.039Z")
        for i in range(20, 41):
            self._add_time_tracking(f"2023-03-06T14:{i}:01.039Z", f"2023-03-06T14:{i}:59.039Z")
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-02-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-03-31T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }
        rsp = self.send_export_request(payload)
        report_section = rsp[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]
        self.assertEqual(
            2,  # 2 periods
            report_section[BillingGeneralReportCPTCodes.CPT_99457],
        )
        self.assertEqual(40, report_section[BillingGeneralReportCPTCodes.MONITORING_TIME_MINS])
        self.assertEqual(
            2,  # 2 calls
            report_section[BillingGeneralReportCPTCodes.NUMBER_OF_CALLS],
        )

    def test_export_general_report_with_cpt_status_99458_1x(self):
        for i in range(10, 52):
            self._add_time_tracking(f"2023-02-06T14:{i}:01.039Z", f"2023-02-06T14:{i}:59.039Z")
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-02-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-02-28T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }
        rsp = self.send_export_request(payload)
        report_section = rsp[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]
        self.assertEqual(1, report_section[BillingGeneralReportCPTCodes.NUMBER_OF_CALLS])
        self.assertEqual(1, report_section[BillingGeneralReportCPTCodes.CPT_99457])
        self.assertEqual(1, report_section[BillingGeneralReportCPTCodes.CPT_99458])
        self.assertEqual(40, report_section[BillingGeneralReportCPTCodes.MONITORING_TIME_MINS])

    def test_export_general_report_with_cpt_status_99458_2x(self):
        for i in range(10, 31):
            for j in range(10, 13):
                self._add_time_tracking(f"2023-02-06T{j}:{i}:01.039Z", f"2023-02-06T{j}:{i}:59.039Z")
        payload = {
            **self._export_payload(),
            ExportParameters.FROM_DATE: "2023-02-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-02-28T00:00:00.039Z",
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }
        rsp = self.send_export_request(payload)
        report_section = rsp[USER_ID][EXPORT_TYPE][0][EXPORT_SECTION]
        self.assertEqual(1, report_section[BillingGeneralReportCPTCodes.NUMBER_OF_CALLS])
        self.assertEqual(1, report_section[BillingGeneralReportCPTCodes.CPT_99457])
        self.assertEqual(2, report_section[BillingGeneralReportCPTCodes.CPT_99458])
        self.assertEqual(60, report_section[BillingGeneralReportCPTCodes.MONITORING_TIME_MINS])

    @freeze_time("2022-06-30T10:00:00.000Z")
    def test_export_general_report_in_csv_format(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.CSV.value,
        }
        self._submit_sample_module_results()
        self._submit_sample_module_results(user_id=USER_ID_2)
        rsp = self.send_export_request(payload, replace_keys=False)
        self._assert_csv_content_is_equal(rsp, "general_report_csv_format_sample.csv")

    @freeze_time("2022-06-30T10:00:00.000Z")
    def test_export_general_report_WHEN_submission_cpt_codes_have_one_cycle(self):
        """
        Testing the submission period of 2022-04-01 to 2022-07-01 (3 months)
        where there is only one period has enough submissions to trigger the
        cpt codes 99453 and 99454
        """
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.INCLUDE_USER_META_DATA: False,
        }
        for i in range(1, 17):
            start_date = f"2022-06-{i:02}T00:00:00.039Z"
            self._submit_sample_module_results(start_date=start_date)
        rsp = self.send_export_request(payload, replace_keys=False)
        rsp = self._simplify_export_response_keys(rsp)
        self._assert_json_content_is_equal(rsp, "general_report_json_format_in_one_period.json")

    @freeze_time("2022-06-30T10:00:00.000Z")
    def test_export_general_report_WHEN_metadata_excluded(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.INCLUDE_USER_META_DATA: False,
        }
        self._submit_sample_module_results()
        rsp = self.send_export_request(payload, replace_keys=False)
        rsp = self._simplify_export_response_keys(rsp)
        self._assert_json_content_is_equal(rsp, "general_report_exclude_user_meta_data.json")

    @freeze_time("2022-06-30T10:00:00.000Z")
    def test_export_general_report_in_json_format_deidentified_data(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.DEIDENTIFIED: True,
        }
        self._submit_sample_module_results()
        rsp = self.send_export_request(payload, replace_keys=False)
        rsp = self._simplify_export_response_keys(rsp)
        self._assert_json_content_is_equal(rsp, "general_report_json_format_deidentified_sample.json")

    @freeze_time("2023-02-08T10:00:00.000Z")
    def test_export_general_report_WHEN_month_is_28_days(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.INCLUDE_USER_META_DATA: False,
            ExportParameters.FROM_DATE: "2023-01-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-03-01T00:00:00.039Z",
        }
        self._submit_sample_module_results(start_date="2023-01-10T00:00:00.039Z")
        for i in range(13, 29):
            self._submit_sample_module_results(start_date=f"2023-01-{i}T00:00:00.039Z")

        rsp = self.send_export_request(payload)
        report_section = rsp[USER_ID]["GeneralBilling"][0]["report"]
        self.assertEqual(1, report_section["cpt99453"])
        self.assertEqual(1, report_section["cpt99454"])
        self.assertEqual(17, report_section["numberOfSubmissions"])
        self.assertEqual(17, report_section["numberOfSubmissionDays"])

    @freeze_time("2023-03-05T10:00:00.000Z")
    def test_export_general_report_WHEN_there_is_a_record_in_export_to_date(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.INCLUDE_USER_META_DATA: False,
            ExportParameters.FROM_DATE: "2023-01-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-03-04T00:00:00.039Z",
        }
        self._submit_sample_module_results(start_date="2023-02-10T00:00:00.039Z")
        self._submit_sample_module_results(start_date="2023-03-04T13:00:00.039Z")
        rsp = self.send_export_request(payload)
        report_section = rsp[USER_ID]["GeneralBilling"][0]["report"]
        self.assertEqual(0, report_section["cpt99453"])
        self.assertEqual(0, report_section["cpt99454"])
        self.assertEqual(2, report_section["numberOfSubmissions"])
        self.assertEqual(2, report_section["numberOfSubmissionDays"])

    @freeze_time("2020-01-15T10:00:00.000Z")
    def test_export_general_report_WHEN_submission_date_is_before_onboarding(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.INCLUDE_USER_META_DATA: False,
            ExportParameters.FROM_DATE: "2020-01-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-03-04T00:00:00.039Z",
        }
        self._submit_sample_module_results(start_date="2020-01-10T00:01:00.039Z")
        self._submit_sample_module_results(start_date="2020-01-10T00:00:00.039Z")

        rsp = self.send_export_request(payload)
        report_section = rsp[USER_ID]["GeneralBilling"][0]["report"]
        self.assertEqual(0, report_section["numberOfSubmissions"])
        self.assertEqual(0, report_section["numberOfSubmissionDays"])

    @freeze_time("2023-01-15T10:00:00.000Z")
    def test_export_general_report_WHEN_there_is_multiple_records_in_one_day(self):
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.INCLUDE_USER_META_DATA: False,
            ExportParameters.FROM_DATE: "2023-01-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2023-03-04T00:00:00.039Z",
        }
        self._submit_sample_module_results(start_date="2023-01-10T00:01:00.039Z")
        self._submit_sample_module_results(start_date="2023-01-10T00:00:00.039Z")
        self._submit_sample_module_results(start_date="2023-01-10T12:00:00.039Z")
        self._submit_sample_module_results(start_date="2023-01-10T22:23:23.039Z")

        rsp = self.send_export_request(payload)
        report_section = rsp[USER_ID]["GeneralBilling"][0]["report"]
        self.assertEqual(0, report_section["cpt99453"])
        self.assertEqual(0, report_section["cpt99454"])
        self.assertEqual(4, report_section["numberOfSubmissions"])
        self.assertEqual(1, report_section["numberOfSubmissionDays"])

    # TODO: #django-test this test doesn't work in postgres anymore, please fix it
    # @freeze_time("2024-01-15T10:00:00.000Z")
    # def test_export_general_report_for_random_complex_scenario(self):
    #     @freeze_time("2023-10-29T10:00:00.000Z")
    #     def _submit_submission_in_first_cycles():
    #         for i in range(10, 16):
    #             self._submit_sample_module_results(start_date=f"2023-10-{i}T20:00:00.039Z")
    #
    #     @freeze_time("2023-11-27T10:00:00.000Z")
    #     def _submit_submission_in_second_cycles():
    #         for i in range(11, 27):
    #             self._submit_sample_module_results(start_date=f"2023-11-{i}T20:00:00.039Z")
    #
    #     @freeze_time("2023-12-30T10:00:00.000Z")
    #     def _submit_submission_in_third_cycles():
    #         for i in range(14, 18):
    #             self._submit_sample_module_results(start_date=f"2023-12-{i}T20:00:00.039Z")
    #
    #     payload = {
    #         **self._export_payload(),
    #         ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
    #         ExportParameters.INCLUDE_USER_META_DATA: False,
    #         ExportParameters.FROM_DATE: "2023-09-01T00:00:00.039Z",
    #         ExportParameters.TO_DATE: "2024-01-30T00:00:00.039Z",
    #     }
    #
    #     _submit_submission_in_first_cycles()
    #     _submit_submission_in_second_cycles()
    #     _submit_submission_in_third_cycles()
    #
    #     rsp = self.send_export_request(payload)
    #     report_section = rsp[USER_ID]["GeneralBilling"][0]["report"]
    #     self.assertEqual(26, report_section["numberOfSubmissions"])
    #     self.assertEqual(1, report_section["cpt99453"])
    #     self.assertEqual(1, report_section["cpt99454"])

    def test_export_is_not_affected_by_consent_or_econsent(self):
        self._enable_consent("Consent")
        self._enable_consent("EConsent")
        payload = {
            **self._export_payload(),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }
        self._submit_sample_module_results()

        rsp = self.send_export_request(payload, replace_keys=False)

        self.assertIn(USER_ID, rsp.json)

    def send_export_request(self, payload: dict, replace_keys: bool = True):
        rsp = self.flask_client.post(
            url_for(
                "billing_export_route.export_deployment",
                deployment_id=DEPLOYMENT_ID,
            ),
            headers=self.get_headers_for_token(MANAGER_ID),
            json=payload,
        )
        if not replace_keys:
            return rsp
        return self._simplify_export_response_keys(rsp)

    def send_add_time_tracking_request(self, payload: dict):
        return self.flask_client.post(
            url_for("billing_route.create_billing_remote_time_tracking", user_id=USER_ID),
            headers=self.get_headers_for_token(CLINICIAN_ID),
            json=payload,
        )

    def _add_time_tracking(self, start_date_time, end_date_time):
        body = {
            CreateBillingRequestObject.START_DATE_TIME: start_date_time,
            CreateBillingRequestObject.END_DATE_TIME: end_date_time,
        }
        rsp = self.send_add_time_tracking_request(body)
        self.assertEqual(201, rsp.status_code)

    @staticmethod
    def _export_payload():
        return {
            ExportParameters.FROM_DATE: "2022-04-01T00:00:00.039Z",
            ExportParameters.TO_DATE: "2022-07-01T00:00:00.039Z",
            ExportParameters.MODULE_NAMES: ["GeneralBilling"],
            ExportParameters.SINGLE_FILE_RESPONSE: True,
            ExportRequestObject.REQUESTER_ID: MANAGER_ID,
            ExportRequestObject.EXCLUDE_FIELDS: ["user.contactEmail"],
        }

    def _assert_json_content_is_equal(self, response, sample_file):
        sample_file_path = Path(__file__).parent.joinpath(f"fixtures/{sample_file}")
        # uncomment to update sample files
        # with open(sample_file_path, "w") as sample:
        #     if isinstance(response, dict):
        #         json.dump(response, sample)
        #     else:
        #         json.dump(json.loads(response.data), sample)
        with open(sample_file_path) as sample:
            file_data = sample.read()
            sample_file_json = json.loads(file_data)
            if isinstance(response, dict):
                self.assertEqual(sample_file_json, response)
            else:
                self.assertEqual(sample_file_json, json.loads(response.data))

    def _assert_csv_content_is_equal(self, response, sample_file):
        sample_file_path = Path(__file__).parent.joinpath(f"fixtures/{sample_file}")
        # uncomment to update sample files
        # with open(sample_file_path, "wb") as sample:
        #     sample.write(response.data)
        with open(sample_file_path, "rb") as sample:
            self.assertTrue(equal_csv_content(sample.read(), response.data))

    def _submit_sample_module_results(self, user_id: str = USER_ID, start_date: str = "2022-06-01T00:00:00.039Z"):
        module_results = {
            PrimitiveDTO.DEPLOYMENT_ID: DEPLOYMENT_ID,
            PrimitiveDTO.START_DATE_TIME: start_date,
            PrimitiveDTO.DEVICE_NAME: "device",
            PrimitiveDTO.SOURCE: f"{PrimitiveSources.HEALTH_KIT.value};other;possible;things",
            BloodPressureDTO.MODULE_ID: "BloodPressure",
            BloodPressureDTO.DIASTOLIC_VALUE: 60,
            BloodPressureDTO.SYSTOLIC_VALUE: 120,
        }
        body = [{**module_results, "type": BloodPressureDTO.get_primitive_name()}]
        rsp = self.flask_client.post(
            f"api/extensions/v1/user/{user_id}/module-result/{BloodPressureModule.moduleId}",
            headers=self.get_headers_for_token(MANAGER_ID),
            json=body,
        )
        self.assertEqual(201, rsp.status_code)

    def _enable_consent(self, onboarding_id="Consent"):
        url = url_for(
            "deployment_route.create_onboarding_module_config",
            deployment_id=DEPLOYMENT_ID,
        )
        body = {
            "onboardingId": onboarding_id,
            "status": EnableStatus.ENABLED.value,
            "order": 1,
        }
        rsp = self.flask_client.post(url, headers=self.get_headers_for_token(SUPER_ADMIN_ID), json=body)
        self.assertEqual(201, rsp.status_code)

    @staticmethod
    def _simplify_export_response_keys(response):
        return simplify_export_response_keys(response, PATTERN_FILE_NAME_DATE, EXPORT_TYPE)
