from flasgger import swag_from
from flask import request, make_response, send_file, g

from huma_plugins.components.export.router.policies import (
    get_identified_export_data,
    get_export_permission,
)
from huma_plugins.components.export.use_case.export_request_objects import (
    ExportUsersRequestObject,
)
from huma_plugins.components.export.use_case.export_use_cases import (
    ExportDeploymentUseCase,
)
from sdk.authorization.dtos.role.default_permissions import PolicyType
from sdk.common.constants import SWAGGER_DIR
from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.utils.flask_request_utils import (
    validate_request_body_type_is_object,
)
from sdk.common.utils.validators import remove_none_values
from sdk.deployment.iam.iam import IAMBlueprint

api = IAMBlueprint(
    "billing_export_route",
    __name__,
    url_prefix="/api/extensions/v1/export",
    policy=PolicyType.EXPORT_PATIENT_DATA,
)


@api.route("/deployment/<deployment_id>", methods=["POST"])
@api.require_policy(get_identified_export_data)
def export_deployment(deployment_id):
    body = validate_request_body_type_is_object(request)
    data = remove_none_values(
        {
            **body,
            ExportUsersRequestObject.DEPLOYMENT_ID: deployment_id,
            ExportUsersRequestObject.REQUESTER: g.authz_user.user,
            ExportUsersRequestObject.REQUESTER_ID: g.authz_user.id,
            ExportUsersRequestObject.FOR_THIRD_PARTY: g.authz_user.is_third_party(),
        }
    )
    request_object = ExportUsersRequestObject.from_dict(data)
    if not (request_object.fromDate and request_object.toDate):
        raise InvalidRequestException("start date and end date are mandatory")
    use_case = ExportDeploymentUseCase()
    response_object = use_case.execute(request_object)
    return make_response(
        send_file(
            response_object.filePath,
            download_name=response_object.filename,
            as_attachment=False,
            mimetype=response_object.contentType,
        )
    )


@api.route("/", methods=["POST"])
@api.require_policy(get_export_permission)
@swag_from(f"{SWAGGER_DIR}/export.yml")
def export():
    body = validate_request_body_type_is_object(request)
    data = remove_none_values(
        {
            **body,
            ExportUsersRequestObject.REQUESTER: g.authz_user.user,
            ExportUsersRequestObject.REQUESTER_ID: g.authz_user.id,
            ExportUsersRequestObject.FOR_THIRD_PARTY: g.authz_user.is_third_party(),
        }
    )
    request_object = ExportUsersRequestObject.from_dict(data)

    if not (request_object.fromDate and request_object.toDate):
        raise InvalidRequestException("start date and end date are mandatory")

    use_case = ExportDeploymentUseCase()
    response_object = use_case.execute(request_object)
    return make_response(
        send_file(
            response_object.filePath,
            download_name=response_object.filename,
            as_attachment=False,
            mimetype=response_object.contentType,
        )
    )
