from flask import Blueprint

from billing.components.helper.router.router import helper_router
from huma_plugins.components.auth_helper.component import AuthHelperComponent
from huma_plugins.components.auth_helper.config.config import AuthHelperConfig


class BillingHelperComponent(AuthHelperComponent):
    config_class = AuthHelperConfig
    tag_name = "authHelper"

    @property
    def blueprint(self) -> Blueprint | list[Blueprint] | None:
        # reusing original blueprints, but import is needed to register the changes
        router_path = "billing.components.helper.router"
        __import__(router_path)
        return [*super().blueprint, helper_router]
