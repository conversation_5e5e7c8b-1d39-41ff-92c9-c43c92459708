from billing.components.core.dtos.billing_models import BillingSubmissionDTO
from billing.components.core.helpers.alerts_helpers import extract_deployment_billing_by_deployment_id
from billing.components.core.helpers.billing_record_change_handler_helpers import (
    calculate_current_billing_period,
)
from billing.components.core.repository.billing_repository import (
    BillingSubmissionRepository,
)
from billing.components.core.tasks import (
    BillingSubmissionScheduleEvent,
    process_module_result_insertion,
)
from billing.components.helper.router.request_object import SubmissionHelperRequestObject
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams
from sdk.common.utils.validators import utc_str_field_to_val

LEAST_REQUIRED_SUBMISSIONS_EXCEPT_LATEST = 15


class SubmissionAutomationHelperUseCase(UseCase):
    request_object: SubmissionHelperRequestObject

    @autoparams()
    def __init__(self, repo: BillingSubmissionRepository):
        self.billing_repo = repo

    def process_request(self, request_object: SubmissionHelperRequestObject):
        billing_config = extract_deployment_billing_by_deployment_id(request_object.deploymentId)
        if billing_config is None or not billing_config.enabled:
            return

        submission = self._prepare_data(billing_config.useCalendarCalculation)
        self.billing_repo.create_update_billing_submission(submission, count=1)
        self._update_alerts()

    def _prepare_data(self, use_calendar: bool) -> BillingSubmissionDTO:
        data = {
            **self.request_object.to_dict(),
            BillingSubmissionDTO.DEVICE_DETAILS: "QA_HELPER",
            BillingSubmissionDTO.START_DATE: self.request_object.startDateTime.date(),
            BillingSubmissionDTO.START_DATE_TIME_UTC: self.request_object.startDateTime,
            BillingSubmissionDTO.IS_COMPLETED: self.request_object.isCompleted
            or self._check_is_completed(use_calendar),
        }
        return BillingSubmissionDTO.from_dict(data)

    def _check_is_completed(self, use_calendar: bool) -> bool:
        (
            very_first_submission,
            period_date_start,
            period_date_end,
        ) = calculate_current_billing_period(
            user_id=self.request_object.userId,
            deployment_id=self.request_object.deploymentId,
            use_calendar_calculation=use_calendar,
        )
        if not very_first_submission:
            return False

        submission_days_count = self.billing_repo.get_total_submissions_for_user_from_to(
            deployment_id=self.request_object.deploymentId,
            user_id=self.request_object.userId,
            from_date=period_date_start,
            to_date=period_date_end,
            return_count=True,
        )

        return submission_days_count >= LEAST_REQUIRED_SUBMISSIONS_EXCEPT_LATEST

    def _update_alerts(self):
        submission_event = BillingSubmissionScheduleEvent.from_dict(
            {
                BillingSubmissionScheduleEvent.USER_ID: self.request_object.userId,
                BillingSubmissionScheduleEvent.DEPLOYMENT_ID: self.request_object.deploymentId,
                BillingSubmissionScheduleEvent.PRIMITIVES_START_DATE_TIMES: [
                    utc_str_field_to_val(self.request_object.startDateTime)
                ],
            }
        )
        process_module_result_insertion.delay(submission_event.to_dict())
