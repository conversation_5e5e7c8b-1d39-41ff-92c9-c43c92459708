from billing.components.core.repository.billing_repository import (
    BillingProfileHistoryLogRepository,
)
from billing.components.helper.router.request_object import QAHelperBillingProfileHistoryLogRequestObject
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams


class QAHelperBillingProfileHistoryLogUseCase(UseCase):
    request_object: QAHelperBillingProfileHistoryLogRequestObject

    @autoparams()
    def process_request(
        self,
        request_object: QAHelperBillingProfileHistoryLogRequestObject,
        repo: BillingProfileHistoryLogRepository,
    ):
        user_id = request_object.userId
        deployment_id = request_object.deploymentId
        idx = repo.create_raw_billing_profile_history_log(
            user_id=user_id, deployment_id=deployment_id, **request_object.data
        )
        repo.add_extra_fields_to_billing_profile_history_log(idx, automation=True)
        return {"id": idx}
