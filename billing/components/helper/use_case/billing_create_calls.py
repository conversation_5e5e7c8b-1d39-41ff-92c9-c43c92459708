from billing.components.core.repository.billing_repository import BillingAlertsRepository
from billing.components.helper.router.request_object import CallHelperRequestObject
from huma_plugins.components.online_offline_call.dtos.video_models import OfflineVideoCallDTO
from huma_plugins.components.online_offline_call.events.post_create_offline_call_event import PostCreateOfflineCallEvent
from huma_plugins.components.online_offline_call.repository.video_repository import OnlineOfflineCallRepository
from sdk.common.adapter.event_bus_adapter import EventBusAdapter
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams


class CallAutomationHelperUseCase(UseCase):
    request_object: CallHelperRequestObject

    @autoparams()
    def __init__(
        self, call_repo: OnlineOfflineCallRepository, billing_repo: BillingAlertsRepository, event_bus: EventBusAdapter
    ):
        self.call_repo = call_repo
        self.billing_repo = billing_repo
        self.event_bus = event_bus

    def process_request(self, request_object: CallHelperRequestObject):
        self.call_repo.create_offline_call(
            manager_id=request_object.managerId,
            user_id=request_object.userId,
            note_id=request_object.noteId or "",
            start_dt=request_object.startDateTime,
            end_dt=request_object.endDateTime,
        )

        self._update_alerts()

    def _update_alerts(self):
        self.billing_repo.update_call_datetime(
            user_id=self.request_object.userId,
            current_call_datetime=self.request_object.endDateTime,
        )

        event = PostCreateOfflineCallEvent(
            user_id=self.request_object.userId,
            manager_id=self.request_object.managerId,
            deployment_id=self.request_object.deploymentId,
            start_dt=self.request_object.startDateTime,
            end_dt=self.request_object.endDateTime,
            status=OfflineVideoCallDTO.CallStatus.ANSWERED,
        )
        self.event_bus.emit(event)
