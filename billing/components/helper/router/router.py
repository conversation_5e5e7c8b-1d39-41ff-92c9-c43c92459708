import logging
from datetime import date

from apiflask import APIBlueprint
from flask import request, jsonify

from billing.components.core.router.billing_calculate_cumulative_requests import (
    CalculateCumulativeMonitoringTimeRequestObject,
)
from billing.components.core.router.billing_requests import CreateBillingRequestObject
from billing.components.core.use_case.billing_calculate_cumulative_use_cases import (
    CalculateCumulativeMonitoringTimeCPTCodeUseCase,
)
from billing.components.core.use_case.billing_use_cases import CreateBillingRemoteTimeTrackingUseCase
from billing.components.helper.router.request_object import (
    QAHelperBillingProfileHistoryLogRequestObject,
    SubmissionHelperRequestObject,
    CallHelperRequestObject,
)
from billing.components.helper.use_case.billing_create_calls import CallAutomationHelperUseCase
from billing.components.helper.use_case.billing_submission_automation_helper import SubmissionAutomationHelperUseCase
from billing.components.helper.use_case.update_billing_profile_history_log_usecase import (
    QAHelperBillingProfileHistoryLogUseCase,
)
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.services.authorization import AuthorizationService
from sdk.common.utils.flask_request_utils import get_request_json_dict_or_raise_exception

logger = logging.getLogger("BillingHelper")


helper_router = APIBlueprint(
    "billing_qa_helper",
    __name__,
    url_prefix="/helper/qa",
    template_folder="templates",
    static_folder="static",
)


@helper_router.post("/monitoring-time/<clinician_id>/user/<user_id>")
def monitoring_time(clinician_id: str, user_id: str):
    """
    Accepts a POST request to create monitoring time for a user.
    The request body should contain the start and end times in ISO format.
    """

    request_object = CreateBillingRequestObject.from_dict(
        {
            **request.json,
            CreateBillingRequestObject.USER: AuthorizedUser(
                AuthorizationService().retrieve_simple_user_profile(user_id)
            ),
            CreateBillingRequestObject.CLINICIAN: AuthorizedUser(
                AuthorizationService().retrieve_simple_user_profile(clinician_id)
            ),
        },
        use_validator_field=False,
    )
    response = CreateBillingRemoteTimeTrackingUseCase().execute(request_object)

    if (
        request_object.startDateTime.year != date.today().year
        or request_object.startDateTime.month != date.today().month
    ):
        return response

    calculate_cumulative_request_object = CalculateCumulativeMonitoringTimeRequestObject.from_dict(
        {CalculateCumulativeMonitoringTimeRequestObject.USER: request_object.user}, use_validator_field=False
    )
    CalculateCumulativeMonitoringTimeCPTCodeUseCase().execute(calculate_cumulative_request_object)

    return response


@helper_router.post("/video-call/user/<user_id>/deployments/<deployment_id>")
def add_video_call_automation_helper(user_id, deployment_id):
    body = get_request_json_dict_or_raise_exception(request)
    request_object = CallHelperRequestObject.from_dict(
        dict(
            userId=user_id,
            deploymentId=deployment_id,
            **body,
        )
    )
    response = CallAutomationHelperUseCase().execute(request_object)
    return jsonify(response), 201


@helper_router.post("/history-log/user/<user_id>")
def add_billing_profile_history_log(user_id: str):
    logger.info(f"add_billing_profile_history_log for user {user_id} - request: {request.json}")
    body = get_request_json_dict_or_raise_exception(request)
    deployment_id = body.get("deploymentId")
    create_date_time = body.get("createDateTime")

    request_obj = QAHelperBillingProfileHistoryLogRequestObject.from_dict(
        dict(
            deploymentId=deployment_id,
            userId=user_id,
            createDateTime=create_date_time,
            data=body,
        )
    )
    response = QAHelperBillingProfileHistoryLogUseCase().execute(request_obj)
    return jsonify(response), 200


@helper_router.post("/submission/user/<user_id>/deployment/<deployment_id>")
def add_submission_automation_helper(user_id, deployment_id):
    body = get_request_json_dict_or_raise_exception(request)
    data = {
        SubmissionHelperRequestObject.USER_ID: user_id,
        SubmissionHelperRequestObject.DEPLOYMENT_ID: deployment_id,
        **body,
    }
    request_object = SubmissionHelperRequestObject.from_dict(data)
    SubmissionAutomationHelperUseCase().execute(request_object)
    return jsonify({}), 201
