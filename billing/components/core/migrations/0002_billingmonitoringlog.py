# Generated by Django 5.1.5 on 2025-01-24 15:41

from django.db import migrations, models

from sdk.common.utils.file_utils import move_mongo_collection_to_postgres_table


class Migration(migrations.Migration):

    dependencies = [
        ("billing", "0001_initial"),
    ]

    @staticmethod
    def migrate_data(apps, schema_editor):
        def transformer(doc):
            if doc.get("initialCreateDateTime") is None:
                return None
            return doc

        move_mongo_collection_to_postgres_table(
            apps,
            "billing",
            migrations=[
                {
                    "model_name": "BillingMonitoringLog",
                    "mongo_model": "billingmonitoringlog",
                    "transformer": transformer,
                },
            ],
        )

    operations = [
        migrations.CreateModel(
            name="BillingMonitoringLog",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("mongoId", models.CharField(max_length=24, unique=True)),
                (
                    "originalLogId",
                    models.CharField(blank=True, max_length=24, null=True),
                ),
                ("deploymentId", models.Char<PERSON>ield(max_length=24)),
                ("createdById", models.CharField(max_length=24)),
                (
                    "lastModifiedById",
                    models.CharField(blank=True, max_length=24, null=True),
                ),
                ("userId", models.CharField(max_length=24)),
                ("timeTrackingId", models.CharField(max_length=24)),
                ("status", models.IntegerField(null=True)),
                ("action", models.CharField(max_length=255)),
                ("addendum", models.CharField(max_length=500, null=True, blank=True)),
                ("timeSpent", models.FloatField(null=True)),
                ("startDateTime", models.DateTimeField()),
                ("endDateTime", models.DateTimeField()),
                ("initialCreateDateTime", models.DateTimeField()),
                ("createDateTime", models.DateTimeField()),
                ("updateDateTime", models.DateTimeField()),
            ],
            options={
                "db_table": "billing_monitoring_log",
                "indexes": [
                    models.Index(
                        fields=["userId", "createDateTime"],
                        name="billing_mon_userId_5355f2_idx",
                    ),
                    models.Index(fields=["deploymentId"], name="billing_mon_deploym_423bee_idx"),
                    models.Index(fields=["createdById"], name="billing_mon_created_a07de2_idx"),
                    models.Index(fields=["timeTrackingId"], name="billing_mon_timeTra_204771_idx"),
                    models.Index(fields=["startDateTime"], name="billing_mon_startDa_37534f_idx"),
                ],
            },
        ),
        migrations.RunPython(migrate_data, lambda *args, **kwargs: 0, atomic=True),
    ]
