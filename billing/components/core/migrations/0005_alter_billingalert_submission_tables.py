# Generated by Django 5.1.5 on 2025-02-18 14:51

import datetime

import django.db.models.deletion
from django.db import migrations, models

import sdk.common.utils.mongo_utils
from sdk.authorization.models import User
from sdk.common.utils.file_utils import move_mongo_collection_to_postgres_table


def transform_alerts(data):
    for key in ["biologicalSex", "dateOfBirth", "familyName", "gender", "givenName", "userId", "labels"]:
        data.pop(key, None)
    data["userId"] = data.pop("userId", data.get("mongoId", None))
    data["user_id"] = data.pop("userId")

    if not User.objects.filter(mongoId=data["user_id"]).exists():
        return None

    return data


class Migration(migrations.Migration):

    dependencies = [
        ("authorization", "0001_initial"),
        ("billing", "0004_billingremotetimetrackingdocument"),
    ]

    @staticmethod
    def migrate_data(apps, schema_editor):
        move_mongo_collection_to_postgres_table(
            apps,
            "billing",
            migrations=[
                {
                    "model_name": "BillingAlert",
                    "mongo_model": "billingalerts",
                    "transformer": transform_alerts,
                },
                {
                    "model_name": "BillingSubmission",
                    "mongo_model": "billingsubmission",
                },
            ],
        )

    operations = [
        migrations.CreateModel(
            name="BillingSubmission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "mongoId",
                    models.CharField(
                        default=sdk.common.utils.mongo_utils.generate_obj_id,
                        max_length=24,
                        unique=True,
                    ),
                ),
                ("deploymentId", models.CharField(max_length=24)),
                ("primitiveId", models.CharField(max_length=24)),
                ("primitiveClassName", models.CharField(max_length=50)),
                ("userId", models.CharField(max_length=24)),
                ("deviceName", models.CharField(max_length=100)),
                ("deviceDetails", models.CharField(max_length=100, null=True, blank=True)),
                ("source", models.CharField(max_length=100, null=True, blank=True)),
                ("startDate", models.DateTimeField()),
                ("startDateTime", models.DateTimeField()),
                ("startDateTimeUTC", models.DateTimeField(null=True, blank=True)),
                ("isCompleted", models.BooleanField(null=True, blank=True)),
                (
                    "createDateTime",
                    models.DateTimeField(default=datetime.datetime.utcnow),
                ),
                (
                    "updateDateTime",
                    models.DateTimeField(default=datetime.datetime.utcnow),
                ),
                ("todaySubmissionCount", models.IntegerField(default=0)),
            ],
            options={
                "db_table": "billing_submission",
                "indexes": [
                    models.Index(
                        fields=["userId", "deploymentId", "startDateTime"],
                        name="user_deployment_start_date",
                    ),
                    models.Index(
                        fields=["userId", "deploymentId", "createDateTime"],
                        name="user_deployment_create_date",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="BillingAlert",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("mongoId", models.CharField(max_length=24, unique=True)),
                ("monitoringMinutes", models.FloatField(null=True, blank=True)),
                (
                    "createDateTime",
                    models.DateTimeField(default=datetime.datetime.utcnow),
                ),
                (
                    "updateDateTime",
                    models.DateTimeField(default=datetime.datetime.utcnow),
                ),
                ("nextSubmissionDoS", models.DateTimeField(null=True, blank=True)),
                ("lastMonitoringDate", models.DateTimeField(null=True, blank=True)),
                (
                    "submissionDates",
                    models.JSONField(encoder=sdk.common.adapter.sql.sql_utils.BSONEncoder, null=True, blank=True),
                ),
                ("callDateTime", models.DateTimeField(null=True, blank=True)),
                ("nextMonitoringDoS", models.DateTimeField(null=True, blank=True)),
                ("deploymentId", models.CharField(max_length=24)),
                ("deleteDateTime", models.DateTimeField(default=None, null=True, blank=True)),
                (
                    "user",
                    models.OneToOneField(
                        db_column="userId",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="authorization.user",
                        to_field="mongoId",
                    ),
                ),
                ("lastSubmitDateTime", models.DateTimeField(null=True, blank=True)),
            ],
            options={
                "db_table": "billing_alert",
            },
        ),
        migrations.RunPython(migrate_data, lambda *args, **kwargs: 0, atomic=True),
    ]
