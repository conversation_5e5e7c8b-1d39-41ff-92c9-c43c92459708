# Generated by Django 5.1.5 on 2025-02-04 13:01

from django.db import migrations, models

import sdk.common.utils.mongo_utils
from sdk.common.utils.file_utils import move_mongo_collection_to_postgres_table


class Migration(migrations.Migration):

    dependencies = [
        ("billing", "0003_alter_billingmonitoringlog_status_and_more"),
    ]

    @staticmethod
    def migrate_data(apps, schema_editor):
        move_mongo_collection_to_postgres_table(
            apps,
            "billing",
            migrations=[
                {
                    "model_name": "BillingRemoteTimeTracking",
                    "mongo_model": "billingremotetimetracking",
                },
            ],
        )

    operations = [
        migrations.CreateModel(
            name="BillingRemoteTimeTracking",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                (
                    "mongoId",
                    models.CharField(
                        default=sdk.common.utils.mongo_utils.generate_obj_id,
                        max_length=24,
                        unique=True,
                    ),
                ),
                ("deploymentId", models.CharField(max_length=24)),
                ("clinicianId", models.CharField(max_length=24)),
                ("startDateTime", models.DateTimeField()),
                ("endDateTime", models.DateTimeField()),
                ("userId", models.CharField(max_length=24)),
                ("effectiveStartDateTime", models.DateTimeField(null=True, blank=True)),
                ("effectiveEndDateTime", models.DateTimeField(null=True, blank=True)),
                ("effectiveDuration", models.FloatField(default=0)),
                ("createDateTime", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "db_table": "billing_remote_time_tracking",
                "indexes": [
                    models.Index(
                        fields=["userId", "createDateTime"],
                        name="billing_rem_userId_485d6a_idx",
                    ),
                    models.Index(fields=["deploymentId"], name="billing_rem_deploym_4079b2_idx"),
                    models.Index(fields=["clinicianId"], name="billing_rem_clinici_331b5b_idx"),
                    models.Index(fields=["startDateTime"], name="billing_rem_startDa_13c814_idx"),
                ],
            },
        ),
        migrations.RunPython(migrate_data, lambda *args, **kwargs: 0, atomic=True),
    ]
