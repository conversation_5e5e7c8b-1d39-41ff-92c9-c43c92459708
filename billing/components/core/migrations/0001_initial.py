# Generated by Django 5.1.5 on 2025-01-23 12:03

import datetime

from django.db import migrations, models

from sdk.common.utils.file_utils import move_mongo_collection_to_postgres_table


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    @staticmethod
    def migrate_data(apps, schema_editor):
        def model_selector(mongo_map: dict):
            cls = mongo_map["_cls"]
            if cls == "MongoBillingDiagnosisHistoryLog":
                return "BillingDiagnosisHistoryLog"
            elif cls == "MongoBillingInsuranceCarrierHistoryLog":
                return "BillingInsuranceCarrierHistoryLog"
            elif cls == "MongoBillingProviderHistoryLog":
                return "BillingProviderHistoryLog"
            raise Exception(f"{cls} is an unknown class")

        def transformer(mongo_map: dict):
            if "userId" not in mongo_map:
                return None

            if "icd10code" in mongo_map:
                mongo_map["icd10Code"] = mongo_map.pop("icd10code")
            if "icd10Code" in mongo_map and len(mongo_map["icd10Code"]) > 10:
                mongo_map["icd10Code"] = mongo_map["icd10Code"][:10]
            if "automation" in mongo_map:
                mongo_map.pop("automation")
            mongo_map["createDateTime"] = mongo_map.pop("createDatetime", None) or mongo_map.pop("createDateTime", None)
            return mongo_map

        move_mongo_collection_to_postgres_table(
            apps,
            "billing",
            migrations=[
                {
                    "model_name": "billing_profile_history tables",
                    "mongo_model": "billingprofilehistory",
                    "transformer": transformer,
                    "model_selector": model_selector,
                },
            ],
        )

    operations = [
        migrations.CreateModel(
            name="BillingDiagnosisHistoryLog",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("mongoId", models.CharField(default=None, max_length=24, unique=True)),
                ("userId", models.CharField(max_length=24)),
                ("deploymentId", models.CharField(max_length=24)),
                (
                    "createDateTime",
                    models.DateTimeField(default=datetime.datetime.utcnow),
                ),
                ("order", models.IntegerField()),
                ("description", models.TextField()),
                ("icd10Code", models.CharField(max_length=10)),
            ],
            options={
                "db_table": "billing_profile_log_diagnosis",
                "indexes": [
                    models.Index(
                        fields=["userId", "createDateTime"],
                        name="billing_pro_userId_8ec5b2_idx",
                    ),
                    models.Index(fields=["userId"], name="billing_pro_userId_5c4122_idx"),
                ],
            },
        ),
        migrations.CreateModel(
            name="BillingInsuranceCarrierHistoryLog",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("mongoId", models.CharField(default=None, max_length=24, unique=True)),
                ("userId", models.CharField(max_length=24)),
                ("deploymentId", models.CharField(max_length=24)),
                (
                    "createDateTime",
                    models.DateTimeField(default=datetime.datetime.utcnow),
                ),
                ("order", models.IntegerField(blank=True, default=None, null=True)),
                ("payerId", models.CharField(max_length=24)),
                ("groupName", models.CharField(max_length=255)),
            ],
            options={
                "db_table": "billing_profile_log_insurance_carrier",
                "indexes": [
                    models.Index(
                        fields=["userId", "createDateTime"],
                        name="billing_pro_userId_a75778_idx",
                    ),
                    models.Index(fields=["userId"], name="billing_pro_userId_c8be16_idx"),
                ],
            },
        ),
        migrations.CreateModel(
            name="BillingProviderHistoryLog",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("mongoId", models.CharField(default=None, max_length=24, unique=True)),
                ("userId", models.CharField(max_length=24)),
                ("deploymentId", models.CharField(max_length=24)),
                (
                    "createDateTime",
                    models.DateTimeField(default=datetime.datetime.utcnow),
                ),
                ("billingProviderId", models.CharField(max_length=24, blank=True, null=True)),
                ("billingProviderName", models.CharField(max_length=255)),
            ],
            options={
                "db_table": "billing_profile_log_provider",
                "indexes": [
                    models.Index(
                        fields=["userId", "createDateTime"],
                        name="billing_pro_userId_cfd9e5_idx",
                    ),
                    models.Index(fields=["userId"], name="billing_pro_userId_00891a_idx"),
                ],
            },
        ),
        migrations.RunPython(migrate_data, lambda *args, **kwargs: 0, atomic=True),
    ]
