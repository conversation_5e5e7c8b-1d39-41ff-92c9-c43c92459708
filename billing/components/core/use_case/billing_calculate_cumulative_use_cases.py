from datetime import datetime

from dateutil.relativedelta import relativedelta

from billing.components.core.dtos.deployment_billing import (
    ProductType,
)
from billing.components.core.helpers.alerts_helpers import (
    get_billing_product_type,
)
from billing.components.core.helpers.calculate_cumulative_helpers import (
    get_status_for_cpt_mapping_3,
    get_status_for_cpt_mapping_4_1x,
    get_status_for_cpt_mapping_4_2x,
)
from billing.components.core.repository.billing_repository import (
    BillingRemoteTimeTrackingRepository,
)
from billing.components.core.router.billing_calculate_cumulative_requests import (
    CalculateCumulativeMonitoringTimeRequestObject,
)
from billing.components.core.router.billing_calculate_cumulative_response_objects import (
    CalculateCumulativeMonitoringTimeResponseObject,
)
from billing.components.core.router.billing_response_objects import (
    CPTCode,
    CPTCodeIterationNumber,
    CPTRecordStatusType,
    MinutesInSeconds,
)
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams


class CalculateCumulativeMonitoringTimeCPTCodeUseCase(UseCase):
    @autoparams()
    def __init__(
        self,
        billing_time_tracking_repo: BillingRemoteTimeTrackingRepository,
    ):
        self.billing_time_tracking_repo = billing_time_tracking_repo

    def process_request(
        self, request_object: CalculateCumulativeMonitoringTimeRequestObject
    ) -> CalculateCumulativeMonitoringTimeResponseObject:
        product_type = get_billing_product_type(request_object.user.deployment)
        now = datetime.now()
        start_date_time = datetime(year=now.year, month=now.month, day=1)
        end_date_time = start_date_time + relativedelta(months=1, seconds=-1)
        time_spent = float(
            self.billing_time_tracking_repo.retrieve_billing_time_spent_remote_time_tracking(
                user_id=request_object.user.id,
                start_date=start_date_time,
                end_date=end_date_time,
            )
        )
        status_cpt_mapping_3 = get_status_for_cpt_mapping_3(
            user_id=request_object.user.id,
            time_spent=time_spent,
            start_date_time=start_date_time,
            end_date_time=end_date_time,
        )
        status_cpt_mapping_4_1x = get_status_for_cpt_mapping_4_1x(
            time_spent=time_spent, status_cpt_mapping_3=status_cpt_mapping_3
        )
        status_cpt_mapping_4_2x = get_status_for_cpt_mapping_4_2x(
            time_spent=time_spent, status_cpt_mapping_3=status_cpt_mapping_3
        )
        (
            cpt_code_to_show,
            cpt_code_iteration,
        ) = self._get_cpt_code_to_show_and_code_iteration(
            status_cpt_mapping_3=status_cpt_mapping_3,
            status_cpt_mapping_4_1x=status_cpt_mapping_4_1x,
            time_spent=time_spent,
            product_type=product_type,
        )
        status = self._get_status_for_cpt_code(
            cpt_code=cpt_code_to_show,
            cpt_code_iteration=cpt_code_iteration,
            status_cpt_mapping_3=status_cpt_mapping_3,
            status_cpt_mapping_4_1x=status_cpt_mapping_4_1x,
            status_cpt_mapping_4_2x=status_cpt_mapping_4_2x,
        )

        return CalculateCumulativeMonitoringTimeResponseObject(
            monitoringTimeSeconds=time_spent,
            userId=request_object.user.id,
            cptCode=cpt_code_to_show,
            cptCodeIteration=cpt_code_iteration,
            status=status,
        )

    def _get_cpt_code_to_show_and_code_iteration(
        self,
        status_cpt_mapping_3: str,
        status_cpt_mapping_4_1x: str,
        time_spent: float,
        product_type: ProductType,
    ) -> tuple[str, int]:
        if status_cpt_mapping_3 == CPTRecordStatusType.PENDING:
            return self._get_cpt_code_to_show_and_code_iteration_for_pending_status_cpt_mapping_3(
                time_spent=time_spent, product_type=product_type
            )

        if status_cpt_mapping_3 == CPTRecordStatusType.IN_PROGRESS:
            return self._get_cpt_code_to_show_and_code_iteration_for_in_progress_status_cpt_mapping_3(
                product_type=product_type
            )

        if status_cpt_mapping_3 == CPTRecordStatusType.COMPLETED:
            return self._get_cpt_code_to_show_and_code_iteration_for_completed_status_cpt_mapping_3(
                status_cpt_mapping_4_1x=status_cpt_mapping_4_1x,
                product_type=product_type,
            )

    @staticmethod
    def _get_cpt_code_to_show_and_code_iteration_for_pending_status_cpt_mapping_3(
        time_spent: float, product_type: ProductType
    ) -> tuple[str, int]:
        if time_spent < MinutesInSeconds.TWENTY_MINUTES_IN_SECONDS.value:
            if product_type is ProductType.RPM:
                return (
                    CPTCode.RPM.CPT_MAPPING_3.value,
                    CPTCodeIterationNumber.IterationFor1x.value,
                )
            return (
                CPTCode.RTM.CPT_MAPPING_3.value,
                CPTCodeIterationNumber.IterationFor1x.value,
            )
        if (
            MinutesInSeconds.TWENTY_MINUTES_IN_SECONDS.value
            <= time_spent
            < MinutesInSeconds.FORTY_MINUTES_IN_SECONDS.value
        ):
            if product_type is ProductType.RPM:
                return (
                    CPTCode.RPM.CPT_MAPPING_4.value,
                    CPTCodeIterationNumber.IterationFor1x.value,
                )
            return (
                CPTCode.RTM.CPT_MAPPING_4.value,
                CPTCodeIterationNumber.IterationFor1x.value,
            )
        if time_spent >= MinutesInSeconds.FORTY_MINUTES_IN_SECONDS.value:
            if product_type is ProductType.RPM:
                return (
                    CPTCode.RPM.CPT_MAPPING_4.value,
                    CPTCodeIterationNumber.IterationFor2x.value,
                )
            return (
                CPTCode.RTM.CPT_MAPPING_4.value,
                CPTCodeIterationNumber.IterationFor2x.value,
            )

    @staticmethod
    def _get_cpt_code_to_show_and_code_iteration_for_in_progress_status_cpt_mapping_3(
        product_type: ProductType,
    ) -> tuple[str, int]:
        if product_type is ProductType.RPM:
            return (
                CPTCode.RPM.CPT_MAPPING_3.value,
                CPTCodeIterationNumber.IterationFor1x.value,
            )
        return (
            CPTCode.RTM.CPT_MAPPING_3.value,
            CPTCodeIterationNumber.IterationFor1x.value,
        )

    @staticmethod
    def _get_cpt_code_to_show_and_code_iteration_for_completed_status_cpt_mapping_3(
        status_cpt_mapping_4_1x: str, product_type: ProductType
    ) -> tuple[str, int]:
        if status_cpt_mapping_4_1x == CPTRecordStatusType.IN_PROGRESS:
            if product_type is ProductType.RPM:
                return (
                    CPTCode.RPM.CPT_MAPPING_4.value,
                    CPTCodeIterationNumber.IterationFor1x.value,
                )
            return (
                CPTCode.RTM.CPT_MAPPING_4.value,
                CPTCodeIterationNumber.IterationFor1x.value,
            )
        if product_type is ProductType.RPM:
            return (
                CPTCode.RPM.CPT_MAPPING_4.value,
                CPTCodeIterationNumber.IterationFor2x.value,
            )
        return (
            CPTCode.RTM.CPT_MAPPING_4.value,
            CPTCodeIterationNumber.IterationFor2x.value,
        )

    @staticmethod
    def _get_status_for_cpt_code(
        cpt_code: str,
        cpt_code_iteration: int,
        status_cpt_mapping_3: str,
        status_cpt_mapping_4_1x: str,
        status_cpt_mapping_4_2x: str,
    ) -> str:
        if cpt_code == CPTCode.RPM.CPT_MAPPING_3.value or cpt_code == CPTCode.RTM.CPT_MAPPING_3.value:
            return status_cpt_mapping_3
        if cpt_code_iteration == CPTCodeIterationNumber.IterationFor1x:
            return status_cpt_mapping_4_1x
        return status_cpt_mapping_4_2x
