from datetime import date

import i18n
from bson import ObjectId

from billing.components.core.dtos.billing_models import (
    BillingMonitoringLogDTO,
    BillingRemoteTimeTrackingDTO,
    ResponseBillingMonitoringLogDTO,
)
from billing.components.core.repository.billing_repository import (
    BillingMonitoringLogRepository,
    BillingRemoteTimeTrackingRepository,
)
from billing.components.core.router.billing_calculate_cumulative_requests import (
    CalculateCumulativeMonitoringTimeRequestObject,
)
from billing.components.core.router.billing_calculate_cumulative_response_objects import (
    CalculateCumulativeMonitoringTimeResponseObject,
)
from billing.components.core.router.billing_requests import (
    BillingAlertTimeTrackingDetailsRequestObject,
    CreateBillingMonitoringLogRequestObject,
    DeleteBillingMonitoringLogRequestObject,
    RetrieveBillingMonitoringLogActionListRequestObject,
    RetrieveBillingMonitoringLogsRequestObject,
    UpdateBillingMonitoringLogRequestObject,
)
from billing.components.core.router.billing_response_objects import (
    BillingMonitoringLogActionListResponseObject,
    BillingMonitoringLogsResponseObject,
    CreateUpdateBillingMonitoringLogResponseObject,
    DeleteBillingMonitoringLogResponseObject,
)
from billing.components.core.statics.billing_monitoring_log_action import (
    BillingMonitoringLogActionKeys,
)
from billing.components.core.use_case.billing_alerts_use_cases import (
    BillingAlertTimeTrackingDetailsUseCase,
)
from billing.components.core.use_case.billing_calculate_cumulative_use_cases import (
    CalculateCumulativeMonitoringTimeCPTCodeUseCase,
)
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams


def calculate_cumulative_time_tracking(
    user: AuthorizedUser,
) -> CalculateCumulativeMonitoringTimeResponseObject:
    req_obj = CalculateCumulativeMonitoringTimeRequestObject.from_dict(
        {CalculateCumulativeMonitoringTimeRequestObject.USER: user}
    )
    return CalculateCumulativeMonitoringTimeCPTCodeUseCase().execute(req_obj)


def update_user_billing_alerts(
    monitoring_time: float,
    user: AuthorizedUser,
    monitoring_date: date,
) -> None:
    req_obj_data = {
        BillingAlertTimeTrackingDetailsRequestObject.USER: user,
        BillingAlertTimeTrackingDetailsRequestObject.MONITORING_SECONDS: monitoring_time,
        BillingAlertTimeTrackingDetailsRequestObject.MONITORING_DATE: monitoring_date,
    }

    request_object_time_tracking_details_billing_alert = BillingAlertTimeTrackingDetailsRequestObject.from_dict(
        req_obj_data
    )
    BillingAlertTimeTrackingDetailsUseCase().execute(request_object_time_tracking_details_billing_alert)


class BillingMonitoringLogUseCase(UseCase):
    @autoparams()
    def __init__(
        self,
        monitoring_log_repo: BillingMonitoringLogRepository,
        time_tracking_repo: BillingRemoteTimeTrackingRepository,
    ):
        self._monitoring_log_repo = monitoring_log_repo
        self._time_tracking_repo = time_tracking_repo

    def process_request(self, request_object: CreateBillingMonitoringLogRequestObject):
        log_dict = self.request_object.to_dict(
            include_none=False,
            exclude_fields=[
                CreateBillingMonitoringLogRequestObject.USER,
                CreateBillingMonitoringLogRequestObject.CLINICIAN,
            ],
        )
        log_dict.update({BillingRemoteTimeTrackingDTO.CLINICIAN_ID: self.request_object.clinician.id})
        time_tracking_id = self._create_time_tracking(log_dict)
        log_dict.update({BillingMonitoringLogDTO.TIME_TRACKING_ID: time_tracking_id})
        log_id = self._create_monitoring_log(log_dict)

        calculation_resp = calculate_cumulative_time_tracking(request_object.user)
        update_user_billing_alerts(
            calculation_resp.monitoringTimeSeconds,
            request_object.user,
            request_object.endDateTime.date(),
        )
        return self._generate_response(log_id, log_dict, calculation_resp)

    def _create_time_tracking(self, data: dict) -> str:
        _validated_data = BillingRemoteTimeTrackingDTO.from_dict(data)

        return self._time_tracking_repo.create_billing_remote_time_tracking(
            deployment_id=_validated_data.deploymentId,
            clinician_id=_validated_data.clinicianId,
            start_datetime=_validated_data.startDateTime,
            end_datetime=_validated_data.endDateTime,
            user_id=_validated_data.userId,
            createDateTime=_validated_data.createDateTime,
        )

    def _create_monitoring_log(self, data: dict) -> str:
        _validated_log = BillingMonitoringLogDTO.from_dict(data)

        return self._monitoring_log_repo.create_monitoring_log(_validated_log)

    @staticmethod
    def _generate_response(
        log_id: str,
        log_dict: dict,
        calculation_resp: CalculateCumulativeMonitoringTimeResponseObject,
    ) -> CreateUpdateBillingMonitoringLogResponseObject:
        calculation_resp_values = calculation_resp.to_dict()
        cpt_status = calculation_resp_values.pop(CalculateCumulativeMonitoringTimeResponseObject.STATUS)
        log_dict.update(
            {
                **calculation_resp_values,
                CreateUpdateBillingMonitoringLogResponseObject.ID: log_id,
                CreateUpdateBillingMonitoringLogResponseObject.CPT_RECORD_STATUS: cpt_status,
            }
        )
        return CreateUpdateBillingMonitoringLogResponseObject.from_dict(log_dict)

    @staticmethod
    def _extract_log_data_from_request_object(
        request_object: CreateBillingMonitoringLogRequestObject,
    ):
        return {
            BillingMonitoringLogDTO.USER_ID: request_object.user.id,
            BillingMonitoringLogDTO.CLINICIAN_ID: request_object.clinician.id,
            BillingMonitoringLogDTO.START_DATE_TIME: request_object.startDateTime,
            BillingMonitoringLogDTO.END_DATE_TIME: request_object.endDateTime,
            BillingMonitoringLogDTO.ACTION: request_object.action.value,
            BillingMonitoringLogDTO.DEPLOYMENT_ID: request_object.deploymentId,
        }


class RetrieveBillingMonitoringLogsUseCase(UseCase):
    request_object: RetrieveBillingMonitoringLogsRequestObject

    @autoparams()
    def __init__(
        self,
        monitoring_log_repo: BillingMonitoringLogRepository,
        user_repo: AuthorizationRepository,
    ):
        self._monitoring_log_repo = monitoring_log_repo
        self._user_repo = user_repo

    def process_request(
        self, request_object: RetrieveBillingMonitoringLogsRequestObject
    ) -> BillingMonitoringLogsResponseObject:
        logs, total = self._monitoring_log_repo.retrieve_active_user_monitoring_logs(
            user_id=request_object.user.id,
            skip=request_object.skip,
            limit=request_object.limit,
        )
        logs = self._prepare_response_logs(logs)
        return BillingMonitoringLogsResponseObject(
            items=logs,
            total=total,
            skip=request_object.skip,
            limit=request_object.limit,
        )

    def _prepare_response_logs(self, logs: list[BillingMonitoringLogDTO]) -> list[ResponseBillingMonitoringLogDTO]:
        if not logs:
            return []

        names = self._fetch_clinician_names(logs)
        return self._compose_response_logs(logs, names)

    @staticmethod
    def _compose_response_logs(
        logs: list[BillingMonitoringLogDTO], names: dict[str, str]
    ) -> list[ResponseBillingMonitoringLogDTO]:
        response_logs = []
        for log in logs:
            response_log = {
                **log.to_dict(include_none=False),
                ResponseBillingMonitoringLogDTO.CREATED_BY_NAME: names.get(log.createdById, ""),
                ResponseBillingMonitoringLogDTO.LAST_MODIFIED_BY_NAME: names.get(log.lastModifiedById, ""),
            }
            response_logs.append(ResponseBillingMonitoringLogDTO.from_dict(response_log))

        return response_logs

    def _fetch_clinician_names(self, logs: list[BillingMonitoringLogDTO]) -> dict[str, str]:
        ids = set()
        for log in logs:
            ids.update((log.createdById, log.lastModifiedById))

        clinicians = self._user_repo.retrieve_simple_user_profiles_by_ids(ids)
        return {clinician.id: clinician.get_full_name() for clinician in clinicians}


class RetrieveBillingMonitoringLogActionListUseCase(UseCase):
    request_object = RetrieveBillingMonitoringLogActionListRequestObject

    def process_request(self, request_object):
        action_mapping = {
            action.value: i18n.t(action.value, locale=self.request_object.language)
            for action in BillingMonitoringLogActionKeys
        }
        return BillingMonitoringLogActionListResponseObject(action_mapping)


class UpdateBillingMonitoringLogUseCase(UseCase):
    request_object: UpdateBillingMonitoringLogRequestObject
    MAXIMUM_EDIT_WINDOW_IN_DAYS = 60

    @autoparams()
    def __init__(
        self,
        monitoring_log_repo: BillingMonitoringLogRepository,
        time_tracking_repo: BillingRemoteTimeTrackingRepository,
    ):
        self._monitoring_log_repo = monitoring_log_repo
        self._time_tracking_repo = time_tracking_repo

    def process_request(self, request_object: UpdateBillingMonitoringLogRequestObject):
        previous_log_entry = self._retrieve_monitoring_log()
        self._validate_log_is_editable(previous_log_entry)
        self._invalidate_previous_log_entry(previous_log_entry)
        updated_log_dict = self._create_updated_log_entry(previous_log_entry)

        self._update_time_tracking(previous_log_entry)
        cumulative_time_resp = calculate_cumulative_time_tracking(self.request_object.user)
        update_user_billing_alerts(
            cumulative_time_resp.monitoringTimeSeconds,
            request_object.user,
            request_object.endDateTime.date(),
        )

        return self._generate_response(updated_log_dict, cumulative_time_resp)

    def _retrieve_monitoring_log(self) -> BillingMonitoringLogDTO:
        return self._monitoring_log_repo.retrieve_monitoring_log_by_id(self.request_object.logId)

    def _validate_log_is_editable(self, log: BillingMonitoringLogDTO):
        if (self.request_object.updateDateTime - log.initialCreateDateTime).days > self.MAXIMUM_EDIT_WINDOW_IN_DAYS:
            raise InvalidRequestException(f"Log cannot be edited after {self.MAXIMUM_EDIT_WINDOW_IN_DAYS} days.")

        if log.status == BillingMonitoringLogDTO.BillingMonitoringLogStatus.DELETED:
            raise InvalidRequestException(f"Log {log.id} is deleted.")

    def _invalidate_previous_log_entry(self, log: BillingMonitoringLogDTO):
        updated_fields = {
            BillingMonitoringLogDTO.STATUS: BillingMonitoringLogDTO.BillingMonitoringLogStatus.EDITED.value,
            BillingMonitoringLogDTO.ADDENDUM: self.request_object.addendum,
            BillingMonitoringLogDTO.UPDATE_DATE_TIME: self.request_object.updateDateTime,
        }
        self._monitoring_log_repo.update_monitoring_log(log_id=log.id, updated_fields=updated_fields)

    def _create_updated_log_entry(self, previous_log_entry: BillingMonitoringLogDTO):
        self.request_object.userId = previous_log_entry.userId
        self.request_object.deploymentId = previous_log_entry.deploymentId
        self.request_object.timeTrackingId = previous_log_entry.timeTrackingId
        self.request_object.createdById = previous_log_entry.createdById
        self.request_object.initialCreateDateTime = previous_log_entry.initialCreateDateTime
        updated_log_dict = self.request_object.to_dict(
            include_none=False,
            exclude_fields=[
                UpdateBillingMonitoringLogRequestObject.USER,
                UpdateBillingMonitoringLogRequestObject.CLINICIAN,
            ],
        )
        updated_log_dict.pop(BillingMonitoringLogDTO.ADDENDUM)
        updated_log = BillingMonitoringLogDTO.from_dict(updated_log_dict)
        log_id = self._monitoring_log_repo.create_monitoring_log(updated_log, previous_log_entry.originalLogId)
        updated_log_dict[BillingMonitoringLogDTO.ID] = log_id
        return updated_log_dict

    def _update_time_tracking(self, previous_log_entry: BillingMonitoringLogDTO):
        updated_log = self.request_object
        if (
            previous_log_entry.startDateTime != updated_log.startDateTime
            or previous_log_entry.endDateTime != updated_log.endDateTime
        ):
            self._time_tracking_repo.update_remote_time_tracking_start_and_end_dt(
                tracking_id=previous_log_entry.timeTrackingId,
                start_dt=updated_log.startDateTime,
                end_dt=updated_log.endDateTime,
            )

    def _generate_response(
        self,
        log_dict: dict,
        cumulative_time_resp: CalculateCumulativeMonitoringTimeResponseObject,
    ) -> CreateUpdateBillingMonitoringLogResponseObject:
        calculation_resp_values = cumulative_time_resp.to_dict()
        cpt_status = calculation_resp_values.pop(CalculateCumulativeMonitoringTimeResponseObject.STATUS)
        log_dict.update(
            {
                **calculation_resp_values,
                BillingMonitoringLogDTO.CLINICIAN_ID: self.request_object.clinician.id,
                CreateUpdateBillingMonitoringLogResponseObject.CPT_RECORD_STATUS: cpt_status,
            }
        )
        return CreateUpdateBillingMonitoringLogResponseObject.from_dict(log_dict)


class DeleteBillingMonitoringLogUseCase(UseCase):
    request_object: DeleteBillingMonitoringLogRequestObject
    MAX_DELETE_WINDOW = 60

    @autoparams()
    def __init__(
        self,
        monitoring_log_repo: BillingMonitoringLogRepository,
        time_tracking_repo: BillingRemoteTimeTrackingRepository,
    ):
        self._monitoring_log_repo = monitoring_log_repo
        self._time_tracking_repo = time_tracking_repo

    def process_request(self, request_object: DeleteBillingMonitoringLogRequestObject):
        log = self._fetch_log_or_raise_error(request_object.logId)
        self._can_delete_log(log)
        self._update_time_tracking_record(log)
        self._update_billing_alerts(log)
        self._delete_log_entry(log, request_object.addendum)

        return DeleteBillingMonitoringLogResponseObject.from_dict({DeleteBillingMonitoringLogResponseObject.ID: log.id})

    def _update_time_tracking_record(self, log: BillingMonitoringLogDTO):
        self._time_tracking_repo.update_remote_time_tracking_start_and_end_dt(
            tracking_id=log.timeTrackingId,
            start_dt=log.startDateTime,
            end_dt=log.startDateTime,
        )

    def _update_billing_alerts(self, log: BillingMonitoringLogDTO):
        cumulative_time_resp = calculate_cumulative_time_tracking(self.request_object.user)
        update_user_billing_alerts(
            cumulative_time_resp.monitoringTimeSeconds,
            self.request_object.user,
            log.endDateTime.date(),
        )

    def _fetch_log_or_raise_error(self, log_id: str) -> BillingMonitoringLogDTO:
        return self._monitoring_log_repo.retrieve_monitoring_log_by_id(log_id)

    def _can_delete_log(self, log: BillingMonitoringLogDTO):
        if (self.request_object.updateDateTime - log.initialCreateDateTime).days > self.__class__.MAX_DELETE_WINDOW:
            raise InvalidRequestException(f"Log can not be deleted after {self.__class__.MAX_DELETE_WINDOW} days.")
        if log.status == BillingMonitoringLogDTO.BillingMonitoringLogStatus.DELETED:
            raise InvalidRequestException(f"Log {log.id} is already deleted.")

    def _delete_log_entry(self, log: BillingMonitoringLogDTO, addendum: str):
        updated_fields = {
            BillingMonitoringLogDTO.STATUS: BillingMonitoringLogDTO.BillingMonitoringLogStatus.DELETED.value,
            BillingMonitoringLogDTO.ADDENDUM: addendum,
            BillingMonitoringLogDTO.UPDATE_DATE_TIME: self.request_object.updateDateTime,
            BillingMonitoringLogDTO.LAST_MODIFIED_BY_ID: ObjectId(self.request_object.lastModifiedById),
        }
        self._monitoring_log_repo.update_monitoring_log(log_id=log.id, updated_fields=updated_fields)
