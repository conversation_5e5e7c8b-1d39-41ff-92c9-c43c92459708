from datetime import datetime, timedelta, date
from functools import cached_property

from dateutil.relativedelta import relativedelta

from billing.components.core.dtos.deployment_billing import (
    DeploymentBillingConfig,
)
from billing.components.core.helpers.alerts_helpers import (
    get_deployment_billing_object,
)
from billing.components.core.helpers.calculate_cumulative_helpers import (
    get_status_for_cpt_mapping_3,
    get_status_for_cpt_mapping_4_2x,
    get_status_for_cpt_mapping_4_1x,
)
from billing.components.core.helpers.export_billing_submission_helpers import (
    get_calendar_submission_period,
    get_start_date_of_first_calendar_billable_or_current_period,
    get_start_date_of_first_billable_or_current_period,
)
from billing.components.core.repository.billing_repository import (
    BillingSubmissionRepository,
    BillingRemoteTimeTrackingRepository,
)
from billing.components.core.router.billing_report_requests import (
    ReportBillingRecordsForCPTRequestObject,
    CPTMappingNumber,
)
from billing.components.core.router.billing_response_objects import (
    CPTRecordStatusType,
    CPTCodeIterationNumber,
    CPTCode,
    MinutesInSeconds,
    CPTReportResponseObject,
)
from huma_plugins.components.online_offline_call.dtos.video_models import (
    OfflineVideoCallDTO,
)
from huma_plugins.components.online_offline_call.router.video_requests import (
    RetrieveMixedCallsWithStatusRequestObject,
)
from huma_plugins.components.online_offline_call.router.video_responses import (
    RetrieveCallsResponseObject,
)
from huma_plugins.components.online_offline_call.use_case.retrieve_calls_use_case import (
    RetrieveUserVideoCallsWithStatusUseCase,
)
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams
from sdk.common.utils.validators import utc_str_to_date


class ReportBillingRecordsForCPTUseCase(UseCase):
    request_object: ReportBillingRecordsForCPTRequestObject

    class CPTMappingFunctionSelector:
        CPT_MAPPING_1 = "process_request_for_cpt_mapping_1"
        CPT_MAPPING_2 = "process_request_for_cpt_mapping_2"
        CPT_MAPPING_3 = "process_request_for_cpt_mapping_3"
        CPT_MAPPING_4 = "process_request_for_cpt_mapping_4"

    @autoparams()
    def __init__(
        self,
        billing_submission_repo: BillingSubmissionRepository,
        billing_time_tracking_repo: BillingRemoteTimeTrackingRepository,
    ):
        self.repo = billing_submission_repo
        self.billing_time_tracking_repo = billing_time_tracking_repo

    def process_request(self, _: ReportBillingRecordsForCPTRequestObject) -> list[CPTReportResponseObject]:
        cpt_mapping_functions = {
            CPTMappingNumber.CPT_99453_98975: self.CPTMappingFunctionSelector.CPT_MAPPING_1,
            CPTMappingNumber.CPT_99454_98976: self.CPTMappingFunctionSelector.CPT_MAPPING_2,
            CPTMappingNumber.CPT_99457_98980: self.CPTMappingFunctionSelector.CPT_MAPPING_3,
            CPTMappingNumber.CPT_99458_98981: self.CPTMappingFunctionSelector.CPT_MAPPING_4,
        }

        result, order = [], 1
        for mapping_number in cpt_mapping_functions:
            func = cpt_mapping_functions[mapping_number]
            mapping_report_data = getattr(self, func)(mapping_number)
            if not mapping_report_data:
                continue
            if isinstance(mapping_report_data, list):
                for item in mapping_report_data:
                    if not item:
                        continue
                    item[CPTReportResponseObject.ORDER] = order
                    order += 1
                    result.append(CPTReportResponseObject.from_dict(item))
            else:
                mapping_report_data[CPTReportResponseObject.ORDER] = order
                order += 1
                result.append(CPTReportResponseObject.from_dict(mapping_report_data))

        return result

    @staticmethod
    def _current_billing_is_billable(is_first_billing: bool, mapping_number: CPTMappingNumber) -> bool:
        return is_first_billing or mapping_number != CPTMappingNumber.CPT_99453_98975

    def process_request_for_cpt_mapping_1(self, mapping_number: CPTMappingNumber) -> dict | None:
        if self.billing_config.useCalendarCalculation:
            return self.process_request_for_calendar_cpt_mapping_1(mapping_number)
        return self.process_request_for_classic_cpt_mapping_1(mapping_number)

    def process_request_for_calendar_cpt_mapping_1(self, mapping_number: CPTMappingNumber) -> dict | None:
        first_bp_start = get_start_date_of_first_calendar_billable_or_current_period(user_id=self.user_id)

        cur_start, cur_end = self._find_current_calendar_period()
        is_first_billing_period = first_bp_start == cur_start
        days_since_period_start = (self._today_utc.date() - cur_start).days
        submission_days_count = self.repo.get_total_record_days_count_from_to(self.user_id, cur_start, cur_end)
        current_billing_status = self._find_billing_status_for_calendar_billing(
            submission_days_count,
            days_since_period_start,
            is_first_billing_period,
            mapping_number,
            cur_start,
            cur_end,
        )
        if current_billing_status == CPTRecordStatusType.NON_BILLABLE:
            return None

        earliest_billing_date = self._find_compliance_date(cur_start, cur_end)
        cpt_mapping_1_response_data = {
            CPTReportResponseObject.CPT_CODE: self.cpt_code_type_cls.CPT_MAPPING_1.value,
            CPTReportResponseObject.START_DATE: cur_start,
            CPTReportResponseObject.END_DATE: cur_end,
            CPTReportResponseObject.STATUS: current_billing_status,
            CPTReportResponseObject.EARLIEST_BILLING_DATE: earliest_billing_date,
            CPTReportResponseObject.DAYS_HAVE_SUBMISSION: submission_days_count,
        }
        return cpt_mapping_1_response_data

    def _find_compliance_date(self, search_from: date, search_to: date) -> date | None:
        submission = self.repo.find_16th_submission_in_period(self.user_id, search_from, search_to)
        return submission.startDateTime.date() if submission else None

    def _find_billing_status_for_calendar_billing(
        self,
        days_with_submissions: int,
        days_since_period_start: int,
        is_first_billing_period: bool,
        mapping_number: CPTMappingNumber,
        cur_start: date,
        cur_end: date,
    ) -> str:
        if not self._current_billing_is_billable(is_first_billing_period, mapping_number):
            return CPTRecordStatusType.NON_BILLABLE

        days_in_period = (cur_end - cur_start).days + 1
        if days_with_submissions == 0:
            if days_in_period - days_since_period_start < 16:
                return CPTRecordStatusType.INCOMPLETE
            return CPTRecordStatusType.PENDING

        if days_with_submissions > 0:
            if days_with_submissions >= 16:
                return CPTRecordStatusType.COMPLETED
            if days_in_period - days_since_period_start + days_with_submissions < 16:
                return CPTRecordStatusType.INCOMPLETE
            return CPTRecordStatusType.IN_PROGRESS

    def _find_current_calendar_period(self) -> tuple[date, date]:
        return get_calendar_submission_period(self._today_utc.date())

    def process_request_for_classic_cpt_mapping_1(self, mapping_number: CPTMappingNumber) -> dict | None:
        first_billable_period_start_date = get_start_date_of_first_billable_or_current_period(user_id=self.user_id)

        if not first_billable_period_start_date:
            return self._get_response_obj_without_calculation(mapping_number)

        days_in_current_billing = self._total_days_passed_in_current_billing(first_billable_period_start_date)
        current_billing_start_date = self._current_billing_start_date(days_in_current_billing)
        current_billing_end_date = current_billing_start_date + timedelta(days=29)

        days_have_submission_in_current_billing = self.repo.get_total_record_days_count_after_specific_date(
            self.user_id, current_billing_start_date
        )

        earliest_billing_date = self._earliest_billing_date_for_classic_calculation(current_billing_start_date)

        current_billing_is_first_billing = current_billing_start_date == first_billable_period_start_date

        current_billing_status = self._get_billing_status_for_current_billing(
            days_in_current_billing,
            days_have_submission_in_current_billing,
            mapping_number,
            current_billing_is_first_billing,
        )

        if current_billing_status == CPTRecordStatusType.NON_BILLABLE:
            return None

        cpt_mapping_1_response_data = {
            CPTReportResponseObject.CPT_CODE: self.cpt_code_type_cls.CPT_MAPPING_1.value,
            CPTReportResponseObject.START_DATE: current_billing_start_date,
            CPTReportResponseObject.END_DATE: current_billing_end_date,
            CPTReportResponseObject.STATUS: current_billing_status,
            CPTReportResponseObject.EARLIEST_BILLING_DATE: earliest_billing_date,
            CPTReportResponseObject.DAYS_HAVE_SUBMISSION: days_have_submission_in_current_billing,
        }
        return cpt_mapping_1_response_data

    def _get_response_obj_without_calculation(self, mapping_number: CPTMappingNumber) -> dict:
        if mapping_number == CPTMappingNumber.CPT_99453_98975:
            return {
                CPTReportResponseObject.STATUS: CPTRecordStatusType.PENDING,
                CPTReportResponseObject.CPT_CODE: self.cpt_code_type_cls.CPT_MAPPING_1.value,
            }
        elif mapping_number == CPTMappingNumber.CPT_99454_98976:
            return {
                CPTReportResponseObject.STATUS: CPTRecordStatusType.PENDING,
                CPTReportResponseObject.CPT_CODE: self.cpt_code_type_cls.CPT_MAPPING_2.value,
            }

    def _total_days_passed_in_current_billing(self, start_date: date) -> int:
        return (self._today_utc.date() - start_date).days % 30

    def _current_billing_start_date(self, days_in_current_billing: int) -> date:
        return self._today_utc.date() - timedelta(days=days_in_current_billing)

    def _earliest_billing_date_for_classic_calculation(self, from_date: date) -> date | None:
        end_date = from_date + timedelta(days=30)
        submission = self.repo.find_16th_submission_in_period(self.user_id, from_date, end_date)
        return submission.startDateTime.date() if submission else None

    def _get_billing_status_for_current_billing(
        self,
        days_passed_from_current_billing: int,
        days_have_submission: int,
        mapping_number: CPTMappingNumber,
        current_billing_is_first_billing: bool = False,
    ) -> str:
        if not self._current_billing_is_billable(current_billing_is_first_billing, mapping_number):
            return CPTRecordStatusType.NON_BILLABLE

        if days_have_submission == 0:
            if days_passed_from_current_billing >= 15:
                return CPTRecordStatusType.INCOMPLETE
            return CPTRecordStatusType.PENDING

        if days_have_submission > 0:
            if days_have_submission >= 16:
                return CPTRecordStatusType.COMPLETED
            if 30 - days_passed_from_current_billing + days_have_submission < 16:
                return CPTRecordStatusType.INCOMPLETE
            return CPTRecordStatusType.IN_PROGRESS

    def process_request_for_cpt_mapping_2(self, mapping_number: CPTMappingNumber) -> dict | None:
        data = self.process_request_for_cpt_mapping_1(mapping_number)
        if not data:
            return None
        data[CPTReportResponseObject.CPT_CODE] = self.cpt_code_type_cls.CPT_MAPPING_2.value
        return data

    def process_request_for_cpt_mapping_3(self, _: CPTMappingNumber) -> dict:
        status = get_status_for_cpt_mapping_3(
            user_id=self.user_id,
            time_spent=self.time_spent,
            start_date_time=self.start_date_time,
            end_date_time=self.end_date_time,
        )
        earliest_billing_date = self._get_earliest_billing_date(
            status=status,
            from_date_time=self.start_date_time,
            to_date_time=self.end_date_time,
            remote_time_tracking_amount_to_check=MinutesInSeconds.TWENTY_MINUTES_IN_SECONDS.value,
        )
        cpt_mapping_3_response_data = {
            CPTReportResponseObject.STATUS: status,
            CPTReportResponseObject.EARLIEST_BILLING_DATE: earliest_billing_date,
            CPTReportResponseObject.START_DATE: self.start_date_time.date(),
            CPTReportResponseObject.END_DATE: self.end_date_time.date(),
            CPTReportResponseObject.TIME_SPENT: self.get_time_spent_for_cpt_code_3_report(self.time_spent),
            CPTReportResponseObject.CPT_CODE: self.cpt_code_type_cls.CPT_MAPPING_3.value,
        }
        return cpt_mapping_3_response_data

    def _get_video_calls(
        self,
        video_call_statuses: list[str],
        from_date_time: datetime,
        to_date_time: datetime,
    ) -> RetrieveCallsResponseObject:
        req = RetrieveMixedCallsWithStatusRequestObject.from_dict(
            {
                RetrieveMixedCallsWithStatusRequestObject.USER_ID: self.user_id,
                RetrieveMixedCallsWithStatusRequestObject.STATUSES: video_call_statuses,
                RetrieveMixedCallsWithStatusRequestObject.FROM_DATE_TIME: from_date_time,
                RetrieveMixedCallsWithStatusRequestObject.TO_DATE_TIME: to_date_time,
            }
        )
        return RetrieveUserVideoCallsWithStatusUseCase().execute(req)

    def _get_earliest_billing_date(
        self,
        status: str,
        from_date_time: datetime,
        to_date_time: datetime,
        remote_time_tracking_amount_to_check: int,
    ) -> date | None:
        if status != CPTRecordStatusType.COMPLETED:
            return
        date_remote_time_tracking_is_completed = self._get_date_remote_time_tracking_is_completed(
            start_date_time=from_date_time,
            end_date_time=to_date_time,
            remote_time_tracking_amount_to_check=remote_time_tracking_amount_to_check,
        )
        date_video_call_is_answered = self._get_date_video_call_is_answered(
            from_date_time=from_date_time, to_date_time=to_date_time
        )
        return max(
            utc_str_to_date(date_remote_time_tracking_is_completed),
            utc_str_to_date(date_video_call_is_answered),
        )

    def _get_date_remote_time_tracking_is_completed(
        self,
        start_date_time: datetime,
        end_date_time: datetime,
        remote_time_tracking_amount_to_check,
    ) -> datetime:
        billing_time_tracking_records = self.billing_time_tracking_repo.retrieve_billing_time_tracking_records(
            user_id=self.user_id, start_date=start_date_time, end_date=end_date_time
        )
        duration = 0
        for billing_time_tracking_record in billing_time_tracking_records:
            duration += billing_time_tracking_record.effectiveDuration
            if duration >= remote_time_tracking_amount_to_check:
                return billing_time_tracking_record.effectiveEndDateTime

    def _get_date_video_call_is_answered(self, from_date_time: datetime, to_date_time: datetime) -> datetime:
        calls_rsp = self._get_video_calls(
            video_call_statuses=[OfflineVideoCallDTO.CallStatus.ANSWERED.value],
            from_date_time=from_date_time,
            to_date_time=to_date_time,
        )
        for call in calls_rsp.calls:
            if call.endDateTime:
                return call.endDateTime

    def process_request_for_cpt_mapping_4(self, _: CPTMappingNumber) -> list[dict | None] | None:
        status_for_cpt_mapping_3 = get_status_for_cpt_mapping_3(
            user_id=self.user_id,
            time_spent=self.time_spent,
            start_date_time=self.start_date_time,
            end_date_time=self.end_date_time,
        )
        if self.should_cpt_code_4_1x_be_excluded(self.time_spent, status_for_cpt_mapping_3):
            return None

        status_for_cpt_mapping_4_1x = get_status_for_cpt_mapping_4_1x(
            time_spent=self.time_spent, status_cpt_mapping_3=status_for_cpt_mapping_3
        )

        earliest_billing_date_for_1x = self._get_earliest_billing_date(
            status=status_for_cpt_mapping_4_1x,
            from_date_time=self.start_date_time,
            to_date_time=self.end_date_time,
            remote_time_tracking_amount_to_check=MinutesInSeconds.FORTY_MINUTES_IN_SECONDS.value,
        )

        cpt_code_mapping_4_1x_report = {
            CPTReportResponseObject.CPT_CODE: self.cpt_code_type_cls.CPT_MAPPING_4.value,
            CPTReportResponseObject.STATUS: status_for_cpt_mapping_4_1x,
            CPTReportResponseObject.EARLIEST_BILLING_DATE: earliest_billing_date_for_1x,
            CPTReportResponseObject.START_DATE: self.start_date_time.date(),
            CPTReportResponseObject.END_DATE: self.end_date_time.date(),
            CPTReportResponseObject.TIME_SPENT: self.get_time_spent_for_cpt_code_4_1x_report(self.time_spent),
            CPTReportResponseObject.CPT_CODE_ITERATION: CPTCodeIterationNumber.IterationFor1x,
        }

        if self.should_cpt_code_4_2x_be_excluded(self.time_spent, status_for_cpt_mapping_4_1x):
            cpt_code_mapping_4_2x_report = None

        else:
            status_for_cpt_mapping_4_2x = get_status_for_cpt_mapping_4_2x(
                time_spent=self.time_spent,
                status_cpt_mapping_3=status_for_cpt_mapping_3,
            )
            earliest_billing_date_for_2x = self._get_earliest_billing_date(
                status=status_for_cpt_mapping_4_2x,
                from_date_time=self.start_date_time,
                to_date_time=self.end_date_time,
                remote_time_tracking_amount_to_check=MinutesInSeconds.SIXTY_MINUTES_IN_SECONDS.value,
            )

            cpt_code_mapping_4_2x_report = {
                CPTReportResponseObject.CPT_CODE: self.cpt_code_type_cls.CPT_MAPPING_4.value,
                CPTReportResponseObject.STATUS: status_for_cpt_mapping_4_2x,
                CPTReportResponseObject.EARLIEST_BILLING_DATE: earliest_billing_date_for_2x,
                CPTReportResponseObject.START_DATE: self.start_date_time.date(),
                CPTReportResponseObject.END_DATE: self.end_date_time.date(),
                CPTReportResponseObject.TIME_SPENT: self.time_spent - MinutesInSeconds.FORTY_MINUTES_IN_SECONDS.value,
                CPTReportResponseObject.CPT_CODE_ITERATION: CPTCodeIterationNumber.IterationFor2x,
            }
        return [cpt_code_mapping_4_1x_report, cpt_code_mapping_4_2x_report]

    @staticmethod
    def get_time_spent_for_cpt_code_3_report(time_spent) -> float:
        if time_spent < MinutesInSeconds.TWENTY_MINUTES_IN_SECONDS:
            return time_spent
        return float(MinutesInSeconds.TWENTY_MINUTES_IN_SECONDS)

    @staticmethod
    def get_time_spent_for_cpt_code_4_1x_report(time_spent) -> float:
        if time_spent - MinutesInSeconds.TWENTY_MINUTES_IN_SECONDS < MinutesInSeconds.TWENTY_MINUTES_IN_SECONDS:
            return time_spent - MinutesInSeconds.TWENTY_MINUTES_IN_SECONDS
        return float(MinutesInSeconds.TWENTY_MINUTES_IN_SECONDS)

    @staticmethod
    def should_cpt_code_4_1x_be_excluded(time_spent, status_for_cpt_mapping_3) -> bool:
        return (
            status_for_cpt_mapping_3 != CPTRecordStatusType.COMPLETED
            and time_spent < MinutesInSeconds.TWENTY_MINUTES_IN_SECONDS.value
        )

    @staticmethod
    def should_cpt_code_4_2x_be_excluded(time_spent, status_for_cpt_mapping_4_1x) -> bool:
        return (
            status_for_cpt_mapping_4_1x != CPTRecordStatusType.COMPLETED
            and time_spent < MinutesInSeconds.FORTY_MINUTES_IN_SECONDS.value
        )

    @cached_property
    def _today_utc(self) -> datetime:
        return datetime.utcnow()

    @cached_property
    def start_date_time(self) -> datetime:
        now = datetime.now()
        return datetime(year=now.year, month=now.month, day=1)

    @cached_property
    def end_date_time(self) -> datetime:
        return self.start_date_time + relativedelta(months=1, seconds=-1)

    @cached_property
    def time_spent(self) -> float:
        time_spent = self.billing_time_tracking_repo.retrieve_billing_time_spent_remote_time_tracking(
            user_id=self.user_id,
            start_date=self.start_date_time,
            end_date=self.end_date_time,
        )
        return float(time_spent)

    @property
    def user_id(self) -> str:
        return self.request_object.user.id

    @cached_property
    def billing_config(self) -> DeploymentBillingConfig:
        return get_deployment_billing_object(self.request_object.user.deployment)

    @cached_property
    def cpt_code_type_cls(self) -> CPTCode.RPM | CPTCode.RTM:
        return getattr(CPTCode, self.billing_config.productType.value)
