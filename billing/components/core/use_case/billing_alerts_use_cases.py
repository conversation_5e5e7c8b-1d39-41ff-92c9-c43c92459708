from billing.components.core.dtos.billing_models import BillingAlertsDTO
from billing.components.core.helpers.alerts_helpers import (
    get_billing_product_type,
    get_deployment_billing_object,
)
from billing.components.core.helpers.export_billing_time_tracking_helpers import (
    calculate_current_period_start_and_end_date,
)
from billing.components.core.repository.billing_repository import (
    BillingAlertsRepository,
)
from billing.components.core.router.billing_requests import (
    BillingAlertTimeTrackingDetailsRequestObject,
    RetrieveAlertsRequestObject,
)
from billing.components.core.router.billing_response_objects import (
    RetrieveAlertsResponseObject,
)
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams
from sdk.deployment.dtos.deployment import DeploymentDTO


class RetrieveBillingAlertUseCase(UseCase):
    @autoparams()
    def __init__(self, alerts_repo: BillingAlertsRepository):
        self.alerts_repo = alerts_repo

    def process_request(self, request_object: RetrieveAlertsRequestObject):
        billing_config = get_deployment_billing_object(request_object.deployment)
        (
            filtered_count,
            total_count,
            missing_call_count,
            missing_submission_count,
            not_synced_count,
            paged_alerts,
        ) = self.alerts_repo.retrieve_billing_alerts(
            sort=request_object.sort,
            search=request_object.search,
            filters=request_object.filters,
            skip=request_object.skip,
            limit=request_object.limit,
            deployment_id=request_object.deployment.id,
            product_type=billing_config.productType.value,
            has_identified_access=request_object.canViewIdentifierData,
            use_calendar_calculation=billing_config.useCalendarCalculation,
        )
        response_object = RetrieveAlertsResponseObject(
            filtered=filtered_count,
            total=total_count,
            missing_call=missing_call_count,
            missing_submission=missing_submission_count,
            not_synced=not_synced_count,
            users=self._build_response_users(paged_alerts),
        )
        return response_object

    def _build_response_users(self, alerts: list[dict]) -> list[dict]:
        exclude_fields = [] if self.request_object.canViewIdentifierData else BillingAlertsDTO.get_identifiable_fields()
        users = []
        for alert in alerts:
            validated_alert = BillingAlertsDTO.from_dict(alert, ignore_none=True)
            user_alert = validated_alert.to_dict(exclude_fields=exclude_fields)
            users.append(user_alert)
        return users

    @staticmethod
    def _get_deployment_product_type_value(deployment: DeploymentDTO):
        product_type = get_billing_product_type(deployment)
        assert product_type is not None
        return product_type.value


class BillingAlertTimeTrackingDetailsUseCase(UseCase):
    @autoparams()
    def __init__(self, alerts_repo: BillingAlertsRepository):
        self.alerts_repo = alerts_repo

    def process_request(self, request_object: BillingAlertTimeTrackingDetailsRequestObject):
        user = request_object.user
        if not user.is_user_onboarded():
            return
        (
            current_period_start_date,
            current_period_end_date,
        ) = calculate_current_period_start_and_end_date()
        monitoring_minutes = float(request_object.monitoringSeconds / 60)
        time_tracking_data = {
            BillingAlertsDTO.MONITORING_MINUTES: monitoring_minutes,
            BillingAlertsDTO.NEXT_MONITORING_DOS: current_period_end_date,
        }
        self.alerts_repo.update_create_user_billing_alert(
            user_id=user.id,
            deployment_id=user.deployment_id(),
            update=time_tracking_data,
        )
        self.alerts_repo.update_last_monitoring_date(
            user_id=user.id, current_monitoring_date=request_object.monitoringDate
        )
