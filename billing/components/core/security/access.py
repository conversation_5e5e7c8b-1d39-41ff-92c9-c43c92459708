from huma_plugins.components.extended_authorization.models.role.custom_permissions import (
    ExtendedPolicyType,
)
from sdk.security.access import BaseAccess
from sdk.security.utils import CheckResult, CheckParams


class BillingAccess(BaseAccess):
    EDIT = ExtendedPolicyType.EDIT_MONITORING_DATA
    DELETE = ExtendedPolicyType.DELETE_MONITORING_DATA
    TRACK_TIME = ExtendedPolicyType.TRACK_TIME

    def check(self, params: CheckParams) -> CheckResult:
        permission = self.value
        for role, _ in params.roles:
            if role.has([permission]):
                return CheckResult(True)
        return CheckResult(False)
