from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

from bson import ObjectId
from freezegun import freeze_time
from werkzeug.test import TestResponse

from billing.components.core.dtos.billing_models import (
    BillingMonitoringLogDTO,
    ResponseBillingMonitoringLogDTO,
)
from billing.components.core.models import BillingMonitoringLog, BillingAlert
from billing.components.core.models import BillingRemoteTimeTracking
from billing.components.core.repository.billing_repository import (
    BillingMonitoringLogRepository,
)
from billing.components.core.router.billing_requests import (
    CreateBillingMonitoringLogRequestObject,
)
from billing.components.core.statics.billing_monitoring_log_action import (
    BillingMonitoringLogActionKeys,
)
from billing.components.core.tests.IntegrationTests.billing_tests import (
    BaseBillingTestCase,
)
from billing.components.core.tests.IntegrationTests.utils import (
    CheckEffectiveTimeExclusiveMixin,
)
from sdk.authorization.models import User
from sdk.common.utils import inject
from sdk.common.utils.validators import utc_str_val_to_field

LOG_COLLECTION = "billingmonitoringlog"
VALID_ADMIN_ID = "5e8f0c74b50aa9656c34129d"
VALID_CLINICIAN_ID = "64e76adedf047d493ba356c3"
USER_ID = "5e8f0c74b50aa9656c34789b"
OTHER_DEPLOYMENT_USER_ID = "5e8f0c74b50aa9656c347891"
USER_ID_WITH_LOG = "5e8f0c74b50aa9656c34789c"
EXISTING_LOG_ID = "64b7b85fb024d4faa61d9f23"


class BaseBillingManualMonitoringTestCase(BaseBillingTestCase, CheckEffectiveTimeExclusiveMixin):
    def setUp(self):
        super().setUp()

        self.token = self.get_headers_for_token(VALID_CLINICIAN_ID)
        self.headers = self.token
        self.base_billing_url = "/api/extensions/v1/billing"
        self.report_url = f"{self.base_billing_url}/user/{'{user_id}'}/report"
        self.log_submission_url = f"{self.base_billing_url}/user/{'{user_id}'}/log"
        self.log_edit = f"{self.base_billing_url}/user/{'{user_id}'}/log"
        self.log_delete = f"{self.base_billing_url}/user/{'{user_id}'}/log/{'{log_id}'}"
        self.logs_url = f"{self.base_billing_url}/user/{USER_ID}/logs"
        self.log_actions = f"{self.base_billing_url}/log-actions"

    def tearDown(self):
        super().tearDown()
        BillingAlert.objects.all().delete()

        BillingRemoteTimeTracking.objects.all().delete()
        BillingMonitoringLog.objects.all().delete()

    @freeze_time("2023-12-01T08:14:35.975Z")
    def test_submit_manual_monitoring_datetime_followed_by_report_check_WHEN_timezone_is_different_from_UTC(
        self,
    ):
        self._onboard_user(USER_ID)
        body = self._generate_sample_body(
            "2023-12-01T08:00:00.000Z",
            "2023-12-01T08:10:00.000Z",
        )
        self.flask_client.post(
            self.log_submission_url.format(user_id=USER_ID),
            json=body,
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )

        time_tracking = self._fetch_time_tracking_for_user(USER_ID)
        logs = self._fetch_logs_for_user(USER_ID)

        report_rsp = self.flask_client.get(
            self.report_url.format(user_id=USER_ID),
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )

        self.assertEqual(logs[0].timeSpent, time_tracking[0].effectiveDuration)
        self.assertEqual(report_rsp.json[-1]["timeSpent"], time_tracking[0].effectiveDuration)

    @freeze_time("2023-07-15")
    def test_submit_manual_monitoring(self):
        self._onboard_user(USER_ID)
        body = self._generate_sample_body()
        body_2 = self._generate_sample_body(
            "2023-07-10T08:21:43.722Z",
            "2023-07-10T08:41:43.722Z",
        )

        rsp = self.flask_client.post(
            self.log_submission_url.format(user_id=USER_ID),
            json=body,
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )

        rsp_2 = self.flask_client.post(
            self.log_submission_url.format(user_id=USER_ID),
            json=body_2,
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )

        self.assertEqual(201, rsp.status_code)
        self.assertEqual(201, rsp_2.status_code)

        logs = self._fetch_logs_for_user(USER_ID)
        alerts = self._fetch_alerts_for_user(USER_ID)
        time_tracking = self._fetch_time_tracking_for_user(USER_ID)
        time_tracking_ids = [tt.mongoId for tt in time_tracking]

        self.assertEqual(2, len(logs))
        self.assertEqual(2, len(time_tracking_ids))
        self.assertEqual(1, len(alerts))

        self.assertIn(logs[0].timeTrackingId, time_tracking_ids)
        self.assertIn(logs[1].timeTrackingId, time_tracking_ids)

        self.check_effective_time_exclusive_with_documents(time_tracking, 60 * 31)

        self.assertEqual(31, alerts[0].monitoringMinutes)
        self.assertEqual("2023-07-31 00:00:00", str(alerts[0].nextMonitoringDoS))

    @freeze_time("2023-07-15")
    def test_log_submission_WHEN_end_is_less_than_start(self):
        body = self._generate_sample_body(
            "2023-07-10T08:21:43.722Z",
            "2023-07-10T08:20:43.722Z",
        )

        rsp = self.flask_client.post(
            self.log_submission_url.format(user_id=USER_ID),
            json=body,
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )

        self.assertEqual(403, rsp.status_code)

    @freeze_time("2023-07-15")
    def test_log_submission_WHEN_start_is_more_than_31_days_ago(self):
        body = self._generate_sample_body(
            "2023-06-10T08:21:43.722Z",
            "2023-06-10T08:25:43.722Z",
        )

        rsp = self.flask_client.post(
            self.log_submission_url.format(user_id=USER_ID),
            json=body,
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )

        self.assertEqual(400, rsp.status_code)

    @freeze_time("2023-07-15")
    def test_log_submission_WHEN_duration_is_more_than_24_hours(self):
        body = self._generate_sample_body(
            "2023-07-10T08:21:43.722Z",
            "2023-07-11T08:25:43.722Z",
        )

        rsp = self.flask_client.post(
            self.log_submission_url.format(user_id=USER_ID),
            json=body,
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )

        self.assertEqual(400, rsp.status_code)

    def test_failure_create_log_when_duration_is_more_than_24_hours(self):
        self._onboard_user(USER_ID)
        start_date_time = datetime.now() - timedelta(days=5)
        end_date_time = start_date_time + timedelta(days=2)
        body = {
            BillingMonitoringLogDTO.START_DATE_TIME: utc_str_val_to_field(start_date_time),
            BillingMonitoringLogDTO.END_DATE_TIME: utc_str_val_to_field(end_date_time),
            BillingMonitoringLogDTO.ACTION: BillingMonitoringLogActionKeys.QUALITY_REVIEW.value,
        }
        rsp = self.flask_client.post(
            self.log_submission_url.format(user_id=USER_ID),
            headers=self.headers,
            json=body,
        )
        self.assertEqual(400, rsp.status_code)
        self.assertEqual("Maximum meeting duration is 24 hours.", rsp.json["message"])

    def test_success_create_log(self):
        self._onboard_user(USER_ID)
        start_date_time = utc_str_val_to_field(datetime.now() - timedelta(days=1))
        end_date_time = utc_str_val_to_field(start_date_time + timedelta(minutes=10))
        action = BillingMonitoringLogActionKeys.QUALITY_REVIEW.value
        body = {
            CreateBillingMonitoringLogRequestObject.START_DATE_TIME: start_date_time,
            CreateBillingMonitoringLogRequestObject.END_DATE_TIME: end_date_time,
            CreateBillingMonitoringLogRequestObject.ACTION: action,
        }
        rsp = self.flask_client.post(
            self.log_submission_url.format(user_id=USER_ID),
            headers=self.headers,
            json=body,
        )
        self.assertEqual(201, rsp.status_code)

    def test_create_billing_log_with_wrong_action(self):
        start_date_time = datetime.now() - timedelta(days=1)
        end_date_time = start_date_time + timedelta(minutes=10)
        body = {
            BillingMonitoringLogDTO.START_DATE_TIME: utc_str_val_to_field(start_date_time),
            BillingMonitoringLogDTO.END_DATE_TIME: utc_str_val_to_field(end_date_time),
            BillingMonitoringLogDTO.ACTION: "wrong_action",
        }
        rsp = self.flask_client.post(
            self.log_submission_url.format(user_id=USER_ID),
            headers=self.headers,
            json=body,
        )
        self.assertEqual(403, rsp.status_code)

    @freeze_time("2023-07-15")
    def test_retrieve_billing_logs_for_user(self):
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        # create 2 logs
        self._onboard_user(USER_ID)
        self._create_manual_log(headers)
        self._create_manual_log(headers)

        body = {"skip": 0, "limit": 1}
        rsp = self.flask_client.post(self.logs_url, headers=headers, json=body)

        self.assertEqual(200, rsp.status_code)
        # check that only 1 log is returned due to limit
        logs, total = rsp.json["items"], rsp.json["total"]
        self.assertEqual(1, len(logs))
        self.assertEqual(2, total)
        self.assertEqual(
            BillingMonitoringLogActionKeys.ATTEMPT_TO_CONTACT_PATIENT.value,
            logs[0][ResponseBillingMonitoringLogDTO.ACTION],
        )
        self.assertTrue(logs[0][ResponseBillingMonitoringLogDTO.CREATED_BY_NAME])
        self.assertTrue(logs[0][ResponseBillingMonitoringLogDTO.LAST_MODIFIED_BY_NAME])

    def test_retrieve_billing_log_actions_list(self):
        # user's language is EN
        rsp = self.flask_client.get(self.log_actions, headers=self.headers)
        self.assertEqual(200, rsp.status_code)

        actions = rsp.json["logActions"]
        self.assertTrue(actions[BillingMonitoringLogActionKeys.QUALITY_REVIEW.value])
        self.assertTrue(actions[BillingMonitoringLogActionKeys.CLINICAL_REVIEW.value])

    def test_edit_log_by_NOT_admin(self):
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        url = self.log_submission_url.format(user_id=OTHER_DEPLOYMENT_USER_ID) + "/" + str(ObjectId())
        rsp = self.flask_client.put(url, headers=headers, json={})
        self.assertEqual(403, rsp.status_code)

    @freeze_time("2023-08-01")
    def test_edit_billing_log(self):
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        self._onboard_user(USER_ID)
        log_rsp = self._create_manual_log(headers)
        log_id = log_rsp.json[BillingMonitoringLogDTO.ID]
        # check amount of logs
        headers = self.get_headers_for_token(VALID_ADMIN_ID)
        url = self.log_submission_url.format(user_id=USER_ID) + "/" + log_id
        body = {
            BillingMonitoringLogDTO.START_DATE_TIME: "2023-07-15T08:01:43.722Z",
            BillingMonitoringLogDTO.END_DATE_TIME: "2023-07-15T08:31:43.722Z",
            BillingMonitoringLogDTO.ACTION: BillingMonitoringLogActionKeys.QUALITY_REVIEW.value,
            BillingMonitoringLogDTO.ADDENDUM: "meaningful",
        }
        rsp = self.flask_client.put(url, headers=headers, json=body)
        self.assertEqual(200, rsp.status_code)
        self.assertNotEqual(rsp.json[BillingMonitoringLogDTO.ID], log_id)
        self.assertNotEqual(
            log_rsp.json[BillingMonitoringLogDTO.ACTION],
            rsp.json[BillingMonitoringLogDTO.ACTION],
        )
        self.assertNotEqual(
            log_rsp.json[BillingMonitoringLogDTO.START_DATE_TIME],
            rsp.json[BillingMonitoringLogDTO.START_DATE_TIME],
        )
        # check invalidated log
        repo = inject.instance(BillingMonitoringLogRepository)
        res = repo.retrieve_monitoring_log_by_id(log_id)
        self.assertEqual(1, res.status)
        self.assertEqual("meaningful", res.addendum)
        # check new log
        body = {"skip": 0, "limit": 10}
        rsp = self.flask_client.post(self.logs_url, headers=headers, json=body)
        rsp_log = rsp.json["items"][0]
        self.assertEqual(1, len(rsp.json["items"]))
        self.assertEqual(0, rsp_log[BillingMonitoringLogDTO.STATUS])
        self.assertNotIn(BillingMonitoringLogDTO.ADDENDUM, rsp_log)
        self.assertEqual(log_id, rsp_log[BillingMonitoringLogDTO.ORIGINAL_LOG_ID])

    def test_edit_non_existing_billing_log(self):
        self._onboard_user(USER_ID)
        headers = self.get_headers_for_token(VALID_ADMIN_ID)
        url = self.logs_url.format(user_id=USER_ID) + "/" + str(ObjectId())
        body = self._generate_sample_body(include_reason=True)
        rsp = self.flask_client.put(url, headers=headers, json=body)
        self.assertEqual(404, rsp.status_code)

    @freeze_time("2023-08-01")
    def test_edit_billing_log_gt_60_days_old(self):
        self._onboard_user(USER_ID_WITH_LOG)
        headers = self.get_headers_for_token(VALID_ADMIN_ID)
        url = self.log_submission_url.format(user_id=USER_ID_WITH_LOG) + "/" + EXISTING_LOG_ID
        body = self._generate_sample_body(include_reason=True)
        rsp = self.flask_client.put(url, headers=headers, json=body)
        self.assertEqual(400, rsp.status_code)
        self.assertEqual("Log cannot be edited after 60 days.", rsp.json["message"])

    @freeze_time("2023-08-01")
    def test_edit_deleted_log(self):
        log_id = self._prepare_sample_log_for_delete()

        rsp = self._delete_log(USER_ID, log_id)

        self.assertEqual(200, rsp.status_code)

        body = self._generate_sample_body(include_reason=True)
        url = self.log_submission_url.format(user_id=USER_ID) + "/" + log_id
        rsp = self.flask_client.put(url, headers=self.get_headers_for_token(VALID_ADMIN_ID), json=body)
        self.assertEqual(400, rsp.status_code)

    @freeze_time("2023-07-15")
    def test_delete_log_WHEN_overlap_happens(self):
        self._prepare_sample_log_for_delete(
            start_dt_str="2023-07-10T08:10:43.722Z",
            end_dt_str="2023-07-10T08:32:43.722Z",
        )

        log_id_2 = self._prepare_sample_log_for_delete(
            start_dt_str="2023-07-10T08:30:43.722Z",
            end_dt_str="2023-07-10T08:52:43.722Z",
        )

        self._prepare_sample_log_for_delete(
            start_dt_str="2023-07-10T08:50:43.722Z",
            end_dt_str="2023-07-10T08:55:43.722Z",
        )

        tts = self._fetch_time_tracking_for_user(USER_ID)
        self.assertEqual(22 * 60, tts[0].effectiveDuration)
        self.assertEqual(20 * 60, tts[1].effectiveDuration)
        self.assertEqual(3 * 60, tts[2].effectiveDuration)

        self._delete_log(USER_ID, log_id_2)

        tts = self._fetch_time_tracking_for_user(USER_ID, order_by="startDateTime")

        self.assertEqual(22 * 60, tts[0].effectiveDuration)
        self.assertEqual(0, tts[1].effectiveDuration)
        self.assertEqual(5 * 60, tts[2].effectiveDuration)

    @freeze_time("2023-07-15")
    def test_delete_simple_log(self):
        log_id = self._prepare_sample_log_for_delete()

        rsp = self._delete_log(USER_ID, log_id)

        self.assertEqual(200, rsp.status_code)

        log = self._fetch_logs_for_user(USER_ID)[0]

        self.assertEqual(log_id, str(log.id))
        self.assertEqual(log_id, str(log.originalLogId))
        self.assertEqual(BillingMonitoringLogDTO.BillingMonitoringLogStatus.DELETED.value, log.status)
        self.assertEqual(VALID_ADMIN_ID, str(log.lastModifiedById))
        self.assertEqual("meaningful", log.addendum)

        tt_entry = self._fetch_time_tracking_for_user(USER_ID)[0]

        self.assertEqual(tt_entry.endDateTime, log.startDateTime)
        self.assertEqual(tt_entry.endDateTime, tt_entry.startDateTime)
        self.assertEqual(tt_entry.endDateTime, tt_entry.effectiveEndDateTime)
        self.assertEqual(tt_entry.endDateTime, tt_entry.effectiveStartDateTime)
        self.assertEqual(0, tt_entry.effectiveDuration)

        alerts = self._fetch_alerts_for_user(USER_ID)[0]

        self.assertEqual(0, alerts.monitoringMinutes)

    @freeze_time("2023-10-15")
    def test_delete_simple_log_WHEN_gt_60_days_passed(self):
        log_id = self._prepare_sample_log_for_delete()

        rsp = self._delete_log(USER_ID, log_id)

        self.assertEqual(400, rsp.status_code)

    @freeze_time("2023-08-15")
    def test_delete_simple_log_WHEN_re_deleting_deleted_entry(self):
        log_id = self._prepare_sample_log_for_delete()

        rsp = self._delete_log(USER_ID, log_id)

        self.assertEqual(200, rsp.status_code)

        rsp = self._delete_log(USER_ID, log_id, "meaningless")

        self.assertEqual(400, rsp.status_code)

    @staticmethod
    def time_str(date="2023-08-10", hour=8, min=31, sec=43, mil=722):
        return f"{date}T{hour:02}:{min:02}:{sec:02}.{mil:03}Z"

    @freeze_time("2023-08-11")
    def test_edit_latest_log_start_time_forward(self):
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        self._onboard_user(USER_ID)
        self._create_manual_log(
            headers,
            start_dt_str=self.time_str(min=20),
            end_dt_str=self.time_str(min=40),
        )
        log_rsp_2 = self._create_manual_log(
            headers,
            start_dt_str=self.time_str(min=30),
            end_dt_str=self.time_str(min=55),
        )
        log_id = log_rsp_2.json[BillingMonitoringLogDTO.ID]
        headers = self.get_headers_for_token(VALID_ADMIN_ID)
        url = self.log_submission_url.format(user_id=USER_ID) + "/" + log_id
        body = {
            BillingMonitoringLogDTO.START_DATE_TIME: self.time_str(min=45),
            BillingMonitoringLogDTO.END_DATE_TIME: self.time_str(min=55),
            BillingMonitoringLogDTO.ACTION: BillingMonitoringLogActionKeys.QUALITY_REVIEW.value,
            BillingMonitoringLogDTO.ADDENDUM: "meaningful",
        }
        rsp = self.flask_client.put(url, headers=headers, json=body)
        self.assertEqual(200, rsp.status_code)
        effective_time_difference = log_rsp_2.json["monitoringTimeSeconds"] - rsp.json["monitoringTimeSeconds"]
        self.assertEqual(60 * 5, effective_time_difference)

    @freeze_time("2023-08-11")
    def test_edit_old_log_end_time_back(self):
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        self._onboard_user(USER_ID)
        log_rsp_1 = self._create_manual_log(
            headers,
            start_dt_str=self.time_str(min=20),
            end_dt_str=self.time_str(min=40),
        )
        log_rsp_2 = self._create_manual_log(
            headers,
            start_dt_str=self.time_str(min=30),
            end_dt_str=self.time_str(min=55),
        )
        log_id = log_rsp_1.json[BillingMonitoringLogDTO.ID]
        headers = self.get_headers_for_token(VALID_ADMIN_ID)
        url = self.log_submission_url.format(user_id=USER_ID) + "/" + log_id
        body = {
            BillingMonitoringLogDTO.START_DATE_TIME: self.time_str(min=20),
            BillingMonitoringLogDTO.END_DATE_TIME: self.time_str(min=35),
            BillingMonitoringLogDTO.ACTION: BillingMonitoringLogActionKeys.QUALITY_REVIEW.value,
            BillingMonitoringLogDTO.ADDENDUM: "meaningful",
        }
        rsp = self.flask_client.put(url, headers=headers, json=body)
        self.assertEqual(200, rsp.status_code)
        effective_time_difference = log_rsp_2.json["monitoringTimeSeconds"] - rsp.json["monitoringTimeSeconds"]
        self.assertEqual(0, effective_time_difference)

    @freeze_time("2023-08-11")
    def test_edit_old_log_end_time_before_latest_start_time(self):
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        self._onboard_user(USER_ID)
        log_rsp_1 = self._create_manual_log(
            headers,
            start_dt_str=self.time_str(min=20),
            end_dt_str=self.time_str(min=40),
        )
        log_rsp_2 = self._create_manual_log(
            headers,
            start_dt_str=self.time_str(min=30),
            end_dt_str=self.time_str(min=55),
        )
        log_id = log_rsp_1.json[BillingMonitoringLogDTO.ID]
        headers = self.get_headers_for_token(VALID_ADMIN_ID)
        url = self.log_submission_url.format(user_id=USER_ID) + "/" + log_id
        body = {
            BillingMonitoringLogDTO.START_DATE_TIME: self.time_str(min=20),
            BillingMonitoringLogDTO.END_DATE_TIME: self.time_str(min=25),
            BillingMonitoringLogDTO.ACTION: BillingMonitoringLogActionKeys.QUALITY_REVIEW.value,
            BillingMonitoringLogDTO.ADDENDUM: "meaningful",
        }
        rsp = self.flask_client.put(url, headers=headers, json=body)
        self.assertEqual(200, rsp.status_code)
        effective_time_difference = log_rsp_2.json["monitoringTimeSeconds"] - rsp.json["monitoringTimeSeconds"]
        self.assertEqual(60 * 5, effective_time_difference)

    @freeze_time("2023-08-11")
    def test_edit_overlap_with_three_logs(self):
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        self._onboard_user(USER_ID)
        self._create_manual_log(
            headers,
            start_dt_str=self.time_str(min=20),
            end_dt_str=self.time_str(min=40),
        )
        log_rsp_2 = self._create_manual_log(
            headers,
            start_dt_str=self.time_str(min=30),
            end_dt_str=self.time_str(min=55),
        )
        log_rsp_3 = self._create_manual_log(
            headers,
            start_dt_str=self.time_str(min=50),
            end_dt_str=self.time_str(min=59),
        )
        log_id = log_rsp_2.json[BillingMonitoringLogDTO.ID]
        headers = self.get_headers_for_token(VALID_ADMIN_ID)
        url = self.log_submission_url.format(user_id=USER_ID) + "/" + log_id
        body = {
            BillingMonitoringLogDTO.START_DATE_TIME: self.time_str(min=10),
            BillingMonitoringLogDTO.END_DATE_TIME: self.time_str(min=30),
            BillingMonitoringLogDTO.ACTION: BillingMonitoringLogActionKeys.QUALITY_REVIEW.value,
            BillingMonitoringLogDTO.ADDENDUM: "meaningful",
        }
        rsp = self.flask_client.put(url, headers=headers, json=body)
        self.assertEqual(200, rsp.status_code)
        effective_time_difference = log_rsp_3.json["monitoringTimeSeconds"] - rsp.json["monitoringTimeSeconds"]
        self.assertEqual(0, effective_time_difference)

    @staticmethod
    def _fetch_logs_for_user(user_id: str) -> list[BillingMonitoringLogDTO]:
        repo = inject.instance(BillingMonitoringLogRepository)
        return repo.retrieve_all_monitoring_logs_for_user(user_id)

    @staticmethod
    def _fetch_alerts_for_user(user_id: str) -> list[BillingAlert]:
        return list(BillingAlert.objects.filter(user_id=user_id).order_by("createDateTime"))

    @staticmethod
    def _fetch_time_tracking_for_user(
        user_id: str,
        order_by: str = "createDateTime",
    ) -> list[BillingRemoteTimeTracking]:
        return list(BillingRemoteTimeTracking.objects.filter(userId=user_id).order_by(order_by))

    @freeze_time("2023-07-15")
    def _prepare_sample_log_for_delete(self, **kwargs) -> str:
        self._onboard_user(USER_ID)
        rsp = self.flask_client.post(
            self.log_submission_url.format(user_id=USER_ID),
            json=self._generate_sample_body(**kwargs),
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )
        self.assertEqual(201, rsp.status_code)

        return rsp.json[BillingMonitoringLogDTO.ID]

    def _delete_log(self, user_id: str, log_id: str, addendum: str = "meaningful") -> TestResponse:
        return self.flask_client.delete(
            self.log_delete.format(user_id=user_id, log_id=log_id),
            headers=self.get_headers_for_token(VALID_ADMIN_ID),
            json={
                BillingMonitoringLogDTO.ADDENDUM: addendum,
            },
        )

    @staticmethod
    def _generate_sample_body(
        start_dt_str: str = "2023-07-10T08:31:43.722Z",
        end_dt_str: str = "2023-07-10T08:52:43.722Z",
        action: str = BillingMonitoringLogActionKeys.ATTEMPT_TO_CONTACT_PATIENT.value,
        include_reason: bool = False,
    ):
        body = {
            CreateBillingMonitoringLogRequestObject.START_DATE_TIME: start_dt_str,
            CreateBillingMonitoringLogRequestObject.END_DATE_TIME: end_dt_str,
            CreateBillingMonitoringLogRequestObject.ACTION: action,
        }
        if include_reason:
            body[BillingMonitoringLogDTO.ADDENDUM] = "meaningful"
        return body

    def _onboard_user(self, user_id: str, boarding_status_update_date: Optional[str] = "2022-01-01"):
        user = User.objects.get(mongoId=user_id)
        user.finishedOnboarding = True
        user.boardingStatus = user.boardingStatus or {}
        user.boardingStatus["status"] = 0
        user.boardingStatus["updateDateTime"] = f"{boarding_status_update_date}T14:08:05.997Z"
        user.save()

    def _create_manual_log(
        self,
        headers=None,
        start_dt_str: str = "2023-07-10T08:31:43.722Z",
        end_dt_str: str = "2023-07-10T08:52:43.722Z",
    ):
        body = self._generate_sample_body(start_dt_str=start_dt_str, end_dt_str=end_dt_str)
        rsp = self.flask_client.post(
            self.log_submission_url.format(user_id=USER_ID),
            headers=headers or self.headers,
            json=body,
        )
        return rsp
