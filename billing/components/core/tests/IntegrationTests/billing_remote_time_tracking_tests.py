from freezegun import freeze_time

from billing.components.core.dtos.billing_models import (
    BillingRemoteTimeTrackingDTO,
)
from billing.components.core.models import BillingRemoteTimeTracking
from billing.components.core.router.billing_calculate_cumulative_response_objects import (
    CalculateCumulativeMonitoringTimeResponseObject,
)
from billing.components.core.router.billing_requests import (
    CreateBillingRequestObject,
)
from billing.components.core.router.billing_response_objects import (
    CPTCode,
    CPTCodeIterationNumber,
    CPTRecordStatusType,
    CPTReportResponseObject,
)
from billing.components.core.tests.IntegrationTests.billing_tests import (
    BaseBillingTestCase,
    USER_ID,
    VALID_CLINICIAN_ID,
)


class BaseBillingRemoteTimeTrackingTestCase(BaseBillingTestCase):
    def setUp(self):
        super().setUp()
        self.token = self.get_headers_for_token(VALID_CLINICIAN_ID)
        self.headers = self.token
        self.base_url = "/api/extensions/v1/billing"

    def test_success_add_time_tracking(self):
        body = {
            CreateBillingRequestObject.START_DATE_TIME: "2022-11-22T14:50:22.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2022-11-22T14:50:59.039Z",
        }
        rsp = self.flask_client.post(
            f"{self.base_url}/user/{USER_ID}",
            json=body,
            headers=self.headers,
        )
        self.assertEqual(201, rsp.status_code)

    def test_success_add_time_tracking_clinician(self):
        body = {
            CreateBillingRequestObject.START_DATE_TIME: "2022-11-22T14:50:22.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2022-11-22T14:50:59.039Z",
        }
        rsp = self.flask_client.post(
            f"{self.base_url}/user/{USER_ID}",
            json=body,
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )
        self.assertEqual(201, rsp.status_code)

    def test_success_add_time_tracking_with_inside_overlap(self):
        overlapping_body = {
            CreateBillingRequestObject.START_DATE_TIME: "2022-11-22T14:50:22.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2022-11-22T14:50:55.039Z",
        }
        self.flask_client.post(
            f"{self.base_url}/user/{USER_ID}",
            json=overlapping_body,
            headers=self.headers,
        )
        body = {
            CreateBillingRequestObject.START_DATE_TIME: "2022-11-22T14:50:28.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2022-11-22T14:50:51.039Z",
        }
        rsp = self.flask_client.post(
            f"{self.base_url}/user/{USER_ID}",
            json=body,
            headers=self.headers,
        )
        doc = BillingRemoteTimeTracking.objects.filter(userId=USER_ID).order_by("-startDateTime").first()
        self.assertEqual(
            doc.effectiveStartDateTime,
            doc.startDateTime,
        )
        self.assertEqual(
            doc.effectiveStartDateTime,
            doc.effectiveEndDateTime,
        )
        self.assertEqual(doc.effectiveDuration, 0)
        self.assertEqual(201, rsp.status_code)

    def test_success_add_time_tracking_with_start_overlap(self):
        overlapping_body = {
            CreateBillingRequestObject.START_DATE_TIME: "2022-11-22T14:50:22.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2022-11-22T14:50:55.039Z",
        }
        self.flask_client.post(
            f"{self.base_url}/user/{USER_ID}",
            json=overlapping_body,
            headers=self.headers,
        )
        body = {
            CreateBillingRequestObject.START_DATE_TIME: "2022-11-22T14:50:20.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2022-11-22T14:50:51.039Z",
        }
        rsp = self.flask_client.post(
            f"{self.base_url}/user/{USER_ID}",
            json=body,
            headers=self.headers,
        )
        overlapping_doc = BillingRemoteTimeTracking.objects.filter(userId=USER_ID).order_by("-startDateTime").first()
        doc = (
            BillingRemoteTimeTracking.objects.filter(userId=USER_ID)
            .order_by(BillingRemoteTimeTrackingDTO.START_DATE_TIME)
            .first()
        )

        self.assertEqual(
            doc.effectiveDuration,
            (overlapping_doc.effectiveStartDateTime - doc.startDateTime).seconds,
        )
        self.assertEqual(
            doc.effectiveEndDateTime,
            overlapping_doc.effectiveStartDateTime,
        )
        self.assertEqual(201, rsp.status_code)

    def test_success_add_time_tracking_with_end_overlap(self):
        overlapping_body = {
            CreateBillingRequestObject.START_DATE_TIME: "2022-11-22T14:50:22.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2022-11-22T14:50:55.039Z",
        }
        self.flask_client.post(
            f"{self.base_url}/user/{USER_ID}",
            json=overlapping_body,
            headers=self.headers,
        )
        body = {
            CreateBillingRequestObject.START_DATE_TIME: "2022-11-22T14:50:26.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2022-11-22T14:50:59.039Z",
        }
        rsp = self.flask_client.post(
            f"{self.base_url}/user/{USER_ID}",
            json=body,
            headers=self.headers,
        )
        doc = BillingRemoteTimeTracking.objects.filter(userId=USER_ID).order_by("-startDateTime").first()
        overlapping_doc = (
            BillingRemoteTimeTracking.objects.filter(userId=USER_ID)
            .order_by(BillingRemoteTimeTrackingDTO.START_DATE_TIME)
            .first()
        )

        self.assertEqual(
            doc.effectiveDuration,
            (doc.endDateTime - overlapping_doc.effectiveEndDateTime).seconds,
        )
        self.assertEqual(
            doc.effectiveStartDateTime,
            overlapping_doc.effectiveEndDateTime,
        )
        self.assertEqual(201, rsp.status_code)

    def test_success_add_time_tracking_with_outside_overlap(self):
        overlapping_body = {
            CreateBillingRequestObject.START_DATE_TIME: "2022-11-22T14:50:22.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2022-11-22T14:50:55.039Z",
        }
        self.flask_client.post(
            f"{self.base_url}/user/{USER_ID}",
            json=overlapping_body,
            headers=self.headers,
        )
        body = {
            CreateBillingRequestObject.START_DATE_TIME: "2022-11-22T14:50:20.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2022-11-22T14:50:59.039Z",
        }
        rsp = self.flask_client.post(
            f"{self.base_url}/user/{USER_ID}",
            json=body,
            headers=self.headers,
        )
        overlapping_doc = BillingRemoteTimeTracking.objects.filter(userId=USER_ID).order_by("-startDateTime").first()
        self.assertEqual(
            overlapping_doc.effectiveDuration,
            0,
        )
        self.assertEqual(
            overlapping_doc.effectiveStartDateTime,
            overlapping_doc.effectiveEndDateTime,
        )
        self.assertEqual(201, rsp.status_code)

    def test_success_add_time_tracking_with_inside_overlap_but_another_user(self):
        body = {
            CreateBillingRequestObject.START_DATE_TIME: "2022-11-22T14:50:22.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2022-11-22T14:50:55.039Z",
        }
        self.flask_client.post(
            f"{self.base_url}/user/{VALID_CLINICIAN_ID}",
            json=body,
            headers=self.headers,
        )
        body = {
            CreateBillingRequestObject.START_DATE_TIME: "2022-11-22T14:50:28.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2022-11-22T14:50:51.039Z",
        }
        rsp = self.flask_client.post(
            f"{self.base_url}/user/{USER_ID}",
            json=body,
            headers=self.headers,
        )
        doc = BillingRemoteTimeTracking.objects.filter(userId=USER_ID).order_by("-startDateTime").first()
        self.assertEqual(
            doc.effectiveDuration,
            (doc.endDateTime - doc.startDateTime).seconds,
        )
        self.assertEqual(
            doc.effectiveStartDateTime,
            doc.startDateTime,
        )
        self.assertEqual(
            doc.effectiveEndDateTime,
            doc.endDateTime,
        )
        self.assertEqual(201, rsp.status_code)

    def test_success_add_time_tracking_without_overlap(self):
        body = {
            CreateBillingRequestObject.START_DATE_TIME: "2022-11-22T14:53:22.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2022-11-22T14:53:54.039Z",
        }
        self.flask_client.post(
            f"{self.base_url}/user/{USER_ID}",
            json=body,
            headers=self.headers,
        )
        body = {
            CreateBillingRequestObject.START_DATE_TIME: "2022-11-22T14:56:20.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2022-11-22T14:57:18.039Z",
        }
        rsp = self.flask_client.post(
            f"{self.base_url}/user/{USER_ID}",
            json=body,
            headers=self.headers,
        )
        doc = BillingRemoteTimeTracking.objects.filter(userId=USER_ID).order_by("-startDateTime").first()
        self.assertEqual(
            doc.effectiveDuration,
            (doc.endDateTime - doc.startDateTime).seconds,
        )
        self.assertEqual(
            doc.effectiveStartDateTime,
            doc.startDateTime,
        )
        self.assertEqual(
            doc.effectiveEndDateTime,
            doc.endDateTime,
        )
        self.assertEqual(201, rsp.status_code)

    def test_failure_add_time_tracking_with_invalid_times(self):
        body = {
            CreateBillingRequestObject.START_DATE_TIME: "2022-11-22T14:50:22.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2022-11-22T14:52:55.039Z",
        }
        rsp = self.flask_client.post(
            f"{self.base_url}/user/{USER_ID}",
            json=body,
            headers=self.headers,
        )
        self.assertEqual(403, rsp.status_code)

    @freeze_time("2023-02-09")
    def test_success_retrieve_time_tracking_with_in_progress_status(self):
        body = {
            CreateBillingRequestObject.START_DATE_TIME: "2023-02-09T00:00:01.039Z",
            CreateBillingRequestObject.END_DATE_TIME: "2023-02-09T00:00:02.039Z",
        }
        self.flask_client.post(
            f"{self.base_url}/user/{USER_ID}",
            json=body,
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )
        rsp = self.flask_client.get(
            f"{self.base_url}/user/{USER_ID}/report",
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(
            CPTRecordStatusType.IN_PROGRESS,
            rsp.json[2].get(CPTReportResponseObject.STATUS),
        )

    @freeze_time("2023-02-23")
    def test_success_retrieve_time_tracking_with_completed_status(self):
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        for i in range(11, 33):
            body = {
                CreateBillingRequestObject.START_DATE_TIME: f"2023-02-09T00:{i}:01.039Z",
                CreateBillingRequestObject.END_DATE_TIME: f"2023-02-09T00:{i}:59.039Z",
            }
            self.flask_client.post(
                f"{self.base_url}/user/{USER_ID}",
                json=body,
                headers=headers,
            )
        rsp = self.flask_client.get(f"{self.base_url}/user/{USER_ID}/report", headers=headers)
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(
            CPTRecordStatusType.COMPLETED,
            rsp.json[2].get(CPTReportResponseObject.STATUS),
        )

    @freeze_time("2023-02-09")
    def test_success_calculate_cumulative_time_tracking(self):
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.get(
            f"{self.base_url}/user/{USER_ID}",
            headers=headers,
        )
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(5, len(rsp.json))
        self.assertEqual(
            CPTRecordStatusType.IN_PROGRESS,
            rsp.json[CalculateCumulativeMonitoringTimeResponseObject.STATUS],
        )
        self.assertEqual(
            rsp.json[CalculateCumulativeMonitoringTimeResponseObject.CPT_CODE],
            CPTCode.RPM.CPT_MAPPING_3.value,
        )
        self.assertEqual(
            CPTCodeIterationNumber.IterationFor1x,
            rsp.json[CalculateCumulativeMonitoringTimeResponseObject.CPT_CODE_ITERATION],
        )
        self.assertEqual(
            0,
            rsp.json[CalculateCumulativeMonitoringTimeResponseObject.MONITORING_TIME_SECONDS],
        )
        self.assertEqual(USER_ID, rsp.json[CalculateCumulativeMonitoringTimeResponseObject.USER_ID])
