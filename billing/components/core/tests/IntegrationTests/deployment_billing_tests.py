import io
import random
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import MagicMock, patch

from bson import ObjectId
from dateutil.relativedelta import relativedelta
from django.db.models import Q
from flask import url_for
from freezegun import freeze_time
from jwt import PyJWS
from parameterized import parameterized

from billing.components.core.callbacks.authorization_callbacks import (
    process_offboard_user,
    process_reactivate_user,
    process_user_onboarding,
)
from billing.components.core.component import BillingComponent
from billing.components.core.dtos.deployment_billing import (
    BILLING_FEATURES_KEY,
    Color,
    ColorHex,
    ColorNames,
    DeploymentBillingConfig,
    DeploymentBillingFileType,
    DeploymentBillingFiles,
    ProductType,
)
from billing.components.core.dtos.user_billing import (
    Diagnosis,
    InsuranceCarrier,
    UserBilling,
    UserBillingFile,
)
from billing.components.core.helpers.billing_periods_calculators import BillingCalendarPeriodCalculator
from billing.components.core.helpers.export_billing_time_tracking_helpers import (
    remove_users_billing_data_overlapping,
)
from billing.components.core.helpers.module_result_helpers import PrimitiveSources
from billing.components.core.models import Billing<PERSON>lert, BillingRemoteTimeTracking, BillingSubmission
from billing.components.core.repository.billing_repository import BillingRemoteTimeTrackingRepository
from billing.components.core.router.billing_requests import CreateBillingRequestObject
from billing.components.core.tests.IntegrationTests.utils import CheckEffectiveTimeExclusiveMixin
from billing.components.export.component import BillingExportComponent
from billing.components.export.models.billing_models import (
    BillingMonthlyReportUserBilling,
    BillingProvider,
    EHRBillingProvider,
)
from billing.components.export.use_case.billing_monthly_export import (
    BillingMonthlyEHRExportableUseCase,
    BillingMonthlyExportableUseCase,
)
from billing.tests.test_helpers import (
    sample_user_billing,
    sample_user_billing_not_enrolled,
    simplify_export_response_keys,
)
from huma_plugins.components.export.dtos.export_models import ExportParameters
from huma_plugins.components.extended_module_result.component import ExtendedModuleResultComponent
from huma_plugins.components.online_offline_call.component import OnlineOfflineCallComponent
from huma_plugins.components.online_offline_call.router.video_requests import CreateOfflineCallRequestObject
from huma_plugins.tests.plugin_test_case import ExtensionServerConfig, PluginsTestCase
from huma_plugins.tests.shared import PLUGIN_CONFIG_PATH, PLUGIN_MIGRATIONS
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.authorization.dtos.user import RoleAssignmentDTO, UserDTO
from sdk.authorization.events import PostUserOffBoardEvent, PostUserReactivationEvent
from sdk.authorization.events.post_user_onboard_event import UserOnboardedEvent
from sdk.authorization.models import User
from sdk.authorization.router.user_profile_request import OffBoardUsersRequestObject
from sdk.calendar.component import CalendarComponent
from sdk.common.adapter.twilio.video_adapter import TwilioVideoAdapter
from sdk.common.common_models.video_models import VideoRoom
from sdk.common.utils import inject
from sdk.common.utils.validators import model_to_dict, utc_str_field_to_val
from sdk.deployment.component import DeploymentComponent
from sdk.deployment.dtos.deployment import DeploymentDTO, Features, ReasonDetails
from sdk.deployment.models.deployment import Deployment
from sdk.deployment.tests.IntegrationTests.abstract_deployment_test_case_tests import (
    AbstractDeploymentTestCase,
    VALID_USER_ID,
)
from sdk.deployment.tests.IntegrationTests.test_helpers import modified_deployment, simple_deployment
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.modules import BloodPressureModule
from sdk.module_result.modules.blood_pressure import BloodPressureDTO
from sdk.organization.component import OrganizationComponent
from sdk.storage.component import StorageComponentV1
from sdk.versioning.component import VersionComponent

DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
MANAGER_ID = "60071f359e7e44330f732037"
USER_ID = "5e8f0c74b50aa9656c34789c"
ALERT_USER_ID = "5e8f0c74b50aa9656c34789b"
VALID_PDF_FILE_SAMPLE = "sample_pdf.pdf"
USERS_KEY = "users"
VALIDATE_FILE_PATH = "billing.components.core.callbacks.deployment_callbacks.validate_deployment_billing_file"
INVITATION_CODE = (
    "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2MTYwNzAwMjQsIm5iZiI6MTYxNjA3MDAyNCwia"
    "nRpIjoiYjliMDRiYzQtNmFiZi00MzkwLWI0MjUtYTM1YTc1NjgyNWQ4IiwiaWRlbnRpdHkiOiJ1c2VyMUBleGFtcGxlLm"
    "NvbSIsInR5cGUiOiJpbnZpdGF0aW9uIn0.6M22glMJAavCoeHGf8CEDOWyn9SBNyITxQot7PpaHZn"
)


class BillingTestCase(PluginsTestCase, AbstractDeploymentTestCase):
    config_file_path = PLUGIN_CONFIG_PATH
    db_migration_path = PLUGIN_MIGRATIONS
    config_class = ExtensionServerConfig

    base_url = "/api/extensions/v1/deployment/%s"
    create_url = "/api/extensions/v1/deployment"
    auth_route = "/api/auth/v1"
    user_path = "/api/extensions/v1/user"

    components = [
        AuthComponent(),
        AuthorizationComponent(),
        DeploymentComponent(),
        OrganizationComponent(),
        StorageComponentV1(),
        CalendarComponent(),
        OnlineOfflineCallComponent(),
        BillingComponent(),
        BillingExportComponent(),
        ExtendedModuleResultComponent(
            additional_modules=[m() for m in [BloodPressureModule]],
        ),
        VersionComponent(server_version="1.0.0", api_version="1.0.0"),
    ]

    fixtures = [Path(__file__).parent.joinpath("fixtures/deployments_dump.json")]

    def _set_deployment_billing(self, deployment_id: str = DEPLOYMENT_ID, body: dict = None):
        billing_dict = self._sample_billing_dict()
        body = body or {
            **modified_deployment(),
            DeploymentDTO.FEATURES: {
                Features.CUSTOM_APP_CONFIG: {
                    BILLING_FEATURES_KEY: billing_dict,
                }
            },
        }
        rsp = self.flask_client.put(self.base_url % deployment_id, json=body, headers=self.headers)
        self.assertEqual(200, rsp.status_code)

    def _sample_billing_dict(self, files_to_update: dict = None, files_payload: list = None):
        file_ids = files_to_update or self.upload_files()
        return {
            DeploymentBillingConfig.ENABLED: True,
            DeploymentBillingConfig.PRODUCT_TYPE: ProductType.RPM.value,
            DeploymentBillingConfig.FILES: files_payload or self._uploaded_files(file_ids),
            DeploymentBillingConfig.COLORS: [
                Color.to_dict(Color(color=ColorHex.WHITE, name=ColorNames.WHITE)),
                Color.to_dict(Color(color=ColorHex.GREY, name=ColorNames.GREY)),
                Color.to_dict(Color(color=ColorHex.RED, name=ColorNames.RED)),
                Color.to_dict(Color(color=ColorHex.AMBER, name=ColorNames.AMBER)),
                Color.to_dict(Color(color=ColorHex.LIGHT_RED, name=ColorNames.LIGHT_RED)),
            ],
        }

    @staticmethod
    def _uploaded_files(file_ids, mapping: dict = None):
        key_file_type_mapping = mapping or {
            DeploymentBillingFileType.INSURANCE_CARRIERS.value: "insurance_carrier_test_file.csv",
            DeploymentBillingFileType.BILLING_PROVIDERS.value: "billing_providers_test_file.csv",
            DeploymentBillingFileType.ICD_10_CODES.value: "ICD10_codes_test_file.csv",
        }
        return [
            {
                DeploymentBillingFiles.TYPE: file_type,
                DeploymentBillingFiles.NAME: f"{file_type}_FileName.csv",
                DeploymentBillingFiles.FILE_ID: file_ids[file_name],
            }
            for file_type, file_name in key_file_type_mapping.items()
        ]

    def _retrieve_user_time_tracked(self, user_id: str):
        docs = BillingRemoteTimeTracking.objects.filter(userId=user_id)
        return [model_to_dict(doc) for doc in docs]

    def upload_files(self, extra_files: set = None):
        extra_files = extra_files or {}
        file_key_mapping = {
            "billing_providers_test_file.csv",
            "ICD10_codes_test_file.csv",
            "insurance_carrier_test_file.csv",
            "billing_file_with_no_data.csv",
            "billing_file_with_column_names_only.csv",
        }.union(extra_files)
        file_ids = dict()
        for file_name in file_key_mapping:
            file_ids[file_name] = self.upload_test_file(VALID_USER_ID, file_name)

        return file_ids

    def upload_test_file(self, user_id, file_name):
        data = {
            "file": (io.BytesIO(self.content_test_file(file_name)), file_name),
        }
        rsp = self.flask_client.post(
            "/api/storage/v1/upload",
            data=data,
            headers=self.get_headers_for_token(identity=user_id),
            content_type="multipart/form-data",
        )
        return rsp.json["id"]

    @staticmethod
    def content_test_file(file_name):
        with open(Path(__file__).parent.joinpath(f"fixtures/{file_name}"), "rb") as file:
            return file.read()

    def _onboard_user(self, user_id):
        user = User.objects.get(mongoId=user_id)
        user.finishedOnboarding = True
        user.boardingStatus = user.boardingStatus or {}
        user.boardingStatus["status"] = 0
        user.boardingStatus["updateDateTime"] = "2021-02-09T14:08:05.997Z"
        user.save()


class TestDeploymentBilling(BillingTestCase):
    def _retrieve_deployment_users(self, deployment_id: str):
        role_query = Q(
            roles__contains=[
                {
                    RoleAssignmentDTO.RESOURCE: f"deployment/{deployment_id}",
                    RoleAssignmentDTO.ROLE_ID: "User",
                }
            ]
        )
        return [model_to_dict(u) for u in User.objects.filter(role_query)]

    def _retrieve_submissions(self, user_id: str):
        return [model_to_dict(s) for s in BillingSubmission.objects.filter(userId=user_id)]

    def test_update_deployment_billing_info__no_billing_previously(self):
        deployment_users_before_update = self._retrieve_deployment_users(DEPLOYMENT_ID)
        for u in deployment_users_before_update:
            if UserDTO.COMPONENTS_DATA in u:
                self.assertNotIn("billing", u[UserDTO.COMPONENTS_DATA])

        billing_dict = self._sample_billing_dict()
        body = {
            **modified_deployment(),
            DeploymentDTO.FEATURES: {
                Features.CUSTOM_APP_CONFIG: {
                    BILLING_FEATURES_KEY: billing_dict,
                }
            },
        }
        rsp = self.flask_client.put(self.base_url % DEPLOYMENT_ID, json=body, headers=self.headers)
        self.assertEqual(200, rsp.status_code)
        self._assert_billing_data_in_deployment(DEPLOYMENT_ID, billing_dict)

        deployment_users_after_update = self._retrieve_deployment_users(DEPLOYMENT_ID)
        for u in deployment_users_after_update:
            self.assertIn(UserDTO.COMPONENTS_DATA, u)
            self.assertEqual(
                0,
                u[UserDTO.COMPONENTS_DATA][BILLING_FEATURES_KEY][UserBilling.STATUS],
            )

    def test_update_deployment_billing__wrong_file_structure(self):
        billing_dict = self._sample_billing_dict()
        billing_dict[DeploymentBillingConfig.FILES] = {"a": "b"}

        body = {
            **modified_deployment(),
            DeploymentDTO.FEATURES: {
                Features.CUSTOM_APP_CONFIG: {
                    BILLING_FEATURES_KEY: billing_dict,
                }
            },
        }
        rsp = self.flask_client.put(self.base_url % DEPLOYMENT_ID, json=body, headers=self.headers)
        self.assertEqual(400, rsp.status_code)

    def test_update_deployment_billing__empty_files(self):
        values_to_test = [[], None]
        billing_dict = self._sample_billing_dict()
        for value in values_to_test:
            billing_dict[DeploymentBillingConfig.FILES] = value

            body = {
                **modified_deployment(),
                DeploymentDTO.FEATURES: {
                    Features.CUSTOM_APP_CONFIG: {
                        BILLING_FEATURES_KEY: billing_dict,
                    }
                },
            }
            rsp = self.flask_client.put(self.base_url % DEPLOYMENT_ID, json=body, headers=self.headers)
            self.assertEqual(403, rsp.status_code)

    def test_update_deployment_billing__empty_file(self):
        file_id = self.upload_test_file(VALID_USER_ID, "billing_file_with_no_data.csv")
        body = self._billing_data_with_overriden_file(file_id)
        rsp = self.flask_client.put(self.base_url % DEPLOYMENT_ID, json=body, headers=self.headers)
        self.assertEqual(400, rsp.status_code)

    def test_update_deployment_billing__no_data_on_csv(self):
        file_id = self.upload_test_file(VALID_USER_ID, "billing_file_with_column_names_only.csv")
        body = self._billing_data_with_overriden_file(file_id)

        rsp = self.flask_client.put(self.base_url % DEPLOYMENT_ID, json=body, headers=self.headers)
        self.assertEqual(400, rsp.status_code)

    def test_update_deployment_without_billing_info(self):
        body = {
            **modified_deployment(),
            DeploymentDTO.FEATURES: {Features.CUSTOM_APP_CONFIG: {}},
        }
        rsp = self.flask_client.put(self.base_url % DEPLOYMENT_ID, json=body, headers=self.headers)
        self.assertEqual(200, rsp.status_code)

    def test_create_deployment_with_billing(self):
        billing_dict = self._sample_billing_dict()
        deployment = {
            **simple_deployment(),
            DeploymentDTO.FEATURES: {
                Features.CUSTOM_APP_CONFIG: {
                    BILLING_FEATURES_KEY: billing_dict,
                }
            },
        }
        rsp = self.flask_client.post(self.create_url, json=deployment, headers=self.headers)
        self.assertEqual(201, rsp.status_code)
        self._assert_billing_data_in_deployment(rsp.json[DeploymentDTO.ID], billing_dict)

    @patch.object(PyJWS, "_verify_signature")
    def test_update_user_billing__without_previous_data(self, _):
        self._set_deployment_billing("612f153c1a297695e4506d53")
        user_without_data_id = self._create_test_user()
        self._onboard_user(user_without_data_id)
        billing_data = sample_user_billing()
        body = {UserDTO.COMPONENTS_DATA: {BILLING_FEATURES_KEY: billing_data}}
        rsp = self.flask_client.post(
            f"/api/extensions/v1/user/{user_without_data_id}",
            json=body,
            headers=self.get_headers_for_token(user_without_data_id),
        )
        self.assertEqual(200, rsp.status_code)

    def test_update_user_billing__without_data(self):
        self._set_deployment_billing()
        self._onboard_user(USER_ID)
        body = {}
        rsp = self.flask_client.post(
            f"/api/extensions/v1/user/{USER_ID}",
            json=body,
            headers=self.get_headers_for_token(USER_ID),
        )
        self.assertEqual(200, rsp.status_code)

    def test_update_user_billing_twice_with_changing_files(self):
        self._set_deployment_billing()
        self._onboard_user(USER_ID)
        billing_data = sample_user_billing()
        body = {UserDTO.COMPONENTS_DATA: {BILLING_FEATURES_KEY: billing_data}}
        rsp = self.flask_client.post(
            f"/api/extensions/v1/user/{USER_ID}",
            json=body,
            headers=self.get_headers_for_token(USER_ID),
        )
        self.assertEqual(200, rsp.status_code)

        file_ids = self.upload_files({"insurance_carrier_test_file_2.csv"})
        key_file_type_mapping = {
            DeploymentBillingFileType.INSURANCE_CARRIERS.value: "insurance_carrier_test_file_2.csv",
            DeploymentBillingFileType.BILLING_PROVIDERS.value: "billing_providers_test_file.csv",
            DeploymentBillingFileType.ICD_10_CODES.value: "ICD10_codes_test_file.csv",
        }
        file_data = self._uploaded_files(file_ids, key_file_type_mapping)
        billing_dict = self._sample_billing_dict(file_ids, file_data)

        self._set_deployment_billing(
            body={
                **modified_deployment(),
                DeploymentDTO.FEATURES: {
                    Features.CUSTOM_APP_CONFIG: {
                        BILLING_FEATURES_KEY: billing_dict,
                    }
                },
            }
        )

        updated_billing_data = sample_user_billing()
        updated_billing_data[UserBilling.INSURANCE_CARRIERS][0][InsuranceCarrier.GROUP_NAME] = "testGroupName11"

        body = {UserDTO.COMPONENTS_DATA: {BILLING_FEATURES_KEY: updated_billing_data}}
        rsp = self.flask_client.post(
            f"/api/extensions/v1/user/{USER_ID}",
            json=body,
            headers=self.get_headers_for_token(USER_ID),
        )

        self.assertEqual(200, rsp.status_code)

    def test_update_user_billing__deployment_billing_matched(self):
        self._set_deployment_billing()
        self._onboard_user(USER_ID)

        billing_data = sample_user_billing()
        body = {UserDTO.COMPONENTS_DATA: {BILLING_FEATURES_KEY: billing_data}}
        rsp = self.flask_client.post(
            f"/api/extensions/v1/user/{USER_ID}",
            json=body,
            headers=self.get_headers_for_token(USER_ID),
        )
        self.assertEqual(200, rsp.status_code)

        rsp = self.flask_client.get(
            f"/api/extensions/v1/user/{USER_ID}",
            headers=self.get_headers_for_token(USER_ID),
        )
        self.assertIn(BILLING_FEATURES_KEY, rsp.json[UserDTO.COMPONENTS_DATA])

        rsp = self.flask_client.post(
            "/api/extensions/v1/user/profiles",
            headers=self.get_headers_for_token(MANAGER_ID),
            json={},
        )
        for profile in rsp.json[USERS_KEY]:
            if profile[UserDTO.ID] != USER_ID:
                continue

            self.assertIn(
                BILLING_FEATURES_KEY,
                profile[UserDTO.COMPONENTS_DATA],
            )

    @patch.object(PyJWS, "_verify_signature")
    def test_update_user_billing__deployment_billing_matched_but_billing_NOT_enrolled(self, _):
        self._set_deployment_billing("612f153c1a297695e4506d53")
        user_id = self._create_test_user()
        self._onboard_user(user_id)
        file_id = self.upload_test_file(user_id, VALID_PDF_FILE_SAMPLE)
        billing_data = sample_user_billing_not_enrolled()
        billing_data[UserBilling.FILE] = {
            UserBillingFile.FILE_ID: file_id,
            UserBillingFile.NAME: "temp.pdf",
        }
        body = {UserDTO.COMPONENTS_DATA: {BILLING_FEATURES_KEY: billing_data}}
        rsp = self.flask_client.post(
            f"/api/extensions/v1/user/{user_id}",
            json=body,
            headers=self.get_headers_for_token(user_id),
        )
        self.assertEqual(200, rsp.status_code)

    def _create_test_user(self):
        sample_name = "user1"
        user_data = {
            "method": 0,
            "email": "<EMAIL>",
            "displayName": sample_name,
            "validationData": {"invitationCode": INVITATION_CODE},
            "userAttributes": {
                UserDTO.FAMILY_NAME: sample_name,
                UserDTO.GIVEN_NAME: sample_name,
            },
            "clientId": "ctest1",
            "projectId": "ptest1",
        }
        rsp = self.flask_client.post(f"{self.auth_route}/signup", json=user_data)
        return rsp.json["uid"]

    @patch.object(PyJWS, "_verify_signature")
    def test_create_user_without_billing_info(self, _):
        self._set_deployment_billing("612f153c1a297695e4506d53")
        user_id = self._create_test_user()

        rsp = self.flask_client.get(
            f"/api/extensions/v1/user/{user_id}",
            headers=self.get_headers_for_token(user_id),
        )
        self.assertEqual(
            UserBilling.BillingStatus.PENDING,
            rsp.json[UserDTO.COMPONENTS_DATA]["billing"][UserBilling.STATUS],
        )
        rsp = self.flask_client.post(
            "/api/extensions/v1/user/profiles",
            headers=self.get_headers_for_token("5eda5db67adadfb49f7ff71d"),
            json={},
        )
        for profile in rsp.json[USERS_KEY]:
            if profile[UserDTO.ID] != user_id:
                continue

            self.assertEqual(
                UserBilling.BillingStatus.PENDING,
                profile[UserDTO.COMPONENTS_DATA]["billing"][UserBilling.STATUS],
            )

    def test_submission_calculation_flag_cannot_be_changed_Calendar_to_30_day(self):
        # Set deployment WITHOUT flag (default to False)
        rsp = self._set_billing_calculation_type(True)
        self.assertEqual(200, rsp.status_code)
        rsp = self._set_billing_calculation_type(False)
        self.assertEqual(400, rsp.status_code)

    def test_transition_from_NON_billing_deployment_to_calendar_calculation(self):
        today = datetime.utcnow()
        this_month_start = datetime(today.year, today.month, 1)
        dates = [
            this_month_start - timedelta(days=2),
            this_month_start - timedelta(days=3),
            this_month_start + timedelta(days=2),
            this_month_start + timedelta(days=3),
        ]
        self._set_billing_alert_dates(dates)

        rsp = self._set_billing_calculation_type(True)
        self.assertEqual(200, rsp.status_code)

        new_alert_dates = self._get_billing_alert_dates()
        self.assertNotIn(dates[0], new_alert_dates)
        self.assertNotIn(dates[1], new_alert_dates)

    @freeze_time("2024-11-10 10:00:00")
    @patch(VALIDATE_FILE_PATH, MagicMock())
    def test_transition_from_30_day_to_calendar_calculation(self):
        today = datetime.utcnow()
        this_month_start = datetime(today.year, today.month, 1)
        dates = [
            this_month_start - timedelta(days=2),
            this_month_start - timedelta(days=3),
            this_month_start,
            this_month_start + timedelta(days=2),
            this_month_start + timedelta(days=3),
        ]
        self._set_billing_alert_dates(dates)

        # set Existing flag is False
        self._set_billing_calculation_type(False, True)
        # change flag to True
        rsp = self._set_billing_calculation_type(True, True)
        self.assertEqual(200, rsp.status_code)

        new_submission_dates, new_dos = self._get_billing_alert_dates()
        self.assertEqual(3, len(new_submission_dates))
        self.assertNotIn(dates[0], new_submission_dates)
        self.assertNotIn(dates[1], new_submission_dates)
        self.assertIn(dates[2], new_submission_dates)

        this_month_end = this_month_start + relativedelta(months=1) - timedelta(days=1)
        self.assertEqual(this_month_end, new_dos)

    @freeze_time("2024-11-10 10:00:00")
    @patch(VALIDATE_FILE_PATH, MagicMock())
    def test_transition_from_30_day_to_calendar_calculation_for_fresh_period(self):
        today = datetime.utcnow()
        this_month_start = datetime(today.year, today.month, 1)
        # add 2 submissions in this month
        self._create_submission_record(this_month_start + timedelta(days=1))
        self._create_submission_record(this_month_start + timedelta(days=2))
        # set existing submissionDates dates without above 2 dates
        dates = [this_month_start - timedelta(days=3), this_month_start]
        self._set_billing_alert_dates(dates)
        # set flag to True
        rsp = self._set_billing_calculation_type(True, True)
        self.assertEqual(200, rsp.status_code)
        # check that the 2 submissions are in the new submissionDates
        new_submission_dates, new_dos = self._get_billing_alert_dates()
        self.assertEqual(3, len(new_submission_dates))

        this_month_end = this_month_start + relativedelta(months=1) - timedelta(days=1)
        self.assertEqual(this_month_end, new_dos)

    @freeze_time("2024-11-10 10:00:00")
    @patch(VALIDATE_FILE_PATH, MagicMock())
    def test_transition_from_30_day_to_calendar_calculation_before_first_submission_in_a_period(
        self,
    ):
        today = datetime.utcnow()
        this_month_start = datetime(today.year, today.month, 1)
        dates = [
            this_month_start - timedelta(days=3),
            this_month_start - timedelta(days=5),
        ]
        self._set_billing_alert_dates(dates)
        # set flag to True
        rsp = self._set_billing_calculation_type(True, True)
        self.assertEqual(200, rsp.status_code)
        # check that all previous month submissions are removed
        new_submission_dates, new_dos = self._get_billing_alert_dates()
        self.assertEqual(0, len(new_submission_dates))

        this_month_end = this_month_start + relativedelta(months=1) - timedelta(days=1)
        self.assertEqual(this_month_end, new_dos)

    def _create_submission_record(self, start_dt: datetime):
        document = BillingSubmission(
            deploymentId=DEPLOYMENT_ID,
            userId=ALERT_USER_ID,
            primitiveId=ObjectId(),
            primitiveClassName="BloodPressure",
            deviceName="IOS",
            deviceDetails="Dummy device details",
            source="Dummy source",
            startDateTime=start_dt,
            createDateTime=start_dt,
            startDate=start_dt,
            isCompleted=None,
        )
        document.save()
        return str(document.mongoId)

    def _get_billing_alert_dates(self):
        result = BillingAlert.objects.filter(mongoId=ALERT_USER_ID).first()
        submission_dates = (
            [datetime.strptime(d.split("T")[0], "%Y-%m-%d") for d in result.submissionDates]
            if result.submissionDates
            else []
        )
        return submission_dates, result.nextSubmissionDoS

    def _set_billing_alert_dates(self, dates: list):
        BillingAlert(
            mongoId=ALERT_USER_ID,
            user_id=ALERT_USER_ID,
            deploymentId=DEPLOYMENT_ID,
            submissionDates=dates,
        ).save()

    def _set_billing_calculation_type(self, flag: bool, mock_billing_files: bool = False):
        files_to_update = (
            {
                "ICD10_codes_test_file.csv": "6773ebeadc0cd8250668ae1e",
                "billing_file_with_column_names_only.csv": "6773ebeadc0cd8250668ae1d",
                "billing_file_with_no_data.csv": "6773ebeadc0cd8250668ae1c",
                "billing_providers_test_file.csv": "6773ebeadc0cd8250668ae1b",
                "insurance_carrier_test_file.csv": "6773ebeadc0cd8250668ae1f",
            }
            if mock_billing_files
            else {}
        )

        billing_dict = self._sample_billing_dict(files_to_update)
        billing_dict[DeploymentBillingConfig.USE_CALENDAR_CALCULATION] = flag
        body = {
            DeploymentDTO.FEATURES: {
                Features.CUSTOM_APP_CONFIG: {
                    BILLING_FEATURES_KEY: billing_dict,
                }
            },
        }
        url = self.base_url % DEPLOYMENT_ID
        headers = self.get_headers_for_token(VALID_USER_ID)
        rsp = self.flask_client.put(url, json=body, headers=headers)
        return rsp

    def _count_billing_alerts_repo(self):
        return BillingAlert.objects.count()

    def test_users_added_to_billing_alerts(self):
        self.assertEqual(0, self._count_billing_alerts_repo())
        user_ids = ["60642e821668fbf7381eefa0", "5e8f0c74b50aa9656c34789c"]
        for user_id in user_ids:
            self._onboard_user(user_id)
        self._set_deployment_billing()
        self.assertEqual(len(user_ids), self._count_billing_alerts_repo())

    def test_offboard_user__billing_set_to_not_applicable(self):
        self._onboard_user(USER_ID)
        self._set_deployment_billing()

        billing_data = sample_user_billing()
        body = {UserDTO.COMPONENTS_DATA: {BILLING_FEATURES_KEY: billing_data}}
        rsp = self.flask_client.post(
            f"/api/extensions/v1/user/{USER_ID}",
            json=body,
            headers=self.get_headers_for_token(USER_ID),
        )
        self.assertEqual(200, rsp.status_code)
        user_ids = [USER_ID]
        data_dict = {
            OffBoardUsersRequestObject.USER_IDS: user_ids,
            OffBoardUsersRequestObject.DETAILS_OFF_BOARDED: ReasonDetails.RECOVERED,
        }
        rsp = self.flask_client.post(
            "/api/extensions/v1/user/offboard",
            headers=self.get_headers_for_token(MANAGER_ID),
            json=data_dict,
        )
        self.assertEqual(rsp.status_code, 200)

        deployment_users = self._retrieve_deployment_users(DEPLOYMENT_ID)
        for user in [u for u in deployment_users]:
            if user[UserDTO.ID] == ObjectId(USER_ID):
                self.assertEqual(
                    UserBilling.BillingStatus.NOT_APPLICABLE,
                    user[UserDTO.COMPONENTS_DATA]["billing"][UserBilling.STATUS],
                )

    def test_update_user_billing__deployment_billing_unmatched(self):
        self._onboard_user(USER_ID)
        self._set_deployment_billing()

        billing_data = {
            UserBilling.STATUS: UserBilling.BillingStatus.PENDING.value,
            UserBilling.INSURANCE_CARRIERS: [
                {
                    InsuranceCarrier.ORDER: 1,
                    InsuranceCarrier.GROUP_NAME: "testGroupName121",
                    InsuranceCarrier.PAYER_ID: "testPayer143",
                },
                {
                    InsuranceCarrier.ORDER: 2,
                    InsuranceCarrier.GROUP_NAME: "testGroupName232",
                    InsuranceCarrier.PAYER_ID: "testPayer2",
                },
            ],
            UserBilling.BILLING_PROVIDER_NAME: "testBillingProviderName123",
            UserBilling.DIAGNOSIS: {
                Diagnosis.ORDER: 1,
                Diagnosis.ICD_CODE: "ICD132",
                Diagnosis.DESCRIPTION: "test description23",
            },
        }
        body = {UserDTO.COMPONENTS_DATA: {BILLING_FEATURES_KEY: billing_data}}
        rsp = self.flask_client.post(
            f"/api/extensions/v1/user/{USER_ID}",
            json=body,
            headers=self.get_headers_for_token(USER_ID),
        )
        self.assertEqual(400, rsp.status_code)

    def test_update_and_retrieve_user_with_billing_info(self):
        self._onboard_user(USER_ID)
        self._setup_billing()
        self._submit_sample_module_results(user_id=USER_ID)
        self.assertIn("billing", self._retrieve_user()[UserDTO.COMPONENTS_DATA])

    def test_calling_onboarding_event__user_added_to_billing_alerts(self):
        self.assertEqual(0, self._count_billing_alerts_repo())
        process_user_onboarding(UserOnboardedEvent(user_id=USER_ID))
        self.assertEqual(1, self._count_billing_alerts_repo())

    def test_off_boarding_reactivation_events__billing_alerts(self):
        self.assertEqual(0, self._count_billing_alerts_repo())
        process_user_onboarding(UserOnboardedEvent(user_id=USER_ID))
        process_offboard_user(PostUserOffBoardEvent(user_id=USER_ID, detail=""))
        self.assertEqual(1, self._count_billing_alerts_repo())
        self.assertIsNotNone(BillingAlert.objects.first().deleteDateTime)
        process_reactivate_user(PostUserReactivationEvent(user_id=USER_ID))
        self.assertIsNone(BillingAlert.objects.first().deleteDateTime)

    def test_reactivation_event_for_non_existing_user__billing_alerts(self):
        self.assertEqual(0, self._count_billing_alerts_repo())
        process_reactivate_user(PostUserReactivationEvent(user_id=USER_ID))
        self.assertEqual(1, self._count_billing_alerts_repo())
        self.assertIsNone(BillingAlert.objects.first().deleteDateTime)

    def test_submit_multiple_module_results(self):
        # According to the latest logic submissions for past periods
        # will not be counted excluding the very first submission
        self._onboard_user(USER_ID)
        self._setup_billing()

        days = [24, 25, 26]
        values = [70, 80, 90]

        self._submit_multiple_module_results(user_id=USER_ID, days=days, values=values)
        self.assertIn("billing", self._retrieve_user()[UserDTO.COMPONENTS_DATA])

        billing_submissions = self._retrieve_submissions(user_id=USER_ID)
        self.assertEqual(1, len(billing_submissions))

    def test_submit_multiple_module_results_same_day(self):
        # According to the latest logic submissions for past periods
        # will not be counted excluding the very first submission
        self._onboard_user(USER_ID)
        self._setup_billing()

        days = [24, 25, 24, 24, 25]
        values = [70, 80, 90, 100, 110]

        self._submit_multiple_module_results(user_id=USER_ID, days=days, values=values)
        self.assertIn("billing", self._retrieve_user()[UserDTO.COMPONENTS_DATA])

        billing_submissions = self._retrieve_submissions(user_id=USER_ID)
        self.assertEqual(1, len(billing_submissions))

    def _setup_billing(self):
        self._set_deployment_billing()
        billing_data = sample_user_billing()
        body = {UserDTO.COMPONENTS_DATA: {BILLING_FEATURES_KEY: billing_data}}
        rsp = self.flask_client.post(
            f"{self.user_path}/{USER_ID}",
            json=body,
            headers=self.get_headers_for_token(USER_ID),
        )
        self.assertEqual(200, rsp.status_code)

        resp_billing_data = self._retrieve_user()[UserDTO.COMPONENTS_DATA][BILLING_FEATURES_KEY]
        for key in [UserBilling.UPDATE_DT, UserBilling.CREATE_DT]:
            self.assertIn(key, resp_billing_data)
            resp_billing_data.pop(key)
            if key in billing_data:
                billing_data.pop(key)
            for key_field in resp_billing_data:
                if isinstance(resp_billing_data[key_field], dict) and resp_billing_data[key_field].get(key):
                    resp_billing_data[key_field].pop(key)
                elif isinstance(resp_billing_data[key_field], list):
                    for i in resp_billing_data[key_field]:
                        if isinstance(i, dict) and i.get(key):
                            i.pop(key)

        self.assertEqual(resp_billing_data, billing_data)

    def _retrieve_user(self, user_id: str = USER_ID):
        rsp = self.flask_client.get(
            f"{self.user_path}/{user_id}",
            headers=self.get_headers_for_token(user_id),
        )
        self.assertEqual(200, rsp.status_code)
        return rsp.json

    def _submit_sample_module_results(self, user_id: str = USER_ID, start_dt: str = None):
        body = [self._simple_blood_pressure(user_id, start_dt=start_dt)]
        rsp = self.flask_client.post(
            f"api/extensions/v1/user/{user_id}/module-result/{BloodPressureModule.moduleId}",
            headers=self.get_headers_for_token(user_id),
            json=body,
        )
        self.assertEqual(201, rsp.status_code)

    def _submit_multiple_module_results(
        self,
        user_id: str = USER_ID,
        days: list = None,
        values: list = None,
    ):
        days = days or [24, 25, 26]
        values = values or [70, 80, 90]
        body = self._multiple_blood_pressures(user_id, days=days, values=values)
        rsp = self.flask_client.post(
            f"api/extensions/v1/user/{user_id}/module-result/{BloodPressureModule.moduleId}",
            headers=self.get_headers_for_token(user_id),
            json=body,
        )
        self.assertEqual(201, rsp.status_code)

    @staticmethod
    def _simple_blood_pressure(user_id: str = USER_ID, value: int = 70, day: int = 24, start_dt: str = None):
        return {
            PrimitiveDTO.USER_ID: user_id,
            PrimitiveDTO.SUBMITTER_ID: user_id,
            PrimitiveDTO.DEPLOYMENT_ID: DEPLOYMENT_ID,
            PrimitiveDTO.CREATE_DATE_TIME: f"2022-11-{day:02}T10:09:17.845Z",
            PrimitiveDTO.START_DATE_TIME: start_dt or f"2021-11-{day:02}T10:09:17.694Z",
            PrimitiveDTO.DEVICE_NAME: "device",
            PrimitiveDTO.SOURCE: f"{PrimitiveSources.HEALTH_KIT.value};other;possible;things",
            PrimitiveDTO.SERVER: {"hostUrl": "local", "server": "1.0.0", "api": "V1"},
            BloodPressureDTO.MODULE_ID: "BloodPressure",
            BloodPressureDTO.MODULE_CONFIG_ID: "5e94b2007773091c9a592650",
            BloodPressureDTO.DIASTOLIC_VALUE: value,
            BloodPressureDTO.SYSTOLIC_VALUE: value + 20,
            "type": BloodPressureDTO.get_primitive_name(),
        }

    def _multiple_blood_pressures(
        self,
        user_id: str = USER_ID,
        values: list[int] = None,
        days: list[int] = None,
    ):
        values = values or [70, 80, 90]
        days = days or [24, 25, 26]
        return [self._simple_blood_pressure(user_id, v, d) for v, d in zip(values, days)]

    def set_up_user_deployment_billing(self):
        self.assertEqual(0, len(self._retrieve_user_time_tracked(USER_ID)))

        self._set_deployment_billing()
        billing_data = sample_user_billing()
        body = {UserDTO.COMPONENTS_DATA: {BILLING_FEATURES_KEY: billing_data}}
        rsp = self.flask_client.post(
            f"{self.user_path}/{USER_ID}",
            json=body,
            headers=self.get_headers_for_token(USER_ID),
        )
        self.assertEqual(200, rsp.status_code)

    def test_time_track_billing_on_offline_video_call(self):
        self._onboard_user(USER_ID)
        self.set_up_user_deployment_billing()

        body = {
            CreateOfflineCallRequestObject.START_DATE_TIME: "2022-11-22T14:50:22.039Z",
            CreateOfflineCallRequestObject.END_DATE_TIME: "2022-11-22T14:50:59.039Z",
            CreateOfflineCallRequestObject.NOTE: "NOTE",
        }
        rsp = self.flask_client.post(
            url_for("online_offline_call_route.create_offline_call", user_id=USER_ID),
            json=body,
            headers=self.get_headers_for_token(MANAGER_ID),
        )
        self.assertEqual(201, rsp.status_code)

        self.assertEqual(1, len(self._retrieve_user_time_tracked(USER_ID)))

    def test_time_track_billing_on_video_call(self):
        self._onboard_user(USER_ID)
        self.set_up_user_deployment_billing()

        body = {}
        with patch.object(TwilioVideoAdapter, "create_room", return_value=VideoRoom(roomSid="room1")):
            rsp = self.flask_client.post(
                f"/api/extensions/v1/manager/{MANAGER_ID}/video/user/{USER_ID}/initiate",
                json=body,
                headers=self.get_headers_for_token(MANAGER_ID),
            )
        self.assertEqual(201, rsp.status_code)
        call_id = rsp.json["videoCallId"]

        after_a_minute = datetime.utcnow() + timedelta(minutes=1)
        with (
            freeze_time(after_a_minute),
            patch.object(TwilioVideoAdapter, "complete_room"),
        ):
            rsp = self.flask_client.post(
                f"/api/extensions/v1/user/{USER_ID}/video/{call_id}/complete",
                json={"reason": "ANSWERED"},
                headers=self.get_headers_for_token(USER_ID),
            )
            self.assertEqual(204, rsp.status_code)

        self.assertEqual(1, len(self._retrieve_user_time_tracked(USER_ID)))

    def test_monthly_export_contains_basic_provider_details(self):
        self._set_deployment_billing()
        self._set_billing_calculation_type(True)
        submission_dt = datetime.utcnow()
        self._submit_sample_module_results(start_dt=utc_str_field_to_val(submission_dt))
        self.add_single_time_tracking()
        report = self._request_export()

        base_data = report[USER_ID]["module"][0]["billing"]
        submission_data = base_data[BillingMonthlyReportUserBilling.CPT_99453_4]
        time_tracking_data = base_data[BillingMonthlyReportUserBilling.CPT_99457_8]
        self.assertEqual(
            "testBillingProviderName1",
            submission_data[BillingProvider.NAME],
        )
        self.assertEqual(
            "ABBR1",
            submission_data[BillingProvider.ABBREVIATION],
        )
        self.assertEqual(
            "testBillingProviderName1",
            time_tracking_data[BillingProvider.NAME],
        )
        self.assertEqual(
            "ABBR1",
            time_tracking_data[BillingProvider.ABBREVIATION],
        )
        self.assertNotIn(EHRBillingProvider.LOCATION_DEPARTMENT, time_tracking_data)

    def test_EHR_monthly_export_contains_all_provider_details(self):
        self._set_deployment_billing()
        self._set_billing_calculation_type(True)
        submission_dt = datetime.utcnow().replace(day=1)
        self._submit_sample_module_results(start_dt=utc_str_field_to_val(submission_dt))
        self.add_single_time_tracking()

        report = self._request_export(export_module=BillingMonthlyEHRExportableUseCase.MODULE_NAME)
        self.assertIn(USER_ID, report, report)
        base_data = report[USER_ID]["module"][0]["billing"]
        submission_data = base_data[BillingMonthlyReportUserBilling.CPT_99453_4]
        time_tracking_data = base_data[BillingMonthlyReportUserBilling.CPT_99457_8]
        self.assertIn(EHRBillingProvider.NAME, time_tracking_data)
        self.assertIn(EHRBillingProvider.NAME, submission_data)
        self.assertEqual(
            "LocationAbbreviation1",
            time_tracking_data[EHRBillingProvider.LOCATION_ABBREVIATION],
        )
        self.assertEqual(
            "LocationDepartment1",
            submission_data[EHRBillingProvider.LOCATION_DEPARTMENT],
        )

    def _request_export(self, export_module=BillingMonthlyExportableUseCase.MODULE_NAME):
        PATTERN_FILE_NAME_DATE = r"MonthlyBilling_\d{4}-\d{2}-\d{2}_\d{4}-\d{2}-\d{2}"
        url = url_for("billing_export_route.export_deployment", deployment_id=DEPLOYMENT_ID)
        start, end = BillingCalendarPeriodCalculator().calculate(datetime.now().date())

        payload = {
            ExportParameters.FROM_DATE: utc_str_field_to_val(datetime.combine(start, datetime.min.time())),
            ExportParameters.TO_DATE: utc_str_field_to_val(datetime.combine(end, datetime.max.time())),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
            ExportParameters.MODULE_NAMES: [export_module],
            ExportParameters.SINGLE_FILE_RESPONSE: True,
            ExportParameters.USER_IDS: [USER_ID],
        }
        headers = self.get_headers_for_token(MANAGER_ID)
        rsp = self.flask_client.post(url, json=payload, headers=headers)
        self.assertEqual(rsp.status_code, 200)
        return simplify_export_response_keys(rsp, PATTERN_FILE_NAME_DATE, "module")

    def add_single_time_tracking(self):
        url = url_for("billing_route.create_billing_remote_time_tracking", user_id=USER_ID)
        now_date = datetime.utcnow().strftime("%Y-%m-%d")
        body = {
            CreateBillingRequestObject.START_DATE_TIME: f"{now_date}T00:00:00.039Z",
            CreateBillingRequestObject.END_DATE_TIME: f"{now_date}T00:01:00.039Z",
        }
        return self.flask_client.post(
            url,
            headers=self.get_headers_for_token(MANAGER_ID),
            json=body,
        )

    def _billing_data_with_overriden_file(self, file_id: str) -> dict:
        deployment = {
            **modified_deployment(),
            DeploymentDTO.FEATURES: {Features.CUSTOM_APP_CONFIG: {BILLING_FEATURES_KEY: self._sample_billing_dict()}},
        }
        deployment[DeploymentDTO.FEATURES][Features.CUSTOM_APP_CONFIG][BILLING_FEATURES_KEY][
            DeploymentBillingConfig.FILES
        ][0] = {
            DeploymentBillingFiles.TYPE: DeploymentBillingFileType.INSURANCE_CARRIERS.value,
            DeploymentBillingFiles.NAME: "FileName",
            DeploymentBillingFiles.FILE_ID: file_id,
        }
        return deployment

    def _assert_billing_data_in_deployment(self, deployment_id: str, billing_dict=None):
        deployment = model_to_dict(Deployment.objects.get(mongoId=deployment_id))
        deployment_files = deployment[DeploymentDTO.FEATURES][Features.CUSTOM_APP_CONFIG][BILLING_FEATURES_KEY][
            DeploymentBillingConfig.FILES
        ]
        for file in deployment_files:
            self.assertIn(DeploymentBillingFiles.UPLOAD_DATETIME, file)
            file.pop(DeploymentBillingFiles.UPLOAD_DATETIME)
        self.assertEqual(
            billing_dict,
            deployment[DeploymentDTO.FEATURES][Features.CUSTOM_APP_CONFIG][BILLING_FEATURES_KEY],
        )


class BillingOverlappingTestCase(BillingTestCase, CheckEffectiveTimeExclusiveMixin):
    def create_remote_tracking_doc(self, start_dt, end_dt):
        duration = (self.convert_str_to_iso_date(end_dt) - self.convert_str_to_iso_date(start_dt)).total_seconds()
        document = BillingRemoteTimeTracking(
            mongoId=str(ObjectId()),
            deploymentId=DEPLOYMENT_ID,
            clinicianId=MANAGER_ID,
            startDateTime=start_dt,
            endDateTime=end_dt,
            userId=USER_ID,
            effectiveStartDateTime=start_dt,
            effectiveEndDateTime=end_dt,
            effectiveDuration=duration,
        )
        document.save()
        return duration

    def insert_remote_tracking_doc_to_db(self, start_dt, end_dt):
        repo = inject.instance(BillingRemoteTimeTrackingRepository)
        repo.create_billing_remote_time_tracking(
            deployment_id=DEPLOYMENT_ID,
            clinician_id=MANAGER_ID,
            start_datetime=start_dt,
            end_datetime=end_dt,
            user_id=USER_ID,
        )

    def update_remote_tracking_doc_time_range(self, document_id, new_start_dt, new_end_dt):
        repo = inject.instance(BillingRemoteTimeTrackingRepository)
        repo.update_remote_time_tracking_start_and_end_dt(
            tracking_id=document_id, start_dt=new_start_dt, end_dt=new_end_dt
        )

    @staticmethod
    def convert_str_to_iso_date(date):
        return datetime.strptime(date, "%Y-%m-%dT%H:%M:%S.%fZ")

    def test_time_overlapping_fix_method(self):
        self._set_deployment_billing()
        billing_data = sample_user_billing()
        body = {
            UserDTO.COMPONENTS_DATA: {
                BILLING_FEATURES_KEY: {
                    **billing_data,
                    UserBilling.STATUS: UserBilling.BillingStatus.COMPLETED.value,
                }
            }
        }
        self._onboard_user(USER_ID)
        rsp = self.flask_client.post(
            f"{self.user_path}/{USER_ID}",
            json=body,
            headers=self.get_headers_for_token(USER_ID),
        )
        self.assertEqual(200, rsp.status_code)

        duration_1 = self.create_remote_tracking_doc("2022-11-22T14:45:22.039Z", "2022-11-22T14:55:59.039Z")
        self.assertEqual(637.0, duration_1)

        duration_2 = self.create_remote_tracking_doc("2022-11-22T14:50:20.039Z", "2022-11-22T14:52:40.039Z")
        self.assertEqual(140.0, duration_2)

        user_tracked_time = self._retrieve_user_time_tracked(USER_ID)
        self.assertEqual(2, len(user_tracked_time))

        remove_users_billing_data_overlapping(start_dt=datetime(2022, 10, 20), end_dt=datetime(2022, 12, 20))

        user_tracked_time = self._retrieve_user_time_tracked(USER_ID)
        self.assertEqual(duration_1, user_tracked_time[0]["effectiveDuration"])
        self.assertEqual(0.0, user_tracked_time[1]["effectiveDuration"])

    @parameterized.expand(
        [
            (
                "covering_overlapping",
                [("45:22", "55:59", 637.0), ("50:20", "52:40", 140.0)],
                637.0,
            ),
            (
                "covering_overlapping_reverse",
                [("45:22", "55:59", 637.0), ("40:00", "59:00", 1140.0)],
                1140.0,
            ),
            (
                "separate_no_change",
                [("45:22", "55:59", 637.0), ("56:20", "58:40", 140.0)],
                777.0,
            ),
            (
                "separate_edge_covering",
                [("45:22", "55:59", 637.0), ("55:59", "56:00", 1.0)],
                638.0,
            ),
            (
                "partial_overlapping",
                [("45:20", "46:00", 40.0), ("45:30", "58:40", 790.0)],
                800.0,
            ),
        ]
    )
    def test_overlapping(
        self,
        _: str,
        times: list = None,
        effective_cumulative_durations: float = None,
    ):
        times = times or [("45:22", "55:59", 637.0), ("50:20", "52:40", 140.0)]
        expected_cumulative_effective_durations = effective_cumulative_durations or 637.0
        self._set_deployment_billing()
        billing_data = sample_user_billing()
        body = {
            UserDTO.COMPONENTS_DATA: {
                BILLING_FEATURES_KEY: {
                    **billing_data,
                    UserBilling.STATUS: UserBilling.BillingStatus.COMPLETED.value,
                }
            }
        }
        self._onboard_user(USER_ID)
        rsp = self.flask_client.post(
            f"{self.user_path}/{USER_ID}",
            json=body,
            headers=self.get_headers_for_token(USER_ID),
        )
        self.assertEqual(200, rsp.status_code)

        for start, end, duration in times:
            calc_duration = self.create_remote_tracking_doc(f"2022-11-22T14:{start}.000Z", f"2022-11-22T14:{end}.000Z")
            self.assertEqual(duration, calc_duration)

        user_tracked_time = self._retrieve_user_time_tracked(USER_ID)
        self.assertEqual(len(times), len(user_tracked_time))

        remove_users_billing_data_overlapping(start_dt=datetime(2022, 10, 20), end_dt=datetime(2022, 12, 20))

        user_tracked_time = self._retrieve_user_time_tracked(USER_ID)

        self.check_effective_time_exclusive(user_tracked_time, expected_cumulative_effective_durations)

    def _get_complex_scenario_time_ranges(self):
        complex_scenario = dict()

        complex_scenario["start_stop_time_difference"] = [
            [2, 12],
            [10, 20],
            [14, 40],
            [16, 30],
            [26, 45],
            [45, 55],
            [50, 70],
            [57, 58],
            [60, 64],
            [65, 70],
            [75, 85],
            [69, 80],
            [85, 95],
            [95, 99],
            [100, 130],
            [105, 125],
            [110, 120],
            [113, 115],
        ]
        complex_scenario["effective_durations"] = [[2, 99], [100, 130]]

        complex_scenario["delete_range_no_change"] = [
            [
                (16, 30),
                (57, 58),
                (60, 64),
                (65, 70),
                (105, 125),
                (110, 120),
                (113, 115),
            ],
            [(14, 40)],
        ]

        complex_scenario["delete_range_with_change"] = [
            [[(50, 70)], [(0, 0), (2, 55), (57, 58), (60, 64), (65, 99), (100, 130)]],
            [
                [(50, 70), (26, 45)],
                [(0, 0), (2, 40), (45, 55), (57, 58), (60, 64), (65, 99), (100, 130)],
            ],
        ]

        return complex_scenario

    def _get_complex_add_scenario(self, start_time: datetime):
        complex_scenario = self._get_complex_scenario_time_ranges()
        start_stop_time_difference = complex_scenario["start_stop_time_difference"]
        effective_durations = complex_scenario["effective_durations"]

        time_ranges = []
        for start_minute, end_minute in start_stop_time_difference:
            time_range = (
                start_time + timedelta(minutes=start_minute),
                start_time + timedelta(minutes=end_minute),
            )
            time_ranges.append(time_range)

        effective_time_ranges = []
        effective_time_duration = timedelta(0)
        for start_minute, end_minute in effective_durations:
            effective_time_range = (
                start_time + timedelta(minutes=start_minute),
                start_time + timedelta(minutes=end_minute),
            )
            effective_time_ranges.append(effective_time_range)
            effective_time_duration += timedelta(minutes=end_minute - start_minute)
        effective_time_info = (effective_time_ranges, effective_time_duration)

        return time_ranges, effective_time_info

    def _get_complex_delete_scenario(self, start_time: datetime):
        complex_scenario = self._get_complex_scenario_time_ranges()
        effective_durations = complex_scenario["effective_durations"]
        delete_with_no_change_in_result = complex_scenario["delete_range_no_change"]
        delete_with_change_in_result = complex_scenario["delete_range_with_change"]

        delete_scenarios = []

        for deleted_time_ranges in delete_with_no_change_in_result:
            time_ranges = []
            for start_minute, end_minute in deleted_time_ranges:
                time_ranges.append(
                    [
                        start_time + timedelta(minutes=start_minute),
                        start_time + timedelta(minutes=end_minute),
                    ]
                )

            effective_time_ranges = []
            effective_time_duration = timedelta(0)
            for start_minute, end_minute in effective_durations:
                effective_time_range = (
                    start_time + timedelta(minutes=start_minute),
                    start_time + timedelta(minutes=end_minute),
                )
                effective_time_ranges.append(effective_time_range)
                effective_time_duration += timedelta(minutes=end_minute - start_minute)
            effective_time_ranges.append((start_time, start_time))
            effective_time_info = (effective_time_ranges, effective_time_duration)

            delete_scenarios.append(
                {
                    "ranges_to_delete": time_ranges,
                    "effective_results": effective_time_info,
                }
            )

        for (
            deleted_time_ranges,
            effective_results_range,
        ) in delete_with_change_in_result:
            time_ranges = []
            for start_minute, end_minute in deleted_time_ranges:
                time_ranges.append(
                    [
                        start_time + timedelta(minutes=start_minute),
                        start_time + timedelta(minutes=end_minute),
                    ]
                )
            effective_time_ranges = []
            effective_time_duration = timedelta(0)
            for start_minute, end_minute in effective_results_range:
                effective_time_range = (
                    start_time + timedelta(minutes=start_minute),
                    start_time + timedelta(minutes=end_minute),
                )
                effective_time_ranges.append(effective_time_range)
                effective_time_duration += timedelta(minutes=end_minute - start_minute)
            effective_time_info = (effective_time_ranges, effective_time_duration)

            delete_scenarios.append(
                {
                    "ranges_to_delete": time_ranges,
                    "effective_results": effective_time_info,
                }
            )

        return delete_scenarios

    @parameterized.expand(
        [
            ("ordered_insert", False),
            ("random_insert_1", True),
            ("random_insert_2", True),
            ("random_insert_3", True),
            ("random_insert_4", True),
        ]
    )
    def test_complex_overlapping_data_add(self, _: str, is_random: bool):
        self._set_deployment_billing()
        billing_data = sample_user_billing()
        body = {
            UserDTO.COMPONENTS_DATA: {
                BILLING_FEATURES_KEY: {
                    **billing_data,
                    UserBilling.STATUS: UserBilling.BillingStatus.COMPLETED.value,
                }
            }
        }
        self._onboard_user(USER_ID)
        rsp = self.flask_client.post(
            f"{self.user_path}/{USER_ID}",
            json=body,
            headers=self.get_headers_for_token(USER_ID),
        )
        self.assertEqual(200, rsp.status_code)

        times, expected_effective_time_info = self._get_complex_add_scenario(
            start_time=datetime(2022, 11, 22, 14, 0, 0)
        )
        if is_random:
            random.shuffle(times)

        for start, end in times:
            self.insert_remote_tracking_doc_to_db(start, end)

        user_tracked_time = self._retrieve_user_time_tracked(USER_ID)
        self.assertEqual(len(times), len(user_tracked_time))

        (
            expected_effective_times,
            expected_effective_time_duration,
        ) = expected_effective_time_info
        user_tracked_time = self._retrieve_user_time_tracked(USER_ID)
        user_effective_time = timedelta(0)
        for tracked_time in user_tracked_time:
            found_in_effective_ranges = False
            for expected_start, expected_end in expected_effective_times:
                if (
                    tracked_time["effectiveStartDateTime"] >= expected_start
                    and tracked_time["effectiveEndDateTime"] <= expected_end
                ):
                    found_in_effective_ranges = True
                    break
            self.assertTrue(found_in_effective_ranges)
            user_effective_time += tracked_time["effectiveEndDateTime"] - tracked_time["effectiveStartDateTime"]
        self.assertEqual(expected_effective_time_duration, user_effective_time)

        user_tracked_time.sort(key=lambda t: t["effectiveStartDateTime"])
        for idx in range(len(user_tracked_time)):
            if idx > 0 and user_tracked_time[idx]["effectiveDuration"] > 0:
                self.assertGreaterEqual(
                    user_tracked_time[idx]["effectiveStartDateTime"],
                    user_tracked_time[idx - 1]["effectiveEndDateTime"],
                )

    @parameterized.expand(
        [
            ("scenario_0", 0),
            ("scenario_1", 1),
            ("scenario_2", 2),
            ("scenario_3", 3),
        ]
    )
    def test_complex_overlapping_data_delete(self, _: str, scenario_index: int):
        base_datetime = datetime(2022, 11, 22, 14, 0, 0)
        self._set_deployment_billing()
        billing_data = sample_user_billing()
        body = {
            UserDTO.COMPONENTS_DATA: {
                BILLING_FEATURES_KEY: {
                    **billing_data,
                    UserBilling.STATUS: UserBilling.BillingStatus.COMPLETED.value,
                }
            }
        }
        self._onboard_user(USER_ID)
        rsp = self.flask_client.post(
            f"{self.user_path}/{USER_ID}",
            json=body,
            headers=self.get_headers_for_token(USER_ID),
        )
        self.assertEqual(200, rsp.status_code)

        times, _ = self._get_complex_add_scenario(start_time=base_datetime)

        for start, end in times:
            self.insert_remote_tracking_doc_to_db(start, end)

        user_tracked_time = self._retrieve_user_time_tracked(USER_ID)
        self.assertEqual(len(times), len(user_tracked_time))

        delete_scenario = self._get_complex_delete_scenario(start_time=base_datetime)[scenario_index]
        for start, end in delete_scenario["ranges_to_delete"]:
            document_id = [
                doc["id"] for doc in user_tracked_time if doc["startDateTime"] == start if doc["endDateTime"] == end
            ][0]
            self.update_remote_tracking_doc_time_range(document_id, base_datetime, base_datetime)

        expected_effective_times, expected_effective_time_duration = delete_scenario["effective_results"]
        user_tracked_time = self._retrieve_user_time_tracked(USER_ID)
        user_effective_time = timedelta(0)
        for tracked_time in user_tracked_time:
            found_in_effective_ranges = False
            for expected_start, expected_end in expected_effective_times:
                if (
                    tracked_time["effectiveStartDateTime"] >= expected_start
                    and tracked_time["effectiveEndDateTime"] <= expected_end
                ):
                    found_in_effective_ranges = True
                    break
            self.assertTrue(found_in_effective_ranges)
            user_effective_time += tracked_time["effectiveEndDateTime"] - tracked_time["effectiveStartDateTime"]
        self.assertEqual(expected_effective_time_duration, user_effective_time)

        user_tracked_time.sort(key=lambda t: t["effectiveStartDateTime"])
        for idx in range(len(user_tracked_time)):
            if idx > 0 and user_tracked_time[idx]["effectiveDuration"] > 0:
                self.assertGreaterEqual(
                    user_tracked_time[idx]["effectiveStartDateTime"],
                    user_tracked_time[idx - 1]["effectiveEndDateTime"],
                )
