from datetime import datetime

from billing.components.core.component import BillingComponent
from billing.components.core.dtos.billing_models import (
    BillingDiagnosisHistoryLogDTO,
    BillingInsuranceCarrierProfileHistoryDTO,
    BillingProviderHistoryLogDTO,
)
from billing.components.core.repository.billing_repository import (
    BillingProfileHistoryLogRepository,
)
from sdk.common.utils import inject
from sdk.common.utils.inject import autoparams
from sdk.deployment.component import DeploymentComponent
from sdk.tests.extension_test_case import ExtensionTestCase

VALID_DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
USER_ID = "5e8f0c74b50aa9656c34789b"


class BaseBillingProfileHistoryLogRepoTest(ExtensionTestCase):
    components = [
        BillingComponent(),
        DeploymentComponent(),
    ]

    @autoparams("log_repo")
    def setUp(self, log_repo: BillingProfileHistoryLogRepository):
        self.repo = log_repo

    @staticmethod
    def get_base_log_sample() -> dict:
        return {
            BillingDiagnosisHistoryLogDTO.USER_ID: USER_ID,
            BillingDiagnosisHistoryLogDTO.DEPLOYMENT_ID: VALID_DEPLOYMENT_ID,
            BillingDiagnosisHistoryLogDTO.CREATE_DATETIME: datetime(2022, 12, 12, 10, 10),
        }

    def get_diagnosis_sample(self, extra_data: dict = None):
        return {
            **self.get_base_log_sample(),
            BillingDiagnosisHistoryLogDTO.DESCRIPTION: "temp_desc",
            BillingDiagnosisHistoryLogDTO.ICD10CODE: "ICD2",
            BillingDiagnosisHistoryLogDTO.ORDER: 1,
            **(extra_data if extra_data else {}),
        }

    def get_carrier_sample(self, extra_data: dict = None):
        return {
            **self.get_base_log_sample(),
            BillingInsuranceCarrierProfileHistoryDTO.PAYER_ID: "payer1",
            BillingInsuranceCarrierProfileHistoryDTO.GROUP_NAME: "g1",
            BillingInsuranceCarrierProfileHistoryDTO.ORDER: 1,
            **(extra_data if extra_data else {}),
        }

    def get_provider_sample(self, extra_data: dict = None):
        return {
            **self.get_base_log_sample(),
            BillingProviderHistoryLogDTO.BILLING_PROVIDER_ID: "p1_id",
            BillingProviderHistoryLogDTO.BILLING_PROVIDER_NAME: "p1",
            **(extra_data if extra_data else {}),
        }

    @staticmethod
    def get_logs_from_db(mongo_cls):
        billing_profile_log_repo = inject.instance(BillingProfileHistoryLogRepository)
        return billing_profile_log_repo.get_class_data(mongo_cls)


class BillingProfileHistoryLogRepoTest(BaseBillingProfileHistoryLogRepoTest):
    def test_create_diagnosis_log(self):
        self.repo.create_diagnosis_log(BillingDiagnosisHistoryLogDTO.from_dict(self.get_diagnosis_sample()))

        record = self.get_logs_from_db("MongoBillingDiagnosisHistoryLog")[0]

        self.assertEqual(str(record.userId), USER_ID)
        self.assertEqual(str(record.deploymentId), VALID_DEPLOYMENT_ID)
        self.assertEqual(record.order, 1)
        self.assertEqual(record.icd10Code, "ICD2")
        self.assertEqual(record.description, "temp_desc")

    def test_create_insurance_log(self):
        self.repo.create_insurance_log(BillingInsuranceCarrierProfileHistoryDTO.from_dict(self.get_carrier_sample()))

        record = self.get_logs_from_db("MongoBillingInsuranceCarrierHistoryLog")[0]

        self.assertEqual(str(record.userId), USER_ID)
        self.assertEqual(str(record.deploymentId), VALID_DEPLOYMENT_ID)
        self.assertEqual(record.order, 1)
        self.assertEqual(record.groupName, "g1")
        self.assertEqual(record.payerId, "payer1")

    def test_create_provider_log(self):
        self.repo.create_provider_log(BillingProviderHistoryLogDTO.from_dict(self.get_provider_sample()))

        record = self.get_logs_from_db("MongoBillingProviderHistoryLog")[0]

        self.assertEqual(str(record.userId), USER_ID)
        self.assertEqual(str(record.deploymentId), VALID_DEPLOYMENT_ID)
        self.assertEqual(record.billingProviderId, "p1_id")
        self.assertEqual(record.billingProviderName, "p1")

    def test_create_multiple_data(self):
        self.repo.create_provider_log(BillingProviderHistoryLogDTO.from_dict(self.get_provider_sample()))
        self.repo.create_insurance_log(BillingInsuranceCarrierProfileHistoryDTO.from_dict(self.get_carrier_sample()))
        self.repo.create_diagnosis_log(BillingDiagnosisHistoryLogDTO.from_dict(self.get_diagnosis_sample()))

        records = (
            self.get_logs_from_db("MongoBillingProviderHistoryLog")
            + self.get_logs_from_db("MongoBillingInsuranceCarrierHistoryLog")
            + self.get_logs_from_db("MongoBillingDiagnosisHistoryLog")
        )

        self.assertEqual(len(records), 3)
