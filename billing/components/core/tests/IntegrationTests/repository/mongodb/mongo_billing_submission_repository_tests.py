from _datetime import <PERSON><PERSON><PERSON>
from copy import copy
from datetime import datetime

from freezegun import freeze_time

from billing.components.core.component import BillingComponent
from billing.components.core.dtos.billing_models import BillingSubmissionDTO
from billing.components.core.models import BillingSubmission
from billing.components.core.repository.billing_repository import (
    BillingSubmissionRepository,
)
from sdk.common.utils.inject import autoparams
from sdk.common.utils.json_utils import camelize
from sdk.deployment.component import DeploymentComponent
from sdk.tests.extension_test_case import ExtensionTestCase

VALID_CLINICIAN_ID = "5e8f0c74b50aa9656c34789d"
VALID_DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
VALID_DEPLOYMENT_ID_2 = "5d386cc6ff885918d96edb2b"
VALID_ADMINISTRATOR_ID = "63ce44193d6527f1c3ceafe3"
VALID_PRIMITIVE_ID = "5f7dd1f0e03d4a97e8007a91"
USER_ID = "5e8f0c74b50aa9656c34789b"
USER_ID_2 = "5e8f0c74b50aa9656c34789c"
VALID_DEVICE = "IOS"


class BaseBillingSubmissionRepoTest(ExtensionTestCase):
    components = [
        BillingComponent(),
        DeploymentComponent(),
    ]

    @autoparams("submission_repo")
    def setUp(self, submission_repo: BillingSubmissionRepository):
        self.repo = submission_repo

    @staticmethod
    def create_record(
        deployment_id=VALID_DEPLOYMENT_ID,
        user_id=USER_ID,
        primitive_id=VALID_PRIMITIVE_ID,
        primitive_class_name="Weight",
        device_name=VALID_DEVICE,
        device_details="Dummy device details",
        source="Dummy source",
        start_datetime=datetime.utcnow(),
        start_datetime_utc=datetime.utcnow(),
        create_datetime=datetime.utcnow(),
        is_completed=None,
    ):
        start_date = start_datetime.date()
        document = BillingSubmission(
            deploymentId=deployment_id,
            userId=user_id,
            primitiveId=primitive_id,
            primitiveClassName=primitive_class_name,
            deviceName=device_name,
            deviceDetails=device_details,
            source=source,
            startDateTime=start_datetime,
            startDateTimeUTC=start_datetime_utc,
            createDateTime=create_datetime,
            startDate=start_date,
            isCompleted=is_completed,
        )
        document.save()
        return str(document.mongoId)

    @staticmethod
    def retrieve_record_by_id(record_id: str):
        return BillingSubmission.objects.filter(mongoId=record_id).first()

    @staticmethod
    def retrieve_all_records():
        return BillingSubmission.objects.all()


class BillingSubmissionCountTest(BaseBillingSubmissionRepoTest):
    def test_create_record_without_submission_count(self):
        document_id = self.create_record()
        self.assertEqual(0, self.retrieve_record_by_id(document_id).todaySubmissionCount)

    @freeze_time("2021-01-05T10:00:00.000Z")
    def test_update_record_with_submission_count(self):
        document_id = self.create_record(**self._params)
        submission_obj = self._create_submission()
        self.repo.create_update_billing_submission(submission_obj, count=1)
        self.assertEqual(1, self.retrieve_record_by_id(document_id).todaySubmissionCount)

    @freeze_time("2021-01-05T10:00:00.000Z")
    def test_create_update_record_with_submission_count(self):
        submission_obj = self._create_submission()
        self.repo.create_update_billing_submission(submission_obj, count=1)
        document = self.retrieve_all_records().first()
        self.assertEqual(0, document.todaySubmissionCount)

        self.repo.create_update_billing_submission(submission_obj, count=1)
        document = self.retrieve_all_records().first()
        self.assertEqual(1, document.todaySubmissionCount)

    @property
    def _params(self) -> dict:
        return {
            "deployment_id": VALID_DEPLOYMENT_ID,
            "user_id": USER_ID,
            "primitive_id": VALID_PRIMITIVE_ID,
            "primitive_class_name": "Weight",
            "device_name": VALID_DEVICE,
            "device_details": "Dummy device details",
            "source": "Dummy source",
            "start_datetime": datetime.utcnow(),
            "start_datetime_utc": datetime.utcnow(),
            "is_completed": False,
        }

    def _create_submission(self):
        params = copy(self._params)
        params["startDate"] = datetime.utcnow().date()
        return BillingSubmissionDTO.from_dict(camelize(params))


class SubmissionBillingRepoIntegrationTest(BaseBillingSubmissionRepoTest):
    @freeze_time("2021-01-05T10:00:00.000Z")
    def test_find_user_first_submission_for_deployment(self):
        _record_submission_date_times = [
            datetime.now(),
            datetime.now() + timedelta(days=2),
            datetime.now() - timedelta(days=2),
        ]
        for start_datetime in _record_submission_date_times:
            self.create_record(create_datetime=start_datetime, start_datetime=start_datetime)
        item = self.repo.find_user_first_submission(USER_ID, VALID_DEPLOYMENT_ID)
        self.assertEqual("2021-01-03", str(item.startDateTime.date()))

    @freeze_time("2021-01-05T10:00:00.000Z")
    def test_get_total_record_days_count_after_specific_date(self):
        _record_submission_data = [
            (datetime.now(), VALID_DEPLOYMENT_ID, USER_ID),
            (datetime.now(), VALID_DEPLOYMENT_ID, USER_ID_2),
            (datetime.now() + timedelta(days=2), VALID_DEPLOYMENT_ID, USER_ID),
            (datetime.now() + timedelta(days=2, hours=1), VALID_DEPLOYMENT_ID, USER_ID),
            (datetime.now() + timedelta(days=3), VALID_DEPLOYMENT_ID, USER_ID_2),
            (datetime.now() + timedelta(days=4), VALID_DEPLOYMENT_ID, USER_ID),
            (datetime.now() + timedelta(days=4), VALID_DEPLOYMENT_ID, USER_ID_2),
            (datetime.now() + timedelta(days=5), VALID_DEPLOYMENT_ID_2, USER_ID),
            (datetime.now() - timedelta(days=2), VALID_DEPLOYMENT_ID, USER_ID),
            (datetime.now() - timedelta(days=2), VALID_DEPLOYMENT_ID, USER_ID),
        ]

        for submission_date, deployment, user in _record_submission_data:
            self.create_record(deployment_id=deployment, user_id=user, start_datetime=submission_date)

        self.assertEqual(
            4,
            self.repo.get_total_record_days_count_after_specific_date(USER_ID, datetime.now().date()),
        )

    @freeze_time("2021-01-05T10:00:00.000Z")
    def test_get_total_record_count_and_days_from_to(self):
        _record_submission_data = [
            (datetime.now(), VALID_DEPLOYMENT_ID, USER_ID),
            (datetime.now() + timedelta(hours=2), VALID_DEPLOYMENT_ID, USER_ID),
            (datetime.now() - timedelta(days=2), VALID_DEPLOYMENT_ID, USER_ID),
            (datetime.now() + timedelta(days=1), VALID_DEPLOYMENT_ID, USER_ID),
        ]

        for submission_date, deployment, user in _record_submission_data:
            self.create_record(deployment_id=deployment, user_id=user, start_datetime=submission_date)
        self.assertEqual(
            2,
            self.repo.get_total_record_days_count_from_to(
                USER_ID,
                datetime.now().date() - timedelta(days=10),
                datetime.now().date(),
                VALID_DEPLOYMENT_ID,
            ),
        )
        self.assertEqual(
            3,
            self.repo.get_total_submissions_for_user_from_to(
                USER_ID,
                VALID_DEPLOYMENT_ID,
                datetime.now().date() - timedelta(days=10),
                datetime.now().date(),
                return_count=True,
            ),
        )

    def tearDown(self):
        super().tearDown()
        BillingSubmission.objects.all().delete()
