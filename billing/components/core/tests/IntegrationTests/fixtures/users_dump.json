{"deployment": [{"_id": {"$oid": "5d386cc6ff885918d96edb2c"}, "name": "Move Patient Billing RPM", "status": "DEPLOYED", "color": "0x007AFF", "code": "AU15", "privacyPolicyUrl": "https://storage.googleapis.com/hu-deployment-static-content/discovernow/Huma%20App%20Privacy%20Notice%20-%20Discover%20Now.pdf", "eulaUrl": "https://medopad.com/medopad_Ltd/EULA.pdf", "termAndConditionUrl": "https://medopad.com/medopad_Ltd/EULA.pdf", "contactUsURL": "https://huma.com/contact", "keyActionsEnabled": true, "features": {"labels": true, "customAppConfig": {"billing": {"enabled": true, "productType": "RPM", "files": [{"type": "insuranceCarriers", "name": "insurance_carrier_test_file.csv", "uploadDateTime": "2023-02-10T15:21:09.090Z", "fileId": "63e660e27ab4034a57aa8e55"}, {"type": "billingProviders", "name": "BillingProviders.csv", "uploadDateTime": "2023-02-10T15:21:09.091Z", "fileId": "63e660e37ab4034a57aa8e56"}, {"type": "icd10Codes", "name": "ICD-10-codes.csv", "uploadDateTime": "2023-02-10T15:21:09.091Z", "fileId": "63e660e4985eb95f05b21e70"}]}}}, "updateDateTime": {"$date": "2020-04-09T12:00:00"}, "createDateTime": {"$date": "2020-04-09T12:00:00"}}], "huma_auth_user": [{"_id": {"$oid": "64e76adedf047d493ba356c3"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999555", "displayName": "manager", "userAttributes": {"familyName": "manager", "givenName": "manager", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": "2020-04-09T12:00:00"}, "updateDateTime": {"$date": "2020-04-09T12:00:00"}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999999", "displayName": "testUser", "userAttributes": {"familyName": "testUser", "givenName": "testUser", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": "2020-04-09T12:00:00"}, "updateDateTime": {"$date": "2020-04-09T12:00:00"}}, {"_id": {"$oid": "6550c55f11af3ef8935229b6"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999990", "displayName": "testUser", "userAttributes": {"familyName": "testUser", "givenName": "testUser", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": "2020-04-09T12:00:00"}, "updateDateTime": {"$date": "2020-04-09T12:00:00"}}], "user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "phoneNumber": "+380999999999", "nhsId": "nhs_id", "dateOfBirth": "1988-02-20", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "timezone": "America/New_York", "createDateTime": {"$date": "2020-04-09T12:00:00"}, "surgeryDateTime": {"$date": "2020-04-09T12:00:00"}, "boardingStatus": {"status": 0, "updateDateTime": "2020-04-09T12:52:20.000Z"}, "componentsData": {"billing": {"status": 1, "billingProviderName": "someName", "diagnosis": {"order": 1, "icd10Code": "ICD", "description": "diagnosis description"}}}}, {"_id": {"$oid": "6550c55f11af3ef8935229b6"}, "givenName": "test1", "familyName": "test1", "email": "<EMAIL>", "phoneNumber": "+380999999990", "nhsId": "nhs_id", "dateOfBirth": "1988-02-20", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "timezone": "America/New_York", "createDateTime": {"$date": "2020-04-09T12:00:00"}, "surgeryDateTime": {"$date": "2020-04-09T12:00:00"}, "boardingStatus": {"status": 0, "updateDateTime": "2020-04-09T12:52:20.000Z"}, "componentsData": {"billing": {"status": 0}}}, {"_id": {"$oid": "64e76adedf047d493ba356c3"}, "givenName": "manager", "familyName": "manager", "email": "<EMAIL>", "phoneNumber": "+380999999555", "roles": [{"roleId": "Clinician", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "Manager", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": "2020-04-09T12:00:00"}}], "invitation": [{"_id": {"$oid": "605345882f7d4c18ef9e6dbc"}, "_cls": "MongoInvitation", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "isActive": true}], "email": "<EMAIL>", "code": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************.6M22glMJAavCoeHGf8CEDOWyn9SBNyITxQot7PpaHZn", "type": "PERSONAL", "expiresAt": {"$date": "2031-04-21T20:23:08.136Z"}, "createDateTime": {"$date": "2024-02-21T20:23:08.136Z"}, "deferredLink": "test_link_1"}]}