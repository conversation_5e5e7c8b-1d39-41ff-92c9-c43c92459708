{"deployment": [{"_id": {"$oid": "5d386cc6ff885918d96edb2c"}, "name": "Move Patient Billing RPM", "status": "DEPLOYED", "color": "0x007AFF", "code": "AU15", "privacyPolicyUrl": "https://storage.googleapis.com/hu-deployment-static-content/discovernow/Huma%20App%20Privacy%20Notice%20-%20Discover%20Now.pdf", "eulaUrl": "https://medopad.com/medopad_Ltd/EULA.pdf", "termAndConditionUrl": "https://medopad.com/medopad_Ltd/EULA.pdf", "contactUsURL": "https://huma.com/contact", "moduleConfigs": [{"about": "", "configBody": {}, "id": "5e94b2007773091c9a592660", "moduleId": "Weight", "moduleName": null, "order": 2, "ragThresholds": [], "status": "ENABLED", "version": 0}], "keyActionsEnabled": true, "features": {"labels": true, "customAppConfig": {"billing": {"enabled": true, "productType": "RPM", "files": [{"type": "insuranceCarriers", "name": "insurance_carrier_test_file.csv", "uploadDateTime": "2023-02-10T15:21:09.090Z", "fileId": "63e660e27ab4034a57aa8e55"}, {"type": "billingProviders", "name": "BillingProviders.csv", "uploadDateTime": "2023-02-10T15:21:09.091Z", "fileId": "63e660e37ab4034a57aa8e56"}, {"type": "icd10Codes", "name": "ICD-10-codes.csv", "uploadDateTime": "2023-02-10T15:21:09.091Z", "fileId": "63e660e4985eb95f05b21e70"}]}}}, "updateDateTime": {"$date": "2020-04-09T12:00:00"}, "createDateTime": {"$date": "2020-04-09T12:00:00"}}, {"_id": {"$oid": "5d386cc6ff885918d96edb2b"}, "name": "Move Patient Billing RTM", "status": "DEPLOYED", "color": "0x007AFF", "code": "AU15", "privacyPolicyUrl": "https://storage.googleapis.com/hu-deployment-static-content/discovernow/Huma%20App%20Privacy%20Notice%20-%20Discover%20Now.pdf", "eulaUrl": "https://medopad.com/medopad_Ltd/EULA.pdf", "termAndConditionUrl": "https://medopad.com/medopad_Ltd/EULA.pdf", "contactUsURL": "https://huma.com/contact", "moduleConfigs": [{"about": "", "configBody": {}, "id": "5e94b2007773091c9a592660", "moduleId": "Weight", "moduleName": null, "order": 2, "ragThresholds": [], "status": "ENABLED", "version": 0}], "keyActionsEnabled": true, "keyActions": [], "surgeryDetails": {}, "features": {"customAppConfig": {"billing": {"enabled": true, "productType": "RTM", "files": [{"type": "insuranceCarriers", "name": "insurance_carrier_test_file.csv", "uploadDateTime": "2023-02-10T15:21:09.090Z", "fileId": "63e660e27ab4034a57aa8e55"}, {"type": "billingProviders", "name": "BillingProviders.csv", "uploadDateTime": "2023-02-10T15:21:09.091Z", "fileId": "63e660e37ab4034a57aa8e56"}, {"type": "icd10Codes", "name": "ICD-10-codes.csv", "uploadDateTime": "2023-02-10T15:21:09.091Z", "fileId": "63e660e4985eb95f05b21e70"}]}}}, "updateDateTime": {"$date": "2020-04-09T12:00:00"}, "createDateTime": {"$date": "2020-04-09T12:00:00"}}, {"_id": {"$oid": "65c611d4ed32374bf83b5341"}, "name": "RTM Calendar Deployment", "status": "DEPLOYED", "color": "0x007AFF", "code": "AU15", "privacyPolicyUrl": "https://storage.googleapis.com/hu-deployment-static-content/discovernow/Huma%20App%20Privacy%20Notice%20-%20Discover%20Now.pdf", "eulaUrl": "https://medopad.com/medopad_Ltd/EULA.pdf", "termAndConditionUrl": "https://medopad.com/medopad_Ltd/EULA.pdf", "contactUsURL": "https://huma.com/contact", "keyActionsEnabled": true, "features": {"customAppConfig": {"billing": {"enabled": true, "useCalendarCalculation": true, "productType": "RTM", "files": [{"type": "insuranceCarriers", "name": "insurance_carrier_test_file.csv", "uploadDateTime": "2023-02-10T15:21:09.090Z", "fileId": "63e660e27ab4034a57aa8e55"}, {"type": "billingProviders", "name": "BillingProviders.csv", "uploadDateTime": "2023-02-10T15:21:09.091Z", "fileId": "63e660e37ab4034a57aa8e56"}, {"type": "icd10Codes", "name": "ICD-10-codes.csv", "uploadDateTime": "2023-02-10T15:21:09.091Z", "fileId": "63e660e4985eb95f05b21e70"}]}}}, "updateDateTime": {"$date": "2020-04-09T12:00:00"}, "createDateTime": {"$date": "2020-04-09T12:00:00"}}], "huma_auth_user": [{"_id": {"$oid": "64e76adedf047d493ba356c3"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999555", "displayName": "manager", "userAttributes": {"familyName": "manager", "givenName": "manager", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": "2020-04-09T12:00:00"}, "updateDateTime": {"$date": "2020-04-09T12:00:00"}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999998", "displayName": "testUser", "userAttributes": {"familyName": "testUser", "givenName": "testUser", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": "2020-04-09T12:00:00"}, "updateDateTime": {"$date": "2020-04-09T12:00:00"}}], "user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "phoneNumber": "+380999999999", "nhsId": "nhs_id", "dateOfBirth": "1988-02-20", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "timezone": "America/New_York", "createDateTime": {"$date": "2020-04-09T12:00:00"}, "surgeryDateTime": {"$date": "2020-04-09T12:00:00"}, "boardingStatus": {"status": 0, "updateDateTime": "2020-04-09T12:52:20.000Z"}, "componentsData": {"billing": {"status": 1, "billingProviderName": "someName", "diagnosis": {"order": 1, "icd10Code": "ICD", "description": "diagnosis description"}}}, "labels": [{"assignedBy": {"$oid": "5e8f0c74b50aa9656c34789d"}, "assignDateTime": "2023-08-18T08:45:09.807713Z", "labelId": {"$oid": "6525cc1a3c410036d6f587a7"}}]}, {"_id": {"$oid": "64e76adedf047d493ba356c3"}, "givenName": "manager", "familyName": "manager", "email": "<EMAIL>", "phoneNumber": "+380999999555", "roles": [{"roleId": "Clinician", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "Manager", "isActive": true}, {"roleId": "Clinician", "resource": "deployment/5d386cc6ff885918d96edb2b", "userType": "Manager", "isActive": true}, {"roleId": "Clinician", "resource": "deployment/65c611d4ed32374bf83b5341", "userType": "Manager", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": "2020-04-09T12:00:00"}}], "billing_alert": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "_cls": "MongoBillingAlertDocument", "user_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "monitoringMinutes": 24.0, "createDateTime": "2023-04-25T16:50:19.456Z", "updateDateTime": "2023-05-14T08:00:00.000Z", "nextSubmissionDoS": "2023-05-01T08:00:00.000Z", "lastMonitoringDate": {"$date": "2023-05-05T08:00:00.000Z"}, "submissionDates": [], "callDateTime": {"$date": "2023-05-05T08:00:00.000Z"}, "nextMonitoringDoS": "2023-05-24T08:00:00.000Z", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}}], "billing_submission": [{"_id": {"$oid": "64a54cd6022dbab085403c28"}, "_cls": "MongoBillingSubmissionDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deviceDetails": null, "deviceName": "bioMetricDevice", "isCompleted": true, "primitiveClassName": "Weight", "todaySubmissionCount": 0, "primitiveId": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "source": "HumaDeviceKit", "startDateTime": {"$date": "2023-04-10T12:00:00.000Z"}, "startDate": {"$date": "2023-04-10T00:00:00.000Z"}, "createDateTime": {"$date": "2023-04-10T12:00:00.000Z"}, "updateDateTime": {"$date": "2023-04-10T12:00:00.000Z"}, "startDateTimeUTC": {"$date": "2023-04-10T17:00:00.000Z"}}, {"_id": {"$oid": "64a54cd6022dbab085403c2b"}, "_cls": "MongoBillingSubmissionDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deviceDetails": null, "deviceName": "bioMetricDevice", "isCompleted": true, "primitiveClassName": "Weight", "todaySubmissionCount": 0, "primitiveId": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "source": "HumaDeviceKit", "startDateTime": {"$date": "2023-04-11T12:00:00.000Z"}, "startDate": {"$date": "2023-04-11T12:00:00.000Z"}, "createDateTime": {"$date": "2023-04-11T12:00:00.000Z"}, "updateDateTime": {"$date": "2023-04-11T12:00:00.000Z"}, "startDateTimeUTC": {"$date": "2023-04-11T12:00:00.000Z"}}, {"_id": {"$oid": "64a5acd6022dbab085403c28"}, "_cls": "MongoBillingSubmissionDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deviceDetails": null, "deviceName": "bioMetricDevice", "isCompleted": true, "primitiveClassName": "Weight", "todaySubmissionCount": 0, "primitiveId": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "source": "HumaDeviceKit", "startDateTime": {"$date": "2023-04-12T12:00:00.000Z"}, "startDate": {"$date": "2023-04-12T12:00:00.000Z"}, "createDateTime": {"$date": "2023-04-12T12:00:00.000Z"}, "updateDateTime": {"$date": "2023-04-12T12:00:00.000Z"}, "startDateTimeUTC": {"$date": "2023-04-12T12:00:00.000Z"}}, {"_id": {"$oid": "64a54aa6022dbab085403c28"}, "_cls": "MongoBillingSubmissionDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deviceDetails": null, "deviceName": "bioMetricDevice", "isCompleted": true, "primitiveClassName": "Weight", "todaySubmissionCount": 0, "primitiveId": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "source": "HumaDeviceKit", "startDateTime": {"$date": "2023-04-13T12:00:00.000Z"}, "startDate": {"$date": "2023-04-13T12:00:00.000Z"}, "createDateTime": {"$date": "2023-04-13T12:00:00.000Z"}, "updateDateTime": {"$date": "2023-04-13T12:00:00.000Z"}, "startDateTimeUTC": {"$date": "2023-04-13T12:00:00.000Z"}}, {"_id": {"$oid": "64a54cd60aadbab085403c28"}, "_cls": "MongoBillingSubmissionDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deviceDetails": null, "deviceName": "bioMetricDevice", "isCompleted": true, "primitiveClassName": "Weight", "todaySubmissionCount": 0, "primitiveId": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "source": "HumaDeviceKit", "startDateTime": {"$date": "2023-04-14T12:00:00.000Z"}, "startDate": {"$date": "2023-04-14T12:00:00.000Z"}, "createDateTime": {"$date": "2023-04-14T12:00:00.000Z"}, "updateDateTime": {"$date": "2023-04-14T12:00:00.000Z"}, "startDateTimeUTC": {"$date": "2023-04-14T12:00:00.000Z"}}, {"_id": {"$oid": "64a54cd6022dbabbbbb03c28"}, "_cls": "MongoBillingSubmissionDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deviceDetails": null, "deviceName": "bioMetricDevice", "isCompleted": true, "primitiveClassName": "Weight", "todaySubmissionCount": 0, "primitiveId": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "source": "HumaDeviceKit", "startDateTime": {"$date": "2023-04-15T12:00:00.000Z"}, "startDate": {"$date": "2023-04-15T12:00:00.000Z"}, "createDateTime": {"$date": "2023-04-15T12:00:00.000Z"}, "updateDateTime": {"$date": "2023-04-15T12:00:00.000Z"}, "startDateTimeUTC": {"$date": "2023-04-15T12:00:00.000Z"}}, {"_id": {"$oid": "64a54cd6022dbab085a03c28"}, "_cls": "MongoBillingSubmissionDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deviceDetails": null, "deviceName": "bioMetricDevice", "isCompleted": true, "primitiveClassName": "Weight", "todaySubmissionCount": 0, "primitiveId": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "source": "HumaDeviceKit", "startDateTime": {"$date": "2023-04-16T12:00:00.000Z"}, "startDate": {"$date": "2023-04-16T00:00:00.000Z"}, "createDateTime": {"$date": "2023-04-16T00:00:00.000Z"}, "updateDateTime": {"$date": "2023-04-16T00:00:00.000Z"}, "startDateTimeUTC": {"$date": "2023-04-16T17:00:00.000Z"}}, {"_id": {"$oid": "64a54cd6022dbabb85403c28"}, "_cls": "MongoBillingSubmissionDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deviceDetails": null, "deviceName": "bioMetricDevice", "isCompleted": true, "primitiveClassName": "Weight", "todaySubmissionCount": 0, "primitiveId": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "source": "HumaDeviceKit", "startDateTime": {"$date": "2023-04-17T12:00:00.000Z"}, "startDate": {"$date": "2023-04-17T00:00:00.000Z"}, "createDateTime": {"$date": "2023-04-17T12:00:00.000Z"}, "updateDateTime": {"$date": "2023-04-17T12:00:00.000Z"}, "startDateTimeUTC": {"$date": "2023-04-17T17:00:00.000Z"}}, {"_id": {"$oid": "64a54cd6022dbab085403aa8"}, "_cls": "MongoBillingSubmissionDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deviceDetails": null, "deviceName": "bioMetricDevice", "isCompleted": true, "primitiveClassName": "Weight", "todaySubmissionCount": 0, "primitiveId": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "source": "HumaDeviceKit", "startDateTime": {"$date": "2023-04-18T12:00:00.000Z"}, "startDate": {"$date": "2023-04-18T00:00:00.000Z"}, "createDateTime": {"$date": "2023-04-18T12:00:00.000Z"}, "updateDateTime": {"$date": "2023-04-18T12:00:00.000Z"}, "startDateTimeUTC": {"$date": "2023-04-18T17:00:00.000Z"}}, {"_id": {"$oid": "64a54cd6022dbab08b403c28"}, "_cls": "MongoBillingSubmissionDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deviceDetails": null, "deviceName": "bioMetricDevice", "isCompleted": true, "primitiveClassName": "Weight", "todaySubmissionCount": 0, "primitiveId": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "source": "HumaDeviceKit", "startDateTime": {"$date": "2023-04-19T12:00:00.000Z"}, "startDate": {"$date": "2023-04-19T00:00:00.000Z"}, "createDateTime": {"$date": "2023-04-19T12:00:00.000Z"}, "updateDateTime": {"$date": "2023-04-19T12:00:00.000Z"}, "startDateTimeUTC": {"$date": "2023-04-19T17:00:00.000Z"}}, {"_id": {"$oid": "64a54cd6022dbab08bb03c28"}, "_cls": "MongoBillingSubmissionDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deviceDetails": null, "deviceName": "bioMetricDevice", "isCompleted": true, "primitiveClassName": "Weight", "todaySubmissionCount": 0, "primitiveId": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "source": "HumaDeviceKit", "startDateTime": {"$date": "2023-04-20T12:00:00.000Z"}, "startDate": {"$date": "2023-04-20T00:00:00.000Z"}, "createDateTime": {"$date": "2023-04-20T12:00:00.000Z"}, "updateDateTime": {"$date": "2023-04-20T12:00:00.000Z"}, "startDateTimeUTC": {"$date": "2023-04-20T17:00:00.000Z"}}, {"_id": {"$oid": "64a54cbb022dbab085403c28"}, "_cls": "MongoBillingSubmissionDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deviceDetails": null, "deviceName": "bioMetricDevice", "isCompleted": true, "primitiveClassName": "Weight", "todaySubmissionCount": 0, "primitiveId": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "source": "HumaDeviceKit", "startDateTime": {"$date": "2023-04-21T12:00:00.000Z"}, "startDate": {"$date": "2023-04-21T00:00:00.000Z"}, "createDateTime": {"$date": "2023-04-21T12:00:00.000Z"}, "updateDateTime": {"$date": "2023-04-21T12:00:00.000Z"}, "startDateTimeUTC": {"$date": "2023-04-21T17:00:00.000Z"}}, {"_id": {"$oid": "64b54cd6022dbab085403c28"}, "_cls": "MongoBillingSubmissionDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deviceDetails": null, "deviceName": "bioMetricDevice", "isCompleted": true, "primitiveClassName": "Weight", "todaySubmissionCount": 0, "primitiveId": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "source": "HumaDeviceKit", "startDateTime": {"$date": "2023-04-22T12:00:00.000Z"}, "startDate": {"$date": "2023-04-22T00:00:00.000Z"}, "createDateTime": {"$date": "2023-04-22T12:00:00.000Z"}, "updateDateTime": {"$date": "2023-04-22T12:00:00.000Z"}, "startDateTimeUTC": {"$date": "2023-04-22T17:00:00.000Z"}}, {"_id": {"$oid": "6aa54cd6022dbab085403c28"}, "_cls": "MongoBillingSubmissionDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deviceDetails": null, "deviceName": "bioMetricDevice", "isCompleted": true, "primitiveClassName": "Weight", "todaySubmissionCount": 0, "primitiveId": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "source": "HumaDeviceKit", "startDateTime": {"$date": "2023-04-23T12:00:00.000Z"}, "startDate": {"$date": "2023-04-23T00:00:00.000Z"}, "createDateTime": {"$date": "2023-04-23T12:00:00.000Z"}, "updateDateTime": {"$date": "2023-04-23T12:00:00.000Z"}, "startDateTimeUTC": {"$date": "2023-04-23T17:00:00.000Z"}}, {"_id": {"$oid": "a4a54cd6022dbab085403c28"}, "_cls": "MongoBillingSubmissionDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deviceDetails": null, "deviceName": "bioMetricDevice", "isCompleted": true, "primitiveClassName": "Weight", "todaySubmissionCount": 0, "primitiveId": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "source": "HumaDeviceKit", "startDateTime": {"$date": "2023-04-24T12:00:00.000Z"}, "startDate": {"$date": "2023-04-24T00:00:00.000Z"}, "createDateTime": {"$date": "2023-04-24T12:00:00.000Z"}, "updateDateTime": {"$date": "2023-04-24T12:00:00.000Z"}, "startDateTimeUTC": {"$date": "2023-04-24T17:00:00.000Z"}}, {"_id": {"$oid": "64a54cdff22dbab085403c28"}, "_cls": "MongoBillingSubmissionDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deviceDetails": null, "deviceName": "bioMetricDevice", "isCompleted": true, "primitiveClassName": "Weight", "todaySubmissionCount": 0, "primitiveId": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "source": "HumaDeviceKit", "startDateTime": {"$date": "2023-04-25T12:00:00.000Z"}, "startDate": {"$date": "2023-04-25T00:00:00.000Z"}, "createDateTime": {"$date": "2023-04-25T12:00:00.000Z"}, "updateDateTime": {"$date": "2023-04-25T12:00:00.000Z"}, "startDateTimeUTC": {"$date": "2023-04-25T17:00:00.000Z"}}, {"_id": {"$oid": "654caf7f60ebb366e075af39"}, "_cls": "MongoBillingSubmissionDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deviceDetails": null, "deviceName": "bioMetricDevice", "isCompleted": true, "primitiveClassName": "Weight", "todaySubmissionCount": 0, "primitiveId": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "source": "HumaDeviceKit", "startDateTime": {"$date": "2023-05-15T12:00:00.000Z"}, "startDate": {"$date": "2023-05-15T00:00:00.000Z"}, "createDateTime": {"$date": "2023-05-15T12:00:00.000Z"}, "updateDateTime": {"$date": "2023-05-15T12:00:00.000Z"}, "startDateTimeUTC": {"$date": "2023-05-15T17:00:00.000Z"}}], "billing_remote_time_tracking": [{"_id": {"$oid": "64a553456afff979bfb865f6"}, "_cls": "MongoBillingRemoteTimeTrackingDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "clinicianId": {"$oid": "64e76adedf047d493ba356c3"}, "startDateTime": {"$date": "2023-04-10T12:10:00.000Z"}, "endDateTime": {"$date": "2023-04-10T12:15:00.000Z"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "effectiveStartDateTime": {"$date": "2023-04-10T12:10:00.000Z"}, "effectiveEndDateTime": {"$date": "2023-04-10T12:15:00.000Z"}, "effectiveDuration": 300, "createDateTime": {"$date": "2023-04-10T12:10:00.000Z"}}, {"_id": {"$oid": "654cb34d60ebb366e075af3a"}, "_cls": "MongoBillingRemoteTimeTrackingDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "clinicianId": {"$oid": "64e76adedf047d493ba356c3"}, "startDateTime": {"$date": "2023-04-12T12:00:00.000Z"}, "endDateTime": {"$date": "2023-04-12T12:05:00.000Z"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "effectiveStartDateTime": {"$date": "2023-04-12T12:00:00.000Z"}, "effectiveEndDateTime": {"$date": "2023-04-12T12:05:00.000Z"}, "effectiveDuration": 300, "createDateTime": {"$date": "2023-04-10T12:10:00.000Z"}}, {"_id": {"$oid": "654cb36760ebb366e075af3b"}, "_cls": "MongoBillingRemoteTimeTrackingDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "clinicianId": {"$oid": "64e76adedf047d493ba356c3"}, "startDateTime": {"$date": "2023-04-15T14:00:00.000Z"}, "endDateTime": {"$date": "2023-04-15T14:20:00.000Z"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "effectiveStartDateTime": {"$date": "2023-04-15T14:00:00.000Z"}, "effectiveEndDateTime": {"$date": "2023-04-15T14:20:00.000Z"}, "effectiveDuration": 1200, "createDateTime": {"$date": "2023-04-10T12:10:00.000Z"}}], "billing_monitoring_log": [{"_id": {"$oid": "64b7b85fb024d4faa61d9f23"}, "_cls": "MongoBillingMonitoringLogDocument", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "createdById": {"$oid": "64e76adedf047d493ba356c3"}, "lastModifiedById": {"$oid": "64e76adedf047d493ba356c3"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "timeTrackingId": {"$oid": "64b7b85fb024d4faa61d9f22"}, "timeSpent": 300, "status": 0, "action": "hu_billing_log_action_8", "startDateTime": {"$date": "2023-04-12T12:00:00.000Z"}, "endDateTime": {"$date": "2023-04-12T12:05:00.000Z"}, "createDateTime": {"$date": "2023-04-12T12:00:00.000Z"}, "updateDateTime": {"$date": "2023-04-12T12:00:00.000Z"}, "initialCreateDateTime": {"$date": "2023-04-12T12:00:00.000Z"}}], "billing_profile_log_provider": [{"_id": {"$oid": "64a43c73b77e7d2ce0155850"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "createDateTime": {"$date": "2023-04-10T12:00:00.000Z"}}], "video_call": [{"_id": {"$oid": "5f0496ab82a630a9725336c1"}, "_cls": "MongoVideoCall", "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "managerId": {"$oid": "64e76adedf047d493ba356c3"}, "roomStatus": "completed", "startDateTime": {"$date": "2023-04-15T14:00:00.000Z"}, "endDateTime": {"$date": "2023-04-15T14:20:00.000Z"}, "updateDateTime": {"$date": "2023-04-15T14:20:00.000Z"}, "createDateTime": {"$date": "2023-04-15T14:00:00.000Z"}, "duration": {"$numberInt": "2"}, "status": "ANSWERED"}], "weight": [{"_id": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "moduleId": "Weight", "moduleConfigId": {"$oid": "5e94b2007773091c9a592660"}, "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "version": 0, "deviceName": "iOS", "startDateTime": {"$date": "2023-04-10T12:00:00.000Z"}, "createDateTime": {"$date": "2023-04-10T12:00:00.000Z"}, "submitterId": {"$oid": "5e8f0c74b50aa9656c34789c"}, "value": 99}]}