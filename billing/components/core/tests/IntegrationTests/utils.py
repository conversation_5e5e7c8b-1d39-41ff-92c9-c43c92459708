import unittest

from billing.components.core.dtos.billing_models import (
    BillingRemoteTimeTrackingDTO,
)

VALID_DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"


class CheckEffectiveTimeExclusiveMixin(unittest.TestCase):
    def check_effective_time_exclusive_with_documents(
        self,
        billing_document_list: list[BillingRemoteTimeTrackingDTO],
        expected_accumulative_duration: float,
    ):
        billing_document_list_of_dict = [
            {
                BillingRemoteTimeTrackingDTO.START_DATE_TIME: document.startDateTime,
                BillingRemoteTimeTrackingDTO.END_DATE_TIME: document.endDateTime,
                BillingRemoteTimeTrackingDTO.EFFECTIVE_START_DATE_TIME: document.effectiveStartDateTime,
                BillingRemoteTimeTrackingDTO.EFFECTIVE_END_DATE_TIME: document.effectiveEndDateTime,
                BillingRemoteTimeTrackingDTO.EFFECTIVE_DURATION: document.effectiveDuration,
            }
            for document in billing_document_list
        ]
        self.check_effective_time_exclusive(billing_document_list_of_dict, expected_accumulative_duration)

    def check_effective_time_exclusive(
        self,
        billing_document_list: list[dict],
        expected_accumulative_duration: float,
    ):
        for document in billing_document_list:
            self.assertGreaterEqual(document["effectiveStartDateTime"], document["startDateTime"])
            self.assertLessEqual(document["effectiveStartDateTime"], document["endDateTime"])
            self.assertGreaterEqual(document["effectiveEndDateTime"], document["startDateTime"])
            self.assertLessEqual(document["effectiveEndDateTime"], document["endDateTime"])

        for idx, doc1 in enumerate(billing_document_list):
            for doc2 in billing_document_list[idx + 1 :]:
                self.assertFalse(
                    doc1["effectiveStartDateTime"] < doc2["effectiveEndDateTime"]
                    and doc1["effectiveEndDateTime"] > doc2["effectiveStartDateTime"]
                    and doc1["effectiveDuration"] > 0
                    and doc2["effectiveDuration"] > 0
                )

        sum_effective_duration = sum(
            [billing_document["effectiveDuration"] for billing_document in billing_document_list]
        )
        self.assertEqual(sum_effective_duration, expected_accumulative_duration)


def sample_module_result():
    return {
        "deploymentId": VALID_DEPLOYMENT_ID,
        "deviceName": "iOS",
        "value": 100,
        "type": "Weight",
        "source": "HumaDeviceKit",
    }
