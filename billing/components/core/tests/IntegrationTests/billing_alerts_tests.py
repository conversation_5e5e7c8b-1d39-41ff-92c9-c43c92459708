import calendar
from datetime import date, datetime, timedelta, timezone, UTC
from functools import cached_property
from pathlib import Path
from typing import Optional
from unittest.mock import patch, MagicMock

from bson import ObjectId
from dateutil.relativedelta import relativedelta
from flask import url_for
from freezegun import freeze_time

from billing.components.core.component import BillingComponent
from billing.components.core.dtos.billing_models import (
    BillingAlertsDTO,
    BillingAlertsColorCoding,
    CPTCompliance,
)
from billing.components.core.models import BillingAlert
from billing.components.core.repository.billing_repository import (
    BillingAlertsRepository,
)
from billing.components.core.router.billing_requests import (
    AlertCPTField,
    AlertField,
    AlertFilterFields,
    AlertFilterParameters,
    AlertOrderField,
    AlertSortParameters,
    AlertsSortFields,
    CreateBillingRequestObject,
    RetrieveAlertsRequestObject,
)
from billing.components.core.router.billing_response_objects import (
    RetrieveAlertsResponseObject,
)
from billing.components.core.tests.IntegrationTests.billing_tests import (
    BaseBillingTestCase,
    VALID_CLINICIAN_ID,
    VALID_DEPLOYMENT_ID,
)
from billing.components.core.tests.IntegrationTests.utils import sample_module_result
from billing.tests.test_helpers import set_submission_calculation_type
from huma_plugins.components.extended_authorization.component import (
    ExtendedAuthorizationComponent,
)
from huma_plugins.components.online_offline_call.component import (
    OnlineOfflineCallComponent,
)
from huma_plugins.components.online_offline_call.router.video_requests import CreateOfflineCallRequestObject
from huma_plugins.components.video_call.dtos.video_call import VideoCallDTO, VideoCallLogDTO
from huma_plugins.components.video_call.models import VideoCall
from sdk.auth.component import AuthComponent
from sdk.authorization.models import User
from sdk.calendar.component import CalendarComponent
from sdk.common.utils.common_functions_utils import find
from sdk.common.utils.inject import autoparams
from sdk.common.utils.validators import utc_str_field_to_val, utc_str_to_date
from sdk.deployment.component import DeploymentComponent
from sdk.module_result.component import ModuleResultComponent
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.modules import WeightModule
from sdk.organization.component import OrganizationComponent
from sdk.versioning.component import VersionComponent

VALID_CLINICIAN_ID_2 = "651145013681ba0f8b9fb67f"
VALID_ADMINISTRATOR_ID = "64e76adedf047d493ba356c4"
USER_ID = "5e8f0c74b50aa9656c34789b"
USER_ID_WITH_ONE_ALERT = "63ce44193d6527f1c3ceafe4"
USER_FOR_TIME_PASSING_CHECK = "63ce44193d6527f1c3ceafe3"
USER_FOR_TEMP_DOCUMENT = "63ce44193d6527f1c3ceadf3"
VALID_CLINICIAN_WITH_RTM_DEPLOYMENT_ID = "5e8f0c74b50aa9656c347890"
USER_FOR_CHECKING_PRE_CALCULATION = "5e8f0c74b50ac9656c34789d"
VALID_LABEL_ID = "6525cc1a3c410036d6f587a7"
VALID_LABEL_ID_2 = "6525cc1a3c410036d6f587a8"
VIDEO_CALL_USE_CASE = "huma_plugins.components.video_call.use_case"


class BaseBillingAlertsTestCase(BaseBillingTestCase):
    components = [
        AuthComponent(),
        ExtendedAuthorizationComponent(),
        BillingComponent(),
        CalendarComponent(),
        DeploymentComponent(),
        OrganizationComponent(),
        OnlineOfflineCallComponent(),
        ModuleResultComponent([WeightModule()]),
        VersionComponent(server_version="1.0.0", api_version="1.0.0"),
    ]
    fixtures = [
        Path(__file__).parent.joinpath("fixtures/billing_dump.json"),
    ]
    override_config = {}

    @autoparams("repo")
    def setUp(self, repo: BillingAlertsRepository):
        super().setUp()
        self.repo = repo
        self.token = self.get_headers_for_token(VALID_CLINICIAN_ID)
        self.headers = self.token
        self.base_url = "/api/extensions/v1/billing"
        self.alerts_url = f"{self.base_url}/profiles"

    def test_retrieve_billing_alerts_fresh_user(self):
        """Test retrieving billing alerts for a fresh user without submissions and DoS"""
        BillingAlert.objects.filter(user_id=USER_FOR_TEMP_DOCUMENT).delete()
        BillingAlert(
            mongoId=ObjectId(),
            user_id=USER_FOR_TEMP_DOCUMENT,
            deploymentId=VALID_DEPLOYMENT_ID,
            submissionDates=[],
        ).save()

        body = {RetrieveAlertsRequestObject.SEARCH: USER_FOR_TEMP_DOCUMENT, RetrieveAlertsRequestObject.SKIP: 0}
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)

        self.assertEqual(1, rsp.json["filtered"], "Should find 1 matching alert")
        self.assertGreaterEqual(rsp.json["total"], 7, "Should have at least 7 total alerts")
        self.assertEqual(1, rsp.json["missingCall"], "User should be missing call")
        self.assertEqual(1, rsp.json["missingSubmission"], "User should be missing submissions")
        self.assertEqual(1, rsp.json["notSynced"], "User should be not synced")

        alert = rsp.json["users"][0]
        self.assertEqual(USER_FOR_TEMP_DOCUMENT, alert[BillingAlertsDTO.USER_ID])
        self.assertEqual(-1, alert[BillingAlertsDTO.SUBMISSION_COUNT], "Should have no submissions")
        self.assertEqual(False, alert[BillingAlertsDTO.HAS_CALL], "Should not have a call")
        self.assertEqual(0.0, alert[BillingAlertsDTO.MONITORING_MINUTES])
        self.assertEqual([], alert[BillingAlertsDTO.SUBMISSION_DATES])

    @freeze_time("2023-05-19")
    def test_success_retrieve_billing_alerts_without_filtering(self):
        body = {
            RetrieveAlertsRequestObject.SKIP: 0,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.FILTERED], 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_CALL], 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_SUBMISSION], 5)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.TOTAL], 7)

    @freeze_time("2023-05-19")
    def test_success_retrieve_billing_alerts_with_monitoring_codes(self):
        body = {
            RetrieveAlertsRequestObject.SKIP: 0,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.FILTERED], 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_CALL], 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_SUBMISSION], 5)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.TOTAL], 7)
        users = rsp.json[RetrieveAlertsResponseObject.USERS]
        users_without_call = [user for user in users if not user.get("hasCall")]
        monitoring_codes = [
            BillingAlertsDTO.CPT_99457_COMPLIANCE,
            BillingAlertsDTO.CPT_99458_COMPLIANCE,
            BillingAlertsDTO.CPT_99458X2_COMPLIANCE,
        ]
        for user in users:
            for code in monitoring_codes:
                self.assertIn(
                    code,
                    user.keys(),
                    msg=f"User {user.get('userId')} does not have {code} key",
                )

        for user in users_without_call:
            monitoring_minutes = user.get(BillingAlertsDTO.MONITORING_MINUTES)
            if monitoring_minutes < 20:
                red = BillingAlertsDTO.CPT_99457_COMPLIANCE
                self._check_compliance_color_is_red_if_started(user, monitoring_codes, red)
            elif monitoring_minutes < 40:
                red = BillingAlertsDTO.CPT_99458_COMPLIANCE
                self._check_compliance_color_is_red_if_started(user, monitoring_codes, red)
            elif monitoring_minutes < 60:
                red = BillingAlertsDTO.CPT_99458X2_COMPLIANCE
                self._check_compliance_color_is_red_if_started(user, monitoring_codes, red)

    @freeze_time("2023-05-24")  # 7 days before end of the month
    def test_success_retrieve_billing_alerts_with_monitoring_codes_normal_month_days_has_call(
        self,
    ):
        body = {
            RetrieveAlertsRequestObject.SKIP: 0,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.FILTERED], 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_CALL], 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_SUBMISSION], 5)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.TOTAL], 7)
        users = rsp.json[RetrieveAlertsResponseObject.USERS]
        users_with_call = [user for user in users if user.get("hasCall")]
        monitoring_codes = [
            BillingAlertsDTO.CPT_99457_COMPLIANCE,
            BillingAlertsDTO.CPT_99458_COMPLIANCE,
            BillingAlertsDTO.CPT_99458X2_COMPLIANCE,
        ]
        for user in users_with_call:
            for code in monitoring_codes:
                self.assertEqual(
                    0,
                    user.get(code).get(CPTCompliance.COLOR_CODE),
                    msg=f"Expected user {user.get('userId')} code {code} key color code to be 0",
                )

    @freeze_time("2023-06-01")
    def test_success_retrieve_billing_alerts_with_no_calls(
        self,
    ):
        body = {
            RetrieveAlertsRequestObject.SKIP: 0,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.FILTERED], 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_CALL], 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_SUBMISSION], 5)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.TOTAL], 7)
        users = rsp.json[RetrieveAlertsResponseObject.USERS]
        users_with_call = [user for user in users if user.get("hasCall")]
        self.assertEmpty(users_with_call)

    @freeze_time("2023-05-29")
    def test_success_retrieve_billing_alerts_with_monitoring_codes_last_3_days_has_call(
        self,
    ):
        body = {
            RetrieveAlertsRequestObject.SKIP: 0,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.FILTERED], 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_CALL], 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_SUBMISSION], 5)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.TOTAL], 7)
        users = rsp.json[RetrieveAlertsResponseObject.USERS]
        users_with_call = [user for user in users if user.get("hasCall")]
        monitoring_codes = [
            BillingAlertsDTO.CPT_99457_COMPLIANCE,
            BillingAlertsDTO.CPT_99458_COMPLIANCE,
            BillingAlertsDTO.CPT_99458X2_COMPLIANCE,
        ]
        for user in users_with_call:
            monitoring_minutes = user.get(BillingAlertsDTO.MONITORING_MINUTES)
            if monitoring_minutes < 20:
                red = BillingAlertsDTO.CPT_99457_COMPLIANCE
                self._check_compliance_color_is_the_only_red(user, monitoring_codes, red)
            elif monitoring_minutes < 40:
                red = BillingAlertsDTO.CPT_99458_COMPLIANCE
                self._check_compliance_color_is_the_only_red(user, monitoring_codes, red)
            elif monitoring_minutes < 60:
                red = BillingAlertsDTO.CPT_99458X2_COMPLIANCE
                self._check_compliance_color_is_the_only_red(user, monitoring_codes, red)

    def _check_compliance_color_is_red_if_started(self, user, codes_to_check, red_code):
        """
        Checks if all the started codes (including the red_code) are red and the rest are white
        """
        red, white = BillingAlertsColorCoding.RED, BillingAlertsColorCoding.WHITE
        color = red
        for code in codes_to_check:
            self.assertEqual(
                color,
                user.get(code).get(CPTCompliance.COLOR_CODE),
                msg=f"User {user.get('userId')} should have {code} key with color code {color}",
            )
            if code == red_code:
                color = white

    def _check_compliance_color_is_the_only_red(self, user, codes_to_check, red_code):
        """
        Checks if only the red_code is red and the rest are white
        """
        red, white = 3, 0
        for code in codes_to_check:
            color = red if code == red_code else white
            self.assertEqual(
                color,
                user.get(code).get(CPTCompliance.COLOR_CODE),
                msg=f"User {user.get('userId')} should have {code} key with color code {color}",
            )

    @freeze_time("2023-05-19")
    def test_success_retrieve_billing_alerts_with_limit(self):
        body = {
            RetrieveAlertsRequestObject.SKIP: 0,
            RetrieveAlertsRequestObject.LIMIT: 3,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.FILTERED], 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_CALL], 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_SUBMISSION], 5)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.TOTAL], 7)

    @freeze_time("2023-05-19")
    def test_success_retrieve_billing_alerts_with_search_user_id(self):
        body = {
            RetrieveAlertsRequestObject.SEARCH: USER_ID_WITH_ONE_ALERT,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 1)

    @freeze_time("2023-05-19")
    def test_success_retrieve_billing_alerts_and_verify_field_updates_over_time(self):
        current_datetime = datetime.now()
        with freeze_time(date(current_datetime.year, current_datetime.month, 31)):
            body = {
                RetrieveAlertsRequestObject.SEARCH: USER_FOR_TIME_PASSING_CHECK,
            }
            headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
            rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
            self.assertEqual(rsp.status_code, 200)
            self.assertEqual(len(rsp.json), 6)
            self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 1)
            self.assertEqual(
                rsp.json[RetrieveAlertsResponseObject.USERS][0][BillingAlertsDTO.HAS_CALL],
                True,
            )
            self.assertNotEqual(
                rsp.json[RetrieveAlertsResponseObject.USERS][0][BillingAlertsDTO.MONITORING_MINUTES],
                0.0,
            )
        with freeze_time(date(current_datetime.year, current_datetime.month + 1, 1)):
            body = {
                RetrieveAlertsRequestObject.SEARCH: USER_FOR_TIME_PASSING_CHECK,
            }
            headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
            rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
            self.assertEqual(rsp.status_code, 200)
            self.assertEqual(len(rsp.json), 6)
            self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 1)
            self.assertEqual(
                rsp.json[RetrieveAlertsResponseObject.USERS][0][BillingAlertsDTO.HAS_CALL],
                False,
            )
            self.assertEqual(
                rsp.json[RetrieveAlertsResponseObject.USERS][0][BillingAlertsDTO.MONITORING_MINUTES],
                0.0,
            )
            """
            callDatetime was on 2023-05-05 and lastMonitoringDate is on 2023-05-05,
            so in the next period which is sixth month,
            the user does not have callDatetime and MonitoringMinutes should be 0.0 .
            """

    @freeze_time("2023-05-19")
    def test_success_retrieve_billing_alerts_with_search_name(self):
        body = {
            RetrieveAlertsRequestObject.SEARCH: "SARAH",
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 3)

    @freeze_time("2023-05-19")
    def test_success_retrieve_billing_alerts_with_search_full_name(self):
        body = {
            RetrieveAlertsRequestObject.SEARCH: "SARAh johnSON",
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 3)

    @freeze_time("2023-05-19")
    def test_success_retrieve_billing_alerts_with_search_name_with_null_call_datetime(
        self,
    ):
        body = {
            RetrieveAlertsRequestObject.SEARCH: "Brat",
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 1)

    @freeze_time("2023-05-19")
    def test_success_retrieve_billing_alerts_with_incomplete_user_id(self):
        body = {
            RetrieveAlertsRequestObject.SEARCH: "63ce44193",
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 2)

    @freeze_time("2023-05-01")
    def test_success_retrieve_billing_alerts_with_filter_missed_call(self):
        body = {
            RetrieveAlertsRequestObject.FILTERS: {
                AlertFilterParameters.BILLING: [
                    AlertFilterFields.MISSED_CALL.value,
                ]
            },
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.FILTERED], 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.TOTAL], 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_CALL], 3)
        for user in rsp.json[RetrieveAlertsResponseObject.USERS]:
            self.assertEqual(user[BillingAlertsDTO.HAS_CALL], False)

    def test_success_retrieve_billing_alerts_with_filter_by_two_labels(self):
        body_2_labels = {
            RetrieveAlertsRequestObject.FILTERS: {AlertFilterParameters.LABELS: [VALID_LABEL_ID, VALID_LABEL_ID_2]},
        }
        self._retrieve_and_assert_billing_alerts(body_2_labels, 2, 2)

    def test_success_retrieve_billing_alerts_with_filter_by_one_label(self):
        body_1_label = {
            RetrieveAlertsRequestObject.FILTERS: {
                AlertFilterParameters.LABELS: [VALID_LABEL_ID],
                AlertFilterParameters.BILLING: [
                    AlertFilterFields.MISSED_CALL.value,
                ],
            },
        }
        self._retrieve_and_assert_billing_alerts(body_1_label, 1, 1)

    def test_retrieve_alerts_with_filter_by_not_synced_users_RPM(self):
        user_id = "63ce44193d6527f1c3ceafe3"
        # 1 user will be with today's last Sync date
        self._update_alerts_submission_dates(user_id, [datetime.utcnow()])
        body = {
            RetrieveAlertsRequestObject.FILTERS: {
                AlertFilterParameters.BILLING: ["NOT_SYNCED"],
            }
        }
        rsp = self._retrieve_and_assert_billing_alerts(body, 6, 6)
        self.assertEqual(7, rsp["total"])
        self.assertEqual(6, rsp["notSynced"])

    def test_retrieve_alerts_with_filter_by_not_synced_users_RTM(self):
        body = {
            RetrieveAlertsRequestObject.FILTERS: {
                AlertFilterParameters.BILLING: ["NOT_SYNCED"],
            }
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID_2)
        url = url_for("billing_route.retrieve_billing_alerts")
        rsp = self.flask_client.post(url, headers=headers, json=body)
        self.assertEqual(400, rsp.status_code)

    def test_failure_retrieve_billing_alerts_filters_with_invalid_labels(self):
        body = {
            RetrieveAlertsRequestObject.FILTERS: {
                AlertFilterParameters.LABELS: [
                    "some_label_id",
                ]
            },
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 403)
        self.assertEqual(rsp.json["code"], 100050)

    @freeze_time("2023-05-01")
    def test_success_retrieve_billing_alerts_with_filter_missed_call_sort_by_time_spent(
        self,
    ):
        body = {
            RetrieveAlertsRequestObject.SORT: {
                AlertSortParameters.FIELDS: [
                    {
                        AlertsSortFields.FIELD: AlertField.TIME_SPENT.value,
                        AlertsSortFields.CPT: AlertCPTField.CPT_99457.value,
                        AlertsSortFields.ORDER: AlertOrderField.DESCENDING.value,
                    },
                ]
            },
            RetrieveAlertsRequestObject.FILTERS: {
                AlertFilterParameters.BILLING: [
                    AlertFilterFields.MISSED_CALL.value,
                ]
            },
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.FILTERED], 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.TOTAL], 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_CALL], 3)
        for user in rsp.json[RetrieveAlertsResponseObject.USERS]:
            self.assertEqual(
                user[BillingAlertsDTO.HAS_CALL],
                False,
            )
        users = rsp.json[RetrieveAlertsResponseObject.USERS]
        monitoring_minutes = [user[BillingAlertsDTO.MONITORING_MINUTES] for user in users]

        for i in range(len(users) - 1):
            self.assertGreaterEqual(monitoring_minutes[i], monitoring_minutes[i + 1])

    @freeze_time("2023-05-19")
    def test_success_retrieve_billing_alerts_with_filter_missed_submission(self):
        body = {
            RetrieveAlertsRequestObject.FILTERS: {
                AlertFilterParameters.BILLING: [
                    AlertFilterFields.MISSED_SUBMISSION.value,
                ]
            },
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 5)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_SUBMISSION], 5)
        self.assertEqual(
            rsp.json[RetrieveAlertsResponseObject.USERS][0][BillingAlertsDTO.SUBMISSION_COUNT],
            0,
        )
        self.assertEqual(
            rsp.json[RetrieveAlertsResponseObject.USERS][1][BillingAlertsDTO.SUBMISSION_COUNT],
            0,
        )

    @freeze_time("2023-05-19")
    def test_success_retrieve_billing_alerts_with_time_spent_99457_descending_sort_rpm(
        self,
    ):
        body = {
            RetrieveAlertsRequestObject.SORT: {
                AlertSortParameters.FIELDS: [
                    {
                        AlertsSortFields.FIELD: AlertField.TIME_SPENT.value,
                        AlertsSortFields.CPT: AlertCPTField.CPT_99457.value,
                        AlertsSortFields.ORDER: AlertOrderField.DESCENDING.value,
                    },
                ]
            },
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        users = rsp.json[RetrieveAlertsResponseObject.USERS]
        monitoring_minutes = [user[BillingAlertsDTO.MONITORING_MINUTES] for user in users]

        for i in range(len(users) - 1):
            self.assertGreaterEqual(monitoring_minutes[i], monitoring_minutes[i + 1])

    @freeze_time("2023-05-19")
    def test_success_retrieve_billing_alerts_with_time_spent_99458_ascending_sort_rpm(
        self,
    ):
        body = {
            RetrieveAlertsRequestObject.SORT: {
                AlertSortParameters.FIELDS: [
                    {
                        AlertsSortFields.FIELD: AlertField.TIME_SPENT.value,
                        AlertsSortFields.CPT: AlertCPTField.CPT_99458.value,
                        AlertsSortFields.ORDER: AlertOrderField.ASCENDING.value,
                    },
                ]
            },
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        users = rsp.json[RetrieveAlertsResponseObject.USERS]
        monitoring_minutes = [user[BillingAlertsDTO.MONITORING_MINUTES] for user in users]

        for i in range(len(users) - 1):
            self.assertLessEqual(monitoring_minutes[i], monitoring_minutes[i + 1])

    @freeze_time("2023-05-19")
    def test_success_retrieve_billing_alerts_with_time_spent_98980_ascending_sort_rtm(
        self,
    ):
        body = {
            RetrieveAlertsRequestObject.SORT: {
                AlertSortParameters.FIELDS: [
                    {
                        AlertsSortFields.FIELD: AlertField.TIME_SPENT.value,
                        AlertsSortFields.CPT: AlertCPTField.CPT_98980.value,
                        AlertsSortFields.ORDER: AlertOrderField.ASCENDING.value,
                    },
                ]
            },
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_WITH_RTM_DEPLOYMENT_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.FILTERED], 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.TOTAL], 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_CALL], 2)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_SUBMISSION], 3)
        users = rsp.json[RetrieveAlertsResponseObject.USERS]
        monitoring_minutes = [user[BillingAlertsDTO.MONITORING_MINUTES] for user in users]

        for i in range(len(users) - 1):
            self.assertLessEqual(monitoring_minutes[i], monitoring_minutes[i + 1])

    @freeze_time("2023-05-19")
    def test_success_retrieve_billing_alerts_with_time_spent_98981_descending_sort_rtm(
        self,
    ):
        body = {
            RetrieveAlertsRequestObject.SORT: {
                AlertSortParameters.FIELDS: [
                    {
                        AlertsSortFields.FIELD: AlertField.TIME_SPENT.value,
                        AlertsSortFields.CPT: AlertCPTField.CPT_98981.value,
                        AlertsSortFields.ORDER: AlertOrderField.DESCENDING.value,
                    },
                ]
            },
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_WITH_RTM_DEPLOYMENT_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.FILTERED], 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.TOTAL], 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_CALL], 2)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_SUBMISSION], 3)
        users = rsp.json[RetrieveAlertsResponseObject.USERS]
        monitoring_minutes = [user[BillingAlertsDTO.MONITORING_MINUTES] for user in users]

        for i in range(len(users) - 1):
            self.assertGreaterEqual(monitoring_minutes[i], monitoring_minutes[i + 1])

    @freeze_time("2023-05-19")
    def test_success_retrieve_billing_alerts_color_coding_red_without_call_99457(self):
        body = {
            RetrieveAlertsRequestObject.SKIP: 0,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.FILTERED], 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_CALL], 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_SUBMISSION], 5)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.TOTAL], 7)
        for user in rsp.json[RetrieveAlertsResponseObject.USERS]:
            if not user[BillingAlertsDTO.HAS_CALL]:
                self.assertEqual(
                    user[BillingAlertsDTO.CPT_99457_COMPLIANCE][CPTCompliance.COLOR_CODE],
                    BillingAlertsColorCoding.RED,
                )

    @freeze_time("2023-05-19")
    def test_success_retrieve_billing_alerts_color_coding_red_without_call_98980(self):
        body = {
            RetrieveAlertsRequestObject.SKIP: 0,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_WITH_RTM_DEPLOYMENT_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 3)
        for user in rsp.json[RetrieveAlertsResponseObject.USERS]:
            if not user[BillingAlertsDTO.HAS_CALL]:
                self.assertEqual(
                    user[BillingAlertsDTO.CPT_98980_COMPLIANCE][CPTCompliance.COLOR_CODE],
                    BillingAlertsColorCoding.RED,
                )

    @freeze_time("2023-05-01")
    def test_success_retrieve_billing_alerts_color_coding_red_without_call_99458(self):
        body = {
            RetrieveAlertsRequestObject.SKIP: 0,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.FILTERED], 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_CALL], 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_SUBMISSION], 5)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.TOTAL], 7)
        for user in rsp.json[RetrieveAlertsResponseObject.USERS]:
            if not user[BillingAlertsDTO.HAS_CALL] and user[BillingAlertsDTO.MONITORING_MINUTES] >= 20.0:
                self.assertEqual(
                    user[BillingAlertsDTO.CPT_99458_COMPLIANCE][CPTCompliance.COLOR_CODE],
                    BillingAlertsColorCoding.RED,
                )
            else:
                self.assertEqual(
                    user[BillingAlertsDTO.CPT_99458_COMPLIANCE][CPTCompliance.COLOR_CODE],
                    BillingAlertsColorCoding.WHITE,
                )

    @freeze_time("2023-05-01")
    def test_success_retrieve_billing_alerts_color_coding_red_without_call_98981(self):
        body = {
            RetrieveAlertsRequestObject.SKIP: 0,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_WITH_RTM_DEPLOYMENT_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 3)
        for user in rsp.json[RetrieveAlertsResponseObject.USERS]:
            if not user[BillingAlertsDTO.HAS_CALL] and user[BillingAlertsDTO.MONITORING_MINUTES] >= 20.0:
                self.assertEqual(
                    user[BillingAlertsDTO.CPT_98981_COMPLIANCE][CPTCompliance.COLOR_CODE],
                    BillingAlertsColorCoding.RED,
                )
            else:
                self.assertEqual(
                    user[BillingAlertsDTO.CPT_98981_COMPLIANCE][CPTCompliance.COLOR_CODE],
                    BillingAlertsColorCoding.WHITE,
                )

    @freeze_time("2023-05-29")
    def test_success_retrieve_billing_alerts_color_coding_red_without_call_99458_3_days_left(
        self,
    ):
        body = {
            RetrieveAlertsRequestObject.SKIP: 0,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.FILTERED], 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_CALL], 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_SUBMISSION], 5)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.TOTAL], 7)
        for user in rsp.json[RetrieveAlertsResponseObject.USERS]:
            if 20.0 <= user[BillingAlertsDTO.MONITORING_MINUTES] < 40.0:
                self.assertEqual(
                    user[BillingAlertsDTO.CPT_99458_COMPLIANCE][CPTCompliance.COLOR_CODE],
                    BillingAlertsColorCoding.RED,
                )

    @freeze_time("2023-05-29")
    def test_success_retrieve_billing_alerts_color_coding_red_without_call_98981_3_days_left(
        self,
    ):
        body = {
            RetrieveAlertsRequestObject.SKIP: 0,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_WITH_RTM_DEPLOYMENT_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.TOTAL], 3)
        for user in rsp.json[RetrieveAlertsResponseObject.USERS]:
            if 20.0 <= user[BillingAlertsDTO.MONITORING_MINUTES] < 40.0:
                self.assertEqual(
                    user[BillingAlertsDTO.CPT_98981_COMPLIANCE][CPTCompliance.COLOR_CODE],
                    BillingAlertsColorCoding.RED,
                )

    @freeze_time("2023-05-19")
    def test_success_retrieve_billing_alerts_color_coding_white_99457(self):
        body = {
            RetrieveAlertsRequestObject.SKIP: 0,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.FILTERED], 7)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_CALL], 3)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.MISSING_SUBMISSION], 5)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.TOTAL], 7)
        for user in rsp.json[RetrieveAlertsResponseObject.USERS]:
            if user[BillingAlertsDTO.HAS_CALL]:
                self.assertEqual(
                    user[BillingAlertsDTO.CPT_99457_COMPLIANCE][CPTCompliance.COLOR_CODE],
                    BillingAlertsColorCoding.WHITE,
                )

    @freeze_time("2023-04-20")
    def test_success_retrieve_billing_alerts(self):
        body = {
            RetrieveAlertsRequestObject.SKIP: 0,
            RetrieveAlertsRequestObject.LIMIT: 10,
            RetrieveAlertsRequestObject.SORT: {
                AlertSortParameters.FIELDS: [
                    {
                        AlertsSortFields.FIELD: AlertField.COMPLIANCE.value,
                        AlertsSortFields.CPT: AlertCPTField.CPT_99454.value,
                        AlertsSortFields.ORDER: AlertOrderField.DESCENDING.value,
                    }
                ]
            },
            RetrieveAlertsRequestObject.FILTERS: {
                AlertFilterParameters.BILLING: [
                    AlertFilterFields.MISSED_CALL.value,
                ]
            },
            RetrieveAlertsRequestObject.SEARCH: "Sarah",
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(2, len(rsp.json[RetrieveAlertsResponseObject.USERS]))

    def test_failure_retrieve_billing_alerts_filters_with_invalid_enums(self):
        invalid_enum = "invalid_enum"
        body = {
            RetrieveAlertsRequestObject.FILTERS: {
                AlertFilterParameters.BILLING: [
                    invalid_enum,
                ]
            },
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 403)

    def test_failure_retrieve_billing_alerts_with_not_unique_field_and_sort_field(self):
        body = {
            RetrieveAlertsRequestObject.SORT: {
                AlertSortParameters.FIELDS: [
                    {
                        AlertsSortFields.FIELD: AlertField.COMPLIANCE.value,
                        AlertsSortFields.CPT: AlertCPTField.CPT_99457.value,
                        AlertsSortFields.ORDER: AlertOrderField.DESCENDING.value,
                    },
                    {
                        AlertsSortFields.FIELD: AlertField.COMPLIANCE.value,
                        AlertsSortFields.CPT: AlertCPTField.CPT_99457.value,
                        AlertsSortFields.ORDER: AlertOrderField.ASCENDING.value,
                    },
                ]
            }
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 400)
        self.assertEqual(
            rsp.json["message"],
            "Pairs of sort field and CPT code should not be repeated in the request",
        )

    def test_failure_when_sorting_based_on_submission_cpt_codes_and_time_spent_sort_field(
        self,
    ):
        body = {
            RetrieveAlertsRequestObject.SORT: {
                AlertSortParameters.FIELDS: [
                    {
                        AlertsSortFields.FIELD: AlertField.TIME_SPENT.value,
                        AlertsSortFields.CPT: AlertCPTField.CPT_99454.value,
                        AlertsSortFields.ORDER: AlertOrderField.DESCENDING.value,
                    },
                ]
            }
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 400)
        self.assertEqual(
            rsp.json["message"],
            f"Sorting cpt {AlertField.TIME_SPENT.value} with TIME_SPENT field is not allowed",
        )

    def test_failure_when_sorting_based_on_time_tracking_cpt_codes_and_compliance_sort_field(
        self,
    ):
        body = {
            RetrieveAlertsRequestObject.SORT: {
                AlertSortParameters.FIELDS: [
                    {
                        AlertsSortFields.FIELD: AlertField.COMPLIANCE.value,
                        AlertsSortFields.CPT: AlertCPTField.CPT_99457.value,
                        AlertsSortFields.ORDER: AlertOrderField.DESCENDING.value,
                    },
                ]
            }
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 400)
        self.assertEqual(
            rsp.json["message"],
            f"Sorting based on {AlertField.COMPLIANCE.value} and COMPLIANCE/DEADLINE sort field is not allowed",
        )

    def test_retrieve_alerts_sorted_by_last_synced_date(self):
        expected_user_id_1 = "63ce44193d6527f1c3ceafe3"
        expected_user_id_2 = "5f0496ab82a63219725332d5"
        body = {
            RetrieveAlertsRequestObject.SORT: {
                AlertSortParameters.FIELDS: [
                    {
                        AlertsSortFields.FIELD: AlertField.LAST_SYNCED.value,
                        AlertsSortFields.ORDER: AlertOrderField.DESCENDING.value,
                    },
                ]
            }
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(expected_user_id_1, rsp.json["users"][0]["userId"])
        self.assertEqual(expected_user_id_2, rsp.json["users"][1]["userId"])

    def test_failure_retrieve_billing_alerts_with_rtm_cpt_codes_from_rpm_deployment(
        self,
    ):
        body = {
            RetrieveAlertsRequestObject.SORT: {
                AlertSortParameters.FIELDS: [
                    {
                        AlertsSortFields.FIELD: AlertField.COMPLIANCE.value,
                        AlertsSortFields.CPT: AlertCPTField.CPT_98976.value,
                        AlertsSortFields.ORDER: AlertOrderField.DESCENDING.value,
                    },
                    {
                        AlertsSortFields.FIELD: AlertField.COMPLIANCE.value,
                        AlertsSortFields.CPT: AlertCPTField.CPT_99454.value,
                        AlertsSortFields.ORDER: AlertOrderField.DESCENDING.value,
                    },
                ]
            }
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 400)
        self.assertEqual(
            rsp.json["message"],
            "filter RTM cpt codes from RPM Deployment is not allowed",
        )

    def test_failure_retrieve_billing_alerts_with_rpm_cpt_codes_from_rtm_deployment(
        self,
    ):
        body = {
            RetrieveAlertsRequestObject.SORT: {
                AlertSortParameters.FIELDS: [
                    {
                        AlertsSortFields.FIELD: AlertField.COMPLIANCE.value,
                        AlertsSortFields.CPT: AlertCPTField.CPT_99454.value,
                        AlertsSortFields.ORDER: AlertOrderField.DESCENDING.value,
                    },
                ]
            }
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_WITH_RTM_DEPLOYMENT_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 400)
        self.assertEqual(
            rsp.json["message"],
            "filter RPM cpt codes from RTM Deployment is not allowed",
        )

    def test_failure_retrieve_billing_alerts_with_missing_items_in_sort_fields(
        self,
    ):
        body = {
            RetrieveAlertsRequestObject.SORT: {
                AlertSortParameters.FIELDS: [
                    {
                        AlertsSortFields.CPT: AlertCPTField.CPT_99454.value,
                        AlertsSortFields.ORDER: AlertOrderField.DESCENDING.value,
                    }
                ]
            }
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 403)

    def test_success_correct_monitoring_minutes_and_last_monitoring_date_after_insert_time_tracking(
        self,
    ):
        self._onboard_user(USER_FOR_CHECKING_PRE_CALCULATION)
        current_date = datetime.now().date()
        body = {
            CreateBillingRequestObject.START_DATE_TIME: f"{current_date}T00:00:22.039Z",
            CreateBillingRequestObject.END_DATE_TIME: f"{current_date}T00:00:52.039Z",
        }
        self.flask_client.post(
            f"{self.base_url}/user/{USER_FOR_CHECKING_PRE_CALCULATION}",
            json=body,
            headers=self.headers,
        )
        doc = BillingAlert.objects.filter(user_id=USER_FOR_CHECKING_PRE_CALCULATION).first()
        self.assertEqual(
            doc.monitoringMinutes,
            0.5,
        )
        self.assertEqual(utc_str_to_date(doc.lastMonitoringDate), utc_str_to_date(current_date))

    @cached_property
    def _today_beginning(self):
        return datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)

    def test_simple_alerts_submission_data(self):
        dos = self._today_beginning + timedelta(days=5)
        period_first_submission = self._today_beginning.date()
        submission_dates = [
            str(period_first_submission),
            str(latest_submission := (period_first_submission + timedelta(days=1))),
        ]
        self._add_billing_alerts_with_submission_data(dos, submission_dates)

        body = {
            RetrieveAlertsRequestObject.SEARCH: USER_FOR_TEMP_DOCUMENT,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        user_data = rsp.json["users"][0]
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_COUNT], 2)
        self.assertEqual(user_data[BillingAlertsDTO.NEXT_SUBMISSION_DOS], str(dos.date()))
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_DAYS_LEFT], 6)
        self.assertEqual(len(user_data[BillingAlertsDTO.SUBMISSION_DATES]), 2)
        expected_latest_synced = utc_str_field_to_val(latest_submission)
        self.assertEqual(expected_latest_synced, user_data[BillingAlertsDTO.LAST_SYNCED_DATE])

    def test_simple_alerts_submission_data_WHEN_next_dos_is_in_past(self):
        dos = self._today_beginning - timedelta(days=5)
        period_first_submission = self._today_beginning.date() - timedelta(days=10)

        submission_dates = [str(period_first_submission)]

        self._add_billing_alerts_with_submission_data(dos, submission_dates)

        body = {
            RetrieveAlertsRequestObject.SEARCH: USER_FOR_TEMP_DOCUMENT,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        user_data = rsp.json["users"][0]
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_COUNT], 0)
        self.assertEqual(
            user_data[BillingAlertsDTO.NEXT_SUBMISSION_DOS],
            str(self._today_beginning.date() + timedelta(days=25)),
        )
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_DAYS_LEFT], 26)
        self.assertEqual(len(user_data[BillingAlertsDTO.SUBMISSION_DATES]), 0)

    def test_simple_alerts_submission_data_WHEN_no_data_exist_in_history(self):
        self._add_billing_alerts_with_submission_data(None, None)

        body = {
            RetrieveAlertsRequestObject.SEARCH: USER_FOR_TEMP_DOCUMENT,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        user_data = rsp.json["users"][0]
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_COUNT], -1)
        self.assertEqual(
            user_data[BillingAlertsDTO.NEXT_SUBMISSION_DOS],
            str(self._today_beginning.date()),
        )
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_DAYS_LEFT], -1)
        self.assertEqual(len(user_data[BillingAlertsDTO.SUBMISSION_DATES]), 0)

    def test_simple_alerts_submission_data_WHEN_dos_is_today(self):
        dos = self._today_beginning
        period_first_submission = self._today_beginning.date() - timedelta(days=1)

        submission_dates = [
            str(period_first_submission),
        ]

        self._add_billing_alerts_with_submission_data(dos, submission_dates)

        body = {
            RetrieveAlertsRequestObject.SEARCH: USER_FOR_TEMP_DOCUMENT,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        user_data = rsp.json["users"][0]
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_COUNT], 1)
        self.assertEqual(
            user_data[BillingAlertsDTO.NEXT_SUBMISSION_DOS],
            str(self._today_beginning.date()),
        )
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_DAYS_LEFT], 1)
        self.assertEqual(len(user_data[BillingAlertsDTO.SUBMISSION_DATES]), 1)

    def test_simple_alerts_submission_data_compliance_WHEN_16_submission_exist(self):
        dos = self._today_beginning + timedelta(days=5)
        period_first_submission = self._today_beginning.date() - timedelta(days=15)

        submission_dates = [str(period_first_submission + timedelta(i)) for i in range(16)]

        self._add_billing_alerts_with_submission_data(dos, submission_dates)

        body = {
            RetrieveAlertsRequestObject.SEARCH: USER_FOR_TEMP_DOCUMENT,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        user_data = rsp.json["users"][0]
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_COUNT], 16)
        self.assertEqual(user_data[BillingAlertsDTO.NEXT_SUBMISSION_DOS], str(dos.date()))
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_DAYS_LEFT], 6)
        self.assertEqual(len(user_data[BillingAlertsDTO.SUBMISSION_DATES]), 16)
        self.assertEqual(
            user_data[BillingAlertsDTO.CPT_99454_COMPLIANCE],
            {
                CPTCompliance.COLOR_CODE: BillingAlertsColorCoding.WHITE,
                CPTCompliance.VALUE: 1000.0,
            },
        )

    def test_simple_alerts_submission_data_compliance_WHEN_16_submission_NOT_met_and_3_days_remaining(
        self,
    ):
        dos = self._today_beginning + timedelta(days=2)
        period_first_submission = self._today_beginning.date() - timedelta(days=15)

        submission_dates = [str(period_first_submission + timedelta(i)) for i in range(15)]

        self._add_billing_alerts_with_submission_data(dos, submission_dates)

        body = {
            RetrieveAlertsRequestObject.SEARCH: USER_FOR_TEMP_DOCUMENT,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        user_data = rsp.json["users"][0]
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_COUNT], 15)
        self.assertEqual(user_data[BillingAlertsDTO.NEXT_SUBMISSION_DOS], str(dos.date()))
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_DAYS_LEFT], 3)
        self.assertEqual(len(user_data[BillingAlertsDTO.SUBMISSION_DATES]), 15)
        self.assertEqual(
            {
                CPTCompliance.COLOR_CODE: BillingAlertsColorCoding.AMBER,
                CPTCompliance.VALUE: 3.0,
            },
            user_data[BillingAlertsDTO.CPT_99454_COMPLIANCE],
        )

    def test_simple_alerts_submission_data_compliance_WHEN_threshold_1_NOT_met_and_5_days_remaining(
        self,
    ):
        dos = self._today_beginning + timedelta(days=4)
        period_first_submission = self._today_beginning.date() - timedelta(days=15)

        submission_dates = [str(period_first_submission + timedelta(i)) for i in range(2)] + [
            str(self._today_beginning)
        ]

        self._add_billing_alerts_with_submission_data(dos, submission_dates)

        body = {
            RetrieveAlertsRequestObject.SEARCH: USER_FOR_TEMP_DOCUMENT,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        user_data = rsp.json["users"][0]
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_COUNT], 3)
        self.assertEqual(user_data[BillingAlertsDTO.NEXT_SUBMISSION_DOS], str(dos.date()))
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_DAYS_LEFT], 5)
        self.assertEqual(len(user_data[BillingAlertsDTO.SUBMISSION_DATES]), 3)
        self.assertEqual(
            user_data[BillingAlertsDTO.CPT_99454_COMPLIANCE],
            {
                CPTCompliance.COLOR_CODE: BillingAlertsColorCoding.GREY,
                CPTCompliance.VALUE: 0.385,
            },
        )

    def test_simple_alerts_submission_data_compliance_WHEN_threshold_1_875_NOT_met_and_has_submission_today(
        self,
    ):
        dos = self._today_beginning + timedelta(days=4)
        period_first_submission = self._today_beginning.date() - timedelta(days=15)

        submission_dates = [str(period_first_submission + timedelta(i)) for i in range(12)] + [
            str(self._today_beginning)
        ]

        self._add_billing_alerts_with_submission_data(dos, submission_dates)

        body = {
            RetrieveAlertsRequestObject.SEARCH: USER_FOR_TEMP_DOCUMENT,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        user_data = rsp.json["users"][0]
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_COUNT], 13)
        self.assertEqual(user_data[BillingAlertsDTO.NEXT_SUBMISSION_DOS], str(dos.date()))
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_DAYS_LEFT], 5)
        self.assertEqual(len(user_data[BillingAlertsDTO.SUBMISSION_DATES]), 13)
        self.assertEqual(
            user_data[BillingAlertsDTO.CPT_99454_COMPLIANCE],
            {
                CPTCompliance.COLOR_CODE: BillingAlertsColorCoding.LIGHT_RED,
                CPTCompliance.VALUE: 1.667,
            },
        )

    def test_simple_alerts_submission_data_compliance_WHEN_threshold_1_875_NOT_met_and_has_NOT_submission_today(
        self,
    ):
        dos = self._today_beginning + timedelta(days=4)
        period_first_submission = self._today_beginning.date() - timedelta(days=15)

        submission_dates = [str(period_first_submission + timedelta(i)) for i in range(13)]

        self._add_billing_alerts_with_submission_data(dos, submission_dates)

        body = {
            RetrieveAlertsRequestObject.SEARCH: USER_FOR_TEMP_DOCUMENT,
        }
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.post(self.alerts_url, headers=headers, json=body)
        user_data = rsp.json["users"][0]
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_COUNT], 13)
        self.assertEqual(user_data[BillingAlertsDTO.NEXT_SUBMISSION_DOS], str(dos.date()))
        self.assertEqual(user_data[BillingAlertsDTO.SUBMISSION_DAYS_LEFT], 5)
        self.assertEqual(len(user_data[BillingAlertsDTO.SUBMISSION_DATES]), 13)
        self.assertEqual(
            {
                CPTCompliance.COLOR_CODE: BillingAlertsColorCoding.RED,
                CPTCompliance.VALUE: 1.667,
            },
            user_data[BillingAlertsDTO.CPT_99454_COMPLIANCE],
        )

    def test_success_retrieve_billing_alerts_unidentified_view(self):
        body = {RetrieveAlertsRequestObject.LIMIT: 3}
        rsp = self._retrieve_and_assert_billing_alerts(body, 3, 7, VALID_ADMINISTRATOR_ID)
        users = rsp[RetrieveAlertsResponseObject.USERS]
        for user in users:
            self.assertIn(BillingAlertsDTO.GENDER, user)
            self.assertNotIn(BillingAlertsDTO.GIVEN_NAME, user)
            self.assertNotIn(BillingAlertsDTO.FAMILY_NAME, user)

    @freeze_time("2023-05-16")
    def test_retrieve_billing_alert__calendar_calculated_period__WHEN_16_submission_ARE_met(
        self,
    ):
        self._set_calendar_calculation_type()

        today = datetime.now(timezone.utc).date()
        days_this_month = calendar.monthrange(today.year, today.month)[1]
        next_dos = datetime(today.year, today.month, days_this_month)
        submission_dates = [str(today + timedelta(days=day)) for day in range(16)]
        self._add_billing_alerts_with_submission_data(next_dos, submission_dates)

        body = {BillingAlertsDTO.DEPLOYMENT_ID: VALID_DEPLOYMENT_ID}
        alerts = self._retrieve_billing_alerts(body)

        user_alert = find(
            lambda u: u[BillingAlertsDTO.USER_ID] == USER_FOR_TEMP_DOCUMENT,
            alerts[RetrieveAlertsResponseObject.USERS],
        )
        self.assertEqual(user_alert[BillingAlertsDTO.SUBMISSION_COUNT], 16)
        self.assertEqual(1000, user_alert[BillingAlertsDTO.CPT_99454_COMPLIANCE]["value"])
        self.assertEqual(16, user_alert[BillingAlertsDTO.SUBMISSION_DAYS_LEFT])  # days to end of month incl. today
        self.assertEqual(next_dos.strftime("%Y-%m-%d"), user_alert[BillingAlertsDTO.NEXT_SUBMISSION_DOS])

    @freeze_time("2023-05-25")
    def test_retrieve_billing_alert__calendar_calculated_period__WHEN_16_submission_NOT_met(
        self,
    ):
        self._set_calendar_calculation_type()

        today = datetime.now(timezone.utc).date()
        last_dos = datetime(today.year, today.month, 1) - relativedelta(days=1)
        self._add_billing_alerts_with_submission_data(last_dos, [])

        body = {BillingAlertsDTO.DEPLOYMENT_ID: VALID_DEPLOYMENT_ID}
        alerts = self._retrieve_billing_alerts(body)
        user_alert = find(
            lambda u: u[BillingAlertsDTO.USER_ID] == USER_FOR_TEMP_DOCUMENT,
            alerts[RetrieveAlertsResponseObject.USERS],
        )
        self.assertEqual(user_alert[BillingAlertsDTO.SUBMISSION_COUNT], 0)
        self.assertNotEqual(1000, user_alert[BillingAlertsDTO.CPT_99454_COMPLIANCE]["value"])
        self.assertEqual(7, user_alert[BillingAlertsDTO.SUBMISSION_DAYS_LEFT])  # days to end of month incl. today

    def test_billing_alert_updated_when_video_call_ended_by_manager(self):
        body = {BillingAlertsDTO.DEPLOYMENT_ID: VALID_DEPLOYMENT_ID}
        alerts = self._retrieve_billing_alerts(body)
        user_alert = find(
            lambda u: u[BillingAlertsDTO.USER_ID] == USER_ID,
            alerts[RetrieveAlertsResponseObject.USERS],
        )
        self.assertFalse(user_alert[BillingAlertsDTO.HAS_CALL])
        self.assertEqual(7, alerts[RetrieveAlertsResponseObject.MISSING_CALL])

        video_call = self._add_video_call()
        self._complete_video_call_by_manager(video_call)

        alerts = self._retrieve_billing_alerts(body)
        user_alert = find(
            lambda u: u[BillingAlertsDTO.USER_ID] == USER_ID,
            alerts[RetrieveAlertsResponseObject.USERS],
        )
        self.assertTrue(user_alert[BillingAlertsDTO.HAS_CALL])
        self.assertEqual(6, alerts[RetrieveAlertsResponseObject.MISSING_CALL])

    def test_billing_alert_updated_when_video_call_ended_by_user(self):
        body = {BillingAlertsDTO.DEPLOYMENT_ID: VALID_DEPLOYMENT_ID}
        alerts = self._retrieve_billing_alerts(body)
        self.assertEqual(7, alerts[RetrieveAlertsResponseObject.MISSING_CALL])

        video_call = self._add_video_call()
        self._complete_video_call_by_user(video_call)

        alerts = self._retrieve_billing_alerts(body)
        users = alerts[RetrieveAlertsResponseObject.USERS]
        user_alert = find(lambda u: u[BillingAlertsDTO.USER_ID] == USER_ID, users)
        self.assertTrue(user_alert[BillingAlertsDTO.HAS_CALL])
        self.assertEqual(6, alerts[RetrieveAlertsResponseObject.MISSING_CALL])

    def test_billing_alert_updated_when_offline_video_call_created(self):
        body = {BillingAlertsDTO.DEPLOYMENT_ID: VALID_DEPLOYMENT_ID}
        alerts = self._retrieve_billing_alerts(body)
        self.assertEqual(7, alerts[RetrieveAlertsResponseObject.MISSING_CALL])

        self._create_offline_video_call()

        alerts = self._retrieve_billing_alerts(body)
        users = alerts[RetrieveAlertsResponseObject.USERS]
        user_alert = find(lambda u: u[BillingAlertsDTO.USER_ID] == USER_ID, users)
        self.assertTrue(user_alert[BillingAlertsDTO.HAS_CALL])
        self.assertEqual(6, alerts[RetrieveAlertsResponseObject.MISSING_CALL])

    @freeze_time("2025-03-05T10:00:00.000Z")
    def test_billing_alert_has_multiple_submission_dates(self):
        now = datetime.now(UTC)
        alert_body = {RetrieveAlertsRequestObject.SEARCH: USER_ID}
        alerts = self._retrieve_billing_alerts(alert_body)
        user_alert = alerts[RetrieveAlertsResponseObject.USERS][0]
        self.assertEmpty(user_alert[BillingAlertsDTO.SUBMISSION_DATES])

        body = sample_module_result()
        for d in range(1, 4):
            body[PrimitiveDTO.START_DATE_TIME] = utc_str_field_to_val(now.replace(day=d))
            self._submit_module_result("Weight", body)

        alerts = self._retrieve_billing_alerts(alert_body)
        user_alert = alerts[RetrieveAlertsResponseObject.USERS][0]
        self.assertEqual(3, len(user_alert[BillingAlertsDTO.SUBMISSION_DATES]))
        self.assertEqual(3, user_alert[BillingAlertsDTO.SUBMISSION_COUNT])
        self.assertEqual("2025-03-30", user_alert[BillingAlertsDTO.NEXT_SUBMISSION_DOS])
        self.assertEqual(utc_str_field_to_val(now), user_alert[BillingAlertsDTO.LAST_SUBMIT_DATETIME])

    @freeze_time("2025-03-05T10:00:00.000Z")
    def test_billing_alert_submission_dates_has_no_duplicates(self):
        now = datetime.now(UTC)
        alert_body = {RetrieveAlertsRequestObject.SEARCH: USER_ID}
        alerts = self._retrieve_billing_alerts(alert_body)
        user_alert = alerts[RetrieveAlertsResponseObject.USERS][0]
        self.assertEmpty(user_alert[BillingAlertsDTO.SUBMISSION_DATES])

        body = sample_module_result()
        body[PrimitiveDTO.START_DATE_TIME] = utc_str_field_to_val(now - timedelta(days=1))
        self._submit_module_result("Weight", body)

        body[PrimitiveDTO.START_DATE_TIME] = utc_str_field_to_val(now - timedelta(hours=5))
        self._submit_module_result("Weight", body)

        body[PrimitiveDTO.START_DATE_TIME] = utc_str_field_to_val(now - timedelta(hours=3))
        self._submit_module_result("Weight", body)

        alerts = self._retrieve_billing_alerts(alert_body)
        user_alert = alerts[RetrieveAlertsResponseObject.USERS][0]
        self.assertEqual(2, len(user_alert[BillingAlertsDTO.SUBMISSION_DATES]))
        self.assertEqual(2, user_alert[BillingAlertsDTO.SUBMISSION_COUNT])

    @staticmethod
    def _add_billing_alerts_with_submission_data(next_dos: Optional[datetime], submission_dates: Optional[list]):
        document = BillingAlert(
            mongoId=ObjectId(),
            submissionDates=submission_dates,
            nextSubmissionDoS=next_dos,
            user_id=USER_FOR_TEMP_DOCUMENT,
            deploymentId=VALID_DEPLOYMENT_ID,
        )
        document.save()
        return str(document.mongoId)

    @staticmethod
    def _onboard_user(user_id):
        user = User.objects.get(mongoId=user_id)
        user.finishedOnboarding = True
        user.boardingStatus = {
            "status": 0,
            "updateDateTime": "2021-02-09T14:08:05.997Z",
        }
        user.save()

    def _retrieve_and_assert_billing_alerts(self, body, user_count, filtered, clinician=VALID_CLINICIAN_ID):
        headers = self.get_headers_for_token(clinician)
        url = url_for("billing_route.retrieve_billing_alerts")
        rsp = self.flask_client.post(url, headers=headers, json=body)
        self.assertEqual(rsp.status_code, 200)
        self.assertEqual(len(rsp.json), 6)
        self.assertEqual(len(rsp.json[RetrieveAlertsResponseObject.USERS]), user_count)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.FILTERED], filtered)
        self.assertEqual(rsp.json[RetrieveAlertsResponseObject.TOTAL], 7)
        return rsp.json

    def _retrieve_billing_alerts(self, body):
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        url = url_for("billing_route.retrieve_billing_alerts")
        rsp = self.flask_client.post(url, json=body, headers=headers)
        self.assertEqual(rsp.status_code, 200)
        return rsp.json

    def _set_calendar_calculation_type(self, use_calendar_calculation=True):
        set_submission_calculation_type(VALID_DEPLOYMENT_ID, use_calendar_calculation)

    def _update_alerts_submission_dates(self, user_id: str, list_of_dates: list):
        BillingAlert.objects.filter(user_id=user_id).update(submissionDates=list_of_dates)

    @staticmethod
    def _add_video_call(manager_id: str = VALID_CLINICIAN_ID, user_id: str = USER_ID) -> str:
        video_call = VideoCall(
            managerId=manager_id,
            userId=user_id,
            startDateTime=datetime.now(UTC),
            type=VideoCallDTO.CallType.SCHEDULED.value,
            mongoId=ObjectId(),
            logs=[
                {
                    VideoCallLogDTO.EVENT: VideoCallLogDTO.EventType.USER_JOINED.value,
                    VideoCallLogDTO.IDENTITY: f"user:{user_id}",
                    VideoCallLogDTO.CREATE_DATE_TIME: utc_str_field_to_val(datetime.now(UTC)),
                },
            ],
        )
        video_call.save()
        return str(video_call.mongoId)

    @patch(f"{VIDEO_CALL_USE_CASE}.BaseVideoUseCase._send_user_push_notification_call_ended", MagicMock())
    @patch("sdk.common.adapter.twilio.video_adapter.TwilioVideoAdapter.complete_room", MagicMock())
    def _complete_video_call_by_manager(self, video_call_id: str):
        url = url_for(
            "video.complete_video_call_by_manager", video_call_id=video_call_id, manager_id=VALID_CLINICIAN_ID
        )
        resp = self.flask_client.post(url, headers=self.get_headers_for_token(VALID_CLINICIAN_ID))
        self.assertEqual(204, resp.status_code)

    @patch("sdk.common.adapter.twilio.video_adapter.TwilioVideoAdapter.complete_room", MagicMock())
    def _complete_video_call_by_user(self, video_call_id: str):
        url = url_for("video.complete_video_call_by_user", video_call_id=video_call_id, user_id=USER_ID)
        body = {"reason": VideoCallDTO.CallStatus.ANSWERED.value}
        resp = self.flask_client.post(url, json=body, headers=self.get_headers_for_token(USER_ID))
        self.assertEqual(204, resp.status_code)

    def _create_offline_video_call(self):
        url = url_for("online_offline_call_route.create_offline_call", user_id=USER_ID)
        now = datetime.now(UTC)
        body = {
            CreateOfflineCallRequestObject.START_DATE_TIME: now - timedelta(minutes=30),
            CreateOfflineCallRequestObject.END_DATE_TIME: now,
            CreateOfflineCallRequestObject.NOTE: "User note",
        }
        rsp = self.flask_client.post(url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)

    def _submit_module_result(self, module_id, data, user_id=USER_ID):
        url = url_for(f"submit_module.create_module_result_{module_id}", user_id=user_id)
        rsp = self.flask_client.post(url, json=[data], headers=self.get_headers_for_token(user_id))
        self.assertEqual(201, rsp.status_code)
        return rsp.json
