from parameterized import parameterized

from billing.components.core.component import BillingComponent
from billing.components.core.dtos.deployment_billing import ProductType
from billing.components.core.router.billing_response_objects import (
    CPTCode,
    CPTCodeIterationNumber,
    CPTRecordStatusType,
)
from billing.components.core.use_case.billing_calculate_cumulative_use_cases import (
    CalculateCumulativeMonitoringTimeCPTCodeUseCase,
)
from sdk.tests.extension_test_case import ExtensionTestCase


class CalculateCumulativeMonitoringTimeCPTCodeUseCaseTest(ExtensionTestCase):
    components = [
        BillingComponent(),
    ]

    def setUp(self) -> None:
        self.use_case = CalculateCumulativeMonitoringTimeCPTCodeUseCase()

    @parameterized.expand(
        [
            (
                CPTRecordStatusType.PENDING,
                CPTRecordStatusType.PENDING,
                0,
                (
                    CPTCode.RPM.CPT_MAPPING_3.value,
                    CPTCodeIterationNumber.IterationFor1x.value,
                ),
            ),
            (
                CPTRecordStatusType.PENDING,
                CPTRecordStatusType.PENDING,
                1200,
                (
                    CPTCode.RPM.CPT_MAPPING_4.value,
                    CPTCodeIterationNumber.IterationFor1x.value,
                ),
            ),
            (
                CPTRecordStatusType.PENDING,
                CPTRecordStatusType.PENDING,
                2400,
                (
                    CPTCode.RPM.CPT_MAPPING_4.value,
                    CPTCodeIterationNumber.IterationFor2x.value,
                ),
            ),
            (
                CPTRecordStatusType.IN_PROGRESS,
                CPTRecordStatusType.PENDING,
                100,
                (
                    CPTCode.RPM.CPT_MAPPING_3.value,
                    CPTCodeIterationNumber.IterationFor1x.value,
                ),
            ),
            (
                CPTRecordStatusType.COMPLETED,
                CPTRecordStatusType.IN_PROGRESS,
                1500,
                (
                    CPTCode.RPM.CPT_MAPPING_4.value,
                    CPTCodeIterationNumber.IterationFor1x.value,
                ),
            ),
            (
                CPTRecordStatusType.COMPLETED,
                CPTRecordStatusType.COMPLETED,
                2500,
                (
                    CPTCode.RPM.CPT_MAPPING_4.value,
                    CPTCodeIterationNumber.IterationFor2x.value,
                ),
            ),
        ]
    )
    def test_get_cpt_code_to_show_and_code_iteration_for_RPM(
        self,
        status_cpt_mapping_3: str,
        status_cpt_mapping_4_1x: str,
        time_spent: float,
        expected_status: str,
    ):
        self.assertEqual(
            expected_status,
            self.use_case._get_cpt_code_to_show_and_code_iteration(
                status_cpt_mapping_3=status_cpt_mapping_3,
                status_cpt_mapping_4_1x=status_cpt_mapping_4_1x,
                time_spent=time_spent,
                product_type=ProductType.RPM,
            ),
        )

    @parameterized.expand(
        [
            (
                CPTRecordStatusType.PENDING,
                CPTRecordStatusType.PENDING,
                0,
                (
                    CPTCode.RTM.CPT_MAPPING_3.value,
                    CPTCodeIterationNumber.IterationFor1x.value,
                ),
            ),
            (
                CPTRecordStatusType.PENDING,
                CPTRecordStatusType.PENDING,
                1200,
                (
                    CPTCode.RTM.CPT_MAPPING_4.value,
                    CPTCodeIterationNumber.IterationFor1x.value,
                ),
            ),
            (
                CPTRecordStatusType.PENDING,
                CPTRecordStatusType.PENDING,
                2400,
                (
                    CPTCode.RTM.CPT_MAPPING_4.value,
                    CPTCodeIterationNumber.IterationFor2x.value,
                ),
            ),
            (
                CPTRecordStatusType.IN_PROGRESS,
                CPTRecordStatusType.PENDING,
                100,
                (
                    CPTCode.RTM.CPT_MAPPING_3.value,
                    CPTCodeIterationNumber.IterationFor1x.value,
                ),
            ),
            (
                CPTRecordStatusType.COMPLETED,
                CPTRecordStatusType.IN_PROGRESS,
                1500,
                (
                    CPTCode.RTM.CPT_MAPPING_4.value,
                    CPTCodeIterationNumber.IterationFor1x.value,
                ),
            ),
            (
                CPTRecordStatusType.COMPLETED,
                CPTRecordStatusType.COMPLETED,
                2500,
                (
                    CPTCode.RTM.CPT_MAPPING_4.value,
                    CPTCodeIterationNumber.IterationFor2x.value,
                ),
            ),
        ]
    )
    def test_get_cpt_code_to_show_and_code_iteration_for_RTM(
        self,
        status_cpt_mapping_3: str,
        status_cpt_mapping_4_1x: str,
        time_spent: float,
        expected_status: str,
    ):
        self.assertEqual(
            expected_status,
            self.use_case._get_cpt_code_to_show_and_code_iteration(
                status_cpt_mapping_3=status_cpt_mapping_3,
                status_cpt_mapping_4_1x=status_cpt_mapping_4_1x,
                time_spent=time_spent,
                product_type=ProductType.RTM,
            ),
        )

    @parameterized.expand(
        [
            (
                CPTCode.RPM.CPT_MAPPING_3.value,
                CPTCodeIterationNumber.IterationFor1x.value,
                CPTRecordStatusType.PENDING,
                CPTRecordStatusType.PENDING,
                CPTRecordStatusType.PENDING,
                CPTRecordStatusType.PENDING,
            ),
            (
                CPTCode.RPM.CPT_MAPPING_4.value,
                CPTCodeIterationNumber.IterationFor2x.value,
                CPTRecordStatusType.COMPLETED,
                CPTRecordStatusType.COMPLETED,
                CPTRecordStatusType.IN_PROGRESS,
                CPTRecordStatusType.IN_PROGRESS,
            ),
            (
                CPTCode.RPM.CPT_MAPPING_4.value,
                CPTCodeIterationNumber.IterationFor1x.value,
                CPTRecordStatusType.COMPLETED,
                CPTRecordStatusType.IN_PROGRESS,
                CPTRecordStatusType.PENDING,
                CPTRecordStatusType.IN_PROGRESS,
            ),
            (
                CPTCode.RTM.CPT_MAPPING_3.value,
                CPTCodeIterationNumber.IterationFor1x.value,
                CPTRecordStatusType.IN_PROGRESS,
                CPTRecordStatusType.PENDING,
                CPTRecordStatusType.PENDING,
                CPTRecordStatusType.IN_PROGRESS,
            ),
            (
                CPTCode.RTM.CPT_MAPPING_4.value,
                CPTCodeIterationNumber.IterationFor2x.value,
                CPTRecordStatusType.COMPLETED,
                CPTRecordStatusType.COMPLETED,
                CPTRecordStatusType.COMPLETED,
                CPTRecordStatusType.COMPLETED,
            ),
        ]
    )
    def test_get_status_for_cpt_code(
        self,
        cpt_code,
        cpt_code_iteration,
        status_cpt_mapping_3,
        status_cpt_mapping_4_1x,
        status_cpt_mapping_4_2x,
        expected_status,
    ):
        self.assertEqual(
            expected_status,
            self.use_case._get_status_for_cpt_code(
                cpt_code=cpt_code,
                cpt_code_iteration=cpt_code_iteration,
                status_cpt_mapping_3=status_cpt_mapping_3,
                status_cpt_mapping_4_1x=status_cpt_mapping_4_1x,
                status_cpt_mapping_4_2x=status_cpt_mapping_4_2x,
            ),
        )
