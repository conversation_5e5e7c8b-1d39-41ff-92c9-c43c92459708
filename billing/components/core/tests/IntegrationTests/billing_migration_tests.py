from datetime import datetime, timedelta
from pathlib import Path

from freezegun import freeze_time

from billing.components.core.component import BillingComponent
from billing.components.core.helpers.module_result_helpers import (
    PrimitiveSources,
)
from billing.components.core.models import BillingSubmission
from billing.components.export.component import BillingExportComponent
from billing.tests.billing_test_case import BillingTestCase
from huma_plugins.components.export.dtos.export_models import ExportParameters
from huma_plugins.components.extended_module_result.component import (
    ExtendedModuleResultComponent,
)
from huma_plugins.components.online_offline_call.component import (
    OnlineOfflineCallComponent,
)
from huma_plugins.tests.shared.common import sample_weight_dict
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.common.utils.validators import model_to_dict
from sdk.deployment.component import DeploymentComponent
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.modules import WeightModule
from sdk.module_result.modules.weight import WeightDTO
from sdk.organization.component import OrganizationComponent
from sdk.storage.component import StorageComponentV1
from sdk.versioning.component import VersionComponent

USER_ID = "5e8f0c74b50aa9656c34789b"
ADMIN_ID = "5e8f0c74b50aa9656c34129d"
DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
PRIMITIVE_ID = "5f7dd1f0e03d4a97e8007a91"


class MigrationTestCase(BillingTestCase):
    """
    Doesn't test the migration itself, but tests that migration is not needed,
    to ensure that old data without local date-times works fine
    along with new data (that has both local and UTC date-times))
    """

    components = [
        AuthComponent(),
        AuthorizationComponent(),
        BillingComponent(),
        DeploymentComponent(),
        OrganizationComponent(),
        OnlineOfflineCallComponent(),
        BillingExportComponent(),
        ExtendedModuleResultComponent(additional_modules=[WeightModule()]),
        StorageComponentV1(),
        VersionComponent(server_version="1.0.0", api_version="1.0.0"),
    ]
    fixtures = [
        Path(__file__).parent.joinpath("fixtures/billing_dump.json"),
    ]

    @freeze_time("2023-07-30")
    def test_rollback_submission_dt_migration(self):
        # Assume having user submissions WITHOUT startDateTimeUTC in a dump
        self._create_submission_record()
        # Add a new submission WITH local date-time
        rsp = self._submit_module_result()
        self.assertEqual(201, rsp.status_code)
        new_submission_id = rsp.json["ids"][0]
        res = model_to_dict(BillingSubmission.objects.filter(primitiveId=new_submission_id).first())
        self.assertIn("startDateTimeUTC", res)
        # Check that report is generated correctly
        rsp = self._generate_report()
        self.assertEqual(200, rsp.status_code)
        self.assertIsNotNone(rsp.data)

    def _create_submission_record(self):
        start_dt = datetime.utcnow() - timedelta(days=20)
        document = BillingSubmission(
            deploymentId=DEPLOYMENT_ID,
            userId=USER_ID,
            primitiveId=PRIMITIVE_ID,
            primitiveClassName="Weight",
            deviceName="IOS",
            deviceDetails="Dummy device details",
            source="Dummy source",
            startDateTime=start_dt,
            createDateTime=start_dt,
            startDate=start_dt.date(),
            isCompleted=None,
        )
        document.save()
        return str(document.mongoId)

    def _submit_module_result(self):
        body = sample_weight_dict()
        body.update(
            {
                PrimitiveDTO.USER_ID: USER_ID,
                PrimitiveDTO.SUBMITTER_ID: USER_ID,
                PrimitiveDTO.DEPLOYMENT_ID: DEPLOYMENT_ID,
                PrimitiveDTO.START_DATE_TIME: "2023-07-15T00:00:00Z",
                "type": WeightDTO.get_primitive_name(),
                "source": PrimitiveSources.HEALTH_KIT.value,
            }
        )
        return self.flask_client.post(
            f"api/extensions/v1/user/{USER_ID}/module-result/{WeightModule.moduleId}",
            headers=self.get_headers_for_token(USER_ID),
            json=[body],
        )

    def _generate_report(self):
        body = {
            ExportParameters.SINGLE_FILE_RESPONSE: True,
            ExportParameters.FORMAT: "JSON",
            ExportParameters.FROM_DATE: "2023-07-01T00:00:00Z",
            ExportParameters.TO_DATE: "2023-08-30T23:59:59Z",
            ExportParameters.USER_IDS: [USER_ID],
        }
        return self.flask_client.post(
            f"api/extensions/v1/export/deployment/{DEPLOYMENT_ID}",
            headers=self.get_headers_for_token(ADMIN_ID),
            json=body,
        )
