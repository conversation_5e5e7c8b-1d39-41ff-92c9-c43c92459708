import datetime as dt
import unittest
from datetime import datetime
from unittest import mock

from parameterized import parameterized

import billing.components.core.helpers.export_billing_submission_helpers as helpers
from billing.components.core.helpers.export_billing_submission_helpers import (
    get_billable_period_count_for_cpt_3,
)
from billing.components.core.router.billing_response_objects import (
    CPTRecordStatusType,
)


class ExportBillingSubmissionHelpersTestCase(unittest.TestCase):
    @parameterized.expand(
        [
            ("test_case_1", datetime(2021, 1, 1), datetime(2021, 3, 1), 2),
            ("test_case_2", datetime(2021, 1, 1), datetime(2021, 1, 1), 0),
            ("test_case_3", datetime(2022, 1, 7), datetime(2022, 12, 31), 12),
            ("test_case_4", datetime(2022, 2, 28), datetime(2022, 3, 1), 1),
            ("test_case_5", datetime(2022, 4, 30), datetime(2022, 6, 30), 3),
            ("test_case_6", datetime(2021, 12, 5), datetime(2022, 1, 1), 1),
            ("test_case_7", datetime(2021, 12, 30), datetime(2021, 12, 31), 1),
            ("test_case_7", datetime(2021, 12, 29), datetime(2021, 12, 30), 0),
        ]
    )
    @mock.patch.object(helpers, "get_time_spent", return_value=1201)
    @mock.patch.object(helpers, "get_status_for_cpt_mapping_3")
    def test_get_billable_period_count_for_cpt_3_with_completed_status(
        self,
        name,
        from_date,
        to_date,
        expected,
        mock_get_status_for_cpt_mapping_3,
        _,
    ):
        mock_get_status_for_cpt_mapping_3.return_value = CPTRecordStatusType.COMPLETED
        billing_period_counts = get_billable_period_count_for_cpt_3(
            user_id="user_id", from_date=from_date, to_date=to_date
        )
        self.assertEqual(
            billing_period_counts,
            expected,
            f"{name} failed",
        )

    @parameterized.expand(
        [
            ("test_case_6", datetime(2021, 12, 5), datetime(2022, 1, 1), 0),
            ("test_case_7", datetime(2021, 12, 30), datetime(2021, 12, 31), 0),
            ("test_case_8", datetime(2022, 1, 1), datetime(2022, 12, 31), 0),
            ("test_case_9", datetime(2022, 2, 1), datetime(2022, 3, 31), 0),
            ("test_case_10", datetime(2022, 4, 1), datetime(2022, 6, 30), 0),
        ]
    )
    @mock.patch.object(helpers, "get_time_spent", return_value=1201)
    @mock.patch.object(helpers, "get_status_for_cpt_mapping_3")
    def test_get_billable_period_count_for_cpt_3_with_in_progress_status(
        self,
        name,
        from_date,
        to_date,
        expected,
        mock_get_status_for_cpt_mapping_3,
        _,
    ):
        mock_get_status_for_cpt_mapping_3.return_value = CPTRecordStatusType.IN_PROGRESS
        billing_period_counts = get_billable_period_count_for_cpt_3(
            user_id="user_id", from_date=from_date, to_date=to_date
        )
        self.assertEqual(
            billing_period_counts,
            expected,
            f"{name} failed",
        )

    def test_get_calendar_submission_period_for_dec_common_year(self):
        date = dt.date(2022, 12, 30)
        start, end = helpers.get_calendar_submission_period(date)
        self.assertEqual(start, dt.date(2022, 12, 1))
        self.assertEqual(end, dt.date(2022, 12, 30))

        date = dt.date(2022, 12, 31)
        start, end = helpers.get_calendar_submission_period(date)
        self.assertEqual(start, dt.date(2022, 12, 31))
        self.assertEqual(end, dt.date(2023, 1, 29))

    def test_get_calendar_submission_period_for_jan_common_year(self):
        date = dt.date(2022, 1, 25)
        start, end = helpers.get_calendar_submission_period(date)
        self.assertEqual(start, dt.date(2021, 12, 31))
        self.assertEqual(end, dt.date(2022, 1, 29))

        date = dt.date(2022, 1, 31)
        start, end = helpers.get_calendar_submission_period(date)
        self.assertEqual(start, dt.date(2022, 1, 30))
        self.assertEqual(end, dt.date(2022, 2, 28))

    def test_get_calendar_submission_period_for_feb_common_year(self):
        date = dt.date(2022, 2, 5)
        start, end = helpers.get_calendar_submission_period(date)
        self.assertEqual(start, dt.date(2022, 1, 30))
        self.assertEqual(end, dt.date(2022, 2, 28))

    def test_get_calendar_submission_period_for_mar_common_year(self):
        date = dt.date(2022, 3, 5)
        start, end = helpers.get_calendar_submission_period(date)
        self.assertEqual(start, dt.date(2022, 3, 1))
        self.assertEqual(end, dt.date(2022, 3, 31))

    def test_get_calendar_submission_period_for_dec_leap_year(self):
        date = dt.date(2024, 12, 24)
        start, end = helpers.get_calendar_submission_period(date)
        self.assertEqual(start, dt.date(2024, 12, 1))
        self.assertEqual(end, dt.date(2024, 12, 30))

        date = dt.date(2024, 12, 31)
        start, end = helpers.get_calendar_submission_period(date)
        self.assertEqual(start, dt.date(2024, 12, 31))
        self.assertEqual(end, dt.date(2025, 1, 29))

    def test_get_calendar_submission_period_for_jan_leap_year(self):
        date = dt.date(2024, 1, 10)
        start, end = helpers.get_calendar_submission_period(date)
        self.assertEqual(start, dt.date(2024, 1, 1))
        self.assertEqual(end, dt.date(2024, 1, 30))

        date = dt.date(2024, 1, 31)
        start, end = helpers.get_calendar_submission_period(date)
        self.assertEqual(start, dt.date(2024, 1, 31))
        self.assertEqual(end, dt.date(2024, 2, 29))

    def test_get_calendar_submission_period_for_feb_leap_year(self):
        date = dt.date(2024, 2, 5)
        start, end = helpers.get_calendar_submission_period(date)
        self.assertEqual(start, dt.date(2024, 1, 31))
        self.assertEqual(end, dt.date(2024, 2, 29))

    def test_get_calendar_submission_period_for_mar_leap_year(self):
        date = dt.date(2024, 3, 5)
        start, end = helpers.get_calendar_submission_period(date)
        self.assertEqual(start, dt.date(2024, 3, 1))
        self.assertEqual(end, dt.date(2024, 3, 31))

    def test_get_calendar_submission_period_for_dec_common_year_with_next_leap(self):
        date = dt.date(2023, 12, 24)
        start, end = helpers.get_calendar_submission_period(date)
        self.assertEqual(start, dt.date(2023, 12, 1))
        self.assertEqual(end, dt.date(2023, 12, 31))

    def test_get_calendar_submission_period_for_jan_common_year_with_next_leap(self):
        date = dt.date(2023, 1, 10)
        start, end = helpers.get_calendar_submission_period(date)
        self.assertEqual(start, dt.date(2022, 12, 31))
        self.assertEqual(end, dt.date(2023, 1, 29))

        date = dt.date(2023, 1, 30)
        start, end = helpers.get_calendar_submission_period(date)
        self.assertEqual(start, dt.date(2023, 1, 30))
        self.assertEqual(end, dt.date(2023, 2, 28))

    def test_get_calendar_submission_period_for_feb_common_year_with_next_leap(self):
        date = dt.date(2023, 2, 5)
        start, end = helpers.get_calendar_submission_period(date)
        self.assertEqual(start, dt.date(2023, 1, 30))
        self.assertEqual(end, dt.date(2023, 2, 28))

    def test_get_calendar_submission_period_for_mar_common_year_with_next_leap(self):
        date = dt.date(2023, 3, 5)
        start, end = helpers.get_calendar_submission_period(date)
        self.assertEqual(start, dt.date(2023, 3, 1))
        self.assertEqual(end, dt.date(2023, 3, 31))


if __name__ == "__main__":
    unittest.main()
