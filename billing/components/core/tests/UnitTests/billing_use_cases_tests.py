import unittest
from datetime import datetime
from unittest.mock import MagicMock, patch

from billing.components.core.dtos.billing_models import BillingSubmissionDTO
from billing.components.core.dtos.billing_submission_schedule_event import (
    BillingSubmissionScheduleEvent,
)
from billing.components.core.dtos.deployment_billing import (
    BillingSubmissionReminder,
    DeploymentBillingConfig,
    ProductType,
)
from billing.components.core.repository.billing_repository import (
    BillingMonitoringLogRepository,
    BillingRemoteTimeTrackingRepository,
    BillingSubmissionRepository,
)
from billing.components.core.router.billing_requests import (
    RetrieveBillingMonitoringLogsRequestObject,
)
from billing.components.core.use_case.billing_monitoring_log_use_cases import (
    RetrieveBillingMonitoringLogsUseCase,
)
from billing.components.core.use_case.billing_use_cases import (
    BillingSubmissionReminderProcessor,
)
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.calendar.dtos.calendar_event import CalendarEventDTO
from sdk.calendar.repo.calendar_repository import CalendarRepository
from sdk.common.utils import inject

RESPONSE_PATH = "billing.components.core.use_case.billing_monitoring_log_use_cases.BillingMonitoringLogsResponseObject"
HELPERS_PATH = "billing.components.core.router.billing_requests.validate_deployment_billing_enabled"
CALENDAR_SERVICE_PATH = "billing.components.core.use_case.billing_use_cases.CalendarService"


class BaseBillingMonitoringLogUseCaseTestCase(unittest.TestCase):
    def setUp(self):
        self.time_tracking_repo = MagicMock()
        self.monitoring_repo = MagicMock()
        self.auth_repo = MagicMock()

        def bind(binder):
            binder.bind(BillingRemoteTimeTrackingRepository, self.time_tracking_repo)
            binder.bind(BillingMonitoringLogRepository, self.monitoring_repo)
            binder.bind(AuthorizationRepository, self.auth_repo)

        inject.clear_and_configure(bind)


class RetrieveBillingMonitoringLogUseCaseTestCase(BaseBillingMonitoringLogUseCaseTestCase):
    def setUp(self):
        super().setUp()
        self.USER_ID = "64b629cec1e5002608383bd9"

        self.submitter = MagicMock(spec=AuthorizedUser)
        self.user = MagicMock(spec=AuthorizedUser)
        self.user.deployment.return_value = MagicMock()

    @patch(HELPERS_PATH)
    def test_user_has_zero_billing_monitoring_logs(self, billing_validator):
        billing_validator.return_value = True
        self.monitoring_repo.retrieve_active_user_monitoring_logs.return_value = [], 0
        self.submitter.get_language.return_value = "en"

        request_data = self._request_data()
        req_obj = RetrieveBillingMonitoringLogsRequestObject.from_dict(request_data)
        use_case = RetrieveBillingMonitoringLogsUseCase()
        with patch(RESPONSE_PATH) as mock_response:
            use_case.execute(req_obj)

        mock_response.assert_called_once_with(items=[], limit=10, skip=0, total=0)

    def _request_data(self, skip=0, limit=10):
        return {
            RetrieveBillingMonitoringLogsRequestObject.SKIP: skip,
            RetrieveBillingMonitoringLogsRequestObject.LIMIT: limit,
            RetrieveBillingMonitoringLogsRequestObject.USER: self.user,
            RetrieveBillingMonitoringLogsRequestObject.SUBMITTER: self.submitter,
        }


@patch(CALENDAR_SERVICE_PATH)
class BillingSubmissionReminderProcessorTestCase(unittest.TestCase):
    def setUp(self):
        CalendarEventDTO.register(BillingSubmissionScheduleEvent.__name__, BillingSubmissionScheduleEvent)
        self.submission = sample_submission()
        self.billing_config = sample_billing_config()
        self.calendar_repo = MagicMock()
        self.submission_repo = MagicMock()
        self.submission_repo.user_has_submission_after_date.return_value = False

        def bind(binder):
            binder.bind(AuthorizationRepository, MagicMock())
            binder.bind(CalendarRepository, self.calendar_repo)
            binder.bind(BillingSubmissionRepository, self.submission_repo)

        inject.clear_and_configure(bind)

    def test_submission_reminder_created_on_new_submission(self, calendar_service):
        reminder_handler = BillingSubmissionReminderProcessor(self.submission, self.billing_config)

        reminder_handler.process()

        self.calendar_repo.batch_delete_next_day_event_raw.assert_called_once()
        calendar_service().create_or_update_event.assert_called_once()

    def test_submission_reminder_not_created_for_past_time_submissions(self, calendar_service):
        self.submission_repo.user_has_submission_after_date.return_value = True
        reminder_handler = BillingSubmissionReminderProcessor(self.submission, self.billing_config)

        reminder_handler.process()

        self.calendar_repo.batch_delete_next_day_event_raw.assert_not_called()
        calendar_service().create_or_update_event.assert_not_called()

    def test_submission_reminder_not_created_for_RTM(self, calendar_service):
        billing_config = sample_billing_config(ProductType.RTM)
        reminder_handler = BillingSubmissionReminderProcessor(self.submission, billing_config)

        reminder_handler.process()

        self.calendar_repo.batch_delete_next_day_event_raw.assert_not_called()
        calendar_service().create_or_update_event.assert_not_called()

    def test_submission_reminder_not_created_without_config(self, calendar_service):
        self.billing_config.submissionReminder.enabled = False

        reminder_handler = BillingSubmissionReminderProcessor(self.submission, self.billing_config)

        reminder_handler.process()

        self.calendar_repo.batch_delete_next_day_event_raw.assert_not_called()
        calendar_service().create_event.assert_not_called()


def sample_submission():
    return BillingSubmissionDTO.from_dict(
        {
            BillingSubmissionDTO.USER_ID: "64b629cec1e5002608383bd9",
            BillingSubmissionDTO.PRIMITIVE_ID: "64b629cec1e5002608383bd9",
            BillingSubmissionDTO.PRIMITIVE_CLASS_NAME: "HeartRate",
            BillingSubmissionDTO.DEVICE_NAME: "device",
            BillingSubmissionDTO.SOURCE: "source",
            BillingSubmissionDTO.DEPLOYMENT_ID: "64b629cec1e5002608383bd9",
            BillingSubmissionDTO.START_DATE: datetime.utcnow(),
            BillingSubmissionDTO.START_DATE_TIME_UTC: datetime.utcnow(),
            BillingSubmissionDTO.START_DATE_TIME: datetime.utcnow(),
        }
    )


def sample_billing_config(product_type=ProductType.RPM):
    reminder_config = BillingSubmissionReminder.from_dict(
        {
            "enabled": True,
            "schedule": {
                "syncThreshold": 2,
                "interval": 2,
                "count": 3,
            },
        }
    )
    return DeploymentBillingConfig(enabled=True, productType=product_type, submissionReminder=reminder_config)
