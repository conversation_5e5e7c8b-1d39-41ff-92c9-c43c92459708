from unittest import TestCase
from unittest.mock import MagicMock, patch

from billing.components.core.callbacks.authorization_callbacks import (
    process_pre_update_user,
)
from sdk.authorization.events import PreUserProfileUpdateEvent

AUTH_CALLBACKS_PATH = "billing.components.core.callbacks.authorization_callbacks"

auth_service, authz_user = MagicMock(), MagicMock()


@patch(f"{AUTH_CALLBACKS_PATH}.AuthorizationService", auth_service)
@patch(f"{AUTH_CALLBACKS_PATH}.AuthorizedUser", authz_user)
class BillingCallbacksAuthorizationEventsTests(TestCase):
    def setUp(self):
        authz_user.return_value.is_user.return_value = True

    @patch(f"{AUTH_CALLBACKS_PATH}._set_billing_update_dt_on_user_update")
    @patch(f"{AUTH_CALLBACKS_PATH}._validate_deployment_billing_data_on_user_update")
    def test_process_pre_update_user_success(
        self,
        validate_deployment_billing,
        set_billing_update,
    ):
        user, previous_state = MagicMock(), MagicMock()
        event = PreUserProfileUpdateEvent(user, previous_state)
        process_pre_update_user(event)

        validate_deployment_billing.assert_called_once_with(user, previous_state)
        set_billing_update.assert_called_once_with(user, previous_state)

    @patch(f"{AUTH_CALLBACKS_PATH}._set_billing_update_dt_on_user_update")
    @patch(f"{AUTH_CALLBACKS_PATH}._validate_deployment_billing_data_on_user_update")
    def test_process_pre_update_user_not_is_user(
        self,
        validate_deployment_billing,
        set_billing_update,
    ):
        authz_user.return_value.is_user.return_value = False
        event = PreUserProfileUpdateEvent(MagicMock(), MagicMock())
        process_pre_update_user(event)

        validate_deployment_billing.assert_not_called()
        set_billing_update.assert_not_called()

    @patch(f"{AUTH_CALLBACKS_PATH}._set_billing_update_dt_on_user_update")
    @patch(f"{AUTH_CALLBACKS_PATH}._validate_deployment_billing_data_on_user_update")
    def test_process_pre_update_user_no_previous_state(
        self,
        validate_deployment_billing,
        set_billing_update,
    ):
        event = PreUserProfileUpdateEvent(MagicMock(), None)
        process_pre_update_user(event)

        validate_deployment_billing.assert_not_called()
        set_billing_update.assert_not_called()

    @patch(f"{AUTH_CALLBACKS_PATH}._set_billing_update_dt_on_user_update")
    @patch(f"{AUTH_CALLBACKS_PATH}._validate_deployment_billing_data_on_user_update")
    @patch(f"{AUTH_CALLBACKS_PATH}._is_valid_user_billing_object")
    def test_process_pre_update_user_not_valid_billing_object(
        self,
        _is_valid_user_billing_object,
        validate_deployment_billing,
        set_billing_update,
    ):
        _is_valid_user_billing_object.return_value = False
        event = PreUserProfileUpdateEvent(MagicMock(), MagicMock())
        process_pre_update_user(event)

        validate_deployment_billing.assert_not_called()
        set_billing_update.assert_not_called()
