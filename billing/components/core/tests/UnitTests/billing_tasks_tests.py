import unittest
from unittest.mock import patch, MagicMock

from freezegun import freeze_time

from billing.components.core.tasks import (
    execute_remove_users_billing_data_overlapping,
)

TASKS_PATH = "billing.components.core.tasks"


class BillingTasksTestCase(unittest.TestCase):
    @freeze_time("2020-07-16")
    @patch(f"{TASKS_PATH}.celery_app", MagicMock())
    @patch(f"{TASKS_PATH}.remove_users_billing_data_overlapping")
    def test_execute_remove_users_billing_data_overlapping__time_is_applied_for_last_24_hours_if_nulled(
        self, remove_users_billing_data_overlapping
    ):
        execute_remove_users_billing_data_overlapping()
        remove_users_billing_data_overlapping.assert_called_with()


if __name__ == "__main__":
    unittest.main()
