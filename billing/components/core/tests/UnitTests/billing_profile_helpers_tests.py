import unittest
from copy import deepcopy
from datetime import datetime, timedelta
from unittest import mock

from billing.components.core.dtos.deployment_billing import BILLING_FEATURES_KEY
from billing.components.core.dtos.user_billing import Diagnosis, UserBilling
from billing.components.core.helpers import billing_profile_helpers
from billing.tests.test_helpers import sample_user_billing


class BillingProfileHelpersTestCase(unittest.TestCase):
    def setUp(self) -> None:
        self.sample_update_dt = datetime(2022, 11, 12, 10, 15, 0, 0)
        self.str_dt = datetime.strftime(self.sample_update_dt, "%Y-%m-%dT%H:%M:%S.%fZ")
        self.curr_user = mock.MagicMock(componentsData={BILLING_FEATURES_KEY: sample_user_billing()})
        self.pre_user = mock.MagicMock(componentsData={BILLING_FEATURES_KEY: sample_user_billing()})
        self._event = mock.MagicMock(user=self.curr_user, previous_state=self.pre_user)

    @mock.patch.object(billing_profile_helpers, "_update_diagnosis_data")
    @mock.patch.object(billing_profile_helpers, "_update_carriers_data")
    @mock.patch.object(billing_profile_helpers, "_update_billing_provider")
    def test_set_update_and_create_date_time_to_user_profile_data(
        self,
        mocked__update_billing_provider,
        mocked__update_carriers_data,
        mocked__update_diagnosis_data,
    ):
        sample_data = sample_user_billing()

        billing_profile_helpers.set_update_and_create_date_time_to_user_profile_data(
            self.curr_user, self.pre_user, self.sample_update_dt
        )

        mocked__update_diagnosis_data.assert_called_once_with(sample_data, sample_data, self.str_dt)
        mocked__update_carriers_data.assert_called_once_with(sample_data, sample_data, self.str_dt)
        mocked__update_billing_provider.assert_called_once_with(sample_data, sample_data, self.str_dt)

    @mock.patch.object(billing_profile_helpers, "_update_diagnosis_data")
    @mock.patch.object(billing_profile_helpers, "_update_carriers_data")
    @mock.patch.object(billing_profile_helpers, "_update_billing_provider")
    def test_set_update_and_create_date_time_to_user_profile_data_WHEN_user_has_NO_billing_data(
        self,
        mocked__update_billing_provider,
        mocked__update_carriers_data,
        mocked__update_diagnosis_data,
    ):
        self.curr_user.componentsData.pop(BILLING_FEATURES_KEY)

        billing_profile_helpers.set_update_and_create_date_time_to_user_profile_data(
            self.curr_user, self.pre_user, self.sample_update_dt
        )

        mocked__update_diagnosis_data.assert_not_called()
        mocked__update_carriers_data.assert_not_called()
        mocked__update_billing_provider.assert_not_called()

    def test_set_update_date_time_to_user_profile_data_WHEN_billing_data_created(self):
        self.pre_user.componentsData.pop(BILLING_FEATURES_KEY)

        billing_profile_helpers.set_update_and_create_date_time_to_user_profile_data(
            self.curr_user, self.pre_user, self.sample_update_dt
        )

        curr_billing_data = self.curr_user.componentsData[BILLING_FEATURES_KEY]

        self.assertEqual(curr_billing_data[UserBilling.UPDATE_DT], self.str_dt)
        self.assertEqual(curr_billing_data[UserBilling.DIAGNOSIS][UserBilling.UPDATE_DT], self.str_dt)
        self.assertEqual(curr_billing_data[UserBilling.DIAGNOSIS][UserBilling.CREATE_DT], self.str_dt)
        self.assertEqual(
            curr_billing_data[UserBilling.INSURANCE_CARRIERS][0][UserBilling.UPDATE_DT],
            self.str_dt,
        )
        self.assertEqual(
            curr_billing_data[UserBilling.INSURANCE_CARRIERS][0][UserBilling.CREATE_DT],
            self.str_dt,
        )
        self.assertEqual(
            curr_billing_data[UserBilling.INSURANCE_CARRIERS][1][UserBilling.UPDATE_DT],
            self.str_dt,
        )
        self.assertEqual(
            curr_billing_data[UserBilling.INSURANCE_CARRIERS][1][UserBilling.CREATE_DT],
            self.str_dt,
        )

    def test_set_update_date_time_to_user_profile_data_WHEN_billing_exists_without_other_data(
        self,
    ):
        user_previous_billing_data = self.pre_user.componentsData[BILLING_FEATURES_KEY]

        for key in [
            UserBilling.DIAGNOSIS,
            UserBilling.INSURANCE_CARRIERS,
            UserBilling.BILLING_PROVIDER_NAME,
        ]:
            user_previous_billing_data.pop(key)

        billing_profile_helpers.set_update_and_create_date_time_to_user_profile_data(
            self.curr_user, self.pre_user, self.sample_update_dt
        )

        curr_billing_data = self.curr_user.componentsData[BILLING_FEATURES_KEY]

        self.assertEqual(curr_billing_data[UserBilling.UPDATE_DT], self.str_dt)
        self.assertEqual(curr_billing_data[UserBilling.DIAGNOSIS][UserBilling.UPDATE_DT], self.str_dt)
        self.assertEqual(curr_billing_data[UserBilling.DIAGNOSIS][UserBilling.CREATE_DT], self.str_dt)
        self.assertEqual(
            curr_billing_data[UserBilling.INSURANCE_CARRIERS][0][UserBilling.UPDATE_DT],
            self.str_dt,
        )
        self.assertEqual(
            curr_billing_data[UserBilling.INSURANCE_CARRIERS][0][UserBilling.CREATE_DT],
            self.str_dt,
        )
        self.assertEqual(
            curr_billing_data[UserBilling.INSURANCE_CARRIERS][1][UserBilling.UPDATE_DT],
            self.str_dt,
        )
        self.assertEqual(
            curr_billing_data[UserBilling.INSURANCE_CARRIERS][1][UserBilling.CREATE_DT],
            self.str_dt,
        )

    def test_set_update_date_time_to_user_profile_data_WHEN_nothing_is_changed(self):
        billing_profile_helpers.set_update_and_create_date_time_to_user_profile_data(
            self.curr_user, self.pre_user, self.sample_update_dt
        )

        curr_billing_data = self.curr_user.componentsData[BILLING_FEATURES_KEY]

        self.assertIsNone(curr_billing_data.get(UserBilling.UPDATE_DT))
        self.assertIsNone(curr_billing_data[UserBilling.DIAGNOSIS].get(UserBilling.UPDATE_DT))
        self.assertIsNone(curr_billing_data[UserBilling.DIAGNOSIS].get(UserBilling.CREATE_DT))
        self.assertIsNone(curr_billing_data[UserBilling.INSURANCE_CARRIERS][0].get(UserBilling.UPDATE_DT))
        self.assertIsNone(curr_billing_data[UserBilling.INSURANCE_CARRIERS][0].get(UserBilling.CREATE_DT))
        self.assertIsNone(curr_billing_data[UserBilling.INSURANCE_CARRIERS][1].get(UserBilling.UPDATE_DT))
        self.assertIsNone(curr_billing_data[UserBilling.INSURANCE_CARRIERS][1].get(UserBilling.CREATE_DT))

    def test_set_update_date_time_to_user_profile_data_WHEN_only_diagnosis_is_changed(
        self,
    ):
        self.curr_user.componentsData[BILLING_FEATURES_KEY][UserBilling.DIAGNOSIS][Diagnosis.ICD_CODE] = "ICD2"

        billing_profile_helpers.set_update_and_create_date_time_to_user_profile_data(
            self.curr_user, self.pre_user, self.sample_update_dt
        )

        curr_billing_data = self.curr_user.componentsData[BILLING_FEATURES_KEY]

        self.assertIsNone(curr_billing_data[UserBilling.DIAGNOSIS].get(UserBilling.CREATE_DT))
        self.assertIsNone(curr_billing_data.get(UserBilling.UPDATE_DT))
        self.assertEqual(
            curr_billing_data[UserBilling.DIAGNOSIS].get(UserBilling.UPDATE_DT),
            self.str_dt,
        )
        self.assertIsNone(curr_billing_data[UserBilling.INSURANCE_CARRIERS][0].get(UserBilling.UPDATE_DT))
        self.assertIsNone(curr_billing_data[UserBilling.INSURANCE_CARRIERS][0].get(UserBilling.CREATE_DT))
        self.assertIsNone(curr_billing_data[UserBilling.INSURANCE_CARRIERS][1].get(UserBilling.UPDATE_DT))
        self.assertIsNone(curr_billing_data[UserBilling.INSURANCE_CARRIERS][1].get(UserBilling.CREATE_DT))

    def test_set_update_date_time_to_user_profile_data_WHEN_only_secondary_carrier_is_added(
        self,
    ):
        self.pre_user.componentsData[BILLING_FEATURES_KEY][UserBilling.INSURANCE_CARRIERS].pop(-1)

        billing_profile_helpers.set_update_and_create_date_time_to_user_profile_data(
            self.curr_user, self.pre_user, self.sample_update_dt
        )

        curr_billing_data = self.curr_user.componentsData[BILLING_FEATURES_KEY]

        self.assertIsNone(curr_billing_data[UserBilling.DIAGNOSIS].get(UserBilling.CREATE_DT))
        self.assertIsNone(curr_billing_data.get(UserBilling.UPDATE_DT))
        self.assertIsNone(curr_billing_data[UserBilling.DIAGNOSIS].get(UserBilling.UPDATE_DT))
        self.assertIsNone(curr_billing_data[UserBilling.INSURANCE_CARRIERS][0].get(UserBilling.UPDATE_DT))
        self.assertIsNone(curr_billing_data[UserBilling.INSURANCE_CARRIERS][0].get(UserBilling.CREATE_DT))
        self.assertEqual(
            curr_billing_data[UserBilling.INSURANCE_CARRIERS][1].get(UserBilling.UPDATE_DT),
            self.str_dt,
        )
        self.assertEqual(
            curr_billing_data[UserBilling.INSURANCE_CARRIERS][1].get(UserBilling.CREATE_DT),
            self.str_dt,
        )

    def test_set_update_date_time_to_user_profile_data_WHEN_only_dates_given(
        self,
    ):
        self.curr_user.componentsData[BILLING_FEATURES_KEY][UserBilling.DIAGNOSIS][UserBilling.UPDATE_DT] = str(
            self.sample_update_dt + timedelta(days=1)
        )
        self.pre_user.componentsData[BILLING_FEATURES_KEY][UserBilling.DIAGNOSIS][UserBilling.UPDATE_DT] = self.str_dt

        billing_profile_helpers.set_update_and_create_date_time_to_user_profile_data(
            self.curr_user, self.pre_user, self.sample_update_dt
        )

        curr_billing_data = self.curr_user.componentsData[BILLING_FEATURES_KEY]

        self.assertEqual(
            curr_billing_data[UserBilling.DIAGNOSIS].get(UserBilling.UPDATE_DT),
            self.str_dt,
        )

    def test_successfully_create_billing_data_without_mrn(
        self,
    ):
        if UserBilling.MRN in self.curr_user.componentsData[BILLING_FEATURES_KEY]:
            del self.curr_user.componentsData[BILLING_FEATURES_KEY][UserBilling.MRN]
        curr_billing_data = UserBilling.from_dict(self.curr_user.componentsData[BILLING_FEATURES_KEY])
        self.assertIsNone(curr_billing_data.mrn)

    def test_successfully_create_billing_data_with_mrn(
        self,
    ):
        billing_dict = deepcopy(self.curr_user.componentsData[BILLING_FEATURES_KEY])
        billing_dict[UserBilling.MRN] = "123456789"
        curr_billing_data = UserBilling.from_dict(billing_dict)
        self.assertEqual(curr_billing_data.mrn, "123456789")
