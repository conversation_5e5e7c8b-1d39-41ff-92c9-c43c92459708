import unittest
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

from freezegun import freeze_time

from billing.components.core.dtos.billing_models import BillingMonitoringLogDTO
from billing.components.core.exceptions import (
    ClinicianNotActive,
    InvalidLogDuration,
    PatientNotActive,
)
from billing.components.core.router.billing_requests import (
    CreateBillingMonitoringLogRequestObject,
    MAXIMUM_LOGGING_DATE_GAP_IN_DAYS,
    UpdateBillingMonitoringLogRequestObject,
)
from billing.components.core.statics.billing_monitoring_log_action import (
    BillingMonitoringLogActionKeys,
)
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.dtos.user import UserDTO
from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.utils.convertible import ConvertibleClassValidationError

BILLING_DEPLOYMENT_PATH = "billing.components.core.router.billing_requests.validate_deployment_billing_enabled"


def time_str(m=6, d=1, h=12, mins=0):
    return f"2023-{m:02}-{d:02}T{h:02}:{mins:02}:00.000Z"


class CreateUpdateMonitoringLogTestCase(unittest.TestCase):
    VALID_EXCEPTIONS = (
        ConvertibleClassValidationError,
        InvalidRequestException,
        InvalidLogDuration,
        ClinicianNotActive,
        PatientNotActive,
    )

    def setUp(self) -> None:
        self.user = self._set_up_onboarded_user()
        self.clinician = self._set_clinician()

        self.req_data = {
            BillingMonitoringLogDTO.ACTION: BillingMonitoringLogActionKeys.QUALITY_REVIEW.value,
            BillingMonitoringLogDTO.START_DATE_TIME: time_str(h=12),
            BillingMonitoringLogDTO.END_DATE_TIME: time_str(h=13),
            UpdateBillingMonitoringLogRequestObject.USER: self.user,
            UpdateBillingMonitoringLogRequestObject.CLINICIAN: self.clinician,
        }

        self.deployment_billing = patch(BILLING_DEPLOYMENT_PATH).start()
        self.addCleanup(self.deployment_billing.stop)
        self.deployment_billing.return_value = True

    def tearDown(self) -> None:
        patch.stopall()

    @staticmethod
    def _set_up_onboarded_user():
        mock_user = MagicMock(spec=AuthorizedUser)
        mock_user.user = MagicMock(spec=UserDTO)
        mock_user.is_user_onboarded.return_value = True
        mock_user.user.finishedOnboarding.return_value = True
        mock_user.get_user_create_date_time.return_value = datetime(2023, 1, 1)
        mock_user.get_update_boarding_status.return_value = datetime(2023, 1, 1)
        return mock_user

    @staticmethod
    def _set_clinician():
        clinician = MagicMock(spec=AuthorizedUser)
        clinician.get_user_create_date_time.return_value = datetime(2023, 1, 1)
        return clinician


class CreateMonitoringLogRequestsTestCase(CreateUpdateMonitoringLogTestCase):
    @freeze_time("2023-07-01")
    def test_create_monitoring_log_request(self):
        try:
            req_obj = CreateBillingMonitoringLogRequestObject.from_dict(self.req_data)
            self.assertEqual(0, req_obj.status)
            self.assertEqual(req_obj.initialCreateDateTime, req_obj.createDateTime)
        except self.VALID_EXCEPTIONS:
            self.fail()

    @freeze_time("2023-07-01")
    def test_billing_for_deployment_not_enabled(self):
        self.deployment_billing.side_effect = InvalidRequestException
        with self.assertRaises(InvalidRequestException):
            CreateBillingMonitoringLogRequestObject.from_dict(self.req_data)

    @freeze_time("2023-07-01")
    def test_surpass_logging_date_gap(self):
        date = datetime(2023, 1, 7) - timedelta(days=MAXIMUM_LOGGING_DATE_GAP_IN_DAYS + 1)

        self.req_data[BillingMonitoringLogDTO.START_DATE_TIME] = time_str(m=date.month, d=date.day)
        with self.assertRaises(ConvertibleClassValidationError):
            CreateBillingMonitoringLogRequestObject.from_dict(self.req_data)

    @freeze_time("2023-07-01")
    def test_addendum_present(self):
        self.req_data[BillingMonitoringLogDTO.ADDENDUM] = "addendum"
        with self.assertRaises(ConvertibleClassValidationError):
            CreateBillingMonitoringLogRequestObject.from_dict(self.req_data)

    @freeze_time("2023-07-01")
    def test_surpass_logging_duration(self):
        self.req_data[BillingMonitoringLogDTO.END_DATE_TIME] = time_str(d=11)
        with self.assertRaises(InvalidLogDuration):
            CreateBillingMonitoringLogRequestObject.from_dict(self.req_data)

    @freeze_time("2023-07-01")
    def test_user_not_registered_in_logging_period(self):
        self.user.get_user_create_date_time.return_value = datetime(2023, 8, 1)
        with self.assertRaises(PatientNotActive):
            CreateBillingMonitoringLogRequestObject.from_dict(self.req_data)

    @freeze_time("2023-07-01")
    def test_clinician_not_active(self):
        self.clinician.get_user_create_date_time.return_value = datetime(2023, 8, 1)
        with self.assertRaises(ClinicianNotActive):
            CreateBillingMonitoringLogRequestObject.from_dict(self.req_data)

    @freeze_time("2023-07-01")
    def test_end_time_before_start_time(self):
        self.req_data[BillingMonitoringLogDTO.END_DATE_TIME] = time_str(h=11)
        with self.assertRaises(ConvertibleClassValidationError):
            CreateBillingMonitoringLogRequestObject.from_dict(self.req_data)

    @freeze_time("2023-06-01")
    def test_end_time_in_future(self):
        self.req_data[BillingMonitoringLogDTO.END_DATE_TIME] = time_str(h=13)
        with self.assertRaises(ConvertibleClassValidationError):
            CreateBillingMonitoringLogRequestObject.from_dict(self.req_data)


class UpdateMonitoringLogRequestsTestCase(CreateUpdateMonitoringLogTestCase):
    def setUp(self) -> None:
        super().setUp()
        self.req_data.update(
            {
                UpdateBillingMonitoringLogRequestObject.LOG_ID: "64da0e4443c2b2d1a11edf40",
                UpdateBillingMonitoringLogRequestObject.ADDENDUM: "addendum",
            }
        )

    @freeze_time("2023-07-01")
    def test_update_monitoring_log_request(self):
        try:
            UpdateBillingMonitoringLogRequestObject.from_dict(self.req_data)
        except self.VALID_EXCEPTIONS:
            self.fail()

    @freeze_time("2023-07-01")
    def test_invalid_log_id(self):
        self.req_data[BillingMonitoringLogDTO.ID] = "123abc"
        with self.assertRaises(ConvertibleClassValidationError):
            UpdateBillingMonitoringLogRequestObject.from_dict(self.req_data)

    @freeze_time("2023-07-01")
    def test_addendum_missing(self):
        self.req_data.pop(BillingMonitoringLogDTO.ADDENDUM)
        with self.assertRaises(ConvertibleClassValidationError):
            UpdateBillingMonitoringLogRequestObject.from_dict(self.req_data)
