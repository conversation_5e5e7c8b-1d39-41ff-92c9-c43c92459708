import unittest
from unittest.mock import MagicMock

from billing.components.core.helpers.module_result_helpers import (
    PrimitiveSources,
    is_source_manual,
    validate_source,
)
from sdk.common.exceptions.exceptions import InvalidRequestException


class ModuleResultHelpersTestCase(unittest.TestCase):
    def test_manual_source(self):
        sources_to_test = [None, PrimitiveSources.MANUAL.value]
        for source in sources_to_test:
            event = MagicMock(source=source)
            res = is_source_manual(event)
            self.assertTrue(res)

    def test_connected_device_sources(self):
        sources_to_test = [v.value for v in PrimitiveSources if v.value != PrimitiveSources.MANUAL.value]
        for source in sources_to_test:
            event = MagicMock(source=source)
            res = is_source_manual(event)
            self.assertFalse(res)

    def test_validate_source(self):
        acceptable_values = [f"{v.value}; version: X.X.X" for v in PrimitiveSources]
        for value in acceptable_values:
            try:
                validate_source(value)
            except InvalidRequestException:
                self.fail()


if __name__ == "__main__":
    unittest.main()
