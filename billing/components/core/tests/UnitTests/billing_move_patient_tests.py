import unittest
from datetime import datetime
from unittest.mock import MagicMock, patch

from billing.components.core.callbacks.move_patient_callbacks import (
    process_moved_user,
    process_move_for_different_billing_configuration,
)
from billing.components.core.dtos.deployment_billing import (
    DeploymentBillingConfig,
)
from billing.components.core.exceptions import MoveBillingUserException
from billing.components.core.repository.billing_repository import (
    BillingMonitoringLogRepository,
    BillingAlertsRepository,
    BillingSubmissionRepository,
    BillingRemoteTimeTrackingRepository,
)
from sdk.authorization.events.post_move_user_event import PostMoveUserEvent
from sdk.authorization.events.pre_move_user_event import PreMoveUserEvent
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.common.utils import inject

DEPLOYMENT_ID_KEY = "deploymentId"
USER_ID = "5e8f0c74b50aa9656c34789b"
DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
DEPLOYMENT_ID_2 = "5d386cc6ff885918d96edb2b"
CLINICIAN_ID = "64e76adedf047d493ba356c3"

BILLING_ENABLED_PATH = "billing.components.core.callbacks.move_patient_callbacks.is_billing_enabled_for_deployment_id"
GET_BILLING_CONFIG = (
    "billing.components.core.callbacks.move_patient_callbacks.extract_deployment_billing_by_deployment_id"
)
GET_PREVIOUS_BILLING_CONFIG = (
    "billing.components.core.callbacks.move_patient_callbacks.get_previous_enabled_billing_config"
)
GET_CURRENT_CYCLE_PATH = (
    "billing.components.core.callbacks._move_patient_helpers._get_current_submission_cycle_start_dt"
)
SET_USER_BILLING_PATH = "billing.components.core.callbacks._move_patient_helpers.set_billing_for_user"


@patch("billing.components.core.callbacks._move_patient_helpers.transaction", MagicMock())
@patch("billing.components.core.helpers.alerts_helpers.DeploymentService", MagicMock())
class MovePatientCallbackTestCase(unittest.TestCase):
    def setUp(self):
        self.auth_repo = MagicMock()
        self.alert_repo = MagicMock()
        self.submission_repo = MagicMock()
        self.monitoring_repo = MagicMock()
        self.time_tracking_repo = MagicMock()

        def bind(binder):
            binder.bind(AuthorizationRepository, self.auth_repo)
            binder.bind(BillingAlertsRepository, self.alert_repo)
            binder.bind(BillingSubmissionRepository, self.submission_repo)
            binder.bind(BillingMonitoringLogRepository, self.monitoring_repo)
            binder.bind(BillingRemoteTimeTrackingRepository, self.time_tracking_repo)

        inject.clear_and_configure(bind)

        self.pre_event = PreMoveUserEvent(
            user_id=USER_ID,
            resource_id=DEPLOYMENT_ID,
            target_resource_id=DEPLOYMENT_ID_2,
        )
        self.post_event = PostMoveUserEvent(
            user_id=USER_ID,
            resource_id=DEPLOYMENT_ID,
            target_resource_id=DEPLOYMENT_ID_2,
        )
        self.patcher = patch(GET_CURRENT_CYCLE_PATH).start()
        self.patcher.return_value = datetime(2023, 4, 10)

    def tearDown(self) -> None:
        patch.stopall()

    @patch(BILLING_ENABLED_PATH)
    def test_move_data__target_without_billing(self, is_billing_enabled):
        is_billing_enabled.side_effect = [False, True]

        process_moved_user(self.post_event)

        self.alert_repo.start_transaction.assert_not_called()

    @patch(BILLING_ENABLED_PATH)
    def test_success_moved_data(self, is_billing_enabled):
        is_billing_enabled.side_effect = [True, True]

        process_moved_user(self.post_event)

        self.alert_repo.move_existing_alert.assert_called_once()
        self.submission_repo.move_submissions_between_deployments.assert_called_once()
        self.time_tracking_repo.move_time_tracks_between_deployments.assert_called_once()
        self.monitoring_repo.move_logs_between_deployments.assert_called_once()

    @patch(BILLING_ENABLED_PATH)
    def test_roll_back_moved_data_when_error__both_deployments_with_billing(self, is_billing_enabled):
        customException = type("abc", (Exception,), {})
        self.monitoring_repo.move_logs_between_deployments.side_effect = customException()
        is_billing_enabled.side_effect = [True, True]

        with self.assertRaises(customException):
            process_moved_user(self.post_event)

        self.alert_repo.move_existing_alert.assert_called_once()

    @patch(SET_USER_BILLING_PATH, MagicMock())
    @patch(BILLING_ENABLED_PATH)
    def test_roll_back_moved_data_when_error__source_without_billing(self, is_billing_enabled):
        self.alert_repo.move_existing_alert.side_effect = Exception()
        is_billing_enabled.side_effect = [True, False]

        with self.assertRaises(Exception):
            process_moved_user(self.post_event)

        self.auth_repo.update_user_profile.assert_called_once()
        self.alert_repo.move_existing_alert.assert_called_once()

    @patch(GET_BILLING_CONFIG)
    def test_reject_move_when_deployments_have_different_calculation_type(self, get_billing_config):
        calendar_billing = MagicMock(spec=DeploymentBillingConfig, useCalendarCalculation=True)
        thirty_day_billing = MagicMock(spec=DeploymentBillingConfig, useCalendarCalculation=False)
        get_billing_config.side_effect = [calendar_billing, thirty_day_billing]

        with self.assertRaises(MoveBillingUserException):
            process_move_for_different_billing_configuration(self.pre_event)

    @patch(GET_BILLING_CONFIG)
    @patch(GET_PREVIOUS_BILLING_CONFIG)
    def test_reject_move_for_rare_move_sequence(self, get_previous_billing_config, get_billing_config):
        calendar_billing = MagicMock(spec=DeploymentBillingConfig, useCalendarCalculation=True)
        thirty_day_billing = MagicMock(spec=DeploymentBillingConfig, useCalendarCalculation=False)
        get_billing_config.side_effect = [calendar_billing, thirty_day_billing]
        get_previous_billing_config.return_value = calendar_billing

        with self.assertRaises(MoveBillingUserException):
            process_move_for_different_billing_configuration(self.pre_event)
