from dataclasses import field
from datetime import datetime
from enum import Enum

from sdk import convertibleclass
from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.utils.convertible import (
    required_field,
    meta,
    positive_integer_field,
    default_field,
)
from sdk.common.utils.validators import (
    default_datetime_meta,
    validate_entity_name,
    validate_len,
    validate_object_id,
    RE_HEX_COLOR,
    validate_range,
)

BILLING_FEATURES_KEY = "billing"


class DeploymentBillingFileType(Enum):
    INSURANCE_CARRIERS = "insuranceCarriers"
    BILLING_PROVIDERS = "billingProviders"
    ICD_10_CODES = "icd10Codes"


@convertibleclass
class DeploymentBillingFiles:
    TYPE = "type"
    NAME = "name"
    FILE_ID = "fileId"
    UPLOAD_DATETIME = "uploadDateTime"

    type: DeploymentBillingFileType = required_field()
    name: str = required_field(metadata=meta(validate_entity_name))
    fileId: str = required_field(metadata=meta(validate_object_id))
    uploadDateTime: datetime = required_field(metadata=default_datetime_meta())


class BillingProviderFile(Enum):
    ID = "Provider ID"
    NAME = "Name"
    ABBREVIATION = "Abbreviation"
    LOCATION_DEPARTMENT = "LocationDepartment"
    LOCATION_ABBREVIATION = "LocationAbbreviation"
    PLACE_OF_SERVICE_ABBREVIATION = "PlaceOfServiceAbbreviation"


class ProductType(Enum):
    RPM = "RPM"
    RTM = "RTM"


class ColorNames:
    WHITE = "White"
    GREY = "Grey"
    RED = "Red"
    AMBER = "Amber"
    LIGHT_RED = "LightRed"


class ColorHex:
    WHITE = "#FFFFFF"
    GREY = "#EBEBEB"
    RED = "#FBCCD7"
    AMBER = "#FFDA9F"
    LIGHT_RED = "#FDE5EB"


def validate_color_name(name):
    if name not in [
        ColorNames.WHITE,
        ColorNames.RED,
        ColorNames.GREY,
        ColorNames.AMBER,
        ColorNames.LIGHT_RED,
    ]:
        raise InvalidRequestException("Color name is not correct.")
    return name


def validate_color_hex(value):
    if not RE_HEX_COLOR.match(value):
        raise InvalidRequestException("Color hex is not correct.")
    return value


@convertibleclass
class Color:
    COLOR = "color"
    NAME = "name"

    color: str = required_field(metadata=meta(validate_color_hex))
    name: str = required_field(metadata=meta(validate_color_name))


@convertibleclass
class BillingSubmissionReminderSchedule:
    """byHour and byMinute must represent desired Local Time when user should be notified"""

    syncThreshold: int = positive_integer_field(metadata=meta(required=True))
    interval: int = positive_integer_field(metadata=meta(required=True))
    count: int = positive_integer_field(metadata=meta(required=True))
    byHour: int = field(default=10, metadata=meta(validate_range(0, 24)))
    byMinute: int = field(default=0, metadata=meta(validate_range(0, 60)))


@convertibleclass
class BillingSubmissionReminder:
    ENABLED = "enabled"
    SCHEDULE = "schedule"

    enabled: bool = required_field(default=False)
    schedule: BillingSubmissionReminderSchedule = required_field()

    def __bool__(self):
        return self.enabled


@convertibleclass
class DeploymentBillingConfig:
    ENABLED = "enabled"
    PRODUCT_TYPE = "productType"
    FILES = "files"
    MANUAL_APPOINTMENT = "manualAppointment"
    COLORS = "colors"
    RTM_MODULE_IDS = "rtmModuleIds"
    USE_CALENDAR_CALCULATION = "useCalendarCalculation"
    SUBMISSION_REMINDER = "submissionReminder"

    enabled: bool = field(default=False)
    productType: ProductType = required_field()
    files: list[DeploymentBillingFiles] = required_field(metadata=meta(validate_len(3)))
    manualAppointment: bool = field(default=False)
    colors: list[Color] = field(default=None)
    rtmModuleIds: list[str] = field(default=None)
    useCalendarCalculation: bool = field(default=False)
    submissionReminder: BillingSubmissionReminder = default_field()

    @classmethod
    def validate(cls, instance):
        default_colors = {
            ColorNames.WHITE: ColorHex.WHITE,
            ColorNames.GREY: ColorHex.GREY,
            ColorNames.RED: ColorHex.RED,
            ColorNames.LIGHT_RED: ColorHex.LIGHT_RED,
            ColorNames.AMBER: ColorHex.AMBER,
        }
        if not instance.colors:
            instance.colors = [
                Color(color=color_hex, name=color_name) for color_name, color_hex in default_colors.items()
            ]
        required_colors = [color_name for color_name in default_colors.keys()]
        for color_name in required_colors:
            if color_name not in [color.name for color in instance.colors]:
                instance.colors.append(Color(color=default_colors[color_name], name=color_name))
