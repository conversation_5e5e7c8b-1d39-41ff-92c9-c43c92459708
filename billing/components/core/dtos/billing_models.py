from dataclasses import field
from datetime import date, datetime
from enum import IntEnum

from billing.components.core.statics.billing_monitoring_log_action import (
    BillingMonitoringLogActionKeys,
)
from billing.components.core.validators import default_date_meta
from sdk import convertibleclass
from sdk.authorization.dtos.user import UserDTO, UserLabel
from sdk.common.utils.convertible import default_field, meta, required_field
from sdk.common.utils.validators import (
    default_datetime_meta,
    validate_entity_name,
    validate_len,
    validate_object_id,
)


@convertibleclass
class BillingRemoteTimeTrackingDTO:
    ID = "id"
    DEPLOYMENT_ID = "deploymentId"
    CLINICIAN_ID = "clinicianId"
    USER_ID = "userId"
    START_DATE_TIME = "startDateTime"
    END_DATE_TIME = "endDateTime"
    CREATE_DATE_TIME = "createDateTime"
    EFFECTIVE_START_DATE_TIME = "effectiveStartDateTime"
    EFFECTIVE_END_DATE_TIME = "effectiveEndDateTime"
    EFFECTIVE_DURATION = "effectiveDuration"

    id: str = default_field(metadata=meta(validate_object_id, value_to_field=str))
    deploymentId: str = required_field(metadata=meta(validate_object_id))
    clinicianId: str = required_field(metadata=meta(validate_object_id))
    userId: str = required_field(metadata=meta(validate_object_id))
    startDateTime: datetime = field(default_factory=datetime.utcnow, metadata=default_datetime_meta())
    endDateTime: datetime = field(default_factory=datetime.utcnow, metadata=default_datetime_meta())
    createDateTime: datetime = default_field(metadata=default_datetime_meta())
    effectiveStartDateTime: datetime = default_field(metadata=default_datetime_meta())
    effectiveEndDateTime: datetime = default_field(metadata=default_datetime_meta())
    effectiveDuration: float = default_field()


@convertibleclass
class BillingSubmissionDTO:
    DEPLOYMENT_ID = "deploymentId"
    PRIMITIVE_ID = "primitiveId"
    PRIMITIVE_CLASS_NAME = "primitiveClassName"
    USER_ID = "userId"
    DEVICE_NAME = "deviceName"
    DEVICE_DETAILS = "deviceDetails"
    SOURCE = "source"
    START_DATE_TIME = "startDateTime"
    START_DATE_TIME_UTC = "startDateTimeUTC"
    START_DATE = "startDate"
    CREATE_DATE_TIME = "createDateTime"
    IS_COMPLETED = "isCompleted"

    deploymentId: str = required_field(metadata=meta(validate_object_id))
    primitiveId: str = required_field(metadata=meta(validate_object_id))
    primitiveClassName: str = required_field(metadata=meta(validate_entity_name))
    userId: str = required_field(metadata=meta(validate_object_id))
    deviceName: str = required_field(metadata=meta(validate_entity_name))
    deviceDetails: str = default_field(metadata=meta(validate_entity_name))
    source: str = default_field(default="Manual")
    startDateTime: datetime = field(default_factory=datetime.utcnow, metadata=default_datetime_meta())
    startDateTimeUTC: datetime = field(default_factory=datetime.utcnow, metadata=default_datetime_meta())
    startDate: date = required_field(metadata=default_date_meta())
    isCompleted: bool = default_field()
    createDateTime: datetime = default_field(metadata=default_datetime_meta())


class BillingAlertsColorCoding:
    WHITE = 0
    GREY = 1
    AMBER = 2
    RED = 3
    LIGHT_RED = 4


@convertibleclass
class CPTCompliance:
    VALUE = "value"
    COLOR_CODE = "colorCode"

    value: float = default_field()
    colorCode: int = default_field()


@convertibleclass
class BillingAlertsDTO:
    USER_ID = "userId"
    DEPLOYMENT_ID = "deploymentId"
    GIVEN_NAME = "givenName"
    FAMILY_NAME = "familyName"
    GENDER = "gender"
    BIOLOGICAL_SEX = "biologicalSex"
    DATE_OF_BIRTH = "dateOfBirth"
    SUBMISSION_COUNT = "submissionCount"
    SUBMISSION_DAYS_LEFT = "submissionDaysLeft"
    MONITORING_MINUTES = "monitoringMinutes"
    HAS_CALL = "hasCall"
    CALL_DATETIME = "callDateTime"
    CPT_99454_COMPLIANCE = "cpt99454Compliance"
    CPT_99457_COMPLIANCE = "cpt99457Compliance"
    CPT_99458_COMPLIANCE = "cpt99458Compliance"
    CPT_99458X2_COMPLIANCE = "cpt99458x2Compliance"
    CPT_98976_COMPLIANCE = "cpt98976Compliance"
    CPT_98980_COMPLIANCE = "cpt98980Compliance"
    CPT_98981_COMPLIANCE = "cpt98981Compliance"
    CPT_98981X2_COMPLIANCE = "cpt98981x2Compliance"
    CREATE_DATETIME = "createDateTime"
    UPDATE_DATETIME = "updateDateTime"
    NEXT_SUBMISSION_DOS = "nextSubmissionDoS"
    LAST_MONITORING_DATE = "lastMonitoringDate"
    SUBMISSION_DATES = "submissionDates"
    NEXT_MONITORING_DOS = "nextMonitoringDoS"
    LABELS = "labels"
    LAST_SYNCED_DATE = "lastSyncedDate"
    LAST_SUBMIT_DATETIME = "lastSubmitDateTime"

    userId: str = required_field(metadata=meta(validate_object_id, value_to_field=str))
    deploymentId: str = default_field(metadata=meta(validate_object_id, value_to_field=str))
    givenName: str = default_field(metadata=meta(validate_entity_name))
    familyName: str = default_field(metadata=meta(validate_entity_name))
    gender: UserDTO.Gender = default_field()
    biologicalSex: UserDTO.BiologicalSex = default_field()
    dateOfBirth: date = default_field(metadata=default_date_meta())
    submissionCount: int = default_field()
    submissionDaysLeft: int = default_field()
    monitoringMinutes: float = default_field()
    hasCall: bool = default_field()
    callDateTime: datetime = default_field(metadata=default_datetime_meta())
    cpt99454Compliance: CPTCompliance = default_field()
    cpt99457Compliance: CPTCompliance = default_field()
    cpt99458Compliance: CPTCompliance = default_field()
    cpt99458x2Compliance: CPTCompliance = default_field()
    cpt98976Compliance: CPTCompliance = default_field()
    cpt98980Compliance: CPTCompliance = default_field()
    cpt98981Compliance: CPTCompliance = default_field()
    cpt98981x2Compliance: CPTCompliance = default_field()
    createDateTime: datetime = default_field(metadata=default_datetime_meta())
    updateDateTime: datetime = default_field(metadata=default_datetime_meta())
    nextSubmissionDoS: date = default_field(metadata=default_date_meta())
    lastMonitoringDate: date = default_field(metadata=default_date_meta())
    submissionDates: list[datetime] = default_field()
    nextMonitoringDoS: date = default_field(metadata=default_date_meta())
    labels: list[UserLabel] = default_field()
    lastSyncedDate: datetime = default_field(metadata=default_datetime_meta())
    lastSubmitDateTime: datetime = default_field(metadata=default_datetime_meta())

    _identifiers = (GIVEN_NAME, FAMILY_NAME, DATE_OF_BIRTH)

    @classmethod
    def get_identifiable_fields(cls):
        return cls._identifiers


@convertibleclass
class BillingMonitoringLogDTO:
    class BillingMonitoringLogStatus(IntEnum):
        ACTIVE = 0
        EDITED = 1
        DELETED = 2

    ID = "id"
    ORIGINAL_LOG_ID = "originalLogId"
    DEPLOYMENT_ID = "deploymentId"
    CLINICIAN_ID = "clinicianId"
    CREATED_BY_ID = "createdById"
    LAST_MODIFIED_BY_ID = "lastModifiedById"
    USER_ID = "userId"
    TIME_TRACKING_ID = "timeTrackingId"
    STATUS = "status"
    ACTION = "action"
    ADDENDUM = "addendum"
    TIME_SPENT = "timeSpent"
    START_DATE_TIME = "startDateTime"
    END_DATE_TIME = "endDateTime"
    INITIAL_CREATE_DATE_TIME = "initialCreateDateTime"
    CREATE_DATE_TIME = "createDateTime"
    UPDATE_DATE_TIME = "updateDateTime"

    id: str = default_field(metadata=meta(validate_object_id, value_to_field=str))
    originalLogId: str = default_field(metadata=meta(validate_object_id, value_to_field=str))
    deploymentId: str = default_field(metadata=meta(validate_object_id, value_to_field=str))
    createdById: str = default_field(metadata=meta(validate_object_id, value_to_field=str))
    lastModifiedById: str = default_field(metadata=meta(validate_object_id, value_to_field=str))
    userId: str = default_field(metadata=meta(validate_object_id, value_to_field=str))
    timeTrackingId: str = default_field(metadata=meta(validate_object_id, value_to_field=str))
    status: BillingMonitoringLogStatus = default_field(metadata=meta(by_value=True))
    action: BillingMonitoringLogActionKeys = default_field(
        metadata=meta(field_to_value=lambda x: x.value if x else None)
    )
    addendum: str = default_field(metadata=meta(validate_len(1, 500)))
    timeSpent: float = default_field()
    startDateTime: datetime = default_field(metadata=default_datetime_meta())
    endDateTime: datetime = default_field(metadata=default_datetime_meta())
    initialCreateDateTime: datetime = default_field(metadata=default_datetime_meta())
    createDateTime: datetime = default_field(metadata=default_datetime_meta())
    updateDateTime: datetime = default_field(metadata=default_datetime_meta())

    def post_init(self):
        self.timeSpent = (self.endDateTime - self.startDateTime).total_seconds()


@convertibleclass
class ResponseBillingMonitoringLogDTO(BillingMonitoringLogDTO):
    ACTION = "action"
    CREATED_BY_NAME = "createdByName"
    LAST_MODIFIED_BY_NAME = "lastModifiedByName"

    action: str = required_field()
    createdByName: str = required_field()
    lastModifiedByName: str = required_field()


@convertibleclass
class BillingProfileHistoryDTO:
    USER_ID = "userId"
    DEPLOYMENT_ID = "deploymentId"
    CREATE_DATETIME = "createDateTime"

    userId: str = required_field(metadata=meta(validate_object_id))
    deploymentId: str = required_field(metadata=meta(validate_object_id))
    createDateTime: datetime = required_field()


@convertibleclass
class BillingDiagnosisHistoryLogDTO(BillingProfileHistoryDTO):
    ORDER = "order"
    DESCRIPTION = "description"
    ICD10CODE = "icd10Code"

    order: int = required_field()
    description: str = required_field()
    icd10Code: str = required_field()


@convertibleclass
class BillingInsuranceCarrierProfileHistoryDTO(BillingProfileHistoryDTO):
    ORDER = "order"
    PAYER_ID = "payerId"
    GROUP_NAME = "groupName"

    order: int = required_field()
    payerId: str = required_field()
    groupName: str = required_field()


@convertibleclass
class BillingProviderHistoryLogDTO(BillingProfileHistoryDTO):
    BILLING_PROVIDER_ID = "billingProviderId"
    BILLING_PROVIDER_NAME = "billingProviderName"

    billingProviderId: str = default_field()
    billingProviderName: str = required_field()
