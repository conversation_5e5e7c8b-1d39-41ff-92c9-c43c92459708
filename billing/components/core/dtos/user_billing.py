from enum import IntEnum

from billing.components.core.validators import FileDownload
from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.utils.convertible import convertibleclass, default_field, meta, natural_number_field, required_field
from sdk.common.utils.validators import (
    validate_entity_name,
    validate_len,
    validate_object_id,
)

MAX_FILE_SIZE = 1024 * 1024 * 100


@convertibleclass
class InsuranceCarrier:
    ORDER = "order"
    GROUP_NAME = "groupName"
    PAYER_ID = "payerId"

    order: int = natural_number_field(default=1)
    groupName: str = default_field(metadata=meta(validate_entity_name))
    payerId: str = default_field(metadata=meta(validate_entity_name))


@convertibleclass
class Diagnosis:
    ORDER = "order"
    ICD_CODE = "icd10Code"
    DESCRIPTION = "description"

    order: int = natural_number_field(default=1)
    icd10Code: str = default_field(metadata=meta(validate_len(0, 7)))
    description: str = default_field(metadata=meta(validate_entity_name))


@convertibleclass
class UserBillingFile:
    NAME = "name"
    FILE_ID = "fileId"

    name: str = required_field()
    fileId: str = required_field()


@convertibleclass
class UserBilling:
    STATUS = "status"
    INSURANCE_CARRIERS = "insuranceCarriers"
    BILLING_PROVIDER_ID = "billingProviderId"
    BILLING_PROVIDER_NAME = "billingProviderName"
    DIAGNOSIS = "diagnosis"
    UPDATE_DT = "updateDateTime"
    CREATE_DT = "createDateTime"
    PREVIOUS_STATE = "previousState"
    FILE = "file"
    MRN = "mrn"

    class BillingStatus(IntEnum):
        PENDING = 0
        COMPLETED = 1
        NOT_APPLICABLE = 2

    status: BillingStatus = default_field(default=BillingStatus.PENDING.value)
    insuranceCarriers: list[InsuranceCarrier] = default_field()
    billingProviderId: str = default_field()
    billingProviderName: str = default_field(metadata=meta(validate_entity_name))
    diagnosis: Diagnosis = default_field()
    updateDateTime: str = default_field()
    createDateTime: str = default_field()
    previousState: BillingStatus = default_field()
    file: UserBillingFile = default_field()
    mrn: str = default_field(metadata=meta(validate_len(0, 20)))

    @classmethod
    def validate(cls, instance):
        if instance.file:
            if not validate_object_id(instance.file.fileId):
                raise InvalidRequestException("File Id is not valid")
            if instance.file.fileId:
                file = FileDownload(instance.file.fileId)
                if not 0 < len(instance.file.name) <= 256:
                    raise InvalidRequestException("File name should be at least 1 and at most 256 characters")
                if file.size > MAX_FILE_SIZE:
                    raise InvalidRequestException(f"File size should not exceed {MAX_FILE_SIZE / 1024 / 1024} MB")
                if file.content_type != "application/pdf":
                    raise InvalidRequestException("Only PDF file are acceptable for now")
