import logging
from dataclasses import dataclass

import i18n

from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.services.authorization import AuthorizationService
from sdk.calendar.dtos.calendar_event import CalendarEventDTO
from sdk.common.push_notifications.push_notifications_utils import (
    prepare_and_send_push_notification,
)

logger = logging.getLogger(__name__)


@dataclass
class BillingSubmissionScheduleEvent(CalendarEventDTO):
    """Calendar event to track user billing submission activity."""

    ACTION = "BILLING_NOT_SYNCED"

    def execute(self, run_async=True) -> None:
        user = AuthorizationService().retrieve_simple_user_profile(self.userId)
        logger.debug(f"Sending {self.ACTION} notification for #{self.userId}")
        self._send_notification(user)

    def pack_extra_fields(self):
        pass

    def _send_notification(self, user):
        language = AuthorizedUser(user).get_language()
        title = i18n.t("Billing.Submission.Reminder.title", locale=language)
        body = i18n.t("Billing.Submission.Reminder.body", locale=language)

        notification_data = {"action": self.ACTION}
        prepare_and_send_push_notification(
            self.userId,
            self.ACTION,
            {"title": title, "body": body},
            notification_data,
            run_async=True,
        )
