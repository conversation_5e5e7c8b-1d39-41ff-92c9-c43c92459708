import logging
from datetime import datetime

from billing.components.core.dtos.deployment_billing import (
    DeploymentBillingConfig,
    BILLING_FEATURES_KEY,
    DeploymentBillingFileType,
    DeploymentBillingFiles,
)
from billing.components.core.dtos.user_billing import UserBilling
from billing.components.core.repository.billing_repository import (
    BillingAlertsRepository,
)
from billing.components.core.validators import validate_deployment_billing_file
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.utils.inject import autoparams
from sdk.deployment.dtos.deployment import DeploymentDTO
from sdk.deployment.events import (
    PreCreateDeploymentEvent,
    PreDeploymentUpdateEvent,
)

logger = logging.getLogger(__name__)


def process_pre_create_deployment(event: PreCreateDeploymentEvent):
    if not _is_billing_in_deployment(event.deployment):
        return

    _process_deployment_billing_data(event.deployment.features.customAppConfig.get(BILLING_FEATURES_KEY))


def process_pre_update_deployment(event: PreDeploymentUpdateEvent):
    deployment_id = event.deployment.id
    event_billing_dict = _extract_billing_dict(event.deployment)
    if not event_billing_dict:
        return

    previous_billing_dict = _extract_billing_dict(event.previous_state)
    if event_billing_dict == previous_billing_dict:
        return

    process_billing_calculation_type_change(deployment_id, previous_billing_dict, event_billing_dict)
    _process_deployment_billing_data(event_billing_dict)
    _set_billing_to_users(event.deployment.id)


@autoparams("alert_repo")
def _set_billing_to_users(deployment_id: str, alert_repo: BillingAlertsRepository):
    _add_billing_to_deployment_user(deployment_id=deployment_id)
    alert_repo.add_users_to_billing_alerts(deployment_id=deployment_id)


@autoparams("auth_repo")
def _add_billing_to_deployment_user(deployment_id: str, auth_repo: AuthorizationRepository):
    user_ids = auth_repo.retrieve_user_ids_in_deployment(deployment_id=deployment_id)
    users = auth_repo.retrieve_simple_user_profiles_by_ids(set(user_ids), force_fetch_all=False)
    updated_users = []
    dt_now = str(datetime.utcnow())
    for user in users:
        if not user.componentsData:
            user.componentsData = {}
        if not user.componentsData.get(BILLING_FEATURES_KEY):
            user.componentsData[BILLING_FEATURES_KEY] = {}
        user.componentsData[BILLING_FEATURES_KEY].update(
            {
                "status": UserBilling.BillingStatus.PENDING,
                "createDateTime": dt_now,
                "updateDateTime": dt_now,
            }
        )
        updated_users.append(user)
    if not updated_users:
        return
    auth_repo.update_user_profiles(updated_users)


def _process_deployment_billing_data(billing_dict: dict):
    _set_files_upload_dt(billing_dict.get(DeploymentBillingConfig.FILES))
    _validate_deployment_billing_features(billing_dict)


def _is_billing_in_deployment(deployment: DeploymentDTO):
    return (
        deployment.features
        and deployment.features.customAppConfig
        and deployment.features.customAppConfig.get(BILLING_FEATURES_KEY)
    )


def _set_files_upload_dt(files: list[dict]):
    if not files:
        return

    for file in files:
        if not isinstance(file, dict):
            raise InvalidRequestException("File should be an object")
        file[DeploymentBillingFiles.UPLOAD_DATETIME] = datetime.utcnow()


def _validate_deployment_billing_features(billing_dict: dict):
    billing = DeploymentBillingConfig.from_dict(billing_dict)
    uploaded_file_types = {f.type.value for f in billing.files}
    file_types_to_exist = {f.value for f in DeploymentBillingFileType}
    if uploaded_file_types != file_types_to_exist:
        raise InvalidRequestException(
            f"All 3 files types should be provided: f{file_types_to_exist}. You provided: [{uploaded_file_types}]"
        )

    for config_file in billing.files:
        validate_deployment_billing_file(config_file.type, config_file.fileId)


def _extract_billing_dict(deployment: DeploymentDTO):
    if not deployment.features or not deployment.features.customAppConfig:
        return None

    return deployment.features.customAppConfig.get(BILLING_FEATURES_KEY)


def process_billing_calculation_type_change(
    deployment_id: str, previous_billing_dict: dict | None, event_billing_dict: dict
):
    key_ = DeploymentBillingConfig.USE_CALENDAR_CALCULATION
    current_calculation_type = event_billing_dict.get(key_, False)

    if previous_billing_dict is None:
        if current_calculation_type is True:
            # Prevents wrong data in case:  30-day -> None -> Calendar
            _transit_from_30_day_to_calendar_calculation(deployment_id)
        return

    previous_calculation_type = previous_billing_dict.get(key_, False)
    if previous_calculation_type != current_calculation_type:
        _prevent_change_of_calculation_type(previous_calculation_type, current_calculation_type)
        _transit_from_30_day_to_calendar_calculation(deployment_id)


def _prevent_change_of_calculation_type(previous_type: bool, current_type: bool):
    if previous_type is True and current_type is False:
        msg = "Billing submission calculation type cannot be changed from Calendar to 30-day"
        raise InvalidRequestException(msg)


@autoparams("alerts_repo")
def _transit_from_30_day_to_calendar_calculation(deployment_id: str, alerts_repo: BillingAlertsRepository):
    logger.info(
        f"Calculation type changed to Calendar for deployment: {deployment_id}. " f"Fixing submission dates for users"
    )
    updated_alerts = alerts_repo.fix_submission_dates_for_deployment_users(deployment_id=deployment_id)
    logger.info(f"Submission dates fixed for {updated_alerts} in deployment: {deployment_id}")
