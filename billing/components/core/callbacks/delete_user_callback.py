import logging

from billing.components.core.router.billing_requests import (
    DeleteUserBillingRequestObject,
)
from billing.components.core.use_case.billing_use_cases import (
    DeleteBillingUseCase,
)
from sdk.auth.events.delete_user_event import DeleteUser<PERSON>vent
from sdk.common.monitoring import report_exception

logger = logging.getLogger(__name__)


def delete_user_billing_on_user_delete_event(event: DeleteUserEvent) -> dict[str, int]:
    """Delete user billing data when user is deleted"""
    request_object = DeleteUserBillingRequestObject(userId=event.user_id)
    use_case = DeleteBillingUseCase()
    logger.info(f"User delete event has been triggered. Deleting user billing for user with ID {event.user_id}")
    try:
        deleted_count = use_case.execute(request_object)
    except Exception as error:
        report_exception(
            error,
            context_name="DeleteUserBilling",
            context_content={"userId": event.user_id},
        )
        raise error
    return {"Billing": deleted_count}
