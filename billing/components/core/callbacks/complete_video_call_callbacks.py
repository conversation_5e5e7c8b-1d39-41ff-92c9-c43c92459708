from billing.components.core.repository.billing_repository import (
    BillingRemoteTimeTrackingRepository,
)
from billing.components.core.tasks import (
    VideoCallCompletionEvent,
    process_video_call_completion,
)
from sdk.auth.events.complete_video_call_event import CompleteVideoCallEvent
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.common.utils.inject import autoparams


@autoparams("repo", "auth_repo")
def completed_video_callback(
    event: CompleteVideoCallEvent,
    repo: BillingRemoteTimeTrackingRepository,
    auth_repo: AuthorizationRepository,
):
    user = auth_repo.retrieve_simple_user_profile(user_id=event.user_id)
    authz_user = AuthorizedUser(user)

    repo.create_billing_remote_time_tracking(
        authz_user.deployment.id,
        event.manager_id,
        event.start_date_time,
        event.end_date_time,
        event.user_id,
    )

    process_video_call_completion.delay(
        VideoCallCompletionEvent.from_dict(
            {
                VideoCallCompletionEvent.USER_ID: event.user_id,
                VideoCallCompletionEvent.MANAGER_ID: event.manager_id,
                VideoCallCompletionEvent.START_DATE_TIME: event.start_date_time,
                VideoCallCompletionEvent.END_DATE_TIME: event.end_date_time,
                VideoCallCompletionEvent.STATUS: event.extra_data.get("status") if event.extra_data else None,
            }
        ).to_dict()
    )
