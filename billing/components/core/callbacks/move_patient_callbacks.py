import logging

from billing.components.core.callbacks._move_patient_helpers import (
    move_billing_data_if_source_has_no_billing,
    move_billing_data_if_source_has_billing,
    get_previous_enabled_billing_config,
)
from billing.components.core.exceptions import MoveBillingUserException
from billing.components.core.helpers.alerts_helpers import (
    is_billing_enabled_for_deployment_id,
    extract_deployment_billing_by_deployment_id,
)
from billing.components.core.repository.billing_repository import (
    BillingAlertsRepository,
)
from sdk.authorization.events.post_move_user_event import PostMoveUserEvent
from sdk.authorization.events.pre_move_user_event import PreMoveUserEvent
from sdk.common.utils.inject import autoparams

logger = logging.getLogger(__name__)


def process_move_for_different_billing_configuration(event: PreMoveUserEvent):
    """Prevents moving users between deployments with different Billing Calculation Type."""
    source_billing_config = extract_deployment_billing_by_deployment_id(event.resource_id)
    target_billing_config = extract_deployment_billing_by_deployment_id(event.target_resource_id)
    if not target_billing_config or not target_billing_config.enabled:
        return

    if not source_billing_config or not source_billing_config.enabled:
        latest_billing_config = get_previous_enabled_billing_config(event.user_id)
        if not latest_billing_config:
            return

        if latest_billing_config.useCalendarCalculation != target_billing_config.useCalendarCalculation:
            raise MoveBillingUserException
        else:
            return

    if source_billing_config.useCalendarCalculation != target_billing_config.useCalendarCalculation:
        raise MoveBillingUserException


@autoparams("billing_repo")
def process_moved_user(event: PostMoveUserEvent, billing_repo: BillingAlertsRepository):
    source_id, target_id = event.resource_id, event.target_resource_id
    if not is_billing_enabled_for_deployment_id(target_id):
        msg = f"Target deployment {target_id} has no billing enabled, keeping all user data intact"
        logger.info(msg)
        return

    logger.info("Start moving user billing data")

    if not is_billing_enabled_for_deployment_id(source_id):
        move_billing_data_if_source_has_no_billing(billing_repo, event.user_id, target_id)
    else:
        move_billing_data_if_source_has_billing(billing_repo, event.user_id, source_id, target_id)

    logger.info("Moving user billing data completed")
