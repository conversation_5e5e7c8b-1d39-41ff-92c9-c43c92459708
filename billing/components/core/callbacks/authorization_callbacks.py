import csv
import io
import logging
from datetime import datetime
from typing import Optional

from billing.components.core.dtos.deployment_billing import (
    BILLING_FEATURES_KEY,
    DeploymentBillingConfig,
    DeploymentBillingFileType,
)
from billing.components.core.dtos.user_billing import (
    Diagnosis,
    InsuranceCarrier,
    UserBilling,
)
from billing.components.core.helpers.alerts_helpers import (
    get_deployment_billing_object,
    validate_deployment_billing_enabled,
)
from billing.components.core.helpers.billing_profile_helpers import (
    generate_user_billing_profile_history_logs,
    set_update_and_create_date_time_to_user_profile_data,
)
from billing.components.core.repository.billing_repository import (
    BillingAlertsRepository,
)
from billing.components.core.validators import FileDownload
from sdk.authorization.di.components import PreCreateUserEvent
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.dtos.user import UserDTO
from sdk.authorization.events import (
    PostUserOffBoardEvent,
    PostUserReactivationEvent,
    PreUserProfileUpdateEvent,
)
from sdk.authorization.events.post_user_onboard_event import UserOnboardedEvent
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.authorization.services.authorization import AuthorizationService
from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.utils.common_functions_utils import deep_get
from sdk.common.utils.convertible import ConvertibleClassValidationError
from sdk.common.utils.inject import autoparams
from sdk.deployment.service.deployment_service import DeploymentService

logger = logging.getLogger(__name__)

BILLING_KEY = BILLING_FEATURES_KEY


def process_user_onboarding(event: UserOnboardedEvent):
    _create_user_billing_alerts(event.user_id)


def process_pre_update_user(event: PreUserProfileUpdateEvent):
    user, previous_state = event.user, event.previous_state

    if (
        _is_valid_user(user.id)
        and _is_billing_data_changed(user, previous_state)
        and _is_valid_user_billing_object(user.componentsData)
    ):
        _validate_deployment_billing_data_on_user_update(user, previous_state)
        _set_billing_update_dt_on_user_update(user, previous_state)


def process_pre_create_user(event: PreCreateUserEvent):
    authz_user = AuthorizedUser(event.user)
    if not authz_user.is_user():
        return

    deployment = DeploymentService().retrieve_deployment(authz_user.deployment_id())
    validate_deployment_billing_enabled(deployment)
    set_billing_for_user(event.user)


def process_reactivate_user(event: PostUserReactivationEvent):
    user_id = event.user_id
    _update_user_billing_status(user_id)
    _reactivate_user_billing_alert(user_id)


def process_offboard_user(event: PostUserOffBoardEvent):
    user_id = event.user_id
    _update_user_billing_status_offboarding(user_id)
    _delete_user_billing_alerts(user_id)


def _is_valid_user(user_id: str):
    user = AuthorizationService().retrieve_simple_user_profile(user_id)
    return AuthorizedUser(user).is_user()


@autoparams("auth_repo")
def _update_user_billing_status_offboarding(user_id: str, auth_repo: AuthorizationRepository):
    user = auth_repo.retrieve_user(user_id=user_id)
    authz_user = AuthorizedUser(user)
    billing_config = get_deployment_billing_object(authz_user.deployment)
    if not billing_config:
        return

    if not user.componentsData:
        user.componentsData = {}

    user_billing = user.componentsData.get(BILLING_KEY)
    if user_billing:
        user.componentsData[BILLING_KEY] = {
            **user_billing,
            UserBilling.PREVIOUS_STATE: user_billing[UserBilling.STATUS],
        }
    else:
        user.componentsData[BILLING_KEY] = {UserBilling.PREVIOUS_STATE: None}

    user.componentsData[BILLING_KEY][UserBilling.STATUS] = UserBilling.BillingStatus.NOT_APPLICABLE
    auth_repo.update_user_profile(user)


@autoparams("auth_repo")
def _update_user_billing_status(user_id: str, auth_repo: AuthorizationRepository):
    user = auth_repo.retrieve_user(user_id=user_id)
    if not user.componentsData:
        return

    authz_user = AuthorizedUser(user)
    billing_config = get_deployment_billing_object(authz_user.deployment)
    if not billing_config:
        return

    user_billing = user.componentsData.get(BILLING_KEY)
    if user_billing:
        user.componentsData[BILLING_KEY] = {
            **user_billing,
            UserBilling.STATUS: user_billing.get(UserBilling.PREVIOUS_STATE),
            UserBilling.PREVIOUS_STATE: UserBilling.BillingStatus.NOT_APPLICABLE,
        }
        auth_repo.update_user_profile(user)


@autoparams("alert_repo")
def _reactivate_user_billing_alert(user_id: str, alert_repo: BillingAlertsRepository):
    logger.info(f"Reactivation or Adding billing alerts for user {user_id}")
    result = alert_repo.reactivate_user_billing_alerts(user_id)
    if result:
        logger.info(f"Billing alerts reactivated for user {user_id}")
        return

    msg = f"Billing alerts should be added for user {user_id} - user not found in billing alerts"
    logger.info(msg)
    _create_user_billing_alerts(user_id)


@autoparams("auth_repo", "alert_repo")
def _create_user_billing_alerts(
    user_id: str,
    alert_repo: BillingAlertsRepository,
    auth_repo: AuthorizationRepository,
):
    logger.info(f"Adding billing alerts for user {user_id}")
    user = auth_repo.retrieve_simple_user_profile(user_id=user_id)
    auth_user = AuthorizedUser(user)
    if not auth_user.is_user():
        logger.info(f"user {user_id} is not a patient, skipping")
        result = alert_repo.delete_user_billing_alerts(user_id=auth_user.id)
        if result:
            logger.info(f"Billing alerts deleted for user {user_id} - {result}")
        return

    user_fields = {}
    alert_repo.update_create_user_billing_alert(user.id, auth_user.deployment_id(), user_fields)
    logger.info(f"Billing alerts added for user {user_id}")


@autoparams("alert_repo")
def _delete_user_billing_alerts(user_id: str, alert_repo: BillingAlertsRepository):
    logger.info(f"Removing billing alerts for user {user_id}")
    result = alert_repo.delete_user_billing_alerts(user_id)
    logger.info(f"Billing alerts removed for user {user_id} - {result}")


def _is_billing_data_changed(user: UserDTO, previous_state: UserDTO):
    data, previous_data = (
        user.componentsData,
        (previous_state.componentsData if previous_state else None),
    )
    return data and previous_data and data.get(BILLING_KEY) != previous_data.get(BILLING_KEY)


def _is_valid_user_billing_object(components_data: dict) -> bool:
    try:
        UserBilling.from_dict(components_data[BILLING_KEY])
    except (KeyError, ConvertibleClassValidationError):
        return False

    return True


def _set_billing_update_dt_on_user_update(user: UserDTO, previous_state: UserDTO):
    update_dt = datetime.utcnow()
    set_update_and_create_date_time_to_user_profile_data(user, previous_state, update_dt)
    generate_user_billing_profile_history_logs(user, previous_state, update_dt)


def set_billing_for_user(user: UserDTO):
    components_data = user.componentsData or {}
    if BILLING_KEY not in components_data:
        components_data[BILLING_KEY] = {}
    components_data[BILLING_KEY][UserBilling.STATUS] = UserBilling.BillingStatus.PENDING
    user.componentsData = components_data


def _validate_deployment_billing_data_on_user_update(user: UserDTO, pre_user: UserDTO = None):
    should_check_pre_state = pre_user is not None
    pre_user_billing = (
        pre_user.componentsData.get(BILLING_KEY) if should_check_pre_state and pre_user.componentsData else {}
    )

    authz_user = AuthorizedUser(AuthorizationService().retrieve_simple_user_profile(user.id))

    deployment = authz_user.deployment
    validate_deployment_billing_enabled(deployment)
    billing = DeploymentBillingConfig.from_dict(deployment.features.customAppConfig.get(BILLING_FEATURES_KEY))
    deployment_billing_files = [f for f in billing.files]
    file_name_file_info_mapping = {
        UserBilling.BILLING_PROVIDER_NAME: DeploymentBillingFileType.BILLING_PROVIDERS,
        UserBilling.INSURANCE_CARRIERS: DeploymentBillingFileType.INSURANCE_CARRIERS,
        UserBilling.DIAGNOSIS: DeploymentBillingFileType.ICD_10_CODES,
    }
    user_billing = user.componentsData.get(BILLING_KEY)
    for key, billing_field_value in user_billing.items():
        if key not in file_name_file_info_mapping:
            continue

        file_type = file_name_file_info_mapping.get(key)
        for file in deployment_billing_files:
            if file.type == file_type:
                if file_id := file.fileId:
                    file = FileDownload(file_id)
                    csv_file = io.TextIOWrapper(file.content, "utf-8")

                    all_file_data = []
                    file_data = csv.reader(csv_file, delimiter=",")
                    # skip headers
                    next(file_data)
                    for row in file_data:
                        all_file_data.extend(row)

                    if should_check_pre_state and _is_value_equal_to_pre_value(
                        key, billing_field_value, pre_user_billing
                    ):
                        continue

                    _validate_field_value_in_files_data(key, billing_field_value, all_file_data, pre_user_billing)


def _is_value_equal_to_pre_value(key, value, pre_billing: Optional[dict]) -> bool:
    if not pre_billing or not isinstance(pre_billing, dict):
        return False

    return value == pre_billing.get(key)


def _validate_field_value_in_files_data(key: str, billing_field_value: dict, all_file_data, pre_user_billing: dict):
    err = None
    if key == UserBilling.BILLING_PROVIDER_NAME:
        if billing_field_value not in all_file_data:
            err = f"{billing_field_value} not in deployment uploaded file. Exceptable values are: {all_file_data}"

    elif key == UserBilling.INSURANCE_CARRIERS:
        previous_carriers = pre_user_billing.get(key, [])
        for index, item in enumerate(billing_field_value):
            previous_carriers_item = deep_get(previous_carriers, str(index)) or {}
            if all(pair in previous_carriers_item.items() for pair in item.items()):
                continue
            fields_to_check = [InsuranceCarrier.PAYER_ID, InsuranceCarrier.GROUP_NAME]
            for data_key, value in item.items():
                if data_key not in fields_to_check:
                    continue
                if value not in all_file_data:
                    err = (
                        f"{billing_field_value} not in deployment uploaded file. Exceptable values are: {all_file_data}"
                    )

    elif key == UserBilling.DIAGNOSIS:
        fields_to_check = [Diagnosis.ICD_CODE, Diagnosis.DESCRIPTION]
        for data_key, value in billing_field_value.items():
            if data_key not in fields_to_check:
                continue
            if value not in all_file_data:
                err = f"{billing_field_value} not in deployment uploaded file. Exceptable values are: {all_file_data}"

    if err:
        raise InvalidRequestException(err)
