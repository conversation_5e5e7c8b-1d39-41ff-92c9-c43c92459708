import logging

from billing.components.core.dtos.deployment_billing import ProductType
from billing.components.core.helpers import module_result_helpers
from billing.components.core.helpers.alerts_helpers import (
    extract_deployment_billing_by_deployment_id,
)
from billing.components.core.tasks import (
    BillingSubmissionScheduleEvent,
    process_module_result_insertion,
)
from sdk.common.utils.validators import utc_str_field_to_val
from sdk.module_result.event_bus.post_create_module_results_batch_event import (
    PostCreateModuleResultBatchEvent,
)

logger = logging.getLogger(__name__)


def handle_post_module_result_creation_event_for_billing(
    event: PostCreateModuleResultBatchEvent,
):
    if not module_result_helpers.is_user_onboarded(event.user_id, event.start_date_time):
        logger.debug(f"User {event.user_id} is not onboarded, skipping submission...")
        return

    billing_config = extract_deployment_billing_by_deployment_id(event.deployment_id)
    if not billing_config or not billing_config.enabled:
        return

    product_type = billing_config.productType
    if not product_type:
        logger.debug(f"Invalid product type for deployment {event.deployment_id}")
        return

    submissions = None
    if product_type == ProductType.RPM:
        submissions = module_result_helpers.handle_billing_submission_for_rpm(event, billing_config)
    elif product_type == ProductType.RTM:
        submissions = module_result_helpers.handle_billing_submission_for_rtm(event, billing_config)

    if submissions:
        submission_event = BillingSubmissionScheduleEvent.from_dict(
            {
                BillingSubmissionScheduleEvent.USER_ID: event.user_id,
                BillingSubmissionScheduleEvent.DEPLOYMENT_ID: event.deployment_id,
                BillingSubmissionScheduleEvent.PRIMITIVES_START_DATE_TIMES: [
                    utc_str_field_to_val(p.startDateTime) for p in submissions
                ],
            }
        )
        process_module_result_insertion.delay(submission_event.to_dict())
