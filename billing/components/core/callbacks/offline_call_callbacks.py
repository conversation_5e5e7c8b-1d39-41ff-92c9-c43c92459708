from billing.components.core.helpers.alerts_helpers import (
    validate_deployment_billing_enabled,
)
from billing.components.core.repository.billing_repository import (
    BillingRemoteTimeTrackingRepository,
)
from billing.components.core.tasks import VideoCallCompletionEvent, process_video_call_completion
from huma_plugins.components.online_offline_call.events.post_create_offline_call_event import PostCreateOfflineCallEvent
from sdk.common.utils.inject import autoparams
from sdk.deployment.service.deployment_service import DeploymentService


def process_pre_create_offline_call_event(event):
    validate_deployment_billing_enabled(DeploymentService().retrieve_deployment(deployment_id=event.deployment_id))


@autoparams("repo")
def process_post_create_offline_call_event(
    event: PostCreateOfflineCallEvent, repo: BillingRemoteTimeTrackingRepository
):
    repo.create_billing_remote_time_tracking(
        event.deployment_id,
        event.manager_id,
        event.start_dt,
        event.end_dt,
        event.user_id,
    )

    process_video_call_completion.delay(
        VideoCallCompletionEvent.from_dict(
            {
                VideoCallCompletionEvent.USER_ID: event.user_id,
                VideoCallCompletionEvent.MANAGER_ID: event.manager_id,
                VideoCallCompletionEvent.START_DATE_TIME: event.start_dt,
                VideoCallCompletionEvent.END_DATE_TIME: event.end_dt,
                VideoCallCompletionEvent.STATUS: event.status,
            }
        ).to_dict()
    )
