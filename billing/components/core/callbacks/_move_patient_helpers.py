from datetime import datetime, timedelta, timezone

from django.db import transaction

from billing.components.core.callbacks.authorization_callbacks import (
    set_billing_for_user,
)
from billing.components.core.dtos.deployment_billing import (
    DeploymentBillingConfig,
)
from billing.components.core.helpers.alerts_helpers import (
    extract_deployment_billing_by_deployment_id,
)
from billing.components.core.helpers.billing_periods_calculators import BillingCalendarPeriodCalculator
from billing.components.core.repository.billing_repository import (
    BillingAlertsRepository,
    BillingSubmissionRepository,
    BillingMonitoringLogRepository,
    BillingRemoteTimeTrackingRepository,
)
from sdk.authorization.dtos.user_move_history import UserMoveHistoryDTO
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.authorization.repository.user_move_history_repository import (
    UserMoveHistoryRepository,
)
from sdk.common.utils.inject import autoparams


def move_billing_data_if_source_has_no_billing(billing_repo, user_id, target_id):
    with transaction.atomic():
        _reset_billing_status(user_id)
        _move_billing_alert(user_id, target_id)


def move_billing_data_if_source_has_billing(billing_repo, user_id, source_id, target_id):
    with transaction.atomic():
        _reset_billing_status(user_id)
        _move_billing_alert(user_id, target_id)
        _move_billing_submissions(user_id, source_id, target_id)
        current_month_first_day = datetime.utcnow().replace(day=1)
        _move_manual_logs(user_id, source_id, target_id, current_month_first_day)
        _move_time_tracking(user_id, source_id, target_id, current_month_first_day)


@autoparams("auth_repo")
def _reset_billing_status(user_id, auth_repo: AuthorizationRepository):
    user = auth_repo.retrieve_simple_user_profile(user_id=user_id)
    set_billing_for_user(user)
    auth_repo.update_user_profile(user)


@autoparams("alert_repo")
def _move_billing_alert(user_id: str, target_id: str, alert_repo: BillingAlertsRepository):
    """Will move user alert regardless of previous deployment id"""
    alert_repo.move_existing_alert(user_id, target_id)


@autoparams("submission_repo")
def _move_billing_submissions(
    user_id: str,
    source_id: str,
    target_id: str,
    submission_repo: BillingSubmissionRepository,
):
    first_submission = submission_repo.find_user_first_submission(user_id)
    if not first_submission:
        return

    first_submission_dt = first_submission.startDateTime
    target_billing_config = extract_deployment_billing_by_deployment_id(target_id)
    closest_period_start = _get_current_submission_cycle_start_dt(first_submission_dt, target_billing_config)
    submission_repo.move_submissions_between_deployments(
        user_id=user_id,
        source_id=source_id,
        target_id=target_id,
        from_dt=closest_period_start,
    )


@autoparams("logs_repo")
def _move_manual_logs(
    user_id: str,
    source_deployment_id: str,
    target_deployment_id: str,
    first_day_of_month: datetime,
    logs_repo: BillingMonitoringLogRepository,
):
    logs_repo.move_logs_between_deployments(
        user_id=user_id,
        source_id=source_deployment_id,
        target_id=target_deployment_id,
        from_dt=first_day_of_month,
    )


@autoparams("tracking_repo")
def _move_time_tracking(
    user_id: str,
    source_id: str,
    target_id: str,
    first_day_of_month: datetime,
    tracking_repo: BillingRemoteTimeTrackingRepository,
):
    tracking_repo.move_time_tracks_between_deployments(
        user_id=user_id,
        source_id=source_id,
        target_id=target_id,
        from_dt=first_day_of_month,
    )


def _get_current_submission_cycle_start_dt(first_submission_dt: datetime, config: DeploymentBillingConfig) -> datetime:
    today = datetime.now(tz=timezone.utc).date()
    if config.useCalendarCalculation:
        calculator = BillingCalendarPeriodCalculator()
        cycle_start_date, _ = calculator.calculate(today)
    else:
        num_days = (today - first_submission_dt.date()).days % 30
        cycle_start_date = today - timedelta(days=num_days)

    return datetime(
        cycle_start_date.year,
        cycle_start_date.month,
        cycle_start_date.day,
    )


def _is_date_in_current_month(date_to_check: datetime.date) -> bool:
    today = datetime.utcnow()
    return today.year == date_to_check.year and today.month == date_to_check.month


@autoparams("move_repo")
def get_sorted_user_move_history(user_id: str, move_repo: UserMoveHistoryRepository) -> list[UserMoveHistoryDTO]:
    history = move_repo.retrieve_user_move_history(user_id)
    return sorted(history, key=lambda x: x.createDateTime, reverse=True)


def get_previous_enabled_billing_config(user_id: str) -> DeploymentBillingConfig | None:
    move_history = get_sorted_user_move_history(user_id)
    for move_log in move_history:
        billing = extract_deployment_billing_by_deployment_id(move_log.resourceId)
        if billing and billing.enabled:
            return billing
    return None
