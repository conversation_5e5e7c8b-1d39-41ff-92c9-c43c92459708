import logging
import os
from typing import Optional, Union

from flask import Blueprint

from billing.components.core.callbacks import (
    handle_post_module_result_creation_event_for_billing,
)
from billing.components.core.callbacks.authorization_callbacks import (
    process_offboard_user,
    process_pre_create_user,
    process_pre_update_user,
    process_reactivate_user,
    process_user_onboarding,
)
from billing.components.core.callbacks.complete_video_call_callbacks import (
    completed_video_callback,
)
from billing.components.core.callbacks.delete_user_callback import (
    delete_user_billing_on_user_delete_event,
)
from billing.components.core.callbacks.deployment_callbacks import (
    process_pre_create_deployment,
    process_pre_update_deployment,
)
from billing.components.core.callbacks.move_patient_callbacks import (
    process_move_for_different_billing_configuration,
    process_moved_user,
)
from billing.components.core.callbacks.offline_call_callbacks import (
    process_post_create_offline_call_event,
    process_pre_create_offline_call_event,
)
from billing.components.core.config.config import BillingConfig
from billing.components.core.di.components import (
    bind_export_repositories,
)
from billing.components.core.dtos.billing_submission_schedule_event import (
    BillingSubmissionScheduleEvent,
)
from billing.components.core.repository.billing_repository import (
    BillingAlertsRepository,
)
from billing.components.core.repository.postgres._billing_alerts_repository import (
    PostgresBillingAlertsRepository,
)
from billing.components.core.router.billing_routers import api as billing_route
from huma_plugins.components.online_offline_call.events.post_create_offline_call_event import (
    PostCreateOfflineCallEvent,
)
from huma_plugins.components.online_offline_call.events.pre_create_offline_call_event import (
    PreCreateOfflineCallEvent,
)
from sdk.auth.events.complete_video_call_event import CompleteVideoCallEvent
from sdk.auth.events.delete_user_event import DeleteUserEvent
from sdk.authorization.di.components import PreCreateUserEvent
from sdk.authorization.events import (
    PostUserOffBoardEvent,
    PostUserReactivationEvent,
    PreUserProfileUpdateEvent,
)
from sdk.authorization.events.post_move_user_event import PostMoveUserEvent
from sdk.authorization.events.post_user_onboard_event import UserOnboardedEvent
from sdk.authorization.events.pre_move_user_event import PreMoveUserEvent
from sdk.calendar.dtos.calendar_event import CalendarEventDTO
from sdk.common.adapter.event_bus_adapter import EventBusAdapter
from sdk.common.utils.inject import Binder, autoparams
from sdk.deployment.component import EventSubscriptions
from sdk.deployment.events import PreCreateDeploymentEvent, PreDeploymentUpdateEvent
from sdk.module_result.event_bus.post_create_module_results_batch_event import (
    PostCreateModuleResultBatchEvent,
)
from sdk.phoenix.component_manager import PhoenixBaseComponent
from sdk.phoenix.config.server_config import PhoenixServerConfig

logger = logging.getLogger(__name__)


class BillingComponent(PhoenixBaseComponent):
    config_class = BillingConfig
    tag_name = "billing"
    tasks = ["billing.components.core"]
    localization_path = os.path.dirname(os.path.realpath(__file__)) + "/localization"

    def bind(self, binder: Binder, config: PhoenixServerConfig):
        bind_export_repositories(binder)
        binder.bind_to_provider(
            BillingAlertsRepository,
            lambda: PostgresBillingAlertsRepository(),
        )
        logger.debug("BillingAlertsRepository bind to PostgresBillingAlertsRepository")

    @property
    def blueprint(self) -> Optional[Union[Blueprint, list[Blueprint]]]:
        blueprints = [billing_route]
        return blueprints

    @autoparams()
    def post_setup(self, event_bus: EventBusAdapter):
        subscriptions: EventSubscriptions = [
            (
                PostCreateModuleResultBatchEvent,
                handle_post_module_result_creation_event_for_billing,
            ),
            (UserOnboardedEvent, process_user_onboarding),
            (PostUserReactivationEvent, process_reactivate_user),
            (PostUserOffBoardEvent, process_offboard_user),
            (PreCreateUserEvent, process_pre_create_user),
            (PreUserProfileUpdateEvent, process_pre_update_user),
            (CompleteVideoCallEvent, completed_video_callback),
            (DeleteUserEvent, delete_user_billing_on_user_delete_event),
            (PreMoveUserEvent, process_move_for_different_billing_configuration),
            (PostMoveUserEvent, process_moved_user),
            (PreCreateDeploymentEvent, process_pre_create_deployment),
            (PreDeploymentUpdateEvent, process_pre_update_deployment),
            (PreCreateOfflineCallEvent, process_pre_create_offline_call_event),
            (PostCreateOfflineCallEvent, process_post_create_offline_call_event),
        ]

        for event, callback in subscriptions:
            event_bus.subscribe(event, callback)

        super().post_setup()
        CalendarEventDTO.register(BillingSubmissionScheduleEvent.__name__, BillingSubmissionScheduleEvent)
