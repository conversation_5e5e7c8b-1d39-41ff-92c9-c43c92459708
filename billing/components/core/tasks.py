import logging
from datetime import datetime

from celery.schedules import crontab

from billing.components.core.helpers.alerts_helpers import (
    extract_deployment_billing_by_deployment_id,
)
from billing.components.core.helpers.billing_record_change_handler_helpers import (
    calculate_current_billing_period,
    check_and_retrieve_user_alerts,
)
from billing.components.core.helpers.export_billing_time_tracking_helpers import (
    is_date_in_current_month,
    remove_users_billing_data_overlapping,
)
from billing.components.core.repository.billing_repository import (
    BillingAlertsRepository,
)
from huma_plugins.components.online_offline_call.dtos.video_models import (
    OfflineVideoCallDTO,
)
from sdk import convertibleclass
from sdk.celery.app import celery_app
from sdk.common.utils import inject
from sdk.common.utils.convertible import default_field, meta
from sdk.common.utils.validators import utc_str_field_to_val, utc_str_val_to_field

logger = logging.getLogger(__name__)


@convertibleclass
class BillingSubmissionScheduleEvent:
    USER_ID = "userId"
    DEPLOYMENT_ID = "deploymentId"
    PRIMITIVES_START_DATE_TIMES = "primitivesStartDateTimes"

    userId: str = default_field()
    deploymentId: str = default_field()
    primitivesStartDateTimes: list[str] = default_field()


@convertibleclass
class VideoCallCompletionEvent:
    USER_ID = "userId"
    MANAGER_ID = "managerId"
    START_DATE_TIME = "startDateTime"
    END_DATE_TIME = "endDateTime"
    STATUS = "status"

    userId: str = default_field()
    managerId: str = default_field()
    startDateTime: str = default_field(metadata=meta(value_to_field=utc_str_field_to_val))
    endDateTime: str = default_field(metadata=meta(value_to_field=utc_str_field_to_val))
    status: OfflineVideoCallDTO.CallStatus = default_field()


@celery_app.on_after_finalize.connect
def setup_periodic_tasks(sender, **_):
    sender.add_periodic_task(
        crontab(minute=15, hour=1),
        execute_remove_users_billing_data_overlapping.s(),
        name="Remove user billing time overlapping",
    )


@celery_app.task
def execute_remove_users_billing_data_overlapping():
    remove_users_billing_data_overlapping()
    logger.info("Remove user billing time overlapping.")


def _update_user_billing_alert(
    user_id: str,
    next_submission_dos: datetime.date,
    submission_date: datetime.date = None,
):
    log_data = f"User ID: {user_id}, Next Submission DoS: {next_submission_dos}"
    logging.info(f"Start updating billing alert DoS details with data {log_data}")
    billing_alerts_repo = inject.instance(BillingAlertsRepository)
    billing_alerts_repo.update_user_billing_alerts_next_submissions_dos(user_id, next_submission_dos)
    if submission_date:
        logging.info(f"Start updating billing alert submission details with data {log_data}")
        billing_alerts_repo.update_user_billing_alerts_submission_details(user_id, submission_date)
    logging.info(f"Finished updating billing alert with data {log_data}")


@celery_app.task
def process_module_result_insertion(submission_event: dict):
    event = BillingSubmissionScheduleEvent.from_dict(submission_event)
    logger.info(f"Process module result insertion for event {event}")
    user_id = event.userId
    deployment_id = event.deploymentId
    billing_config = extract_deployment_billing_by_deployment_id(deployment_id)
    if billing_config is None or not billing_config.enabled:
        logging.warning(f"Missing billing config for deployment {deployment_id}. Skipping")
        return

    _created, _ = check_and_retrieve_user_alerts(user_id=user_id)
    if not _created:
        logging.warning(f"User with ID:{user_id} does not have billing alert yet")
        return
    (
        first_submission_date_in_history,
        current_period_start_date,
        current_period_end_date,
    ) = calculate_current_billing_period(
        user_id=user_id,
        deployment_id=deployment_id,
        use_calendar_calculation=billing_config.useCalendarCalculation,
    )
    for start_date_time in event.primitivesStartDateTimes:
        submission_date = utc_str_val_to_field(start_date_time).date()
        if submission_date < current_period_start_date:
            logging.info(
                f"Submission date {submission_date} is lower than {current_period_start_date}."
                f" Skipping submission for user {user_id}"
            )
            if submission_date == first_submission_date_in_history:
                msg = f"Updating only dos for very first submission in date {submission_date} for user {user_id}"
                logging.info(msg)
                _update_user_billing_alert(user_id, current_period_end_date)
            return

        _update_user_billing_alert(user_id, current_period_end_date, submission_date)


@celery_app.task
def process_video_call_completion(video_event: dict):
    event = VideoCallCompletionEvent.from_dict(video_event)

    billing_alerts_repo = inject.instance(BillingAlertsRepository)
    if (
        not is_date_in_current_month(utc_str_val_to_field(event.startDateTime))
        or event.status != OfflineVideoCallDTO.CallStatus.ANSWERED
    ):
        return

    user_id = event.userId
    logging.info(f"Updating videoCallTime billing alert for user {user_id}. Updates are {event}")
    if not event.endDateTime:
        msg = f"Video call endDateTime is not present for user {user_id} - skipping"
        logging.info(msg)
        return

    billing_alerts_repo.update_call_datetime(
        user_id=user_id,
        current_call_datetime=utc_str_val_to_field(event.endDateTime),
    )
