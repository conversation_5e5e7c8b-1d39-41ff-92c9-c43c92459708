from abc import ABC, abstractmethod
from datetime import date, datetime

from billing.components.core.dtos.billing_models import (
    BillingDiagnosisHistoryLogDTO,
    BillingInsuranceCarrierProfileHistoryDTO,
    BillingMonitoringLogDTO,
    BillingProviderHistoryLogDTO,
    BillingRemoteTimeTrackingDTO,
    BillingSubmissionDTO,
)
from billing.components.core.router.billing_requests import AlertFilterParameters, AlertSortParameters
from sdk.common.utils.inject import autoparams
from sdk.phoenix.config.server_config import PhoenixServerConfig


class BillingRepository(ABC):
    """This is the base repository class for handling different types of billing"""

    @autoparams()
    def __init__(self, config: PhoenixServerConfig):
        self._config = config

    @abstractmethod
    def delete_user_data(self, user_id):
        raise NotImplementedError


class BillingRemoteTimeTrackingRepository(BillingRepository):
    @abstractmethod
    def create_billing_remote_time_tracking(
        self,
        deployment_id: str,
        clinician_id: str,
        start_datetime: datetime,
        end_datetime: datetime,
        user_id: str,
        **extra_direct_fields,
    ):
        raise NotImplementedError

    @abstractmethod
    def update_remote_time_tracking_start_and_end_dt(self, tracking_id: str, start_dt: datetime, end_dt: datetime):
        raise NotImplementedError

    @abstractmethod
    def retrieve_billing_time_spent_remote_time_tracking(self, user_id: str, start_date: datetime, end_date: datetime):
        raise NotImplementedError

    @abstractmethod
    def retrieve_billing_time_tracking_records(self, user_id: str, start_date: datetime, end_date: datetime):
        raise NotImplementedError

    @abstractmethod
    def remove_all_overlaps(self, user_id: str, start_date: datetime | None, end_date: datetime | None):
        raise NotImplementedError

    @abstractmethod
    def move_time_tracks_between_deployments(self, user_id: str, source_id: str, target_id: str, from_dt: datetime):
        raise NotImplementedError

    @abstractmethod
    def retrieve_users_automated_logs_from_to(
        self,
        user_ids: list,
        start_date: datetime,
        end_date: datetime,
    ) -> list[BillingRemoteTimeTrackingDTO]:
        raise NotImplementedError


class BillingSubmissionRepository(BillingRepository):
    @abstractmethod
    def create_update_billing_submission(self, submission: BillingSubmissionDTO, count: int):
        raise NotImplementedError

    def get_total_submission_count_from_to(
        self,
        user_id: str,
        from_date: datetime.date,
        to_date: datetime.date,
        deployment_id: str = None,
    ):
        raise NotImplementedError

    def find_user_first_submission(self, user_id: str, deployment_id: str = None):
        raise NotImplementedError

    @abstractmethod
    def get_total_record_days_count_after_specific_date(self, user_id: str, date: datetime.date):
        raise NotImplementedError

    @abstractmethod
    def get_total_record_days_count_from_to(
        self,
        user_id: str,
        from_date: datetime.date,
        to_date: datetime.date,
        deployment_id: str = None,
    ):
        raise NotImplementedError

    @abstractmethod
    def find_16th_submission_in_period(self, user_id: str, start_date: datetime.date, end_date: datetime.date):
        pass

    @abstractmethod
    def is_classic_period_billable_by_start_date(self, user_id: str, start_date: datetime.date):
        raise NotImplementedError

    @abstractmethod
    def move_submissions_between_deployments(self, user_id: str, source_id: str, target_id: str, from_dt: datetime):
        raise NotImplementedError

    @abstractmethod
    def get_total_submissions_for_user_from_to(
        self,
        user_id: str,
        deployment_id: str = None,
        from_date: datetime.date = None,
        to_date: datetime.date = None,
        return_count: bool = False,
    ):
        raise NotImplementedError

    @abstractmethod
    def find_billable_calendar_months_in_periods(
        self, user_id: str, periods: list[dict[str, datetime]]
    ) -> list[datetime] | None:
        raise NotImplementedError

    @abstractmethod
    def user_has_submission_after_date(self, user_id: str, reference_dt: datetime) -> bool:
        raise NotImplementedError


class BillingAlertsRepository(ABC):
    @abstractmethod
    def retrieve_billing_alerts(
        self,
        sort: AlertSortParameters,
        search: str,
        filters: AlertFilterParameters,
        skip: int,
        limit: int,
        deployment_id: str,
        product_type: str,
        has_identified_access: bool = False,
        use_calendar_calculation: bool = False,
    ):
        raise NotImplementedError

    @abstractmethod
    def update_create_user_billing_alert(self, user_id: str, deployment_id: str, update: dict):
        raise NotImplementedError

    @abstractmethod
    def add_users_to_billing_alerts(self, deployment_id: str):
        raise NotImplementedError

    def move_existing_alert(self, user_id: str, deployment_id: str):
        raise NotImplementedError

    @abstractmethod
    def update_last_monitoring_date(self, user_id: str, current_monitoring_date: date):
        raise NotImplementedError

    @abstractmethod
    def retrieve_user_billing_alerts(self, user_id: str):
        raise NotImplementedError

    @abstractmethod
    def update_user_billing_alerts_next_submissions_dos(self, user_id: str, next_submission_dos: date):
        raise NotImplementedError

    @abstractmethod
    def update_user_billing_alerts_submission_details(self, user_id: str, submission_date: date):
        raise NotImplementedError

    @abstractmethod
    def update_call_datetime(self, user_id: str, current_call_datetime: datetime):
        raise NotImplementedError

    @abstractmethod
    def delete_user_billing_alerts(self, user_id: str):
        raise NotImplementedError

    @abstractmethod
    def reactivate_user_billing_alerts(self, user_id: str):
        raise NotImplementedError

    @abstractmethod
    def fix_submission_dates_for_deployment_users(self, deployment_id: str):
        raise NotImplementedError

    @abstractmethod
    def delete_user_data(self, user_id):
        raise NotImplementedError


class BillingProfileHistoryLogRepository(BillingRepository):
    @abstractmethod
    def create_diagnosis_log(self, log: BillingDiagnosisHistoryLogDTO):
        raise NotImplementedError

    @abstractmethod
    def create_insurance_log(self, log: BillingInsuranceCarrierProfileHistoryDTO):
        raise NotImplementedError

    @abstractmethod
    def create_provider_log(self, log: BillingProviderHistoryLogDTO):
        raise NotImplementedError

    def get_billing_provider_logs(self, to_date: datetime.date, user_id: str) -> list:
        raise NotImplementedError

    def get_diagnosis(self, user_id: str, deployment_id: str):
        raise NotImplementedError

    def create_raw_billing_profile_history_log(self, user_id: str, deployment_id: str, **kwargs):
        raise NotImplementedError

    def add_extra_fields_to_billing_profile_history_log(self, idx: str, **kwargs):
        raise NotImplementedError

    def get_class_data(self, cls: str) -> list:
        raise NotImplementedError


class BillingMonitoringLogRepository(BillingRepository):
    @abstractmethod
    def create_monitoring_log(self, log: BillingMonitoringLogDTO, original_log_id: str) -> str:
        raise NotImplementedError

    @abstractmethod
    def retrieve_active_user_monitoring_logs(
        self, user_id: str, limit: int, skip: int
    ) -> tuple[list[BillingMonitoringLogDTO], int]:
        raise NotImplementedError

    @abstractmethod
    def retrieve_user_monitoring_logs_from_to(
        self,
        from_dt: datetime,
        to_dt: datetime,
        user_ids: list[str],
        deployment_id: str = None,
    ) -> dict[str:list]:
        raise NotImplementedError

    @abstractmethod
    def retrieve_monitoring_log_by_id(self, log_id: str) -> BillingMonitoringLogDTO:
        raise NotImplementedError

    @abstractmethod
    def update_monitoring_log(self, log_id: str, updated_fields: dict) -> str:
        raise NotImplementedError

    @abstractmethod
    def move_logs_between_deployments(self, user_id: str, source_id: str, target_id: str, from_dt: datetime):
        raise NotImplementedError

    @abstractmethod
    def retrieve_all_monitoring_logs_for_user(self, user_id: str) -> list[BillingMonitoringLogDTO]:
        raise NotImplementedError
