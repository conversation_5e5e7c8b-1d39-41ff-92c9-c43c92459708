from datetime import datetime, time

from bson import ObjectId

from billing.components.core.dtos.billing_models import (
    BillingDiagnosisHistoryLogDTO,
    BillingInsuranceCarrierProfileHistoryDTO,
    BillingProfileHistoryDTO,
    BillingProviderHistoryLogDTO,
)
from billing.components.core.models import (
    BillingDiagnosisHistoryLog,
    BillingInsuranceCarrierHistoryLog,
    BillingProfileHistory,
    BillingProviderHistoryLog,
)
from billing.components.core.repository.billing_repository import (
    BillingProfileHistoryLogRepository,
)
from sdk.common.utils.validators import model_to_dict


class PostgresBillingProfileHistoryRepository(BillingProfileHistoryLogRepository):
    def create_diagnosis_log(self, log: BillingDiagnosisHistoryLogDTO):
        doc = BillingDiagnosisHistoryLog(**{**log.to_dict(), BillingDiagnosisHistoryLog.MONGO_ID: str(ObjectId())})
        doc.save()
        return doc.mongoId

    def create_insurance_log(self, log: BillingInsuranceCarrierProfileHistoryDTO):
        doc = BillingInsuranceCarrierHistoryLog(
            **{
                **log.to_dict(),
                BillingInsuranceCarrierHistoryLog.MONGO_ID: str(ObjectId()),
            }
        )
        doc.save()
        return doc.mongoId

    def create_provider_log(self, log: BillingProviderHistoryLogDTO):
        doc = BillingProviderHistoryLog(**{**log.to_dict(), BillingProviderHistoryLog.MONGO_ID: str(ObjectId())})
        doc.save()
        return doc.mongoId

    def get_billing_provider_logs(self, to_date: datetime.date, user_id: str) -> list:
        to_date = datetime.combine(to_date, time.max)
        billing_provider_logs = BillingProviderHistoryLog.objects.filter(
            userId=user_id, createDateTime__lte=to_date
        ).order_by("-createDateTime")
        return [BillingProviderHistoryLogDTO.from_dict(model_to_dict(doc)) for doc in billing_provider_logs]

    def get_diagnosis(self, user_id: str, deployment_id: str):
        diagnosis = BillingDiagnosisHistoryLog.objects.filter(
            userId=user_id,
            deploymentId=deployment_id,
        ).order_by("-createDateTime")
        if diagnosis:
            return BillingDiagnosisHistoryLogDTO.from_dict(model_to_dict(diagnosis.first()))
        return None

    def delete_user_data(self, user_id: str):
        query = {BillingProfileHistoryDTO.USER_ID: user_id}
        results = []
        for model in [
            BillingProviderHistoryLog,
            BillingInsuranceCarrierHistoryLog,
            BillingDiagnosisHistoryLog,
        ]:
            deleted_count, _ = model.objects.filter(**query).delete()
            results.append(deleted_count)
        return sum(results)

    def create_raw_billing_profile_history_log(self, user_id: str, deployment_id: str, **kwargs):
        """
        This is only used by auth-helper to create the initial billing profile history logs.
        Automation is using this method to create the initial billing profile history logs for testing purposes.
        """
        kwargs.update(
            {
                BillingProfileHistoryDTO.USER_ID: user_id,
                BillingProfileHistoryDTO.DEPLOYMENT_ID: deployment_id,
            }
        )

        cls = kwargs.pop("cls")
        mongo_id_key = BillingProfileHistory.MONGO_ID
        if cls == "MongoBillingDiagnosisHistoryLog":
            document = BillingDiagnosisHistoryLog(**{**kwargs, mongo_id_key: str(ObjectId())})
            document.save()
        elif cls == "MongoBillingInsuranceCarrierHistoryLog":
            document = BillingInsuranceCarrierHistoryLog(**{**kwargs, mongo_id_key: str(ObjectId())})
            document.save()
        elif cls == "MongoBillingProviderHistoryLog":
            document = BillingProviderHistoryLog(**{**kwargs, mongo_id_key: str(ObjectId())})
            document.save()
        else:
            raise Exception("Unknown class")
        return document.mongoId

    def get_class_data(self, cls: str) -> list:
        """
        This is only used by tests to get all the data from a specific class.
        """
        if cls == "MongoBillingDiagnosisHistoryLog":
            return list(BillingDiagnosisHistoryLog.objects.all())
        elif cls == "MongoBillingInsuranceCarrierHistoryLog":
            return list(BillingInsuranceCarrierHistoryLog.objects.all())
        elif cls == "MongoBillingProviderHistoryLog":
            return list(BillingProviderHistoryLog.objects.all())
        else:
            raise Exception("Unknown class")

    def add_extra_fields_to_billing_profile_history_log(self, idx: str, **kwargs):
        for model in [
            BillingProviderHistoryLog,
            BillingInsuranceCarrierHistoryLog,
            BillingDiagnosisHistoryLog,
        ]:
            document = model.objects.filter(mongoId=idx).first()
            if document:
                for key, value in kwargs.items():
                    setattr(document, key, value)
                document.save()
                return 1
        return 0
