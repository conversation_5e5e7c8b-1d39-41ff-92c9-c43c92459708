import json
import logging
from datetime import date, datetime, time, timedelta

from bson import ObjectId
from django.db import connection
from django.db.models import Case, DateTimeField, ExpressionWrapper, F, Func, JSONField, Q as DjangoQ, Value, When
from django.db.models.expressions import RawSQL
from django.db.models.fields import BigIntegerField, BooleanField, FloatField, IntegerField
from django.db.models.functions import Cast, Ceil, Coalesce, Concat, Lower, Round

from billing.components.core.dtos.billing_models import (
    BillingAlertsColorCoding,
    BillingAlertsDTO,
    CPTCompliance,
)
from billing.components.core.dtos.deployment_billing import ProductType
from billing.components.core.helpers.export_billing_submission_helpers import (
    get_calendar_submission_period,
)
from billing.components.core.helpers.export_billing_time_tracking_helpers import (
    calculate_current_period_start_and_end_date,
)
from billing.components.core.models import BillingAlert
from billing.components.core.repository.billing_repository import (
    Billing<PERSON>lertsRepository,
)
from billing.components.core.router.billing_requests import (
    AlertCPTField,
    AlertField,
    AlertFilterFields,
    AlertFilterParameters,
    AlertOrderField,
    AlertSortParameters,
)
from sdk.authorization.dtos.role.role import RoleName
from sdk.authorization.dtos.user import RoleAssignmentDTO
from sdk.authorization.models import User
from sdk.common.utils.common_functions_utils import escape
from sdk.common.utils.validators import id_as_obj_id, model_to_dict


class _ExtraFields:
    SUBMISSIONS_LEFT = "submissionsLeft"
    DAYS_TO_DOS = "daysToDoS"
    CPT_2_TEMP_COMPLAINCE = "cpt2TempCompliance"
    CPT_2_THRESHOLD = "cpt2Threshold"
    CPT_2_WITHOUT_PRODUCT_TYPE = "cpt2WithoutProductType"
    ACTUAL_NEXT_SUBMISSION_DOS = "actualNextSubmissionDoS"
    ACTUAL_SUBMISSION_COUNT = "actualSubmissionCount"
    IS_SUBMISSION_GREY = "isSubmissionGrey"
    TODAY_DATE = "todayDate"
    HAS_DOS = "hasDoS"
    PRODUCT_TYPE = "productType"
    LOWER_GIVEN_NAME = "lowerGivenName"
    LOWER_FAMILY_NAME = "lowerFamilyName"
    MONITORING_MINUTES = "monitoringMinutes2"
    SUBMISSION_DATES = "submissionDates2"
    NEXT_SUBMISSION_DOS = "nextSubmissionDoS2"
    NEXT_SUBMISSION_DOS_FINAL = "nextSubmissionDoS3"
    LAST_SYNCED_DATE = "lastSyncedDate2"
    SUBMISSION_COUNT = "submissionCount2"
    SUBMISSION_COUNT_FINAL = "submissionCount3"
    HAS_CALL = "hasCall2"
    SUBMISSION_DAYS_LEFT = "submissionDaysLeft"
    CPT_99454_COMPLIANCE = "cpt99454Compliance2"
    CPT_98976_COMPLIANCE = "cpt98976Compliance2"


class _AlertConstants:
    DAY_IN_MILLI_SECONDS = 86400000
    THIRTY_DAYS_IN_MILLI_SECONDS = 2592000000
    PERIOD_LENGTH_IN_DAYS = 30
    MINIMUM_SUBMISSION_COUNT_NEEDED_PER_PERIOD = 16
    UPPER_BOUND_FOR_CPT_2_THRESHOLD = 1000.0


class ExtractEpoch(Func):
    function = "EXTRACT"
    template = "%(function)s(EPOCH FROM %(expressions)s)"
    output_field = IntegerField()


class JSONBPathQueryFirst(Func):
    function = "jsonb_path_query_first"
    template = "%(function)s(%(expressions)s)"


class PostgresBillingAlertsRepository(BillingAlertsRepository):
    ID_STR = "idStr"
    FULL_NAME = "fullName"
    COUNT = "count"

    @id_as_obj_id
    def retrieve_billing_alerts(
        self,
        sort: AlertSortParameters,
        search: str,
        filters: AlertFilterParameters,
        skip: int,
        limit: int,
        deployment_id: str,
        product_type: str,
        has_identified_access: bool = False,
        use_calendar_calculation: bool = False,
    ):
        query = BillingAlert.objects.select_related("user").filter(
            deploymentId=deployment_id, deleteDateTime__isnull=True
        )

        if search:
            query = query.annotate(**{self.FULL_NAME: Concat(F("user__givenName"), Value(" "), F("user__familyName"))})
            query = query.filter(self.get_search_stage(search, has_identified_access))

        query = query.annotate(
            **{
                BillingAlertsDTO.GIVEN_NAME: F("user__givenName"),
                BillingAlertsDTO.FAMILY_NAME: F("user__familyName"),
                BillingAlertsDTO.GENDER: F("user__gender"),
                BillingAlertsDTO.DATE_OF_BIRTH: F("user__dateOfBirth"),
                BillingAlertsDTO.BIOLOGICAL_SEX: F("user__biologicalSex"),
                BillingAlertsDTO.LABELS: F("user__labels"),
                BillingAlertsDTO.LAST_SUBMIT_DATETIME: F("user__lastSubmitDateTime"),
            }
        )
        query = query.annotate(**self._generate_monitoring_minutes_field_pipeline())
        query = query.annotate(**self._generate_pre_validation_pipeline(product_type))
        for a in self._generate_submission_initial_fields_pipeline():
            query = query.annotate(**a)

        if use_calendar_calculation:
            _, current_period_dos = get_calendar_submission_period(datetime.utcnow().date())
            query = query.annotate(**self._set_next_submission_dos_calendar(current_period_dos))
        else:
            query = query.annotate(**self._set_next_submission_dos_classic())
        query = query.annotate(**self._generate_has_call_field_pipeline())

        if filters:
            search_filters = DjangoQ()
            if filters.billing:
                search_filters &= self.get_filter_stage(filters)

            if labels := filters.labels:
                labels_ids = json.dumps(
                    [str(ObjectId(label_id)) for label_id in labels]
                )  # convert to objectId for sql injection protection
                query = query.annotate(
                    is_label_matched=RawSQL(
                        "EXISTS (SELECT 1 FROM jsonb_array_elements(labels) elem WHERE %s::jsonb @> to_jsonb(elem->>'labelId'))",
                        (labels_ids,),
                    )
                )
                search_filters &= DjangoQ(is_label_matched=True)

            if labels is not None and len(labels) == 0:
                search_filters &= DjangoQ(user__labels__isnull=True) | DjangoQ(user__labels__exact=[])

            query = query.filter(search_filters)

        count_query = query.filter()

        for a in self._generate_submission_pipeline():
            query = query.annotate(**a)
        query = query.annotate(**self._generate_complaince_exchange_pipeline())
        query = query.annotate(**self._generate_final_validation_pipeline())

        if sort and sort.fields:
            is_sorting_by_submission = self._is_sorting_by_submission(sort)

            if is_sorting_by_submission:
                for a in self._generate_submission_sort_keys_pipeline():
                    query = query.annotate(**a)

            sort_stage = self.get_sort_stage(sort, is_sorting_by_submission)
            query = query.order_by(*sort_stage)

        if skip and limit:
            query = query[skip : skip + limit]
        elif skip:
            query = query[skip:]
        elif limit:
            query = query[:limit]

        alerts = [a for a in query.values()]

        result = []
        for alert in alerts:
            for field in self._get_fields_to_remove():
                alert.pop(field, None)
            for src, dest in self._get_fields_to_replace().items():
                alert[dest] = alert.pop(src, None)

            alert[BillingAlertsDTO.CPT_98976_COMPLIANCE] = (
                {
                    CPTCompliance.VALUE: alert.get(BillingAlertsDTO.CPT_98976_COMPLIANCE),
                }
                if alert.get(BillingAlertsDTO.CPT_98976_COMPLIANCE)
                else None
            )
            alert[BillingAlertsDTO.CPT_99454_COMPLIANCE] = (
                {
                    CPTCompliance.VALUE: alert.get(BillingAlertsDTO.CPT_99454_COMPLIANCE),
                }
                if alert.get(BillingAlertsDTO.CPT_99454_COMPLIANCE)
                else None
            )
            for field in self._get_date_fields_to_convert():
                if isinstance(alert[field], int):
                    alert[field] = date.fromtimestamp(alert[field] / 1000)
                if alert[field] and alert[field] == datetime.min:
                    alert[field] = None
            self._convert_dates(alert)
            result.append(alert)

        self._add_color_codings_for_submission(result)
        self._add_color_codings_for_time_tracking(result)

        (
            filtered_count,
            total_count,
            missing_call_count,
            missing_submission_count,
            not_synced_count,
        ) = self._count_logic(deployment_id, count_query)

        return (
            filtered_count,
            total_count,
            missing_call_count,
            missing_submission_count,
            not_synced_count,
            result,
        )

    def _count_logic(self, deployment_id, count_query):
        filtered_count = count_query.filter().count()
        total_count = BillingAlert.objects.filter(deploymentId=deployment_id, deleteDateTime__isnull=True).count()
        missing_call_count = count_query.filter(hasCall2=False).count()
        missing_submission_count = count_query.filter(submissionCount2=0).count()
        not_synced_count = count_query.filter(self._not_synced_filter()).count()

        return (
            filtered_count,
            total_count,
            missing_call_count,
            missing_submission_count,
            not_synced_count,
        )

    @staticmethod
    def _convert_dates(alert: dict) -> None:
        if not alert.get(BillingAlertsDTO.SUBMISSION_DATES):
            return

        converted_dates = []
        for d in alert[BillingAlertsDTO.SUBMISSION_DATES]:
            date_part = d.split("T")[0] if "T" in d else d.split()[0]
            raw_date = datetime.strptime(date_part, "%Y-%m-%d")
            converted_dates.append(raw_date)
        alert[BillingAlertsDTO.SUBMISSION_DATES] = converted_dates

    def update_create_user_billing_alert(self, user_id: str, deployment_id: str, update: dict):
        if not BillingAlert.objects.filter(user_id=user_id).exists():
            BillingAlert(
                **{
                    **update,
                    "user_id": user_id,
                    BillingAlertsDTO.DEPLOYMENT_ID: deployment_id,
                    "mongoId": user_id,
                }
            ).save()
        else:
            if update:
                BillingAlert.objects.filter(user_id=user_id).update(
                    **{
                        **update,
                        "updateDateTime": datetime.utcnow(),
                    }
                )

    def add_users_to_billing_alerts(self, deployment_id: str):
        role_query = DjangoQ(
            roles__contains=[
                {RoleAssignmentDTO.RESOURCE: f"deployment/{deployment_id}", RoleAssignmentDTO.ROLE_ID: RoleName.USER}
            ]
        )
        query = role_query & DjangoQ(boardingStatus__status=0, finishedOnboarding=True)

        # TODO #django-test this should be converted to a sql join to only run for missing users, not everyone
        for user in User.objects.filter(query):
            has_alert = BillingAlert.objects.filter(user_id=user.mongoId).count() > 0
            if has_alert:
                continue

            BillingAlert(
                **{
                    "mongoId": user.mongoId,
                    "user_id": user.mongoId,
                    "deploymentId": deployment_id,
                    "createDateTime": user.createDateTime,
                }
            ).save()

    def move_existing_alert(self, user_id: str, deployment_id: str):
        count = BillingAlert.objects.filter(user_id=user_id).update(
            deploymentId=deployment_id,
            updateDateTime=datetime.utcnow(),
            deleteDateTime=None,
        )
        return count

    def get_sort_stage(self, sort: AlertSortParameters, is_sorting_by_submission: bool) -> list[str]:
        pre_sort_stage = []

        if is_sorting_by_submission:
            pre_sort_stage = [
                *pre_sort_stage,
                _ExtraFields.IS_SUBMISSION_GREY,
                _ExtraFields.HAS_DOS,
            ]

        requested_sort_stage = []
        for sort_field in sort.fields:
            if sort_field.field is AlertField.LAST_SYNCED:
                added_key = _ExtraFields.LAST_SYNCED_DATE

            elif sort_field.field is AlertField.COMPLIANCE:
                added_key = f"cpt{sort_field.cpt.value}Compliance"

            elif sort_field.field is AlertField.TIME_SPENT:
                added_key = _ExtraFields.MONITORING_MINUTES

            elif sort_field.field is AlertField.DEADLINE and (
                sort_field.cpt is AlertCPTField.CPT_99454 or sort_field.cpt is AlertCPTField.CPT_98976
            ):
                added_key = _ExtraFields.SUBMISSION_DAYS_LEFT

            else:
                added_key = sort_field.field.value

            requested_sort_stage.append(self._get_order_mongo_mapping(sort_field.order) + added_key)

        alphabetical_default_sort_stage = [
            _ExtraFields.LOWER_FAMILY_NAME,
            _ExtraFields.LOWER_GIVEN_NAME,
        ]

        return [
            *pre_sort_stage,
            *requested_sort_stage,
            *alphabetical_default_sort_stage,
        ]

    @staticmethod
    def _is_sorting_by_submission(sort: AlertSortParameters):
        return sort.fields[0].cpt in [AlertCPTField.CPT_99454, AlertCPTField.CPT_98976]

    @staticmethod
    def _get_order_mongo_mapping(order: AlertOrderField):
        return {AlertOrderField.ASCENDING: "", AlertOrderField.DESCENDING: "-"}.get(order)

    def get_search_stage(self, search: str, has_identified_access: bool = False) -> DjangoQ:
        user_id = str(search) if ObjectId.is_valid(search) else None

        if user_id:
            return DjangoQ(user_id=user_id)

        if not has_identified_access:
            return DjangoQ(user_id__iregex=escape(search))

        return DjangoQ(
            DjangoQ(user__mongoId__iregex=escape(search))
            | DjangoQ(user__givenName__iregex=escape(search))
            | DjangoQ(user__familyName__iregex=escape(search))
            | DjangoQ(fullName__iregex=escape(search))
        )

    def get_filter_stage(self, filters: AlertFilterParameters) -> DjangoQ:
        q = DjangoQ()
        for alert_filter in filters.billing:
            if alert_filter == AlertFilterFields.MISSED_CALL:
                q &= DjangoQ(hasCall2=False)
            elif alert_filter == AlertFilterFields.MISSED_SUBMISSION:
                q &= DjangoQ(submissionCount2=0)
            elif alert_filter == AlertFilterFields.NOT_SYNCED:
                q &= self._not_synced_filter()
        return q

    def update_last_monitoring_date(self, user_id: str, current_monitoring_date: date):
        BillingAlert.objects.filter(
            DjangoQ(lastMonitoringDate__isnull=True) | DjangoQ(lastMonitoringDate__lt=current_monitoring_date),
            user_id=user_id,
        ).update(lastMonitoringDate=current_monitoring_date)

    def retrieve_user_billing_alerts(self, user_id: str) -> BillingAlertsDTO | None:
        entity = BillingAlert.objects.filter(user_id=user_id).first()
        if not entity:
            return None

        alert = model_to_dict(entity)

        user: User = entity.user
        alert[BillingAlertsDTO.GIVEN_NAME] = user.givenName
        alert[BillingAlertsDTO.FAMILY_NAME] = user.familyName
        alert[BillingAlertsDTO.GENDER] = user.gender
        alert[BillingAlertsDTO.DATE_OF_BIRTH] = user.dateOfBirth
        alert[BillingAlertsDTO.BIOLOGICAL_SEX] = user.biologicalSex
        alert[BillingAlertsDTO.LABELS] = user.labels
        alert[BillingAlertsDTO.USER_ID] = user_id
        self._convert_dates(alert)

        return BillingAlertsDTO.from_dict(alert, ignore_none=True)

    def update_user_billing_alerts_next_submissions_dos(self, user_id: str, next_submission_dos: date):
        current_next_submission_dos = datetime.combine(next_submission_dos, time.min)
        BillingAlert.objects.filter(
            DjangoQ(user_id=user_id)
            & (DjangoQ(nextSubmissionDoS__isnull=True) | DjangoQ(nextSubmissionDoS__lt=current_next_submission_dos))
        ).update(
            nextSubmissionDoS=current_next_submission_dos,
            submissionDates=Value([], output_field=JSONField()),
        )

    def update_user_billing_alerts_submission_details(self, user_id: str, submission_date: date):
        submission_date_str = submission_date.isoformat()
        BillingAlert.objects.filter(user_id=user_id).update(
            submissionDates=Case(
                When(submissionDates__isnull=True, then=Func(Value(submission_date_str), function="jsonb_build_array")),
                When(submissionDates__contains=[submission_date_str], then=F("submissionDates")),
                default=Func(
                    F("submissionDates"),
                    Func(Value(submission_date_str), function="jsonb_build_array"),
                    function="jsonb_concat",
                ),
                output_field=JSONField(),
            )
        )

    def update_call_datetime(self, user_id: str, current_call_datetime: datetime):
        count = BillingAlert.objects.filter(
            DjangoQ(callDateTime__isnull=True) | DjangoQ(callDateTime__lt=current_call_datetime),
            user__mongoId=user_id,
        ).update(callDateTime=current_call_datetime)
        logging.info(f"Updated callDateTime for user {user_id}: {count}")

    def delete_user_billing_alerts(self, user_id: str):
        return BillingAlert.objects.filter(user_id=user_id).update(deleteDateTime=datetime.utcnow())

    def reactivate_user_billing_alerts(self, user_id: str):
        return BillingAlert.objects.filter(user_id=user_id).update(
            deleteDateTime=None,
            updateDateTime=datetime.utcnow(),
        )

    @id_as_obj_id
    def fix_submission_dates_for_deployment_users(self, deployment_id: str) -> int:
        """
        This method is used when the billing calculation type is changed from 30-day to Calendar.
        Taking into account possible missed submissions in the current month.
        Returns the number of updated alerts.
        """
        deployment_id = str(deployment_id)
        now = datetime.utcnow()
        this_month_start_dt, this_month_end_dt = get_calendar_submission_period(now)
        this_month_start = datetime.combine(this_month_start_dt, time.min)
        this_month_end = datetime.combine(this_month_end_dt, time.min)

        query = """
            WITH result_set AS (
                SELECT
                    a."mongoId" AS id,
                    (
                        SELECT jsonb_agg(DISTINCT sd)
                        FROM (
                            SELECT DISTINCT s."startDate" as sd
                            FROM billing_submission s
                            WHERE s."userId" = a."mongoId"
                            AND s."startDate" >= %(this_month_start)s

                            UNION

                            SELECT DISTINCT sd::varchar::TIMESTAMPTZ as sd
                            FROM jsonb_array_elements(a."submissionDates") AS sd
                            WHERE sd::varchar::TIMESTAMPTZ >= %(this_month_start)s
                        ) AS combined
                    ) as combined_submission_dates
                FROM
                    billing_alert a
                WHERE
                    a."deploymentId" = %(deployment_id)s
            )
            UPDATE billing_alert a
            SET "submissionDates" = c.combined_submission_dates,
                "nextSubmissionDoS" = %(this_month_end)s
            FROM result_set c
            WHERE a."mongoId" = c.id
            RETURNING a."mongoId"
        """
        params = {
            "deployment_id": deployment_id,
            "this_month_start": this_month_start,
            "this_month_end": this_month_end,
        }

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            return cursor.rowcount

    @id_as_obj_id
    def delete_user_data(self, user_id: str):
        count, _ = BillingAlert.objects.filter(user_id=user_id).delete()
        return count

    @staticmethod
    def _generate_submission_sort_keys_pipeline() -> list[dict[str, any]]:
        return [
            {
                _ExtraFields.CPT_2_WITHOUT_PRODUCT_TYPE: Case(
                    When(
                        productType=ProductType.RPM.value,
                        then=BillingAlertsDTO.CPT_99454_COMPLIANCE,
                    ),
                    default=BillingAlertsDTO.CPT_98976_COMPLIANCE,
                ),
            },
            {
                _ExtraFields.IS_SUBMISSION_GREY: Case(
                    When(
                        cpt2WithoutProductType__isnull=False,
                        cpt2WithoutProductType__lt=1,
                        then=Value(1),
                    ),
                    default=Value(0),
                )
            },
        ]

    @staticmethod
    def _generate_pre_validation_pipeline(product_type: str) -> dict[str, any]:
        today_date = datetime.utcnow().date()
        return {
            _ExtraFields.TODAY_DATE: Value(today_date),
            _ExtraFields.HAS_DOS: Case(
                When(nextSubmissionDoS__isnull=False, then=Value(True)),
                default=Value(False),
                output_field=BooleanField(),
            ),
            _ExtraFields.SUBMISSION_DATES: Coalesce(
                F("submissionDates"),
                Value([], output_field=JSONField()),
                output_field=JSONField(),
            ),
            _ExtraFields.PRODUCT_TYPE: Value(product_type),
            _ExtraFields.NEXT_SUBMISSION_DOS: Coalesce(F("nextSubmissionDoS"), Value(today_date)),
            _ExtraFields.LOWER_GIVEN_NAME: Case(
                When(user__givenName__isnull=False, then=Lower(F("user__givenName"))),
                default=Value(None),
            ),
            _ExtraFields.LOWER_FAMILY_NAME: Case(
                When(user__familyName__isnull=False, then=Lower(F("user__familyName"))),
                default=Value(None),
            ),
        }

    @staticmethod
    def _generate_submission_initial_fields_pipeline() -> list[dict[str, any]]:
        return [
            {
                _ExtraFields.LAST_SYNCED_DATE: Coalesce(
                    RawSQL(
                        f'("{BillingAlertsDTO.SUBMISSION_DATES}"->>(jsonb_array_length("{BillingAlertsDTO.SUBMISSION_DATES}") - 1))::date',
                        [],
                        output_field=DateTimeField(),
                    ),
                    Value(datetime.min, output_field=DateTimeField()),
                    output_field=DateTimeField(),
                ),
                _ExtraFields.SUBMISSION_COUNT: Func(
                    F(_ExtraFields.SUBMISSION_DATES),
                    function="jsonb_array_length",
                    output_field=IntegerField(),
                ),
                _ExtraFields.DAYS_TO_DOS: Ceil(
                    (
                        ExtractEpoch(
                            F(_ExtraFields.NEXT_SUBMISSION_DOS) - F(_ExtraFields.TODAY_DATE),
                            output_field=IntegerField(),
                        )
                        * 1000
                    )
                    / _AlertConstants.DAY_IN_MILLI_SECONDS,
                    output_field=IntegerField(),
                ),
            },
            {
                _ExtraFields.ACTUAL_SUBMISSION_COUNT: Case(
                    When(nextSubmissionDoS2__lt=F(_ExtraFields.TODAY_DATE), then=Value(0)),
                    default=F(_ExtraFields.SUBMISSION_COUNT),
                    output_field=IntegerField(),
                ),
            },
        ]

    @staticmethod
    def _set_next_submission_dos_classic() -> dict[str, any]:
        return {
            _ExtraFields.ACTUAL_NEXT_SUBMISSION_DOS: Case(
                When(
                    nextSubmissionDoS2__lt=F(_ExtraFields.TODAY_DATE),
                    then=ExpressionWrapper(
                        ExtractEpoch(F(_ExtraFields.TODAY_DATE), output_field=IntegerField()) * 1000
                        + (
                            ExpressionWrapper(
                                Func(
                                    Cast(F(_ExtraFields.DAYS_TO_DOS), BigIntegerField()),
                                    _AlertConstants.PERIOD_LENGTH_IN_DAYS,
                                    function="MOD",
                                )
                                + _AlertConstants.PERIOD_LENGTH_IN_DAYS,
                                output_field=IntegerField(),
                            )
                        )
                        * _AlertConstants.DAY_IN_MILLI_SECONDS,
                        output_field=IntegerField(),
                    ),
                ),
                default=ExpressionWrapper(
                    ExtractEpoch(F(_ExtraFields.NEXT_SUBMISSION_DOS)) * 1000,
                    output_field=IntegerField(),
                ),
                output_field=IntegerField(),
            )
        }

    @staticmethod
    def _set_next_submission_dos_calendar(current_period_dos: date) -> dict[str, any]:
        seconds = int(datetime.combine(current_period_dos, time.min).timestamp()) * 1000
        return {
            _ExtraFields.ACTUAL_NEXT_SUBMISSION_DOS: Value(seconds),
        }

    @staticmethod
    def _generate_monitoring_minutes_field_pipeline() -> dict[str, any]:
        (
            current_period_start_date,
            current_period_end_date,
        ) = calculate_current_period_start_and_end_date()
        return {
            _ExtraFields.MONITORING_MINUTES: Case(
                When(
                    DjangoQ(lastMonitoringDate__isnull=False)
                    & DjangoQ(lastMonitoringDate__gte=current_period_start_date),
                    then=Coalesce(F("monitoringMinutes"), Value(0.0)),
                ),
                default=Value(0.0),
                output_field=FloatField(),
            ),
        }

    @staticmethod
    def _generate_has_call_field_pipeline() -> dict[str, any]:
        (
            current_period_start_date,
            current_period_end_date,
        ) = calculate_current_period_start_and_end_date()
        return {
            _ExtraFields.HAS_CALL: Case(
                When(
                    callDateTime__isnull=False,
                    callDateTime__gte=current_period_start_date,
                    then=Value(True),
                ),
                default=Value(False),
                output_field=BooleanField(),
            ),
        }

    @staticmethod
    def is_last_7_days_of_period():
        _, current_period_end_date = calculate_current_period_start_and_end_date()
        today = datetime.now()
        result = current_period_end_date - datetime(today.year, today.month, today.day)
        return result.days < 7

    @staticmethod
    def _generate_submission_pipeline() -> list[dict[str, any]]:
        return [
            {
                _ExtraFields.SUBMISSION_DAYS_LEFT: ExpressionWrapper(
                    1
                    + Ceil(
                        (
                            F(_ExtraFields.ACTUAL_NEXT_SUBMISSION_DOS)
                            - ExtractEpoch(
                                F(_ExtraFields.TODAY_DATE),
                                output_field=IntegerField(),
                            )
                            * 1000
                        )
                        / _AlertConstants.DAY_IN_MILLI_SECONDS,
                        output_field=IntegerField(),
                    ),
                    output_field=IntegerField(),
                ),
                _ExtraFields.SUBMISSIONS_LEFT: ExpressionWrapper(
                    16 - F(_ExtraFields.ACTUAL_SUBMISSION_COUNT),
                    output_field=IntegerField(),
                ),
            },
            {
                _ExtraFields.CPT_2_THRESHOLD: Case(
                    When(
                        actualSubmissionCount__gte=_AlertConstants.MINIMUM_SUBMISSION_COUNT_NEEDED_PER_PERIOD,
                        then=Value(
                            _AlertConstants.UPPER_BOUND_FOR_CPT_2_THRESHOLD,
                            output_field=FloatField(),
                        ),
                    ),
                    default=ExpressionWrapper(
                        Round(
                            Cast(F(_ExtraFields.SUBMISSION_DAYS_LEFT), FloatField())
                            / Cast(F(_ExtraFields.SUBMISSIONS_LEFT), FloatField()),
                            precision=3,
                        ),
                        output_field=FloatField(),
                    ),
                    output_field=FloatField(),
                ),
            },
            {
                _ExtraFields.CPT_2_TEMP_COMPLAINCE: F(_ExtraFields.CPT_2_THRESHOLD),
            },
        ]

    @staticmethod
    def _generate_complaince_exchange_pipeline() -> dict[str, any]:
        return {
            BillingAlertsDTO.CPT_99454_COMPLIANCE: Case(
                When(
                    productType=ProductType.RPM.value,
                    then=F(_ExtraFields.CPT_2_TEMP_COMPLAINCE),
                ),
                default=Value(None),
            ),
            BillingAlertsDTO.CPT_98976_COMPLIANCE: Case(
                When(
                    productType=ProductType.RTM.value,
                    then=F(_ExtraFields.CPT_2_TEMP_COMPLAINCE),
                ),
                default=Value(None),
            ),
        }

    @staticmethod
    def _get_fields_to_remove() -> list[str]:
        return [
            _ExtraFields.CPT_2_TEMP_COMPLAINCE,
            _ExtraFields.SUBMISSIONS_LEFT,
            _ExtraFields.CPT_2_THRESHOLD,
            _ExtraFields.ACTUAL_SUBMISSION_COUNT,
            _ExtraFields.ACTUAL_NEXT_SUBMISSION_DOS,
            _ExtraFields.TODAY_DATE,
            _ExtraFields.HAS_DOS,
            _ExtraFields.IS_SUBMISSION_GREY,
            _ExtraFields.DAYS_TO_DOS,
            _ExtraFields.LOWER_FAMILY_NAME,
            _ExtraFields.LOWER_GIVEN_NAME,
        ]

    @staticmethod
    def _get_fields_to_replace() -> dict[str, str]:
        return {
            _ExtraFields.MONITORING_MINUTES: BillingAlertsDTO.MONITORING_MINUTES,
            _ExtraFields.SUBMISSION_DATES: BillingAlertsDTO.SUBMISSION_DATES,
            _ExtraFields.NEXT_SUBMISSION_DOS: BillingAlertsDTO.NEXT_SUBMISSION_DOS,
            _ExtraFields.LAST_SYNCED_DATE: BillingAlertsDTO.LAST_SYNCED_DATE,
            _ExtraFields.SUBMISSION_COUNT: BillingAlertsDTO.SUBMISSION_COUNT,
            _ExtraFields.HAS_CALL: BillingAlertsDTO.HAS_CALL,
            _ExtraFields.SUBMISSION_DAYS_LEFT: BillingAlertsDTO.SUBMISSION_DAYS_LEFT,
            _ExtraFields.SUBMISSION_COUNT_FINAL: BillingAlertsDTO.SUBMISSION_COUNT,
            _ExtraFields.NEXT_SUBMISSION_DOS_FINAL: BillingAlertsDTO.NEXT_SUBMISSION_DOS,
            _ExtraFields.CPT_99454_COMPLIANCE: BillingAlertsDTO.CPT_99454_COMPLIANCE,
            _ExtraFields.CPT_98976_COMPLIANCE: BillingAlertsDTO.CPT_98976_COMPLIANCE,
            "user_id": BillingAlertsDTO.USER_ID,
        }

    @staticmethod
    def _get_date_fields_to_convert() -> list[str]:
        return [
            BillingAlertsDTO.LAST_SYNCED_DATE,
            BillingAlertsDTO.NEXT_SUBMISSION_DOS,
        ]

    @staticmethod
    def _generate_final_validation_pipeline() -> dict[str, any]:
        return {
            _ExtraFields.SUBMISSION_COUNT_FINAL: Case(
                When(hasDoS=False, then=Value(-1)),
                default=F(_ExtraFields.ACTUAL_SUBMISSION_COUNT),
                output_field=IntegerField(),
            ),
            _ExtraFields.NEXT_SUBMISSION_DOS_FINAL: F(_ExtraFields.ACTUAL_NEXT_SUBMISSION_DOS),
            _ExtraFields.SUBMISSION_DAYS_LEFT: Case(
                When(hasDoS=False, then=Value(-1)),
                default=F(BillingAlertsDTO.SUBMISSION_DAYS_LEFT),
                output_field=IntegerField(),
            ),
            _ExtraFields.SUBMISSION_DATES: Case(
                When(
                    DjangoQ(nextSubmissionDoS2__lt=F(_ExtraFields.TODAY_DATE)) | DjangoQ(hasDoS=False),
                    then=Value([], output_field=JSONField()),
                ),
                default=F(_ExtraFields.SUBMISSION_DATES),
                output_field=JSONField(),
            ),
            _ExtraFields.CPT_99454_COMPLIANCE: Case(
                When(hasDoS=False, then=None),
                default=F(BillingAlertsDTO.CPT_99454_COMPLIANCE),
                output_field=FloatField(),
            ),
            _ExtraFields.CPT_98976_COMPLIANCE: Case(
                When(hasDoS=False, then=None),
                default=F(BillingAlertsDTO.CPT_98976_COMPLIANCE),
                output_field=FloatField(),
            ),
        }

    @staticmethod
    def _add_color_codings_for_submission(result: list):
        today_beginning = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)

        for billing_alert in result:
            submission_compliance = billing_alert.get(BillingAlertsDTO.CPT_98976_COMPLIANCE)
            if not submission_compliance:
                submission_compliance = billing_alert.get(BillingAlertsDTO.CPT_99454_COMPLIANCE)
                field = BillingAlertsDTO.CPT_99454_COMPLIANCE
            else:
                field = BillingAlertsDTO.CPT_98976_COMPLIANCE

            submission_dates = billing_alert.get(BillingAlertsDTO.SUBMISSION_DATES)
            submission_count = billing_alert.get(BillingAlertsDTO.SUBMISSION_COUNT)

            if (
                not submission_compliance
                or not isinstance(submission_compliance, dict)
                or not isinstance(submission_dates, list)
                or not isinstance(submission_count, int)
            ):
                continue

            threshold = submission_compliance.get(CPTCompliance.VALUE)
            submission_days_left = billing_alert.get(BillingAlertsDTO.SUBMISSION_DAYS_LEFT)

            if not threshold or not submission_days_left:
                continue

            if threshold < 1:
                color_code = BillingAlertsColorCoding.GREY
            elif threshold < 1.875:
                if today_beginning in submission_dates:
                    color_code = BillingAlertsColorCoding.LIGHT_RED
                else:
                    color_code = BillingAlertsColorCoding.RED
            elif submission_days_left <= 3 and submission_count < 16:
                color_code = BillingAlertsColorCoding.AMBER
            else:
                color_code = BillingAlertsColorCoding.WHITE

            submission_compliance[CPTCompliance.COLOR_CODE] = color_code
            billing_alert[field] = submission_compliance

    @staticmethod
    def _add_color_codings_for_time_tracking(result: list):
        if not result:
            return
        first_billing_alert = result[0]
        if first_billing_alert.get(_ExtraFields.PRODUCT_TYPE) == ProductType.RTM.value:
            monitoring_codes = [
                BillingAlertsDTO.CPT_98980_COMPLIANCE,
                BillingAlertsDTO.CPT_98981_COMPLIANCE,
                BillingAlertsDTO.CPT_98981X2_COMPLIANCE,
            ]
        else:
            monitoring_codes = [
                BillingAlertsDTO.CPT_99457_COMPLIANCE,
                BillingAlertsDTO.CPT_99458_COMPLIANCE,
                BillingAlertsDTO.CPT_99458X2_COMPLIANCE,
            ]
        last_7_days = PostgresBillingAlertsRepository.is_last_7_days_of_period()
        for billing_alert in result:
            monitoring_minutes = billing_alert.get(BillingAlertsDTO.MONITORING_MINUTES)
            has_call = billing_alert.get(BillingAlertsDTO.HAS_CALL)
            for code, expected_minutes in zip(monitoring_codes, [0, 20, 40]):
                color = BillingAlertsColorCoding.WHITE
                started = expected_minutes <= monitoring_minutes
                in_progress = expected_minutes <= monitoring_minutes < expected_minutes + 20

                if last_7_days and in_progress:
                    color = BillingAlertsColorCoding.RED
                if started and not has_call:
                    color = BillingAlertsColorCoding.RED

                billing_alert[code] = {CPTCompliance.COLOR_CODE: color}

    @staticmethod
    def _not_synced_filter() -> DjangoQ:
        sync_threshold = datetime.combine(datetime.utcnow(), time.min) - timedelta(days=2)
        return DjangoQ(lastSyncedDate2__lt=sync_threshold) | DjangoQ(lastSyncedDate2__isnull=True)
