from datetime import datetime, timedelta
from typing import Optional

from django.db.models import Q, Sum

from billing.components.core.dtos.billing_models import (
    BillingRemoteTimeTrackingDTO,
)
from billing.components.core.models import BillingRemoteTimeTracking
from billing.components.core.repository.billing_repository import (
    BillingRemoteTimeTrackingRepository,
)
from sdk.common.utils.validators import model_to_dict


class PostgresBillingRemoteTimeTrackingRepository(BillingRemoteTimeTrackingRepository):
    def create_billing_remote_time_tracking(
        self,
        deployment_id: str,
        clinician_id: str,
        start_datetime: datetime,
        end_datetime: datetime,
        user_id: str,
        **extra_direct_fields,
    ):
        document = BillingRemoteTimeTracking(
            deploymentId=deployment_id,
            clinicianId=clinician_id,
            startDateTime=start_datetime,
            endDateTime=end_datetime,
            userId=user_id,
            effectiveStartDateTime=None,
            effectiveEndDateTime=None,
            effectiveDuration=0,
            **extra_direct_fields,
        )
        document.save()
        document_id = document.mongoId
        self.remove_all_overlaps(user_id, start_datetime, end_datetime)
        return document_id

    def update_remote_time_tracking_start_and_end_dt(self, tracking_id: str, start_dt: datetime, end_dt: datetime):
        document = self._retrieve_remote_time_tracking_by_id(tracking_id)
        checking_start = min(start_dt, document.startDateTime)
        checking_end = max(end_dt, document.endDateTime)
        document.startDateTime = start_dt
        document.endDateTime = end_dt
        document.effectiveStartDateTime = start_dt
        document.effectiveEndDateTime = end_dt
        document.effectiveDuration = self._check_set_effective_duration_time(document)
        document.save()
        self.remove_all_overlaps(document.userId, checking_start, checking_end)

    @staticmethod
    def _retrieve_remote_time_tracking_by_id(tracking_id: str):
        return BillingRemoteTimeTracking.objects.filter(mongoId=tracking_id).first()

    def get_updated_overlapping_docs(self, document, including_document=False, document_start=None, document_end=None):
        match_query = {"userId": document.userId}

        if including_document:
            match_query["id__ne"] = document.id

        docs = (
            BillingRemoteTimeTracking.objects.filter(**match_query)
            .filter(
                (Q(startDateTime__lte=document_start) & Q(endDateTime__gte=document_end))
                | (Q(startDateTime__gte=document_start) & Q(startDateTime__lt=document_end))
                | (Q(endDateTime__gt=document_start) & Q(endDateTime__lte=document_end))
            )
            .order_by("startDateTime")
        )

        for doc in docs:
            doc.effectiveEndDateTime = doc.endDateTime
            doc.effectiveStartDateTime = doc.startDateTime
            doc.effectiveDuration = self._check_set_effective_duration_time(doc)
            doc.save()

        return docs

    @staticmethod
    def _check_set_effective_duration_time(
        document: BillingRemoteTimeTracking,
    ):
        if document.effectiveEndDateTime <= document.effectiveStartDateTime:
            return 0
        return (document.effectiveEndDateTime - document.effectiveStartDateTime).total_seconds()

    def retrieve_billing_time_spent_remote_time_tracking(self, user_id: str, start_date: datetime, end_date: datetime):
        time_spent = BillingRemoteTimeTracking.objects.filter(
            userId=user_id,
            effectiveStartDateTime__gte=start_date,
            effectiveEndDateTime__lt=end_date,
        ).aggregate(total=Sum(BillingRemoteTimeTrackingDTO.EFFECTIVE_DURATION))["total"]
        return time_spent or 0

    def remove_all_overlaps(self, user_id: str, start_date: Optional[datetime], end_date: Optional[datetime]):
        time_tracking_records = BillingRemoteTimeTracking.objects.filter(userId=user_id)
        if start_date:
            time_tracking_records = time_tracking_records.filter(endDateTime__gte=start_date)
        if end_date:
            time_tracking_records = time_tracking_records.filter(startDateTime__lte=end_date)
        time_tracking_records = time_tracking_records.order_by(
            BillingRemoteTimeTrackingDTO.START_DATE_TIME,
            BillingRemoteTimeTracking.MONGO_ID,
        )

        last_covered_time: Optional[datetime] = None
        for record in time_tracking_records:
            if last_covered_time is None:
                if record.effectiveEndDateTime != record.endDateTime or record.effectiveStartDateTime is None:
                    if record.effectiveStartDateTime is None:
                        record.effectiveStartDateTime = record.startDateTime
                    record.effectiveEndDateTime = record.endDateTime
                    record.effectiveDuration = self._check_set_effective_duration_time(record)
                    record.save()
            elif record.endDateTime > last_covered_time:
                expected_effective_start_time = max(last_covered_time, record.startDateTime)
                if (
                    record.effectiveStartDateTime != expected_effective_start_time
                    or record.effectiveEndDateTime != record.endDateTime
                ):
                    record.effectiveStartDateTime = expected_effective_start_time
                    record.effectiveEndDateTime = record.endDateTime
                    record.effectiveDuration = self._check_set_effective_duration_time(record)
                    record.save()
            else:
                if (
                    record.effectiveStartDateTime != record.startDateTime
                    or record.effectiveEndDateTime != record.startDateTime
                ):
                    record.effectiveStartDateTime = record.startDateTime
                    record.effectiveEndDateTime = record.startDateTime
                    record.effectiveDuration = self._check_set_effective_duration_time(record)
                    record.save()

            if last_covered_time is None or record.endDateTime > last_covered_time:
                last_covered_time = record.endDateTime

    def retrieve_billing_time_tracking_records(self, user_id: str, start_date: datetime, end_date: datetime):
        time_tracking_records = (
            BillingRemoteTimeTracking.objects.filter(
                userId=user_id,
                effectiveStartDateTime__gte=start_date,
                effectiveEndDateTime__lt=end_date,
            )
        ).order_by(BillingRemoteTimeTrackingDTO.EFFECTIVE_START_DATE_TIME)
        return time_tracking_records

    def delete_user_data(self, user_id: str):
        BillingRemoteTimeTracking.objects.filter(userId=user_id).delete()

    def move_time_tracks_between_deployments(
        self,
        user_id: str,
        source_id: str,
        target_id: str,
        from_dt: datetime,
    ):
        query = Q(userId=user_id, deploymentId=source_id, startDateTime__gte=from_dt)
        BillingRemoteTimeTracking.objects.filter(query).update(deploymentId=target_id)

    def retrieve_users_automated_logs_from_to(
        self,
        user_ids: list,
        start_date: datetime,
        end_date: datetime,
    ) -> list[BillingRemoteTimeTrackingDTO]:
        end_date += timedelta(days=1)
        query = Q(
            userId__in=user_ids,
            startDateTime__gte=start_date,
            startDateTime__lte=end_date,
        )
        docs = BillingRemoteTimeTracking.objects.filter(query).order_by(
            BillingRemoteTimeTrackingDTO.USER_ID,
            BillingRemoteTimeTrackingDTO.START_DATE_TIME,
        )

        return [BillingRemoteTimeTrackingDTO.from_dict(model_to_dict(doc)) for doc in docs]
