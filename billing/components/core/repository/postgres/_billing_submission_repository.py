from _datetime import <PERSON><PERSON><PERSON>
from datetime import datetime

from django.db.models import <PERSON>, <PERSON>, When, ExpressionWrapper, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.db.models.aggregates import Count, Sum

from billing.components.core.dtos.billing_models import BillingSubmissionDTO
from billing.components.core.models import BillingSubmission
from billing.components.core.repository.billing_repository import (
    BillingSubmissionRepository,
)
from sdk.common.utils.validators import id_as_obj_id, model_to_dict

LEAST_REQUIRED_SUBMISSIONS_FOR_BILLABLE_PERIOD = 16


class PostgresBillingSubmissionRepository(BillingSubmissionRepository):
    def create_update_billing_submission(self, submission: BillingSubmissionDTO, count: int):
        utc_now = datetime.utcnow()
        find_query = {
            BillingSubmissionDTO.DEPLOYMENT_ID: submission.deploymentId,
            BillingSubmissionDTO.USER_ID: submission.userId,
            BillingSubmissionDTO.START_DATE: submission.startDate,
        }
        existing_submission = BillingSubmission.objects.filter(**find_query).first()
        if existing_submission:
            existing_submission.updateDateTime = utc_now
            existing_submission.todaySubmissionCount += count
            existing_submission.isCompleted = submission.isCompleted
            existing_submission.save()
            return BillingSubmissionDTO.from_dict(model_to_dict(existing_submission))

        new_submission = BillingSubmission(
            **submission.to_dict(include_none=False),
            todaySubmissionCount=count - 1,  # 0-based counter
            createDateTime=utc_now,
            updateDateTime=utc_now,
        )
        new_submission.save()
        return BillingSubmissionDTO.from_dict(model_to_dict(new_submission))

    def find_user_first_submission(self, user_id: str, deployment_id: str = None):
        query = BillingSubmission.objects.filter(userId=user_id)

        if deployment_id is not None:
            query = query.filter(deploymentId=deployment_id)

        result = query.order_by(BillingSubmissionDTO.CREATE_DATE_TIME).first()
        return BillingSubmissionDTO.from_dict(model_to_dict(result)) if result else None

    def get_total_record_days_count_after_specific_date(self, user_id: str, date: datetime.date):
        query = BillingSubmission.objects.filter(userId=user_id, startDate__gte=date).aggregate(
            count=Count("startDate", distinct=True)
        )
        return query["count"] if query else 0

    def get_total_submission_count_from_to(
        self,
        user_id: str,
        from_date: datetime.date,
        to_date: datetime.date,
        deployment_id: str = None,
    ):
        filters = Q(userId=user_id)
        if deployment_id:
            filters &= Q(deploymentId=deployment_id)
        if from_date and to_date:
            filters &= Q(startDate__gte=from_date, startDate__lt=to_date + timedelta(days=1))

        query = BillingSubmission.objects.filter(filters).aggregate(
            tsc_sum=Sum(
                Case(
                    When(todaySubmissionCount__isnull=True, then=1),
                    default=ExpressionWrapper(F("todaySubmissionCount") + 1, output_field=IntegerField()),
                )
            )
        )
        return query["tsc_sum"] or 0

    def get_total_record_days_count_from_to(
        self,
        user_id: str,
        from_date: datetime.date,
        to_date: datetime.date,
        deployment_id: str = None,
    ):
        filters = Q(userId=user_id)
        if deployment_id:
            filters &= Q(deploymentId=deployment_id)
        if from_date and to_date:
            filters &= Q(startDate__gte=from_date, startDate__lt=to_date + timedelta(days=1))

        query = BillingSubmission.objects.filter(filters).aggregate(count=Count("startDate", distinct=True))
        return query["count"] or 0

    def find_16th_submission_in_period(self, user_id: str, start_date: datetime.date, end_date: datetime.date):
        query = BillingSubmission.objects.filter(
            userId=user_id,
            startDate__gte=start_date,
            startDate__lte=end_date,
        ).order_by("startDateTime")[15:16]
        result = query.first()
        return BillingSubmissionDTO.from_dict(model_to_dict(result)) if result else None

    def is_classic_period_billable_by_start_date(self, user_id: str, start_date: datetime.date):
        _billing_period_end_date_upper_bound = start_date + timedelta(days=30)
        return (
            BillingSubmission.objects.filter(
                userId=user_id,
                startDate__gte=start_date,
                startDate__lt=_billing_period_end_date_upper_bound,
            ).count()
            >= LEAST_REQUIRED_SUBMISSIONS_FOR_BILLABLE_PERIOD
        )

    def get_total_submissions_for_user_from_to(
        self,
        user_id: str,
        deployment_id: str = None,
        from_date: datetime.date = None,
        to_date: datetime.date = None,
        return_count=False,
    ):
        query = Q(userId=user_id)
        if deployment_id:
            query &= Q(deploymentId=deployment_id)
        if from_date:
            query &= Q(startDate__gte=from_date)
        if to_date:
            query &= Q(startDate__lte=to_date)

        result = BillingSubmission.objects.filter(query).order_by("startDateTime")
        if return_count:
            return result.count()
        return [BillingSubmissionDTO.from_dict(model_to_dict(r)) for r in result]

    @id_as_obj_id
    def find_billable_calendar_months_in_periods(
        self, user_id: str, periods: list[dict[str, datetime]]
    ) -> list[datetime]:
        """Finds periods with at least 16 submissions in the given periods. Returns the start date of each period."""
        if not periods:
            return []

        min_date = min(period["start"] for period in periods)
        max_date = max(period["end"] for period in periods)

        submissions = list(
            BillingSubmission.objects.filter(
                userId=user_id, startDate__gte=min_date, startDate__lte=max_date
            ).values_list("startDate", flat=True)
        )

        result = []
        for period in periods:
            period_start = period["start"]
            period_end = period["end"]

            period_count = sum(1 for date in submissions if period_start <= date <= period_end)
            if period_count >= LEAST_REQUIRED_SUBMISSIONS_FOR_BILLABLE_PERIOD:
                result.append(period_start)

        return sorted(set(result))

    def user_has_submission_after_date(self, user_id: str, reference_dt: datetime) -> bool:
        return BillingSubmission.objects.filter(userId=user_id, startDateTimeUTC__gt=reference_dt).exists()

    @id_as_obj_id
    def delete_user_data(self, user_id: str):
        count, _ = BillingSubmission.objects.filter(userId=user_id).delete()
        return count

    @id_as_obj_id
    def move_submissions_between_deployments(
        self,
        user_id: str,
        source_id: str,
        target_id: str,
        from_dt: datetime,
    ):
        return BillingSubmission.objects.filter(
            userId=user_id, deploymentId=source_id, startDateTime__gte=from_dt
        ).update(deploymentId=target_id)
