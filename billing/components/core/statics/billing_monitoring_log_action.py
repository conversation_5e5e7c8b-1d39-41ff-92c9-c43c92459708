from enum import Enum


class BillingMonitoringLogActionKeys(Enum):
    ATTEMPT_TO_CONTACT_PATIENT = "hu_billing_log_action_1"
    LEFT_A_VOICE_CALL = "hu_billing_log_action_2"
    PREPARATION_AND_DELIVERY_OF_PATIENT_EDUCATION = "hu_billing_log_action_3"
    CARE_COORDINATION_WITH_SPECIALISTS = "hu_billing_log_action_4"
    ESCALATION_AND_COMMUNICATION_WITH_PROVIDER = "hu_billing_log_action_5"
    PATIENT_PORTAL_COMMUNICATION = "hu_billing_log_action_6"
    INPATIENT_FACILITY_COMMUNICATION = "hu_billing_log_action_7"
    EHR_CHART_REVIEW = "hu_billing_log_action_8"
    TEXT_MESSAGE_COMMUNICATION = "hu_billing_log_action_9"
    EMAIL_COMMUNICATION = "hu_billing_log_action_10"
    CLINICAL_REVIEW = "hu_billing_log_action_11"
    QUALITY_REVIEW = "hu_billing_log_action_12"
