import calendar
from datetime import datetime, time, timedelta
from typing import Union

from billing.components.core.repository.billing_repository import (
    BillingRemoteTimeTrackingRepository,
)
from huma_plugins.components.online_offline_call.dtos.video_models import (
    OfflineVideoCallDTO,
)
from huma_plugins.components.online_offline_call.repository.video_repository import (
    OnlineOfflineCallRepository,
)
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.common.utils.inject import autoparams


@autoparams("billing_repo")
def get_time_spent(
    billing_repo: BillingRemoteTimeTrackingRepository,
    user_id: str,
    start_date: datetime.date = None,
    end_date: datetime.date = None,
):
    start_date = datetime.combine(start_date, time.min)
    end_date = datetime.combine(end_date, time.max)
    return billing_repo.retrieve_billing_time_spent_remote_time_tracking(
        user_id=user_id,
        start_date=start_date,
        end_date=end_date,
    )


@autoparams("video_call_repo")
def get_number_of_interactive_sessions(
    video_call_repo: OnlineOfflineCallRepository,
    user_id: str,
    from_date: datetime.date,
    to_date: datetime.date,
):
    from_date = datetime.combine(from_date, time.min)
    to_date = datetime.combine(to_date, time.max)
    return video_call_repo.retrieve_user_calls_with_statuses(
        user_id=user_id,
        statuses=[OfflineVideoCallDTO.CallStatus.ANSWERED.value],
        from_date_time=from_date,
        to_date_time=to_date,
        return_count=True,
    )


def calculate_current_period_start_and_end_date():
    today = datetime.now()
    current_period_start_date = datetime(today.year, today.month, 1)
    current_period_end_date = datetime(
        today.year,
        today.month,
        day=calendar.monthrange(today.year, today.month)[1],
    )
    return current_period_start_date, current_period_end_date


def is_date_in_current_month(date_to_check: Union[datetime.date, datetime]):
    if isinstance(date_to_check, datetime):
        date_to_check = datetime.date(date_to_check)
    (
        start_datetime,
        end_datetime,
    ) = calculate_current_period_start_and_end_date()
    start_date = datetime.date(start_datetime)
    end_date = datetime.date(end_datetime)
    return start_date <= date_to_check <= end_date


@autoparams("billing_repo")
def remove_user_billing_data_overlapping(
    user_id: str,
    billing_repo: BillingRemoteTimeTrackingRepository,
    start_dt: datetime = None,
    end_dt: datetime = None,
):
    if not start_dt and not end_dt:
        start_dt = datetime.utcnow() - timedelta(hours=27)
        end_dt = datetime.utcnow()
    billing_repo.remove_all_overlaps(user_id, start_dt, end_dt)


@autoparams("auth_repo")
def remove_users_billing_data_overlapping(
    auth_repo: AuthorizationRepository,
    start_dt: datetime = None,
    end_dt: datetime = None,
):
    # TODO: #django-test "deployment_id=None, search=None" should be null or the definition of the function should allow nullable
    users = auth_repo.retrieve_user_profiles(
        filters={"componentsData.billing.status": 1}, deployment_id=None, search=None
    )
    for user in users[0]:
        remove_user_billing_data_overlapping(user_id=user.id, start_dt=start_dt, end_dt=end_dt)
