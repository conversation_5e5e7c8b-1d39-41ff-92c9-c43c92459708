import logging
from collections import defaultdict
from datetime import datetime, timedelta
from enum import Enum

import pytz

from billing.components.core.dtos.billing_models import BillingSubmissionDTO
from billing.components.core.dtos.deployment_billing import (
    DeploymentBillingConfig,
)
from billing.components.core.helpers.export_billing_submission_helpers import (
    get_calendar_submission_period,
)
from billing.components.core.repository.billing_repository import (
    BillingSubmissionRepository,
)
from billing.components.core.use_case.billing_use_cases import (
    BillingSubmissionReminderProcessor,
)
from sdk.authorization.dtos.user import BoardingStatus
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.common.exceptions.exceptions import InvalidRequestException, ObjectDoesNotExist
from sdk.common.monitoring import report_exception
from sdk.common.utils.date_utils import localize_from_utc
from sdk.common.utils.inject import autoparams
from sdk.common.utils.validators import remove_none_values
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.event_bus.post_create_module_results_batch_event import (
    PostCreateModuleResultBatchEvent,
)
from sdk.module_result.modules import (
    BloodGlucoseModule,
    BloodPressureModule,
    HeartRateModule,
    HighFrequencyHeartRateModule,
    OxygenSaturationModule,
    PeakFlowModule,
    PulseOximetryModule,
    QuestionnaireModule,
    RespiratoryRateModule,
    StepModule,
    TemperatureModule,
    WeightModule,
)
from sdk.module_result.modules.questionnaire import QuestionnaireDTO

logger = logging.getLogger(__name__)

LEAST_REQUIRED_SUBMISSIONS_EXCEPT_LATEST = 15

RPM_MODULE_IDS = [
    WeightModule.moduleId,
    BloodPressureModule.moduleId,
    TemperatureModule.moduleId,
    HeartRateModule.moduleId,
    BloodGlucoseModule.moduleId,
    RespiratoryRateModule.moduleId,
    OxygenSaturationModule.moduleId,
    PeakFlowModule.moduleId,
    HighFrequencyHeartRateModule.moduleId,
]

RTM_MODULE_IDS = [
    StepModule.moduleId,
    RespiratoryRateModule.moduleId,
    PulseOximetryModule.moduleId,
    QuestionnaireModule.moduleId,
]


class PrimitiveSources(Enum):
    MANUAL = "Manual"
    HEALTH_KIT = "HealthKit"
    GOOGLE_FIT = "GoogleFit"
    HAPPI_TECH_SDK = "HappitechSDK"
    FIT_BIT = "Fitbit"
    HUMA_DEVICE_KIT = "HumaDeviceKit"
    HUMA_WATCH = "HumaWatch"


@autoparams("auth_repo")
def is_user_onboarded(user_id: str, start_date_time: datetime, auth_repo: AuthorizationRepository):
    user = auth_repo.retrieve_simple_user_profile(user_id=user_id)
    try:
        return (
            user.boardingStatus.status is BoardingStatus.Status.ACTIVE
            and user.boardingStatus.updateDateTime < start_date_time
        )
    except AttributeError:
        return False


def is_source_manual(event: PostCreateModuleResultBatchEvent) -> bool:
    return event.source is None or event.source == PrimitiveSources.MANUAL.value


def validate_source(source: str):
    if not source or source == PrimitiveSources.MANUAL.value:
        return

    source = source.split(";")
    if source and source[0] in [v.value for v in PrimitiveSources]:
        return

    raise InvalidRequestException(f"{source[0]} is not acceptable value")


def handle_billing_submission_for_rtm(event: PostCreateModuleResultBatchEvent, config: DeploymentBillingConfig):
    if event.module_id not in RTM_MODULE_IDS:
        logger.debug(f"Non-billable submission for RTM. Module: [{event.module_id}]")
        return
    if event.module_id == QuestionnaireModule.moduleId:
        found = False
        for primitive in event.raw_primitives:
            if primitive is QuestionnaireDTO and primitive.questionnaireName in config.rtmModuleIds:
                found = True
                break
        if not found:
            logger.debug("Non-billable submission for RTM. Module: [Questionnaire]")
            return
    return create_billing_record_for_module_result_submission(event, config)


def handle_billing_submission_for_rpm(event: PostCreateModuleResultBatchEvent, config: DeploymentBillingConfig):
    if event.module_id not in RPM_MODULE_IDS:
        logger.debug(f"Non-billable submission for RPM. Module: [{event.module_id}]")
        return

    validate_source(event.source)

    if is_source_manual(event):
        logger.debug(f"For RPM data recorded only on connected devices. Source: [{event.source}]")
        return

    submissions = create_billing_record_for_module_result_submission(event, config)
    if submissions:
        BillingSubmissionReminderProcessor(submissions[-1], config).process()  # use last submission in a batch
    return submissions


@autoparams("submission_repo")
def create_billing_record_for_module_result_submission(
    event: PostCreateModuleResultBatchEvent,
    billing_config: DeploymentBillingConfig,
    submission_repo: BillingSubmissionRepository,
) -> list[BillingSubmissionDTO]:
    user_id, deployment_id = event.user_id, event.deployment_id
    calendar_calculation = billing_config.useCalendarCalculation
    primitives = event.raw_primitives
    if not primitives:
        return []

    user_timezone = _get_user_timezone(user_id)
    primitives_by_date = _group_primitive_by_dates(event, user_timezone)
    first_submission = submission_repo.find_user_first_submission(user_id)
    first_submission_date = first_submission.startDateTime.date() if first_submission else None
    created_submissions = []
    for submission_date, primitives in primitives_by_date.items():
        count = len(primitives)
        primitive = primitives[-1]
        try:
            if first_submission_date is None:
                first_submission_date = submission_date
                created_submissions.append(_create_submission(primitive, count, is_completed=False))
                continue

            if _current_submission_is_earlier_than_first_submission(
                user_id, submission_date, first_submission_date
            ) or not _submission_is_in_current_billing_period(
                user_id, submission_date, first_submission_date, calendar_calculation
            ):
                continue

            is_completed = _billing_period_is_completed(
                user_id, deployment_id, first_submission_date, calendar_calculation
            )
            created_submissions.append(_create_submission(primitive, count, is_completed))

        except Exception as e:
            message = "Could not create a billing record"
            logger.error(
                message,
                extra={
                    "userId": event.user_id,
                    "deploymentId": event.deployment_id,
                    "startDateTime": primitive.startDateTime,
                },
            )

            report_exception(
                e,
                tags={
                    "deploymentId": event.deployment_id,
                    "startDateTime": primitive.startDateTime,
                },
                user_id=event.user_id,
            )

    return created_submissions


def _group_primitive_by_dates(
    event: PostCreateModuleResultBatchEvent, timezone: str
) -> dict[datetime.date, list[PrimitiveDTO]]:
    if not event:
        return {}

    tz = pytz.timezone(timezone)
    if isinstance(event.raw_primitives, PrimitiveDTO):
        primitive = event.raw_primitives
        localized_dt = localize_from_utc(primitive.startDateTime, tz)
        primitive.local_dt = localized_dt
        return {localized_dt.date(): [primitive]}

    primitives = sorted(event.raw_primitives, key=lambda x: x.startDateTime)
    grouped_by_date = defaultdict(list)
    for primitive in primitives:
        localized_dt = localize_from_utc(primitive.startDateTime, tz)
        primitive.local_dt = localized_dt
        grouped_by_date[localized_dt.date()].append(primitive)

    return grouped_by_date


@autoparams("auth_repo")
def _get_user_timezone(user_id, auth_repo: AuthorizationRepository):
    try:
        return auth_repo.retrieve_users_timezones([user_id])[user_id]
    except ObjectDoesNotExist:
        return "UTC"


def _current_submission_is_earlier_than_first_submission(
    user_id: str, submission_date: datetime.date, first_submission_date: datetime.date
):
    if submission_date < first_submission_date:
        logger.debug(
            f"Submission with startDateTime: {str(submission_date)} for user {user_id}"
            f" is earlier than very first submission. skipping ... "
        )
        return True
    return False


def _find_period_boundaries(first_submission_date: datetime.date, use_calendar_calculation: bool):
    today_date = datetime.utcnow().date()
    if use_calendar_calculation:
        start_date, end_date = get_calendar_submission_period(today_date)
    else:
        total_periods_until_event_date = (today_date - first_submission_date).days // 30
        start_date = first_submission_date + timedelta(30 * total_periods_until_event_date)
        end_date = start_date + timedelta(days=29)

    return start_date, end_date


def _submission_is_in_current_billing_period(
    user_id: str,
    submission_date: datetime.date,
    first_submission_date: datetime.date,
    use_calendar_calculation: bool,
):
    start_date, end_date = _find_period_boundaries(first_submission_date, use_calendar_calculation)
    if not start_date <= submission_date <= end_date:
        logger.debug(
            f"Submission with startDateTime: {str(submission_date)} for user {user_id}"
            f" is not in current period. skipping ... "
        )
        return False
    return True


@autoparams("submission_repo")
def _billing_period_is_completed(
    user_id: str,
    deployment_id: str,
    first_submission_date: datetime.date,
    use_calendar_calculation: bool,
    submission_repo: BillingSubmissionRepository,
):
    start_date, end_date = _find_period_boundaries(first_submission_date, use_calendar_calculation)
    submission_days_count = submission_repo.get_total_submissions_for_user_from_to(
        deployment_id=deployment_id,
        user_id=user_id,
        from_date=start_date,
        to_date=end_date,
        return_count=True,
    )

    return submission_days_count >= LEAST_REQUIRED_SUBMISSIONS_EXCEPT_LATEST


@autoparams("submission_repo")
def _create_submission(
    primitive: PrimitiveDTO,
    count: int,
    is_completed: bool,
    submission_repo: BillingSubmissionRepository,
) -> BillingSubmissionDTO:
    submission_dict = remove_none_values(
        {
            BillingSubmissionDTO.USER_ID: primitive.userId,
            BillingSubmissionDTO.DEPLOYMENT_ID: primitive.deploymentId,
            BillingSubmissionDTO.PRIMITIVE_ID: primitive.id,
            BillingSubmissionDTO.PRIMITIVE_CLASS_NAME: primitive.get_primitive_name(),
            BillingSubmissionDTO.DEVICE_NAME: primitive.deviceName,
            BillingSubmissionDTO.SOURCE: primitive.source,
            BillingSubmissionDTO.START_DATE_TIME: primitive.local_dt,
            BillingSubmissionDTO.START_DATE_TIME_UTC: primitive.startDateTime,
            BillingSubmissionDTO.START_DATE: primitive.local_dt.date(),
            BillingSubmissionDTO.IS_COMPLETED: is_completed if is_completed else None,
        }
    )
    submission = BillingSubmissionDTO.from_dict(submission_dict)
    submission_repo.create_update_billing_submission(submission, count)
    return submission
