import calendar
from _datetime import timedelta
from datetime import date, datetime, time
from typing import Optional

from dateutil.relativedelta import relativedelta

from billing.components.core.dtos.deployment_billing import ProductType
from billing.components.core.helpers.billing_periods_calculators import (
    BillingCalendarPeriodCalculator,
)
from billing.components.core.helpers.calculate_cumulative_helpers import (
    get_status_for_cpt_mapping_3,
    get_status_for_cpt_mapping_4_1x,
    get_status_for_cpt_mapping_4_2x,
)
from billing.components.core.helpers.export_billing_time_tracking_helpers import (
    get_time_spent,
)
from billing.components.core.repository.billing_repository import (
    BillingSubmissionRepository,
)
from billing.components.core.router.billing_response_objects import (
    CPTRecordStatusType,
)
from billing.components.export.models.billing_models import (
    BillingGeneralReportCPTCodes,
)
from sdk.common.utils.inject import autoparams

LEAST_REQUIRED_SUBMISSIONS_EXCEPT_LATEST = 15


@autoparams("billing_repo")
def get_user_total_submission_days_count(
    billing_repo: BillingSubmissionRepository,
    user_id: str,
    deployment_id: str = None,
    from_date: date = None,
    to_date: date = None,
):
    return billing_repo.get_total_record_days_count_from_to(user_id, from_date, to_date, deployment_id)


@autoparams("billing_repo")
def get_user_total_submission_count(
    billing_repo: BillingSubmissionRepository,
    user_id: str,
    deployment_id: str = None,
    from_date: date = None,
    to_date: date = None,
):
    return billing_repo.get_total_submission_count_from_to(user_id, from_date, to_date, deployment_id)


@autoparams("billing_repo")
def find_compliance_date(
    user_id: str,
    period_start: date,
    period_end: date,
    billing_repo: BillingSubmissionRepository,
) -> Optional[date]:
    record = billing_repo.find_16th_submission_in_period(user_id, period_start, period_end)
    return record.startDateTime.date() if record else None


def get_calendar_submission_period(ref_date: datetime.date) -> tuple[date, date]:
    calculator = BillingCalendarPeriodCalculator()
    return calculator.calculate(ref_date)


def get_billable_period_counts_for_user(
    user_id: str,
    product_type: ProductType,
    from_date: date = None,
    to_date: date = None,
    use_calendar: bool = False,
):
    reports = get_billable_period_count(
        user_id=user_id,
        from_date=from_date,
        to_date=to_date,
        use_calendar=use_calendar,
    )
    if product_type is ProductType.RPM:
        return {
            BillingGeneralReportCPTCodes.CPT_99453: reports[0],
            BillingGeneralReportCPTCodes.CPT_99454: reports[1],
            BillingGeneralReportCPTCodes.CPT_99457: reports[2],
            BillingGeneralReportCPTCodes.CPT_99458: reports[3] + reports[4],
            **get_non_billable_report_for_cpt_codes(ProductType.RTM),
        }
    elif product_type is ProductType.RTM:
        return {
            BillingGeneralReportCPTCodes.CPT_98975: reports[0],
            BillingGeneralReportCPTCodes.CPT_98976: reports[1],
            BillingGeneralReportCPTCodes.CPT_98980: reports[2],
            BillingGeneralReportCPTCodes.CPT_98981: reports[3] + reports[4],
            **get_non_billable_report_for_cpt_codes(ProductType.RPM),
        }


@autoparams("billing_repo")
def get_user_first_submission(user_id: str, billing_repo: BillingSubmissionRepository, deployment_id: str = None):
    return billing_repo.find_user_first_submission(user_id, deployment_id)


def get_billable_period_count(
    user_id: str,
    from_date: date,
    to_date: date,
    use_calendar: bool,
):
    first_billable_period_start_date = _find_first_billable_period_start(user_id, use_calendar, to_date)
    if first_billable_period_start_date:
        from_date = max(first_billable_period_start_date, from_date)

    return [
        get_billable_period_count_for_cpt_1(
            user_id, first_billable_period_start_date, from_date, to_date, use_calendar
        ),
        get_billable_period_count_for_cpt_2(
            user_id, first_billable_period_start_date, from_date, to_date, use_calendar
        ),
        get_billable_period_count_for_cpt_3(user_id=user_id, from_date=from_date, to_date=to_date),
        get_billable_period_count_for_cpt_4_1x(user_id=user_id, from_date=from_date, to_date=to_date),
        get_billable_period_count_for_cpt_4_2x(user_id=user_id, from_date=from_date, to_date=to_date),
    ]


def _find_first_billable_period_start(user_id: str, use_calendar: bool, to_date: date):
    if use_calendar:
        return get_start_date_of_first_calendar_billable_or_current_period(user_id=user_id, check_upper_bound=to_date)

    return get_start_date_of_first_billable_or_current_period(user_id=user_id, check_upper_bound=to_date)


@autoparams("billing_repo")
def get_billable_period_count_for_cpt_1(
    user_id: str,
    first_period_start: Optional[date],
    from_date: date,
    to_date: date,
    use_calendar: bool,
    billing_repo: BillingSubmissionRepository,
) -> int:
    if not first_period_start:
        return 0

    if use_calendar:
        _, dos = get_calendar_submission_period(first_period_start)
        if not from_date <= dos <= to_date:
            return 0

        return get_billable_period_count_for_cpt_calendar(user_id, first_period_start, dos, return_first=True)

    first_period_end = first_period_start + timedelta(days=29)
    if from_date <= first_period_end <= to_date and billing_repo.is_classic_period_billable_by_start_date(
        user_id, first_period_start
    ):
        return 1
    return 0


@autoparams("billing_repo")
def get_billable_period_count_for_cpt_2(
    user_id: str,
    first_billing_start_date: Optional[date],
    from_date: date,
    to_date: date,
    use_calendar: bool,
    billing_repo: BillingSubmissionRepository,
) -> int:
    if not first_billing_start_date:
        return 0

    if use_calendar:
        return get_billable_period_count_for_cpt_calendar(user_id, from_date, to_date)
    else:
        return get_billable_period_count_for_cpt_2_classic(
            user_id, first_billing_start_date, from_date, to_date, billing_repo
        )


@autoparams("billing_repo")
def get_billable_period_count_for_cpt_2_classic(
    user_id: str,
    first_billing_start_date: Optional[date],
    from_date: date,
    to_date: date,
    billing_repo: BillingSubmissionRepository,
) -> int:
    check_start_date = first_billing_start_date
    billing_period_counts = 0

    while check_start_date + timedelta(days=29) < from_date:
        check_start_date += timedelta(days=30)

    while check_start_date + timedelta(days=29) <= to_date:
        if billing_repo.is_classic_period_billable_by_start_date(user_id, check_start_date):
            billing_period_counts += 1
        check_start_date += timedelta(days=30)

    return billing_period_counts


@autoparams("repo")
def get_billable_period_count_for_cpt_calendar(
    user_id: str,
    from_date: date,
    to_date: date,
    repo: BillingSubmissionRepository,
    return_first: bool = False,
) -> int:
    periods = generate_calendar_bp_periods(from_date, to_date, True)
    if not periods:
        return 0

    billable_periods = repo.find_billable_calendar_months_in_periods(user_id=user_id, periods=periods)
    billing_period_counts = len(billable_periods)
    if billing_period_counts > 0 and return_first:
        return 1
    return billing_period_counts


def get_billable_period_count_for_cpt_3(user_id: str, from_date: date, to_date: date) -> int:
    billing_period_counts = 0
    start_date_to_check_in_repository = datetime(from_date.year, from_date.month, 1)
    end_date_to_check_in_repository = datetime(
        from_date.year,
        from_date.month,
        day=calendar.monthrange(from_date.year, from_date.month)[1],
    )
    while end_date_to_check_in_repository <= datetime(to_date.year, to_date.month, to_date.day):
        time_spent = get_time_spent(
            user_id=user_id,
            start_date=start_date_to_check_in_repository,
            end_date=end_date_to_check_in_repository,
        )
        status = get_status_for_cpt_mapping_3(
            user_id=user_id,
            time_spent=time_spent,
            start_date_time=datetime.combine(start_date_to_check_in_repository, time.min),
            end_date_time=datetime.combine(end_date_to_check_in_repository, time.max),
        )
        if status == CPTRecordStatusType.COMPLETED:
            billing_period_counts += 1
        start_date_to_check_in_repository = start_date_to_check_in_repository + relativedelta(months=1)
        end_date_to_check_in_repository = datetime(
            start_date_to_check_in_repository.year,
            start_date_to_check_in_repository.month,
            day=calendar.monthrange(
                start_date_to_check_in_repository.year,
                start_date_to_check_in_repository.month,
            )[1],
        )
    return billing_period_counts


def get_billable_period_count_for_cpt_4_1x(user_id: str, from_date: date, to_date: date) -> int:
    billing_period_counts = 0
    start_date_to_check_in_repository = datetime(from_date.year, from_date.month, 1)
    end_date_to_check_in_repository = datetime(
        from_date.year,
        from_date.month,
        day=calendar.monthrange(from_date.year, from_date.month)[1],
    )
    while end_date_to_check_in_repository <= datetime(to_date.year, to_date.month, to_date.day):
        time_spent = get_time_spent(
            user_id=user_id,
            start_date=start_date_to_check_in_repository,
            end_date=end_date_to_check_in_repository,
        )
        status_cpt_mapping_3 = get_status_for_cpt_mapping_3(
            user_id=user_id,
            time_spent=time_spent,
            start_date_time=datetime.combine(start_date_to_check_in_repository, time.min),
            end_date_time=datetime.combine(end_date_to_check_in_repository, time.max),
        )
        status_4_1x = get_status_for_cpt_mapping_4_1x(time_spent=time_spent, status_cpt_mapping_3=status_cpt_mapping_3)
        if status_4_1x == CPTRecordStatusType.COMPLETED:
            billing_period_counts += 1
        start_date_to_check_in_repository = start_date_to_check_in_repository + relativedelta(months=1)
        end_date_to_check_in_repository = datetime(
            start_date_to_check_in_repository.year,
            start_date_to_check_in_repository.month,
            day=calendar.monthrange(
                start_date_to_check_in_repository.year,
                start_date_to_check_in_repository.month,
            )[1],
        )
    return billing_period_counts


def get_billable_period_count_for_cpt_4_2x(user_id: str, from_date: date, to_date: date) -> int:
    billing_period_counts = 0
    start_date_to_check_in_repository = datetime(from_date.year, from_date.month, 1)
    end_date_to_check_in_repository = datetime(
        from_date.year,
        from_date.month,
        day=calendar.monthrange(from_date.year, from_date.month)[1],
    )
    while end_date_to_check_in_repository <= datetime(to_date.year, to_date.month, to_date.day):
        time_spent = get_time_spent(
            user_id=user_id,
            start_date=start_date_to_check_in_repository,
            end_date=end_date_to_check_in_repository,
        )
        status_cpt_mapping_3 = get_status_for_cpt_mapping_3(
            user_id=user_id,
            time_spent=time_spent,
            start_date_time=datetime.combine(start_date_to_check_in_repository, time.min),
            end_date_time=datetime.combine(end_date_to_check_in_repository, time.max),
        )
        status_4_2x = get_status_for_cpt_mapping_4_2x(time_spent=time_spent, status_cpt_mapping_3=status_cpt_mapping_3)
        if status_4_2x == CPTRecordStatusType.COMPLETED:
            billing_period_counts += 1
        start_date_to_check_in_repository = start_date_to_check_in_repository + relativedelta(months=1)
        end_date_to_check_in_repository = datetime(
            start_date_to_check_in_repository.year,
            start_date_to_check_in_repository.month,
            day=calendar.monthrange(
                start_date_to_check_in_repository.year,
                start_date_to_check_in_repository.month,
            )[1],
        )
    return billing_period_counts


def get_non_billable_report_for_cpt_codes(product_type: ProductType):
    if product_type is ProductType.RPM:
        return {
            BillingGeneralReportCPTCodes.CPT_99453: 0,
            BillingGeneralReportCPTCodes.CPT_99454: 0,
            BillingGeneralReportCPTCodes.CPT_99457: 0,
            BillingGeneralReportCPTCodes.CPT_99458: 0,
        }
    return {
        BillingGeneralReportCPTCodes.CPT_98975: 0,
        BillingGeneralReportCPTCodes.CPT_98976: 0,
        BillingGeneralReportCPTCodes.CPT_98980: 0,
        BillingGeneralReportCPTCodes.CPT_98981: 0,
    }


def generate_periodical_result_for_submission_cpt_codes(
    user_id: str,
    from_date: datetime.date,
    to_date: datetime.date,
):
    earliest_billing_date = find_compliance_date(user_id=user_id, period_start=from_date, period_end=to_date)
    earliest_billing_date = str(earliest_billing_date) if earliest_billing_date else None

    return (
        str(from_date),
        str(to_date),
        get_user_total_submission_count(
            user_id=user_id,
            from_date=from_date,
            to_date=to_date,
        ),
        get_user_total_submission_days_count(
            user_id=user_id,
            from_date=from_date,
            to_date=to_date,
        ),
        earliest_billing_date,
    )


@autoparams("billing_repo")
def get_start_date_of_first_billable_or_current_period(
    billing_repo: BillingSubmissionRepository,
    user_id: str,
    deployment_id: str = None,
    check_upper_bound: datetime.date = None,
    return_current_period: bool = True,
) -> date | None:
    """Finds the first 30-day Billable period.
    Billable period is a period with at least 16 submissions for a user.
    If no such period, optionally returns the start date of the current period."""
    all_submissions = list(
        billing_repo.get_total_submissions_for_user_from_to(
            user_id=user_id, deployment_id=deployment_id, to_date=check_upper_bound
        )
    )
    if not all_submissions:
        return None
    iter_submission_start_date = all_submissions[0].startDateTime.date()
    upper_bound_start_date = min(
        (all_submissions[-1].startDateTime + timedelta(days=30)).date(),
        check_upper_bound or datetime.utcnow().date(),
    )
    while iter_submission_start_date < upper_bound_start_date:
        period_end_upper_bound = iter_submission_start_date + timedelta(days=30)
        selected = list(
            filter(
                lambda sub: iter_submission_start_date <= sub.startDateTime.date() < period_end_upper_bound,
                all_submissions,
            )
        )
        if len(selected) > LEAST_REQUIRED_SUBMISSIONS_EXCEPT_LATEST:
            return iter_submission_start_date
        if period_end_upper_bound > datetime.utcnow().date():
            return iter_submission_start_date
        iter_submission_start_date = period_end_upper_bound
    return iter_submission_start_date if return_current_period else None


@autoparams("repo")
def get_start_date_of_first_calendar_billable_or_current_period(
    repo: BillingSubmissionRepository,
    user_id: str,
    check_upper_bound: date = None,
) -> date:
    """Finds the first CALENDAR Billable period. with at least 16 submissions for a user.
    Billable period is a period with at least 16 submissions for a user.
    If no such period, returns the start date of the current period (month)."""
    today = datetime.utcnow().date()
    first_submission = repo.find_user_first_submission(user_id=user_id)
    if first_submission:
        from_date, _ = get_calendar_submission_period(first_submission.startDate)
        to_date = check_upper_bound or today
        if from_date <= to_date:
            periods = generate_calendar_bp_periods(from_date, to_date)
            billable_periods = repo.find_billable_calendar_months_in_periods(user_id=user_id, periods=periods)
            if billable_periods:
                return billable_periods[0].date()

    reference_date = today
    if check_upper_bound:
        reference_date = min(today, check_upper_bound)
    period_start, _ = get_calendar_submission_period(reference_date)
    return period_start


def generate_calendar_bp_periods(
    from_date: date, to_date: date, dos_in_timeframe: bool = False
) -> list[dict[str, datetime]]:
    billing_periods = []
    while from_date <= to_date:
        start, end = get_calendar_submission_period(from_date)
        if end > to_date:
            if dos_in_timeframe:
                break
            end = to_date

        start_bp = datetime.combine(start, time.min)
        end_bp = datetime.combine(end, time.min)
        billing_periods.append({"start": start_bp, "end": end_bp})
        from_date += relativedelta(months=1)

    return billing_periods
