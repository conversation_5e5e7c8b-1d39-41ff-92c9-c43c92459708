from typing import Optional

from billing.components.core.dtos.deployment_billing import (
    BILLING_FEATURES_KEY,
    DeploymentBillingConfig,
)
from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.utils.convertible import ConvertibleClassValidationError
from sdk.deployment.service.deployment_service import DeploymentService


def get_deployment_billing_object(deployment):
    try:
        billing_dict = deployment.features.customAppConfig.get(BILLING_FEATURES_KEY)
        if billing_dict is None:
            return None

        return DeploymentBillingConfig.from_dict(billing_dict)

    except (AttributeError, ConvertibleClassValidationError):
        return None


def validate_deployment_billing_enabled(deployment):
    if not is_billing_enabled_for_deployment(deployment):
        raise InvalidRequestException(f"Billing feature is not enabled for deployment {deployment.id}")


def is_billing_enabled_for_deployment(deployment):
    billing = get_deployment_billing_object(deployment)
    return billing and billing.enabled


def is_billing_enabled_for_deployment_id(deployment_id: str):
    deployment = DeploymentService().retrieve_deployment(deployment_id=deployment_id)
    return is_billing_enabled_for_deployment(deployment)


def get_billing_product_type(deployment, include_config=False):
    def _consider_config(result, config=None):
        if include_config:
            return result, config
        return result

    billing = get_deployment_billing_object(deployment)
    if billing:
        return _consider_config(billing.productType, billing)
    return _consider_config(None)


def extract_billing_product_type_by_deployment_id(deployment_id: str, include_config=False):
    return get_billing_product_type(
        DeploymentService().retrieve_deployment(deployment_id=deployment_id),
        include_config,
    )


def extract_deployment_billing_by_deployment_id(
    deployment_id: str,
) -> Optional[DeploymentBillingConfig]:
    deployment = DeploymentService().retrieve_deployment(deployment_id=deployment_id)
    return get_deployment_billing_object(deployment)
