import calendar
from datetime import date, datetime

from sdk.common.utils.common_functions_utils import find


class BillingCalendarPeriodCalculator:
    WINTER_MONTHS = (
        12,
        1,
        2,
    )

    def calculate(self, reference_date: date) -> tuple[date, date]:
        y, m = reference_date.year, reference_date.month
        if m in self.WINTER_MONTHS:
            start, end = self._get_period_for_winter_months(reference_date)
        else:
            start = reference_date.replace(day=1)
            _, days_in_month = calendar.monthrange(y, m)
            end = date(y, m, days_in_month)
        return start, end

    def _get_period_for_winter_months(self, ref_date: datetime.date) -> tuple[date, date]:
        if calendar.isleap(ref_date.year):
            periods = self._get_leap_year_months
        elif calendar.isleap(ref_date.year + 1):
            periods = self._get_common_with_next_leap_year
        else:
            periods = self._get_common_year_months

        start, end = find(lambda p: p[0] <= ref_date <= p[1], periods(ref_date))
        return start, end

    @staticmethod
    def _get_common_year_months(d: datetime.date) -> list:
        common_year_periods = [
            (
                d.replace(year=d.year - 1, month=12, day=31),
                d.replace(month=1, day=29),
            ),
            (
                d.replace(month=1, day=30),
                d.replace(month=2, day=28),
            ),
            (
                d.replace(month=12, day=1),
                d.replace(month=12, day=30),
            ),
            (
                d.replace(month=12, day=31),
                d.replace(year=d.year + 1, month=1, day=29),
            ),
        ]
        return common_year_periods

    @staticmethod
    def _get_leap_year_months(d: datetime.date) -> list:
        leap_year_periods = [
            (
                d.replace(month=1, day=1),
                d.replace(month=1, day=30),
            ),
            (
                d.replace(month=1, day=31),
                d.replace(month=2, day=29),
            ),
            (
                d.replace(month=12, day=1),
                d.replace(month=12, day=30),
            ),
            (
                d.replace(month=12, day=31),
                d.replace(year=d.year + 1, month=1, day=29),
            ),
        ]
        return leap_year_periods

    @staticmethod
    def _get_common_with_next_leap_year(d: datetime.date) -> list:
        common_with_next_leap_year = [
            (
                d.replace(year=d.year - 1, month=12, day=31),
                d.replace(month=1, day=29),
            ),
            (
                d.replace(month=1, day=30),
                d.replace(month=2, day=28),
            ),
            (
                d.replace(month=12, day=1),
                d.replace(month=12, day=31),
            ),
        ]
        return common_with_next_leap_year
