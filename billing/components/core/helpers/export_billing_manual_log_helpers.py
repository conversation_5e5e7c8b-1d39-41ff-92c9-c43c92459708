from datetime import datetime

from billing.components.core.repository.billing_repository import (
    BillingMonitoringLogRepository,
)
from sdk.common.utils.inject import autoparams


@autoparams("billing_monitoring_log_repo")
def retrieve_user_monitoring_logs_from_to(
    from_dt: datetime,
    to_dt: datetime,
    user_ids: list[str],
    billing_monitoring_log_repo: BillingMonitoringLogRepository,
    deployment_id: str = None,
):
    return billing_monitoring_log_repo.retrieve_user_monitoring_logs_from_to(
        deployment_id=deployment_id, from_dt=from_dt, to_dt=to_dt, user_ids=user_ids
    )
