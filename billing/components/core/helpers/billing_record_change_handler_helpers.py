import datetime
from datetime import timedelta
from typing import Optional

from billing.components.core.helpers.export_billing_submission_helpers import (
    get_calendar_submission_period,
)
from billing.components.core.repository.billing_repository import (
    BillingAlertsRepository,
    BillingSubmissionRepository,
)
from sdk.common.utils.inject import autoparams


@autoparams("billing_alerts_repo")
def check_and_retrieve_user_alerts(user_id: str, billing_alerts_repo: BillingAlertsRepository) -> (bool, dict):
    result = billing_alerts_repo.retrieve_user_billing_alerts(user_id=user_id)
    result = result.to_dict() if result else {}
    return bool(result), result


@autoparams("repo")
def calculate_classic_billing_period(
    deployment_id,
    user_id,
    repo: BillingSubmissionRepository,
) -> (Optional[datetime.date], Optional[datetime.date], Optional[datetime.date]):
    first_submission = repo.find_user_first_submission(user_id, deployment_id)
    if first_submission is None:
        return None, None, None

    _first_submission_date = first_submission.startDateTime.date()
    _event_submission_date = datetime.datetime.utcnow().date()

    if _first_submission_date > _event_submission_date:
        return (
            _first_submission_date,
            _event_submission_date,
            _event_submission_date + timedelta(days=29),
        )

    total_periods_until_event_date = ((_event_submission_date - first_submission.startDateTime.date()).days) // 30

    _event_period_start_date = _first_submission_date + timedelta(30 * total_periods_until_event_date)
    _event_period_end_date = _event_period_start_date + timedelta(days=29)

    return _first_submission_date, _event_period_start_date, _event_period_end_date


@autoparams("repo")
def calculate_calendar_based_billing_period(
    deployment_id,
    user_id,
    repo: BillingSubmissionRepository,
):
    first_submission = repo.find_user_first_submission(user_id, deployment_id)
    if first_submission is None:
        return None, None, None

    _first_submission_date = first_submission.startDateTime.date()
    _event_submission_date = datetime.datetime.utcnow().date()

    _event_period_start_date, _event_period_end_date = get_calendar_submission_period(_event_submission_date)

    return _first_submission_date, _event_period_start_date, _event_period_end_date


def calculate_current_billing_period(deployment_id: str, user_id: str, use_calendar_calculation: bool = False):
    if use_calendar_calculation:
        return calculate_calendar_based_billing_period(deployment_id, user_id)
    return calculate_classic_billing_period(deployment_id, user_id)
