from datetime import date
from enum import Enum, IntEnum

from billing.components.core.dtos.billing_models import (
    ResponseBillingMonitoringLogDTO,
)
from sdk import convertibleclass
from sdk.common.usecase.response_object import Response, ResponseObject
from sdk.common.utils.convertible import (
    default_field,
    meta,
    positive_integer_field,
    required_field,
)


class CPTCodeIterationNumber(IntEnum):
    IterationFor1x = 1
    IterationFor2x = 2


class MinutesInSeconds(IntEnum):
    TWENTY_MINUTES_IN_SECONDS = 1200
    FORTY_MINUTES_IN_SECONDS = 2400
    SIXTY_MINUTES_IN_SECONDS = 3600


class CPTRecordStatusType:
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    INCOMPLETE = "INCOMPLETE"
    NON_BILLABLE = "NON_BILLABLE"

    @classmethod
    def field_description(cls):
        values = [v for v in cls.__dict__ if v.isupper()]
        return f"Any of: {', '.join(values)}"


class CPTCode:
    ALL_CODES: Enum

    class RPM(Enum):
        CPT_MAPPING_1 = "99453"
        CPT_MAPPING_2 = "99454"
        CPT_MAPPING_3 = "99457"
        CPT_MAPPING_4 = "99458"

    class RTM(Enum):
        CPT_MAPPING_1 = "98975"
        CPT_MAPPING_2 = "98976"
        CPT_MAPPING_3 = "98980"
        CPT_MAPPING_4 = "98981"

    @classmethod
    def all_codes_enum(cls):
        members = {f"{enum.__name__}_{member.name}": member.value for enum in [cls.RTM, cls.RPM] for member in enum}
        return Enum("AllCPTCodes", members)


CPTCode.ALL_CODES = CPTCode.all_codes_enum()


@convertibleclass
class CPTReportResponseObject(ResponseObject):
    STATUS = "status"
    EARLIEST_BILLING_DATE = "earliestBillingDate"
    START_DATE = "startDate"
    END_DATE = "endDate"
    DAYS_HAVE_SUBMISSION = "daysHaveSubmission"
    TIME_SPENT = "timeSpent"
    CPT_CODE_ITERATION = "cptCodeIteration"
    CPT_CODE = "cptCode"
    ORDER = "order"

    status: str = required_field(metadata=meta(description=CPTRecordStatusType.field_description()))
    earliestBillingDate: date = default_field()
    startDate: date = default_field()
    endDate: date = default_field()
    daysHaveSubmission: int = default_field()
    timeSpent: float = default_field()
    cptCodeIteration: CPTCodeIterationNumber = default_field(metadata=meta(by_value=True))
    cptCode: CPTCode.ALL_CODES = required_field()
    order: int = required_field()


class RetrieveAlertsResponseObject(Response):
    FILTERED = "filtered"
    TOTAL = "total"
    MISSING_CALL = "missingCall"
    MISSING_SUBMISSION = "missingSubmission"
    NOT_SYNCED = "notSynced"
    USERS = "users"

    @convertibleclass
    class Response:
        filtered: int = required_field()
        total: int = required_field()
        missingCall: int = required_field()
        missingSubmission: int = required_field()
        notSynced: int = default_field()
        users: list[dict] = required_field()

    def __init__(
        self,
        filtered: int,
        total: int,
        missing_call: int,
        missing_submission: int,
        not_synced: int,
        users: list[dict],
    ):
        super().__init__(
            value=self.Response(
                filtered=filtered,
                total=total,
                missingCall=missing_call,
                missingSubmission=missing_submission,
                notSynced=not_synced,
                users=users,
            )
        )


@convertibleclass
class CreateUpdateBillingMonitoringLogResponseObject(Response):
    ID = "id"
    MONITORING_TIME_SECONDS = "monitoringTimeSeconds"
    CPT_CODE = "cptCode"
    CPT_CODE_ITERATION = "cptCodeIteration"
    CPT_RECORD_STATUS = "cptRecordStatus"

    id: str = required_field()
    userId: str = required_field()
    clinicianId: str = required_field()
    deploymentId: str = required_field()
    startDateTime: str = required_field()
    endDateTime: str = required_field()
    createDateTime: str = required_field()
    updateDateTime: str = required_field()
    action: str = required_field()
    monitoringTimeSeconds: float = default_field()
    cptCode: str = default_field()
    cptCodeIteration: int = default_field()
    cptRecordStatus: str = default_field()


@convertibleclass
class DeleteBillingMonitoringLogResponseObject(Response):
    ID = "id"

    id: str = required_field()


@convertibleclass
class BillingMonitoringLogsResponseObject(ResponseObject):
    items: list[ResponseBillingMonitoringLogDTO] = required_field()
    total: int = required_field()
    limit: int = positive_integer_field(default=0)
    skip: int = positive_integer_field(default=20)


class BillingMonitoringLogActionListResponseObject(Response):
    @convertibleclass
    class Response:
        logActions: dict[str, str] = required_field()

    def __init__(self, actions: dict[str, str]):
        super().__init__(value=self.Response(logActions=actions))
