from billing.components.core.router.billing_response_objects import (
    CPTRecordStatusType,
)
from sdk.common.usecase.response_object import ResponseObject
from sdk.common.utils.convertible import required_field, convertibleclass, meta


@convertibleclass
class CalculateCumulativeMonitoringTimeResponseObject(ResponseObject):
    MONITORING_TIME_SECONDS = "monitoringTimeSeconds"
    USER_ID = "userId"
    CPT_CODE = "cptCode"
    CPT_CODE_ITERATION = "cptCodeIteration"
    STATUS = "status"

    monitoringTimeSeconds: float = required_field()
    userId: str = required_field()
    cptCode: str = required_field()
    cptCodeIteration: int = required_field()
    status: str = required_field(metadata=meta(description=CPTRecordStatusType.field_description()))
