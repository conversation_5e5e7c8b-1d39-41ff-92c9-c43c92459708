from dataclasses import field
from datetime import date, datetime, timedelta
from enum import Enum

from billing.components.core.dtos.billing_models import BillingMonitoringLogDTO
from billing.components.core.dtos.deployment_billing import (
    DeploymentBillingConfig,
    ProductType,
)
from billing.components.core.exceptions import (
    ClinicianNotActive,
    InvalidLogDuration,
    PatientNotActive,
)
from billing.components.core.helpers.alerts_helpers import (
    get_deployment_billing_object,
    validate_deployment_billing_enabled,
)
from sdk import convertibleclass
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.utils import raise_exception_if_user_is_offboarded
from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.usecase.request_object import RequestObject
from sdk.common.utils.convertible import (
    ConvertibleClassValidationError,
    default_field,
    meta,
    natural_number_field,
    positive_integer_field,
    required_field,
)
from sdk.common.utils.validators import (
    default_datetime_meta,
    must_be_present,
    must_not_be_present,
    validate_object_id,
    validate_object_ids,
)
from sdk.deployment.dtos.deployment import DeploymentDTO

DEFAULT_PROFILE_RESULT_PAGE_SIZE = 20
HOUR_IN_SECONDS = 3600
MANUAL_MONITORING_UPPER_BOUND_IN_HOURS = 24
MAXIMUM_LOGGING_DATE_GAP_IN_DAYS = 31
ALLOWED_FUTURE_TIME_DELTA = timedelta(minutes=10)


@convertibleclass
class BillingMonitoringLogDTORequestObject(BillingMonitoringLogDTO):
    USER = "user"
    CLINICIAN = "clinician"

    user: AuthorizedUser = required_field()
    clinician: AuthorizedUser = required_field()

    @classmethod
    def _has_valid_duration(cls, start: datetime, end: datetime):
        return (
            round(round((end - start).total_seconds(), 2) / HOUR_IN_SECONDS, 2)
        ) < MANUAL_MONITORING_UPPER_BOUND_IN_HOURS

    @classmethod
    def _is_user_registered_on_monitoring_period(cls, user: AuthorizedUser, start_date_time: datetime) -> bool:
        user_create_date_time = user.get_user_create_date_time()
        if user_create_date_time:
            return user_create_date_time <= start_date_time
        return False

    @classmethod
    def _start_date_time_after_user_update_boarding_status(cls, user: AuthorizedUser, start_date_time: datetime):
        update_boarding_status = user.get_update_boarding_status()
        return start_date_time >= update_boarding_status

    @classmethod
    def validate(cls, request_object):
        must_be_present(
            action=request_object.action,
            startDateTime=request_object.startDateTime,
            endDateTime=request_object.endDateTime,
        )
        must_not_be_present(
            timeSpent=request_object.timeSpent,
            createDateTime=request_object.createDateTime,
            updateDateTime=request_object.updateDateTime,
            timeTrackingId=request_object.timeTrackingId,
            status=request_object.status,
        )

        deployment = request_object.user.deployment
        validate_deployment_billing_enabled(deployment)

        if not cls._is_user_registered_on_monitoring_period(
            user=request_object.clinician, start_date_time=request_object.startDateTime
        ):
            raise ClinicianNotActive("Clinician was not active during this period.")

        if not cls._is_user_registered_on_monitoring_period(
            user=request_object.user, start_date_time=request_object.startDateTime
        ):
            raise PatientNotActive("Patient was not active during this period.")

        if not cls._has_valid_duration(request_object.startDateTime, request_object.endDateTime):
            raise InvalidLogDuration(f"Maximum meeting duration is {MANUAL_MONITORING_UPPER_BOUND_IN_HOURS} hours.")
        if request_object.startDateTime > request_object.endDateTime or request_object.endDateTime > datetime.utcnow():
            raise ConvertibleClassValidationError("Given datetime(s) not valid")


@convertibleclass
class CreateBillingMonitoringLogRequestObject(BillingMonitoringLogDTORequestObject):
    @classmethod
    def _is_date_gap_valid(cls, dt: datetime):
        return (datetime.utcnow() - dt).days < MAXIMUM_LOGGING_DATE_GAP_IN_DAYS

    def post_init(self):
        self.userId = self.user.id
        self.status = BillingMonitoringLogDTO.BillingMonitoringLogStatus.ACTIVE.value
        self.deploymentId = self.user.deployment.id
        self.createdById = self.lastModifiedById = self.clinician.id
        self.initialCreateDateTime = self.createDateTime = self.updateDateTime = datetime.utcnow()

    @classmethod
    def validate(cls, request_object):
        must_not_be_present(addendum=request_object.addendum)
        super().validate(request_object)

        if not cls._is_date_gap_valid(request_object.startDateTime):
            raise InvalidLogDuration

        if (
            request_object.status
            and request_object.status not in BillingMonitoringLogDTO.BillingMonitoringLogStatus.__members__.values()
        ):
            raise ConvertibleClassValidationError("Invalid status")


@convertibleclass
class RetrieveBillingMonitoringLogsRequestBody(RequestObject):
    SKIP = "skip"
    LIMIT = "limit"

    skip: int = positive_integer_field(default=0)
    limit: int = natural_number_field(default=DEFAULT_PROFILE_RESULT_PAGE_SIZE)


@convertibleclass
class RetrieveBillingMonitoringLogsRequestObject(RetrieveBillingMonitoringLogsRequestBody):
    SUBMITTER = "submitter"
    USER = "user"

    submitter: AuthorizedUser = required_field()
    user: AuthorizedUser = required_field()

    @classmethod
    def validate(cls, request_object):
        deployment = request_object.user.deployment
        validate_deployment_billing_enabled(deployment)


@convertibleclass
class UpdateBillingMonitoringLogRequestObject(BillingMonitoringLogDTORequestObject):
    LOG_ID = "logId"

    logId: str = required_field(metadata=meta(validate_object_id))

    def post_init(self):
        self.status = BillingMonitoringLogDTO.BillingMonitoringLogStatus.ACTIVE.value
        self.lastModifiedById = self.clinician.id
        self.createDateTime = self.updateDateTime = datetime.utcnow()

    @classmethod
    def validate(cls, request_object):
        must_be_present(addendum=request_object.addendum)
        super().validate(request_object)


@convertibleclass
class DeleteBillingMonitoringLogRequestObject(BillingMonitoringLogDTORequestObject):
    LOG_ID = "logId"

    logId: str = required_field(metadata=meta(validate_object_id))

    def post_init(self):
        self.lastModifiedById = self.clinician.id
        self.updateDateTime = datetime.utcnow()

    @classmethod
    def validate(cls, request_object):
        must_be_present(addendum=request_object.addendum)


@convertibleclass
class RetrieveBillingMonitoringLogActionListRequestObject(RequestObject):
    LANGUAGE = "language"

    language: str = required_field()


@convertibleclass
class CreateBillingRequestBody(RequestObject):
    START_DATE_TIME = "startDateTime"
    END_DATE_TIME = "endDateTime"

    startDateTime: datetime = field(default_factory=datetime.utcnow, metadata=default_datetime_meta())
    endDateTime: datetime = field(default_factory=datetime.utcnow, metadata=default_datetime_meta())


@convertibleclass
class CreateBillingRequestObject(CreateBillingRequestBody):
    USER = "user"
    CLINICIAN = "clinician"

    user: AuthorizedUser = required_field()
    clinician: AuthorizedUser = required_field()

    @classmethod
    def validate(cls, request_object):
        validate_deployment_billing_enabled(request_object.user.deployment)
        raise_exception_if_user_is_offboarded(request_object.user.user)

        time_delta = request_object.endDateTime - request_object.startDateTime
        relaxed_utc_now = datetime.utcnow() + ALLOWED_FUTURE_TIME_DELTA
        if (
            time_delta.total_seconds() > 60
            or request_object.startDateTime > request_object.endDateTime
            or request_object.startDateTime > relaxed_utc_now
            or request_object.endDateTime > relaxed_utc_now
        ):
            raise ConvertibleClassValidationError("Given datetime(s) not valid")


class AlertField(Enum):
    COMPLIANCE = "COMPLIANCE"
    DEADLINE = "DEADLINE"
    TIME_SPENT = "TIME_SPENT"
    LAST_SYNCED = "LAST_SYNCED"


class AlertCPTField(Enum):
    CPT_99454 = "99454"
    CPT_99457 = "99457"
    CPT_99458 = "99458"
    CPT_98976 = "98976"
    CPT_98980 = "98980"
    CPT_98981 = "98981"


class AlertOrderField(Enum):
    DESCENDING = "DESCENDING"
    ASCENDING = "ASCENDING"


@convertibleclass
class AlertsSortFields:
    FIELD = "field"
    CPT = "cpt"
    ORDER = "order"

    field: AlertField = required_field()
    order: AlertOrderField = required_field()
    cpt: AlertCPTField = default_field()


@convertibleclass
class AlertSortParameters:
    FIELDS = "fields"
    fields: list[AlertsSortFields] = required_field()


class AlertFilterFields(Enum):
    MISSED_CALL = "MISSED_CALL"
    MISSED_SUBMISSION = "MISSED_SUBMISSION"
    NOT_SYNCED = "NOT_SYNCED"

    missed_call: str = default_field()
    missed_submission: str = default_field()
    not_synced: str = default_field()


@convertibleclass
class AlertFilterParameters:
    BILLING = "billing"
    LABELS = "labels"

    billing: list[AlertFilterFields] = default_field()
    labels: list[str] = default_field(metadata=meta(validate_object_ids))


@convertibleclass
class RetrieveAlertsRequestObject(RequestObject):
    SORT = "sort"
    SEARCH = "search"
    SKIP = "skip"
    LIMIT = "limit"
    FILTERS = "filters"
    DEPLOYMENT = "deployment"
    CAN_VIEW_IDENTIFIER_DATA = "canViewIdentifierData"

    sort: AlertSortParameters = default_field()
    search: str = default_field()
    skip: int = positive_integer_field(default=0)
    limit: int = natural_number_field(default=DEFAULT_PROFILE_RESULT_PAGE_SIZE)
    filters: AlertFilterParameters = default_field()
    deployment: DeploymentDTO = default_field()
    canViewIdentifierData: bool = field(default=False)

    @classmethod
    def validate(cls, request_object):
        billing_config = get_deployment_billing_object(request_object.deployment)
        cls.validate_billing_enabled(billing_config, request_object.deployment.id)
        product_type = billing_config.productType
        cls.validate_filter_params(request_object, product_type)
        cls.validate_sort_params(request_object, product_type)

    @staticmethod
    def validate_billing_enabled(billing_config: DeploymentBillingConfig, deployment_id: str):
        if not billing_config or not billing_config.enabled:
            msg = f"Deployment {deployment_id} does not have billing set up"
            raise InvalidRequestException(msg)

    @staticmethod
    def validate_filter_params(request_object: "RetrieveAlertsRequestObject", product_type: ProductType):
        if not request_object.filters:
            return

        billing_filters = request_object.filters.billing or []
        if AlertFilterFields.NOT_SYNCED in billing_filters and product_type is not ProductType.RPM:
            msg = f"{AlertFilterFields.NOT_SYNCED} filter is allowed only for RPM Deployment"
            raise InvalidRequestException(msg)

    @staticmethod
    def validate_sort_params(request_object: "RetrieveAlertsRequestObject", product_type: ProductType):
        if not request_object.sort:
            return

        for sort_field in request_object.sort.fields:
            if sort_field.field is AlertField.LAST_SYNCED and product_type is not ProductType.RPM:
                raise InvalidRequestException(f"{AlertFilterFields.NOT_SYNCED} sort is allowed only for RPM Deployment")

        RetrieveAlertsRequestObject._check_sort_keys_duplication(request_object.sort)
        RetrieveAlertsRequestObject._check_if_sort_is_valid_based_on_sort_field_and_cpt_code(request_object.sort)
        RetrieveAlertsRequestObject._check_deployment_product_type_matches_cpt_code(request_object.sort, product_type)

    @staticmethod
    def _check_sort_keys_duplication(
        sort: AlertSortParameters,
    ):
        set_sort_filters = set([(sort_filter.field, sort_filter.cpt) for sort_filter in sort.fields])
        if len(set_sort_filters) != len(sort.fields):
            raise InvalidRequestException("Pairs of sort field and CPT code should not be repeated in the request")

    @staticmethod
    def _check_deployment_product_type_matches_cpt_code(sort: AlertSortParameters, product_type: ProductType):
        for sort_cpt in sort.fields:
            if sort_cpt.cpt is None:
                continue

            if product_type is ProductType.RPM:
                rtm_cpt_codes = [
                    AlertCPTField.CPT_98976.value,
                    AlertCPTField.CPT_98980.value,
                    AlertCPTField.CPT_98981.value,
                ]
                if sort_cpt.cpt.value in rtm_cpt_codes:
                    raise InvalidRequestException("filter RTM cpt codes from RPM Deployment is not allowed")

            elif product_type is ProductType.RTM:
                rpm_cpt_codes = [
                    AlertCPTField.CPT_99454.value,
                    AlertCPTField.CPT_99457.value,
                    AlertCPTField.CPT_99458.value,
                ]
                if sort_cpt.cpt.value in rpm_cpt_codes:
                    raise InvalidRequestException("filter RPM cpt codes from RTM Deployment is not allowed")

    @staticmethod
    def _check_if_sort_is_valid_based_on_sort_field_and_cpt_code(
        sort: AlertSortParameters,
    ):
        for sort_field in sort.fields:
            if sort_field.cpt is None:
                continue

            if sort_field.cpt == AlertCPTField.CPT_99454 or sort_field.cpt == AlertCPTField.CPT_98976:
                if sort_field.field is AlertField.TIME_SPENT:
                    raise InvalidRequestException(
                        f"Sorting cpt {sort_field.field.value} with TIME_SPENT field is not allowed"
                    )
            elif sort_field.field is not AlertField.TIME_SPENT:
                raise InvalidRequestException(
                    f"Sorting based on {sort_field.field.value} and COMPLIANCE/DEADLINE sort field is not allowed"
                )


@convertibleclass
class BillingAlertTimeTrackingDetailsRequestObject(RequestObject):
    USER = "user"
    MONITORING_SECONDS = "monitoringSeconds"
    MONITORING_DATE = "monitoringDate"
    user: AuthorizedUser = required_field()
    monitoringSeconds: float = required_field()
    monitoringDate: date = required_field()


@convertibleclass
class DeleteUserBillingRequestObject(RequestObject):
    USER_ID = "userId"

    userId: str = required_field(
        metadata=meta(validate_object_id, value_to_field=str),
    )
