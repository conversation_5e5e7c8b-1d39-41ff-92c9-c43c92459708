from enum import Enum

from billing.components.core.helpers.alerts_helpers import (
    validate_deployment_billing_enabled,
)
from sdk import convertibleclass
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.common.usecase.request_object import RequestObject
from sdk.common.utils.convertible import required_field


class CPTMappingNumber(Enum):
    CPT_99453_98975 = "1"
    CPT_99454_98976 = "2"
    CPT_99457_98980 = "3"
    CPT_99458_98981 = "4"


@convertibleclass
class ReportBillingRecordsForCPTRequestObject(RequestObject):
    USER = "user"

    user: AuthorizedUser = required_field()

    @classmethod
    def validate(cls, request_object):
        deployment = request_object.user.deployment
        validate_deployment_billing_enabled(deployment)
