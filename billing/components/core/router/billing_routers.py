from flasgger import swag_from
from flask import request, jsonify, g

from billing.components.core.router.billing_calculate_cumulative_requests import (
    CalculateCumulativeMonitoringTimeRequestObject,
)
from billing.components.core.router.billing_calculate_cumulative_response_objects import (
    CalculateCumulativeMonitoringTimeResponseObject,
)
from billing.components.core.router.billing_report_requests import (
    ReportBillingRecordsForCPTRequestObject,
)
from billing.components.core.router.billing_requests import (
    CreateBillingRequestObject,
    RetrieveAlertsRequestObject,
    BillingAlertTimeTrackingDetailsRequestObject,
    CreateBillingMonitoringLogRequestObject,
    RetrieveBillingMonitoringLogsRequestObject,
    RetrieveBillingMonitoringLogActionListRequestObject,
    UpdateBillingMonitoringLogRequestObject,
    DeleteBillingMonitoringLogRequestObject,
    CreateBillingRequestBody,
    RetrieveBillingMonitoringLogsRequestBody,
)
from billing.components.core.router.billing_response_objects import (
    BillingMonitoringLogsResponseObject,
    CPTReportResponseObject,
)
from billing.components.core.security.access import BillingAccess
from billing.components.core.use_case.billing_alerts_use_cases import (
    RetrieveBillingAlertUseCase,
    BillingAlertTimeTrackingDetailsUseCase,
)
from billing.components.core.use_case.billing_calculate_cumulative_use_cases import (
    CalculateCumulativeMonitoringTimeCPTCodeUseCase,
)
from billing.components.core.use_case.billing_monitoring_log_use_cases import (
    BillingMonitoringLogUseCase,
    RetrieveBillingMonitoringLogsUseCase,
    RetrieveBillingMonitoringLogActionListUseCase,
    UpdateBillingMonitoringLogUseCase,
    DeleteBillingMonitoringLogUseCase,
)
from billing.components.core.use_case.billing_report_use_cases import (
    ReportBillingRecordsForCPTUseCase,
)
from billing.components.core.use_case.billing_use_cases import (
    CreateBillingRemoteTimeTrackingUseCase,
)
from sdk.common.constants import SWAGGER_DIR
from sdk.common.utils.flask_request_utils import (
    get_request_json_dict_or_raise_exception,
)
from sdk.common.utils.validators import remove_none_values, utc_str_to_date
from sdk.security import ProtectedBlueprint, Access

api = ProtectedBlueprint(
    "billing_route",
    __name__,
    url_prefix="/api/extensions/v1/billing",
)


@api.post("/user/<user_id>")
@api.requires(Access.USER.VIEW_DATA & BillingAccess.TRACK_TIME)
@api.input(CreateBillingRequestBody.Schema)
@api.output(CalculateCumulativeMonitoringTimeResponseObject.Schema, 201)
@swag_from(f"{SWAGGER_DIR}/create_time_tracking.yml")
def create_billing_remote_time_tracking(user_id, json_data: CreateBillingRequestBody):
    request_object = CreateBillingRequestObject.from_dict(
        {
            **json_data.dump(),
            CreateBillingRequestObject.USER: g.authz_path_user,
            CreateBillingRequestObject.CLINICIAN: g.authz_user,
        }
    )
    CreateBillingRemoteTimeTrackingUseCase().execute(request_object)

    calculate_cumulative_request_object = CalculateCumulativeMonitoringTimeRequestObject.from_dict(
        {CalculateCumulativeMonitoringTimeRequestObject.USER: g.authz_path_user}
    )
    response_object = CalculateCumulativeMonitoringTimeCPTCodeUseCase().execute(calculate_cumulative_request_object)

    request_object_time_tracking_details_billing_alert = BillingAlertTimeTrackingDetailsRequestObject.from_dict(
        {
            BillingAlertTimeTrackingDetailsRequestObject.USER: g.authz_path_user,
            BillingAlertTimeTrackingDetailsRequestObject.MONITORING_SECONDS: response_object.monitoringTimeSeconds,
            BillingAlertTimeTrackingDetailsRequestObject.MONITORING_DATE: utc_str_to_date(json_data.endDateTime),
        }
    )
    BillingAlertTimeTrackingDetailsUseCase().execute(request_object_time_tracking_details_billing_alert)
    return response_object


@api.post("/user/<user_id>/log")
@api.requires(Access.USER.EDIT_DATA)
@swag_from(f"{SWAGGER_DIR}/create_monitoring_log.yml")
def create_billing_monitoring_log(user_id):
    body = get_request_json_dict_or_raise_exception(request)
    data = {
        CreateBillingMonitoringLogRequestObject.USER: g.authz_path_user,
        CreateBillingMonitoringLogRequestObject.CLINICIAN: g.authz_user,
    }
    data.update(body)

    req_obj = CreateBillingMonitoringLogRequestObject.from_dict(data)

    resp_obj = BillingMonitoringLogUseCase().execute(req_obj)

    return jsonify(resp_obj.to_dict()), 201


@api.post("/user/<user_id>/logs")
@api.requires(Access.USER.VIEW_DATA)
@api.input(RetrieveBillingMonitoringLogsRequestBody.Schema)
@api.output(BillingMonitoringLogsResponseObject.Schema)
@swag_from(f"{SWAGGER_DIR}/retrieve_monitoring_log.yml")
def retrieve_billing_monitoring_logs(user_id, json_data: RetrieveBillingMonitoringLogsRequestBody):
    req_obj = RetrieveBillingMonitoringLogsRequestObject.from_dict(
        {
            **json_data.dump(),
            RetrieveBillingMonitoringLogsRequestObject.USER: g.authz_path_user,
            RetrieveBillingMonitoringLogsRequestObject.SUBMITTER: g.authz_user,
        }
    )
    return RetrieveBillingMonitoringLogsUseCase().execute(req_obj)


@api.put("/user/<user_id>/log/<log_id>")
@api.requires(Access.USER.VIEW_DATA & BillingAccess.EDIT)
@swag_from(f"{SWAGGER_DIR}/update_monitoring_log.yml")
def update_billing_monitoring_log(user_id, log_id):
    body = get_request_json_dict_or_raise_exception(request)
    body.update(
        {
            UpdateBillingMonitoringLogRequestObject.USER: g.authz_path_user,
            UpdateBillingMonitoringLogRequestObject.CLINICIAN: g.authz_user,
            UpdateBillingMonitoringLogRequestObject.LOG_ID: log_id,
        }
    )
    req_obj = UpdateBillingMonitoringLogRequestObject.from_dict(body)
    response = UpdateBillingMonitoringLogUseCase().execute(req_obj)
    return jsonify(response.to_dict()), 200


@api.delete("/user/<user_id>/log/<log_id>")
@api.requires(Access.USER.VIEW_DATA & BillingAccess.DELETE)
@swag_from(f"{SWAGGER_DIR}/delete_monitoring_log.yml")
def delete_billing_monitoring_log(user_id, log_id):
    body = get_request_json_dict_or_raise_exception(request)
    body.update(
        {
            DeleteBillingMonitoringLogRequestObject.USER: g.authz_path_user,
            DeleteBillingMonitoringLogRequestObject.CLINICIAN: g.authz_user,
            DeleteBillingMonitoringLogRequestObject.LOG_ID: log_id,
        }
    )

    req_obj = DeleteBillingMonitoringLogRequestObject.from_dict(body)
    resp = DeleteBillingMonitoringLogUseCase().execute(req_obj)
    return jsonify(resp), 200


@api.get("/log-actions")
@swag_from(f"{SWAGGER_DIR}/retrieve_monitoring_log_action_list.yml")
def retrieve_billing_monitoring_log_action_list():
    language = g.authz_user.get_language()
    req_obj = RetrieveBillingMonitoringLogActionListRequestObject.from_dict(
        {RetrieveBillingMonitoringLogActionListRequestObject.LANGUAGE: language}
    )
    response = RetrieveBillingMonitoringLogActionListUseCase().execute(req_obj)
    return jsonify(response.value), 200


@api.get("/user/<user_id>/report")
@api.requires(Access.USER.VIEW_DATA & BillingAccess.TRACK_TIME)
@api.output(CPTReportResponseObject.Schema(many=True))
@swag_from(f"{SWAGGER_DIR}/cpt_report.yml")
def report_billing_records_for_cpt(user_id):
    data = {ReportBillingRecordsForCPTRequestObject.USER: g.authz_path_user}
    request_object = ReportBillingRecordsForCPTRequestObject.from_dict(data)
    return ReportBillingRecordsForCPTUseCase().execute(request_object)


@api.post("/profiles")
@api.requires(Access.GLOBAL.VIEW_DATA)
@swag_from(f"{SWAGGER_DIR}/retrieve_billing_alerts.yml")
def retrieve_billing_alerts():
    body = get_request_json_dict_or_raise_exception(request)
    is_identified = g.authz_user.has_identifier_data_permission()
    data = {
        RetrieveAlertsRequestObject.DEPLOYMENT: g.authz_user.deployment,
        RetrieveAlertsRequestObject.CAN_VIEW_IDENTIFIER_DATA: is_identified,
    }
    data.update(body)
    request_object = RetrieveAlertsRequestObject.from_dict(data)
    response = RetrieveBillingAlertUseCase().execute(request_object=request_object)
    return jsonify(remove_none_values(response.value.to_dict())), 200


@api.get("/user/<user_id>")
@api.requires(Access.USER.VIEW_DATA & BillingAccess.TRACK_TIME)
@api.output(CalculateCumulativeMonitoringTimeResponseObject.Schema)
def calculate_cumulative_monitoring_time_and_cpt_code(user_id):
    data = {CalculateCumulativeMonitoringTimeRequestObject.USER: g.authz_path_user}
    request_object = CalculateCumulativeMonitoringTimeRequestObject.from_dict(data)
    return CalculateCumulativeMonitoringTimeCPTCodeUseCase().execute(request_object)
