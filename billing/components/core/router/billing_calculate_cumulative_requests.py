from billing.components.core.helpers.alerts_helpers import (
    validate_deployment_billing_enabled,
)
from sdk import convertibleclass
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.common.usecase.request_object import RequestObject
from sdk.common.utils.convertible import required_field


@convertibleclass
class CalculateCumulativeMonitoringTimeRequestObject(RequestObject):
    USER = "user"
    user: AuthorizedUser = required_field()

    def validate(self):
        validate_deployment_billing_enabled(self.user.deployment)
