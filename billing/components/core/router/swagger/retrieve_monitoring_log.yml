Retrieve Monitoring Logs
---
tags:
  - billing

security:
  - Bearer: []

parameters:
  - in: path
    name: user_id
    required: true
    type: string
  - name: body
    in: body
    description: body
    required: true
    schema:
      $ref: "#/definitions/RetrieveBillingMonitoringLogRequest"
responses:
  201:
    description: Retrieve monitoring log response
    schema:
      $ref: "#/definitions/RetrieveBillingMonitoringLogResponse"

definitions:
  RetrieveBillingMonitoringLogRequest:
    type: object
    properties:
      skip:
        type: integer
      limit:
        type: integer

  RetrieveBillingMonitoringLogResponse:
    type: object
    properties:
      skip:
        type: integer
      limit:
        type: integer
      total:
        type: integer
      logs:
        items:
          $ref: "#/definitions/ResponseBillingMonitoringLog"

  BillingMonitoringLog:
    type: object
    properties:
      userId:
        type: string
      status:
        type: string
      action:
        type: string
      createdById:
        type: string
      lastModifiedById:
        type: string
      deploymentId:
        type: string
      timeTrackingId:
        type: string
      createDateTime:
        type: string
        format: date-time
      updateDateTime:
        type: string
        format: date-time
      startDateTime:
        type: string
        format: date-time
      endDateTime:
        type: string
        format: date-time

  ResponseBillingMonitoringLog:
    allOf:
      - $ref: "#/definitions/BillingMonitoringLog"
      - properties:
          id:
            type: string
          createdByName:
            type: string
          lastModifiedByName:
            type: string
          timeSpent:
            type: number
