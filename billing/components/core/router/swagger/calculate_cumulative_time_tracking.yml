Calculate Cumulative Time Tracking
---
tags:
  - billing

security:
  - Bearer: []

parameters:
  - in: path
    name: user_id
    required: true
    type: string

responses:
  200:
    description: Calculated Cumulative Time Tracking
    schema:
      $ref: "#/definitions/CalculateCumulativeTimeTracking"

definitions:
  CalculateCumulativeTimeTracking:
    type: object
    properties:
      cptCode:
        type: string
      cptCodeIteration:
        type: integer
      monitoringTimeSeconds:
        type: number
      status:
        type: string
      userId:
        type: string
