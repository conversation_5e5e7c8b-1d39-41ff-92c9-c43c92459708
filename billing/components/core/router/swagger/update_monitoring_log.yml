Edit Monitoring Log
---
tags:
  - billing

security:
  - Bearer: []

parameters:
  - in: path
    name: user_id
    required: true
    type: string
  - in: path
    name: log_id
    required: true
    type: string
  - name: body
    in: body
    description: body
    required: true
    schema:
      $ref: "#/definitions/EditBillingMonitoringLogRequest"
responses:
  200:
    description: edited monitoring log response
    schema:
      $ref: "#/definitions/CreateBillingMonitoringLogResponse"

definitions:
  EditBillingMonitoringLogRequest:
    allOf:
      - $ref: "#/definitions/CreateBillingMonitoringLogRequest"
      - required:
          - addendum
      - properties:
          addendum:
            type: string
