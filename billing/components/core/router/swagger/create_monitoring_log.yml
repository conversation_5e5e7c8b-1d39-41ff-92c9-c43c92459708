Create Monitoring Log
---
tags:
  - billing

security:
  - Bearer: []

parameters:
  - in: path
    name: user_id
    required: true
    type: string
  - name: body
    in: body
    description: body
    required: true
    schema:
      $ref: "#/definitions/CreateBillingMonitoringLogRequest"
responses:
  201:
    description: created monitoring log response
    schema:
      $ref: "#/definitions/CreateBillingMonitoringLogResponse"

definitions:
  BaseTimeTrackingRequest:
    type: object
    properties:
      startDateTime:
        type: string
        format: date-time
      endDateTime:
        type: string
        format: date-time
      action:
        type: string
  CreateBillingMonitoringLogRequest:
    allOf:
      - $ref: "#/definitions/BaseTimeTrackingRequest"
      - required:
          - startDateTime
          - endDateTime
          - action
  CreateBillingMonitoringLogResponse:
    type: object
    properties:
      id:
        type: string
      userId:
        type: string
      clinicianId:
        type: string
      deploymentId:
        type: string
      startDateTime:
        type: string
      endDateTime:
        type: string
      createDateTime:
        type: string
      updateDateTime:
        type: string
      action:
        type: string
      monitoringTimeSeconds:
        type: number
      cptCode:
        type: string
      cptCodeIteration:
        type: integer
      cptRecordStatus:
        type: string
