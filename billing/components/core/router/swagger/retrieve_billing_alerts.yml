Retrieve Billing Alerts
---
tags:
  - billing

security:
  - Bearer: []

parameters:
  - in: body
    name: body
    schema:
      $ref: "#/definitions/AlertsRequest"
responses:
  200:
    description: List Billing Alerts
    schema:
      $ref: '#/definitions/AlertsResponse'

definitions:
  AlertsRequest:
    type: object
    properties:
      search:
        type: string
        example: "test"
      sort:
        description: sort parameters
        $ref: "#/definitions/SortParams"
      skip:
        type: integer
        minimum: 0
        example: 0
      limit:
        type: integer
        minimum: 1
        example: 10
      filter:
        description: filter parameters
        $ref: "#/definitions/FilterParams"


  SortParams:
    type: object
    properties:
      fields:
        type: array
        items:
          $ref: "#/definitions/AlertsFieldItem"

  FilterParams:
    type:
      object
    properties:
      filters:
        type:
          array
        items:
          $ref: "#/definitions/FilterItem"

  FilterItem:
    type: object
    properties:
      billing:
        type: string
        enum: [
          "MISSED_CALL",
          "MISSED_SUBMISSION",
          "NOT_SYNCED",
        ]
      labels:
        type: array
        items:
          type: string
          example: "5d386cc6ff885918d96edb2c"

  AlertsFieldItem:
    type: object
    properties:
      field:
        type: string
        enum: [
          "COMPLIANCE",
          "DEADLINE",
          "TIME_SPENT",
          "LAST_SYNCED",
        ]
      cpt:
        type: string
        enum: ["99453", "99454", "99457", "99458", "98975", "98976", "98980", "98981"]
      order:
        type: string
        enum: ["DESCENDING", "ASCENDING"]

  AlertsResponse:
    type: object
    properties:
      users:
        type: object
      filtered:
        type: integer
      total:
        type: integer
      missingCall:
        type: integer
      missingSubmission:
        type: integer
      notSynced:
        type: integer
