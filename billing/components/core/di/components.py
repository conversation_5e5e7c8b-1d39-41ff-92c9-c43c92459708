import logging

from billing.components.core.repository.billing_repository import (
    BillingMonitoringLogRepository,
    BillingProfileHistoryLogRepository,
    BillingRemoteTimeTrackingRepository,
    BillingSubmissionRepository,
)
from billing.components.core.repository.postgres import (
    PostgresBillingMonitoringLogRepository,
    PostgresBillingProfileHistoryRepository,
    PostgresBillingRemoteTimeTrackingRepository,
)
from billing.components.core.repository.postgres._billing_submission_repository import (
    PostgresBillingSubmissionRepository,
)

logger = logging.getLogger(__name__)


def bind_export_repositories(binder):
    binder.bind_to_provider(
        BillingRemoteTimeTrackingRepository,
        lambda: PostgresBillingRemoteTimeTrackingRepository(),
    )
    logger.debug("BillingRemoteTimeTrackingRepository bind to PostgresBillingRemoteTimeTrackingRepository")
    binder.bind_to_provider(
        BillingSubmissionRepository,
        lambda: PostgresBillingSubmissionRepository(),
    )
    logger.debug("BillingSubmissionRepository bind to PostgresBillingSubmissionRepository")
    binder.bind_to_provider(
        BillingProfileHistoryLogRepository,
        lambda: PostgresBillingProfileHistoryRepository(),
    )
    logger.debug("BillingProfileHistoryLogRepository bind to PostgresBillingProfileHistoryRepository")
    binder.bind_to_provider(
        BillingMonitoringLogRepository,
        lambda: PostgresBillingMonitoringLogRepository(),
    )
    logger.debug("BillingMonitoringLogRepository bind to PostgresBillingMonitoringLogRepository")
