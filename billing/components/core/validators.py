import csv
import io

from billing.components.core.dtos.deployment_billing import (
    DeploymentBillingFileType,
    BillingProviderFile,
)
from sdk import meta
from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.utils.validators import utc_date_to_str, utc_str_to_date
from sdk.storage.use_case.storage_request_objects import DownloadFileRequestObjectV1
from sdk.storage.use_case.storage_use_cases import DownloadFileUseCaseV1

MAX_FILE_SIZE = 1024 * 1024 * 10


def default_date_meta(dump_only=False):
    return meta(
        field_to_value=utc_date_to_str,
        value_to_field=utc_str_to_date,
        dump_only=dump_only,
    )


class FileDownload:
    def __init__(self, file_id: str):
        self.id = file_id
        req = DownloadFileRequestObjectV1.from_dict(
            {
                DownloadFileRequestObjectV1.FILE_ID: file_id,
            }
        )
        rsp = DownloadFileUseCaseV1().execute(req)
        self.name = rsp.fileName
        self.content_type = rsp.contentType
        self.size = rsp.contentLength
        self.content = rsp.content


def validate_deployment_billing_file(billing_file_type: DeploymentBillingFileType, file_id: str):
    file = FileDownload(file_id)

    if not file.name.endswith(".csv"):
        raise InvalidRequestException("Only CSV file are acceptable for now")

    validate_file_size(file.size)

    validation_by_file_type_mapping = {
        DeploymentBillingFileType.INSURANCE_CARRIERS.value: _validate_insurance_carriers_row,
        DeploymentBillingFileType.BILLING_PROVIDERS.value: _validate_billing_providers_row,
        DeploymentBillingFileType.ICD_10_CODES.value: _validate_icd_10_codes_row,
    }
    validation_to_use = validation_by_file_type_mapping.get(billing_file_type.value)
    if not validation_to_use:
        raise InvalidRequestException(
            f"Invalid billing file type [{billing_file_type.value}]. Acceptable values: {validation_by_file_type_mapping.keys()}"
        )

    file_data = csv.DictReader(io.TextIOWrapper(file.content, "utf-8"))
    try:
        if not file_data.fieldnames:
            raise InvalidRequestException(f"No Column names in {file.name}")
    except csv.Error:
        raise InvalidRequestException(f"No Column names in {file.name}")

    is_data_found = False
    for row in file_data:
        is_data_found = True
        validation_to_use(row)

    if not is_data_found:
        raise InvalidRequestException(f"No data in {file.name}")


def _validate_insurance_carriers_row(row_data: dict):
    group_name = row_data.get("Group name")
    payer_id = row_data.get("Payer ID")

    if not group_name or not payer_id:
        raise InvalidRequestException("Group name and Payer ID should be provided")

    if len(group_name) > 255:
        raise InvalidRequestException("Group name should not exceed 255 chars")


def _validate_billing_providers_row(row_data: dict):
    clinician_name = row_data.get(BillingProviderFile.NAME.value)
    if not clinician_name:
        raise InvalidRequestException("Name should be provided")


def _validate_icd_10_codes_row(row_data: dict):
    icd_code = row_data.get("ICD-10 code")
    description = row_data.get("Description")
    if not icd_code or not description:
        raise InvalidRequestException("ICD-10 code and Description should be provided")

    if len(description) > 255:
        raise InvalidRequestException("Description should not exceed 255 chars")

    if len(icd_code) > 7:
        raise InvalidRequestException("ICD-10 code should not exceed 7 chars")


def validate_file_size(file_size: int):
    if file_size > MAX_FILE_SIZE:
        raise InvalidRequestException(f"File size should not exceed {MAX_FILE_SIZE / 1024 / 1024} MB")
