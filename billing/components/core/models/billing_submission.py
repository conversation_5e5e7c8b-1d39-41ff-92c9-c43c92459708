from datetime import datetime

from django.db import models

from sdk.common.utils.mongo_utils import generate_obj_id


class BillingSubmission(models.Model):
    class Meta:
        db_table = "billing_submission"
        app_label = "billing"
        indexes = [
            models.Index(
                fields=["userId", "deploymentId", "startDateTime"],
                name="user_deployment_start_date",
            ),
            models.Index(
                fields=["userId", "deploymentId", "createDateTime"],
                name="user_deployment_create_date",
            ),
        ]

    # BaseDeployment fields
    mongoId = models.CharField(max_length=24, unique=True, default=generate_obj_id)
    deploymentId = models.CharField(max_length=24)
    primitiveId = models.CharField(max_length=24)
    primitiveClassName = models.CharField(max_length=50)
    userId = models.CharField(max_length=24)
    deviceName = models.CharField(max_length=100)
    deviceDetails = models.CharField(max_length=100, null=True, blank=True)
    source = models.CharField(max_length=100, null=True, blank=True)
    startDate = models.DateTimeField()
    startDateTime = models.DateTimeField()
    startDateTimeUTC = models.DateTimeField(null=True, blank=True)
    isCompleted = models.BooleanField(null=True, blank=True)
    createDateTime = models.DateTimeField(default=datetime.utcnow)
    updateDateTime = models.DateTimeField(default=datetime.utcnow)
    todaySubmissionCount = models.IntegerField(default=0)
