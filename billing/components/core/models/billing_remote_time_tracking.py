from django.db import models

from sdk.common.utils.mongo_utils import generate_obj_id


class BillingRemoteTimeTracking(models.Model):
    class Meta:
        db_table = "billing_remote_time_tracking"

        # TODO: Review indexes after repository implementation
        indexes = [
            models.Index(fields=["userId", "createDateTime"]),
            models.Index(fields=["deploymentId"]),
            models.Index(fields=["clinicianId"]),
            models.Index(fields=["startDateTime"]),
        ]

        app_label = "billing"

    MONGO_ID = "mongoId"

    id: str = models.AutoField(primary_key=True)
    mongoId = models.CharField(max_length=24, unique=True, default=generate_obj_id)
    deploymentId = models.CharField(max_length=24)
    clinicianId = models.CharField(max_length=24)
    startDateTime = models.DateTimeField()
    endDateTime = models.DateTimeField()
    userId = models.CharField(max_length=24)
    effectiveStartDateTime = models.DateTimeField(null=True, blank=True)
    effectiveEndDateTime = models.DateTimeField(null=True, blank=True)
    effectiveDuration = models.FloatField(default=0)
    createDateTime = models.DateTimeField(auto_now_add=True)
