from django.db import models
from datetime import datetime


class BillingProfileHistory(models.Model):
    MONGO_ID = "mongoId"

    class Meta:
        abstract = True

    id = models.AutoField(primary_key=True)
    mongoId = models.CharField(max_length=24, unique=True, default=None)
    userId = models.CharField(max_length=24)
    deploymentId = models.CharField(max_length=24)
    createDateTime = models.DateTimeField(default=datetime.utcnow)


class BillingInsuranceCarrierHistoryLog(BillingProfileHistory):
    class Meta:
        db_table = "billing_profile_log_insurance_carrier"
        indexes = [
            models.Index(fields=["userId", "createDateTime"]),
            models.Index(fields=["userId"]),
        ]
        app_label = "billing"

    order = models.IntegerField(null=True, blank=True, default=None)
    payerId = models.CharField(max_length=24)
    groupName = models.Char<PERSON>ield(max_length=255)


class BillingDiagnosisHistoryLog(BillingProfileHistory):
    class Meta:
        db_table = "billing_profile_log_diagnosis"
        indexes = [
            models.Index(fields=["userId", "createDateTime"]),
            models.Index(fields=["userId"]),
        ]
        app_label = "billing"

    order = models.IntegerField()
    description = models.TextField()
    icd10Code = models.CharField(max_length=10)


class BillingProviderHistoryLog(BillingProfileHistory):
    class Meta:
        db_table = "billing_profile_log_provider"
        indexes = [
            models.Index(fields=["userId", "createDateTime"]),
            models.Index(fields=["userId"]),
        ]
        app_label = "billing"

    MONGO_ID = "mongoId"

    billingProviderId = models.CharField(max_length=24, null=True, blank=True)
    billingProviderName = models.CharField(max_length=255)
