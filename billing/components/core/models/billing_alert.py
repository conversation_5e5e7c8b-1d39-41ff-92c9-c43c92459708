from datetime import datetime

from django.db import models

from sdk.authorization.models import User
from sdk.common.adapter.sql.sql_utils import BSONEncoder


class BillingAlert(models.Model):
    class Meta:
        db_table = "billing_alert"
        app_label = "billing"

    mongoId = models.CharField(max_length=24, unique=True)
    user = models.OneToOneField(User, on_delete=models.CASCADE, to_field="mongoId", db_column="userId")
    monitoringMinutes = models.FloatField(null=True, blank=True)
    createDateTime = models.DateTimeField(default=datetime.utcnow)
    updateDateTime = models.DateTimeField(default=datetime.utcnow)
    nextSubmissionDoS = models.DateTimeField(null=True, blank=True)
    lastMonitoringDate = models.DateTimeField(null=True, blank=True)
    submissionDates = models.JSONField(encoder=BSONEncoder, null=True, blank=True)
    callDateTime = models.DateTimeField(null=True, blank=True)
    nextMonitoringDoS = models.DateTimeField(null=True, blank=True)
    deploymentId = models.CharField(max_length=24)
    deleteDateTime = models.DateTimeField(default=None, null=True, blank=True)
