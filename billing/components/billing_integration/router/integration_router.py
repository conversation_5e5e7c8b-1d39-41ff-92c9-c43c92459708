from flask import jsonify, request

from billing.components.billing_integration.config.config import (
    BillingIntegrationAction,
)
from billing.components.billing_integration.tasks import (
    send_monthly_billing_report,
)
from sdk.common.utils.flask_request_utils import (
    get_request_json_dict_or_raise_exception,
)
from sdk.phoenix.audit_logger import audit
from sdk.security import ProtectedBlueprint, Access

integration_billing_route = ProtectedBlueprint(
    "integration_billing_route",
    __name__,
    url_prefix="/api/extensions/v1/integration",
    tag="BILLING_INTEGRATION",
)


@integration_billing_route.post("/billing_test", requires=Access.SUPER.ADMIN)
@audit(BillingIntegrationAction.BillingTest)
def manually_run_billing():
    body = get_request_json_dict_or_raise_exception(request)
    include_users = body.get("include_users", [])
    dry_run = body.get("dry_run", True)

    send_monthly_billing_report.delay(include_users=include_users, dry_run=dry_run)

    return jsonify({"result": "monthly report sent"}), 200
