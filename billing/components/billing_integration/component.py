from typing import Optional, Union

from flask import Blueprint

from billing.components.billing_integration.config.config import (
    BillingIntegrationConfig,
)
from billing.components.billing_integration.router.integration_router import (
    integration_billing_route,
)
from sdk.common.utils.inject import Binder
from sdk.phoenix.component_manager import PhoenixBaseComponent
from sdk.phoenix.config.server_config import PhoenixServerConfig


class BillingIntegrationComponent(PhoenixBaseComponent):
    tag_name = "billing_integration"

    config_class = BillingIntegrationConfig
    tasks = ["billing.components.billing_integration"]

    @property
    def blueprint(self) -> Optional[Union[Blueprint, list[Blueprint]]]:
        blueprints = [integration_billing_route]
        return blueprints

    def bind(self, binder: Binder, config: PhoenixServerConfig):
        # disable billing integration component  if the integration component is also disabled
        if not config.server.integration.enable:
            config.server.billing_integration.enable = False
