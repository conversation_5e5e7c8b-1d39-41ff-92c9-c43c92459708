sample_redox_financial_data = {
    "Location": {
        "LocationDepartment": "RCCSM",
        "LocationAbr": "MD",
        "PlaceOfServiceAbr": "Home",
    },
    "Patient": {
        "Identifiers": [{"ID": "**********", "IDType": "MR"}],
        "Demographics": {"FirstName": "Ma<PERSON>", "LastName": "Biria"},
    },
    "Transactions": [
        {
            "ID": "13213259164838",
            "Type": "Charge",
            "DateTimeOfService": "2022-06-30T00:00:00.000Z",
            "EndDateTime": "2022-06-30T00:00:00.000Z",
            "Chargeable": {
                "Code": "12442124",
                "Codeset": "RedoxHealthSystemChargeables",
                "Description": "BIOPSY-SKIN",
                "Quantity": None,
                "Amount": None,
            },
            "location": {
                "locationDepartment": "RCCSM",
                "locationAbr": "MD",
                "placeOfServiceAbr": "Home",
            },
            "Diagnoses": [
                {
                    "Code": "S06.0X",
                    "Codeset": "ICD-10",
                    "Name": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                }
            ],
            "Performers": [
                {
                    "ID": "**********",
                    "IDType": "NPI",
                    "FirstName": "Pat",
                    "LastName": "Granite",
                    "Credentials": ["MD"],
                    "Location": {
                        "Type": None,
                        "Facility": None,
                        "Department": None,
                        "Room": None,
                    },
                }
            ],
            "OrderingProviders": [
                {
                    "ID": "**********",
                    "IDType": "NPI",
                    "FirstName": "Pat",
                    "LastName": "Granite",
                }
            ],
            "Procedure": {
                "Code": "11100",
                "Codeset": "CPT",
                "Description": "Biopsy of skin",
                "Modifiers": [],
            },
        }
    ],
}

sample_redox_financial_data_response = {
    "Meta": {
        "DataModel": "Financial",
        "EventType": "Transaction",
        "Source": {"ID": "8e489e03-7dcd-4735-a879-da16b017ec85", "Name": "Huma"},
        "Destinations": [{"ID": "af394f14-b34a-464f-8d24-895f370af4c9", "Name": "Redox EMR"}],
        "Logs": [
            {
                "ID": "73e05f64-be7e-43af-a8b0-4ee7664e5d0f",
                "AttemptID": "9bcb7dbd-d8d6-4848-bbfc-57d36b7d391c",
            }
        ],
    }
}

sample_identifiers1 = []
sample_identifiers2 = {}
sample_identifiers3 = [
    {"ID": "155423", "IDType": "Unity ID"},
    {"ID": "241370", "IDType": "PatientNumber"},
    {"ID": "667650", "IDType": "OtherNumber"},
]
sample_identifiers4 = [
    {"ID": "PFIMAR1202", "IDType": "MRN"},
    {"ID": "148613", "IDType": "Unity ID"},
    {"ID": "174430", "IDType": "PatientNumber"},
    {"ID": "527820", "IDType": "OtherNumber"},
]

sample_identifiers5 = [
    {"ID": "**********", "IDType": "MR"},
    {"ID": "a1d4ee8aba494ca", "IDType": "NIST"},
    {"ID": "PFIMAR1202", "IDType": "MRN"},
]
