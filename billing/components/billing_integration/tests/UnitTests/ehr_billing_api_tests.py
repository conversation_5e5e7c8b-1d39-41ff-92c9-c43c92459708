import unittest
from unittest.mock import MagicMock, patch

from billing.components.billing_integration.adapters.generic.ehr_billing_api import (
    EHRBillingApi,
)
from billing.components.billing_integration.adapters.utils import (
    convert_huma_billing_report_to_redox,
    get_billing_key,
)
from billing.components.billing_integration.tests.IntegrationTests.sample_data import (
    sample_huma_billing_0,
    sample_huma_billing_1,
    sample_huma_billing_2,
    sample_huma_billing_3,
    sample_huma_billing_4,
    sample_huma_billing_5,
    sample_huma_billing_6,
    sample_huma_billing_7,
    sample_huma_billing_onboarding_missing,
)
from billing.components.billing_integration.tests.UnitTests.sample_data import (
    sample_redox_financial_data,
    sample_redox_financial_data_response,
)
from huma_plugins.components.integration.dtos.generic_ehr import GenericEHRDTO

AUTHENTICATE_PATH = "huma_plugins.components.integration.adapters.generic.ehr_api.EHRApi.authenticate"
POST_PATH = "huma_plugins.components.integration.adapters.generic.ehr_api.requests.Session.post"

USER_ID = "5e8f0c74b50aa9656c34789b"


class EHRBillingApiTestCase(unittest.TestCase):
    @patch(AUTHENTICATE_PATH)
    @patch(POST_PATH)
    def test_send_financial_transactions_success(self, mock_post, mock_authenticate):
        mock_authenticate.return_value = True

        mock_response = MagicMock()
        mock_response.ok = True
        mock_response.json.return_value = sample_redox_financial_data_response
        mock_post.return_value = mock_response

        mock_integration = MagicMock()
        mock_integration.generic_ehr = MagicMock()

        destination = GenericEHRDTO.Destination()
        destination.destination_func = GenericEHRDTO.Destination.DestinationFunc.send_financial_transactions

        mock_integration.generic_ehr.destination = [destination]

        ehr_billing_api = EHRBillingApi(mock_integration)

        result = ehr_billing_api.send_financial_transactions(sample_redox_financial_data)

        # Assertions
        self.assertEqual("Financial", result["Meta"]["DataModel"])

        # Make sure the post method was called with the correct arguments
        mock_post.assert_called_once()

    @patch("huma_plugins.components.integration.adapters.generic.ehr_api.EHRApi.login_basic")
    @patch("huma_plugins.components.integration.adapters.generic.ehr_api.EHRApi.update_generic_ehr_tokens")
    @patch(POST_PATH)
    def test_send_financial_transactions_with_oauth2_basic(self, mock_post, mock_update_tokens, mock_login_basic):
        mock_login_basic.return_value = True
        mock_update_tokens.return_value = None

        mock_response = MagicMock()
        mock_response.ok = True
        mock_response.json.return_value = sample_redox_financial_data_response
        mock_post.return_value = mock_response

        mock_integration = MagicMock()
        mock_integration.generic_ehr = MagicMock()
        mock_integration.generic_ehr.authType = GenericEHRDTO.GenericEHRAuthType.OAUTH2_BASIC
        mock_integration.generic_ehr.access_token = "test_token"
        mock_integration.generic_ehr.access_token_expiration = "2020-01-01T00:00:00Z"

        destination = GenericEHRDTO.Destination()
        destination.destination_func = GenericEHRDTO.Destination.DestinationFunc.send_financial_transactions

        mock_integration.generic_ehr.destination = [destination]

        ehr_billing_api = EHRBillingApi(mock_integration)

        result = ehr_billing_api.send_financial_transactions(sample_redox_financial_data)

        # Assertions
        self.assertEqual("Financial", result["Meta"]["DataModel"])

        # Verify login_basic was called
        mock_login_basic.assert_called_once()
        # Verify update_generic_ehr_tokens was called
        mock_update_tokens.assert_called_once()

    def test_convert_huma_billing_report_to_redox(self):
        financials_report0 = convert_huma_billing_report_to_redox(sample_huma_billing_0)
        self.assertEqual(0, len(financials_report0[USER_ID]))

        financials_report0 = convert_huma_billing_report_to_redox(sample_huma_billing_7)
        self.assertEqual(0, len(financials_report0[USER_ID]))

        financials_report1 = convert_huma_billing_report_to_redox(sample_huma_billing_1)
        self.assertEqual(1, len(financials_report1[USER_ID]))

        transaction = financials_report1[USER_ID][0]

        key = get_billing_key(sample_huma_billing_1[USER_ID], USER_ID)
        self.assertEqual(
            sample_huma_billing_1[USER_ID][key][0]["cptCodesDetails"]["cpt99454"]["billingEndDate_0"],
            transaction["DateTimeOfService"],
        )

        financials_report2 = convert_huma_billing_report_to_redox(sample_huma_billing_2)
        self.assertEqual(2, len(financials_report2[USER_ID]))

        transaction = financials_report2[USER_ID][0]
        self.assertEqual(
            sample_huma_billing_2[USER_ID][key][0]["cptCodesDetails"]["cpt99454"]["billingEndDate_0"],
            transaction["EndDateTime"],
        )

        transaction = financials_report2[USER_ID][1]
        self.assertEqual(
            sample_huma_billing_2[USER_ID][key][0]["cptCodesDetails"]["cpt99454"]["billingEndDate_1"],
            transaction["EndDateTime"],
        )

        financials_report3 = convert_huma_billing_report_to_redox(sample_huma_billing_3)
        self.assertEqual(2, len(financials_report3[USER_ID]))

        transaction = financials_report3[USER_ID][1]
        self.assertEqual(
            str(sample_huma_billing_3[USER_ID][key][0]["cptCodesDetails"]["cpt99458"]["totalBillable"]),
            transaction["Chargeable"]["Quantity"],
        )

        financials_report4 = convert_huma_billing_report_to_redox(sample_huma_billing_4)
        self.assertEqual(1, len(financials_report4[USER_ID]))

        financials_report5 = convert_huma_billing_report_to_redox(sample_huma_billing_5)
        self.assertEqual(1, len(financials_report5[USER_ID]))

        financials_report6 = convert_huma_billing_report_to_redox(sample_huma_billing_6)
        self.assertEqual(1, len(financials_report6[USER_ID]))

        transaction = financials_report2[USER_ID][0]
        self.assertEqual(
            sample_huma_billing_6[USER_ID][key][0]["cptCodesDetails"]["cpt99454"]["billingEndDate_0"],
            transaction["EndDateTime"],
        )

    def test_convert_huma_billing_report_to_redox_missing_onboarding_data(self):
        financials_report0 = convert_huma_billing_report_to_redox(sample_huma_billing_onboarding_missing)
        self.assertEqual(0, len(financials_report0[USER_ID]))


if __name__ == "__main__":
    unittest.main()
