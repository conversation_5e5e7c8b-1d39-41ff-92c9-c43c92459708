from pathlib import Path
from unittest import mock
from unittest.mock import patch

from freezegun import freeze_time

from billing.components.billing_integration.tasks import (
    execute_integration_billing,
    send_monthly_billing_report,
    setup_periodic_billing_tasks,
)
from billing.components.billing_integration.tests.IntegrationTests.sample_data import (
    sample_huma_billing_1,
    sample_huma_billing_2,
)
from billing.components.billing_integration.tests.IntegrationTests.sample_data import (
    sample_integration,
    sample_generic_ehr,
)
from billing.components.core.component import BillingComponent
from billing.components.export.component import BillingExportComponent
from billing.tests.billing_test_case import BillingTestCase
from huma_plugins.components.integration.component import IntegrationComponent
from huma_plugins.components.integration.dtos.generic_ehr import GenericEHRDTO
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.deployment.component import DeploymentComponent
from sdk.organization.component import OrganizationComponent
from sdk.versioning.component import VersionComponent

USER_ID = "615db4dd92a28f0cee2e14c1"
FINANCIAL_REQUEST_SESSION = "huma_plugins.components.integration.adapters.generic.ehr_api.EHRApi.setup_session"
EXECUTE_INTEGRATION_PATH = "billing.components.billing_integration.tasks.execute_integration_billing"

MANAGER_ID = "5e8f0c74b50aa9656c34789b"

DEPLOYMENT_ID = "61926cbe9cb844829c967f8a"
DEPLOYMENT_ID2 = "5ed8ae76cf99540b259a7315"
USER_EMAIL = "<EMAIL>"

MRN = "0000000001"


class IntegrationEHRApiTestCase(BillingTestCase):
    components = [
        AuthComponent(),
        AuthorizationComponent(),
        BillingComponent(),
        DeploymentComponent(),
        IntegrationComponent(),
        BillingExportComponent(),
        OrganizationComponent(),
        VersionComponent(server_version="1.0.0", api_version="1.0.0"),
    ]
    fixtures = [Path(__file__).parent.joinpath("fixtures/data.json")]
    migration_path: str = str(Path(__file__).parent.parent.parent) + "/migrations"

    def setUp(self):
        super().setUp()

        self.data = sample_huma_billing_1

    @freeze_time("2023-08-01")  # Set a date that's the last day of the month
    def test_periodic_billing_tasks(self):
        sender_mock = mock.Mock()
        setup_periodic_billing_tasks(sender_mock)  # Manually trigger the task

    def test_send_monthly_billing_report(self):
        with patch(EXECUTE_INTEGRATION_PATH) as mock_execute_integration:
            # todo add a module result or something so you would get a result
            send_monthly_billing_report()
            mock_execute_integration.delay.assert_called()

    def test_billing_execute_integration(self):
        data = sample_huma_billing_1

        generic_ehr = GenericEHRDTO.from_dict(sample_generic_ehr)
        sample_integration.generic_ehr = generic_ehr

        execute_integration_billing(
            sample_integration.to_dict(include_none=False),
            data,
            sample_integration.deploymentIds[0],
        )

    #     todo assert result or calls to the ehr api
    def test_billing_execute_integration_multiple_transactions(self):
        data = sample_huma_billing_2

        generic_ehr = GenericEHRDTO.from_dict(sample_generic_ehr)
        sample_integration.generic_ehr = generic_ehr

        execute_integration_billing(
            sample_integration.to_dict(include_none=False),
            data,
            sample_integration.deploymentIds[0],
        )
