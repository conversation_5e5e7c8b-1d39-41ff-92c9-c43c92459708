{"integration": [{"_id": {"$oid": "63f785dc24588002f8e93e19"}, "integrationType": "GENERICEHR", "generic_ehr": {"url": "https://api.redoxengine.com", "authType": "LEGACY", "api_key": "724c91b2-7b4a-44b6-b551-56abd8dae222", "api_secret": "O0Yndmg89NUCGH98TPwArQqL7wqggFjKr600WXIqRYEnTwmtLH0kg7lB2P2QU2Fd9pTN7Poj", "key_id": "o1MMWMUwCKjlQFZPRer6d_rMiuABiSJNg_0Hm7UB61I", "client_id": "0324febb-2c2c-4242-b95b-8a5a79fbe98a", "private_key": "123", "source_id": "a1bcd2fd-14bc-4029-a695-5953817a8aab", "source_name": "Redox API Endpoint", "destination": [{"destination_id": "af394f14-b34a-464f-8d24-895f370af4c9", "destination_name": "Flowsheet", "destination_func": "new_flowsheet", "is_test": false}, {"destination_id": "0f4bd1d1-451d-4351-8cfd-b767d1b488d6", "destination_name": "PatientSearch", "destination_func": "query_patient_search", "is_test": false}, {"destination_id": "af394f14-b34a-464f-8d24-895f370af4c9", "destination_name": "PatientSearch", "destination_func": "send_financial_transactions", "is_test": false}], "logs_id": "d9f5d293-7110-461e-a875-3beb089e79f3", "logs_attempt_id": "925d1617-2fe0-468c-a14c-f8c04b572c54", "retry": 1, "extraCustomFields": {"studyID": "12345678", "idType": "MR"}}, "name": "HL7 Integration Generic EHR - Redox", "enabled": true, "organizationIds": [], "deploymentIds": [{"$oid": "61926cbe9cb844829c967f8a"}]}], "organization": [{"_id": {"$oid": "5fde855f12db509a2785da06"}, "name": "ABC Pharmaceuticals EU Trials 123", "deploymentIds": ["5ed8ae76cf99540b259a7315"], "status": "DEPLOYED"}], "deployment": [{"_id": {"$oid": "61926cbe9cb844829c967f8a"}, "name": "Integration Test Deployment", "status": "DEPLOYED"}, {"_id": {"$oid": "5ed8ae76cf99540b259a7315"}, "name": "Integration Test Deployment", "status": "DEPLOYED"}], "huma_auth_user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "test"}], "user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "roles": [{"roleId": "Admin", "resource": "deployment/61926cbe9cb844829c967f8a", "isActive": true}, {"roleId": "Admin", "resource": "deployment/614b51bce422623dcb0e455c", "isActive": true}]}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789a"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "roles": [{"roleId": "SuperAdmin", "resource": "deployment/*", "isActive": true}], "timezone": "UTC"}]}