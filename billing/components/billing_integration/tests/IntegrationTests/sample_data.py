from huma_plugins.components.integration.dtos.integration import IntegrationDTO

sample_integration = IntegrationDTO(
    id="63f785dc24588002f8e93e19",
    name="Generic EHR Integration",
    integrationType=IntegrationDTO.Type.GENERICEHR,
    organizationIds=["5f652a9661c37dd829c8d23a"],
    deploymentIds=["5f652a9661c37dd829c8d23a", "617a6ade2ad9606b933e3d8e"],
    moduleNames=None,
    excludedModuleNames=None,
)

sample_generic_ehr = {
    "url": "https://api.redoxengine.com",
    "authType": "LEGACY",
    "api_key": "724c91b2-7b4a-44b6-b551-56abd8dae222",
    "api_secret": "O0Yndmg89NUCGH98TPwArQqL7wqggFjKr600WXIqRYEnTwmtLH0kg7lB2P2QU2Fd9pTN7Poj",
    "key_id": "o1MMWMUwCKjlQFZPRer6d_rMiuABiSJNg_0Hm7UB61I",
    "client_id": "0324febb-2c2c-4242-b95b-8a5a79fbe98a",
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    "source_id": "a1bcd2fd-14bc-4029-a695-5953817a8aab",
    "source_name": "Redox API Endpoint",
    "destination": [
        {
            "destination_id": "af394f14-b34a-464f-8d24-895f370af4c9",
            "destination_name": "Flowsheet",
            "destination_func": "new_flowsheet",
        },
        {
            "destination_id": "0f4bd1d1-451d-4351-8cfd-b767d1b488d6",
            "destination_name": "PatientSearch",
            "destination_func": "query_patient_search",
        },
        {
            "destination_id": "af394f14-b34a-464f-8d24-895f370af4c9",
            "destination_name": "PatientSearch",
            "destination_func": "send_financial_transactions",
        },
    ],
    "extraCustomFields": {
        "isTest": True,
    },
    "logs_id": "d9f5d293-7110-461e-a875-3beb089e79f3",
    "logs_attempt_id": "925d1617-2fe0-468c-a14c-f8c04b572c54",
    "verification_token": "123456",
    "retry": 1,
}

sample_financial_data = {
    "Location": {
        "LocationDepartment": "RCCSM",
        "LocationAbr": "MD",
        "PlaceOfServiceAbr": "Home",
    },
    "Patient": {
        "Identifiers": [
            {"ID": "**********", "IDType": "MR"},
        ],
        "Demographics": {
            "FirstName": "Mahdi",
            "LastName": "Biria",
            # "DOB": "2008-01-06",
        },
    },
    "Transactions": [
        {
            "ID": "13213259164838",
            "Type": "Charge",
            "DateTimeOfService": "2022-06-01T00:00:00.000Z",
            "EndDateTime": "2022-06-30T00:00:00.000Z",
            "Chargeable": {
                "Code": "12442124",
                "Codeset": "RedoxHealthSystemChargeables",
                "Description": "BIOPSY-SKIN",
                "Quantity": None,
                "Amount": None,
            },
            "Diagnoses": [
                {
                    "Code": "S06.0X",
                    "Codeset": "ICD-10",
                    "Name": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                }
            ],
            "Performers": [
                {
                    "ID": "**********",
                    "IDType": "NPI",
                    "FirstName": "Pat",
                    "LastName": "Granite",
                    "Credentials": ["MD"],
                    "Location": {
                        "Type": None,
                        "Facility": None,
                        "Department": None,
                        "Room": None,
                    },
                }
            ],
            "OrderingProviders": [
                {
                    "ID": "**********",
                    "IDType": "NPI",
                    "FirstName": "Pat",
                    "LastName": "Granite",
                }
            ],
            "Procedure": {
                "Code": "11100",
                "Codeset": "CPT",
                "Description": "Biopsy of skin",
                "Modifiers": [],
            },
        }
    ],
}

sample_wrong_financial_data = {
    "Location": {
        "LocationDepartment": "RCCSM",
        "LocationAbr": "MD",
        "PlaceOfServiceAbr": "Home",
    },
    "Patient": {
        "Identifiers": [
            {"ID": "**********", "IDType": "MR"},
        ],
        "Demographics": {
            "FirstName": "Timothy",
            "LastName": "Bixby",
            # "DOB": "2008-01-06",
        },
    },
    "Transactions": [
        {
            "ID": "13213259164838",
            "Type": "Charge",
            "DateTimeOfService": "2022-06-01T00:00:00.000Z",
            "EndDateTime": "2022-06-30T00:00:00.000Z",
            "Chargeable": {
                "Codeset": "RedoxHealthSystemChargeables",
                "Description": "BIOPSY-SKIN",
                "Quantity": None,
                "Amount": None,
            },
            "Diagnoses": [
                {
                    "Code": "S06.0X",
                    "Codeset": "ICD-10",
                    "Name": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                }
            ],
            "Performers": [
                {
                    "ID": "**********",
                    "IDType": "NPI",
                    "FirstName": "Pat",
                    "LastName": "Granite",
                    "Credentials": ["MD"],
                    "Location": {
                        "Type": None,
                        "Facility": None,
                        "Department": None,
                        "Room": None,
                    },
                }
            ],
            "OrderingProviders": [
                {
                    "ID": "**********",
                    "IDType": "NPI",
                    "FirstName": "Pat",
                    "LastName": "Granite",
                    "Location": {
                        "Type": None,
                        "Facility": None,
                        "Department": None,
                        "Room": None,
                    },
                }
            ],
            "Procedure": {
                "Code": "11100",
                "Codeset": "CPT",
                "Description": "Biopsy of skin",
                "Modifiers": [],
            },
        }
    ],
}

# todo make sure this data is up to date or we get this from api directly
sample_huma_billing_0 = {
    "5e8f0c74b50aa9656c34789b": {
        "EHRMonthlyBilling_2022-04-01_2022-07-01": [
            {
                "userId": "5e8f0c74b50aa9656c34789b",
                "moduleId": "billing",
                "reportStartDate": "2022-06-01",
                "reportEndDate": "2022-06-30",
                "user": {
                    "id": "5e8f0c74b50aa9656c34789b",
                    "updateDateTime": "2022-06-30T10:00:00.000000Z",
                    "createDateTime": "1970-01-19T08:40:22.340000Z",
                    "lastSubmitDateTime": "2022-06-30T10:00:00.000000Z",
                    "givenName": "test",
                    "familyName": "test",
                    "gender": None,
                    "biologicalSex": None,
                    "ethnicity": None,
                    "dateOfBirth": "1988-02-20",
                    "email": "<EMAIL>",
                    "phoneNumber": "+380999999999",
                    "primaryAddress": None,
                    "race": None,
                    "bloodGroup": None,
                    "emergencyPhoneNumber": None,
                    "height": None,
                    "additionalContactDetails": None,
                    "familyMedicalHistory": None,
                    "pastHistory": None,
                    "presentSymptoms": None,
                    "operationHistory": None,
                    "chronicIllness": None,
                    "allergyHistory": None,
                    "pregnancy": None,
                    "dateOfLastPhysicalExam": None,
                    "extraCustomFields": None,
                    "surgeryDetails": None,
                    "fenlandCohortId": None,
                    "nhsId": "nhs_id",
                    "insuranceNumber": None,
                    "wechatId": None,
                    "kardiaId": None,
                    "pamThirdPartyIdentifier": None,
                    "timezone": "UTC",
                    "labels": None,
                    "surgeryDateTime": "1970-01-19",
                    "preferredUnits": None,
                    "addressComponent": None,
                    "personalDocuments": None,
                    "enrollmentId": None,
                    "enrollmentNumber": None,
                    "deployments": None,
                    "deploymentsInfo": None,
                    "boardingStatus": {
                        "status": 0,
                        "updateDateTime": "2021-02-09T14:08:05.997000Z",
                        "submitterId": None,
                        "reasonOffBoarded": None,
                        "detailsOffBoarded": None,
                    },
                    "language": None,
                    "finishedOnboarding": None,
                    "stats": None,
                    "ragScore": None,
                    "flags": None,
                    "unseenFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "recentFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "lastLoginDateTime": None,
                    "badges": None,
                    "onboardingConfigs": None,
                    "researchTeamActions": None,
                    "hasAssignedManagers": None,
                    "componentsData": {
                        "billing": {
                            "status": 1,
                            "billingProviderName": "someName",
                            "diagnosis": {
                                "order": 1,
                                "icd10Code": "ICD",
                                "description": "diagnosis description",
                            },
                        }
                    },
                    "consent": None,
                    "econsent": None,
                    "age": 34,
                },
                "billing": {
                    "billingProviderName": "someName",
                    "icd10Code": "ICD",
                    "description": "diagnosis description",
                },
                "cptCodesDetails": {
                    "cpt99453": {
                        "billingStartDate": "2022-06-01",
                        "billingEndDate": "2022-06-30",
                        "countOfSubmissions": 1,
                        "countOfSubmissionDays": 1,
                        "earliestBillingDate": None,
                        "totalBillable": 0,
                    },
                    "cpt99454": {
                        "billingStartDate_0": "2022-06-01",
                        "billingEndDate_0": "2022-06-30",
                        "countOfSubmissions_0": 1,
                        "countOfSubmissionDays_0": 1,
                        "earliestBillingDate_0": None,
                        "totalBillable": 0,
                    },
                },
            }
        ]
    }
}
sample_huma_billing_1 = {
    "5e8f0c74b50aa9656c34789b": {
        "EHRMonthlyBilling_2022-04-01_2022-07-01": [
            {
                "userId": "5e8f0c74b50aa9656c34789b",
                "moduleId": "billing",
                "reportStartDate": "2022-06-01",
                "reportEndDate": "2022-06-30",
                "user": {
                    "id": "5e8f0c74b50aa9656c34789b",
                    "updateDateTime": "2022-06-30T10:00:00.000000Z",
                    "createDateTime": "1970-01-19T08:40:22.340000Z",
                    "lastSubmitDateTime": "2022-06-30T10:00:00.000000Z",
                    "givenName": "test",
                    "familyName": "test",
                    "gender": None,
                    "biologicalSex": None,
                    "ethnicity": None,
                    "dateOfBirth": "1988-02-20",
                    "email": "<EMAIL>",
                    "phoneNumber": "+380999999999",
                    "primaryAddress": None,
                    "race": None,
                    "bloodGroup": None,
                    "emergencyPhoneNumber": None,
                    "height": None,
                    "additionalContactDetails": None,
                    "familyMedicalHistory": None,
                    "pastHistory": None,
                    "presentSymptoms": None,
                    "operationHistory": None,
                    "chronicIllness": None,
                    "allergyHistory": None,
                    "pregnancy": None,
                    "dateOfLastPhysicalExam": None,
                    "extraCustomFields": None,
                    "surgeryDetails": None,
                    "fenlandCohortId": None,
                    "nhsId": "nhs_id",
                    "insuranceNumber": None,
                    "wechatId": None,
                    "kardiaId": None,
                    "pamThirdPartyIdentifier": None,
                    "timezone": "UTC",
                    "labels": None,
                    "surgeryDateTime": "1970-01-19",
                    "preferredUnits": None,
                    "addressComponent": None,
                    "personalDocuments": None,
                    "enrollmentId": None,
                    "enrollmentNumber": None,
                    "deployments": None,
                    "deploymentsInfo": None,
                    "boardingStatus": {
                        "status": 0,
                        "updateDateTime": "2021-02-09T14:08:05.997000Z",
                        "submitterId": None,
                        "reasonOffBoarded": None,
                        "detailsOffBoarded": None,
                    },
                    "language": None,
                    "finishedOnboarding": None,
                    "stats": None,
                    "ragScore": None,
                    "flags": None,
                    "unseenFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "recentFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "lastLoginDateTime": None,
                    "badges": None,
                    "onboardingConfigs": None,
                    "researchTeamActions": None,
                    "hasAssignedManagers": None,
                    "componentsData": {
                        "billing": {
                            "status": 1,
                            "billingProviderName": "someName",
                            "diagnosis": {
                                "order": 1,
                                "icd10Code": "ICD",
                                "description": "diagnosis description",
                            },
                        }
                    },
                    "consent": None,
                    "econsent": None,
                    "age": 34,
                },
                "billing": {
                    "billingProviderName": "someName",
                    "icd10Code": "ICD",
                    "description": "diagnosis description",
                },
                "cptCodesDetails": {
                    "cpt99453": {
                        "billingStartDate": "2022-06-01",
                        "billingEndDate": "2022-06-30",
                        "countOfSubmissions": 1,
                        "countOfSubmissionDays": 1,
                        "earliestBillingDate": None,
                        "totalBillable": 0,
                    },
                    "cpt99454": {
                        "billingStartDate_0": "2022-06-01",
                        "billingEndDate_0": "2022-06-30",
                        "countOfSubmissions_0": 170,
                        "countOfSubmissionDays_0": 17,
                        "earliestBillingDate_0": "2022-06-18",
                        "totalBillable": 1,
                    },
                },
            }
        ]
    }
}

sample_huma_billing_2 = {
    "5e8f0c74b50aa9656c34789b": {
        "EHRMonthlyBilling_2022-04-01_2022-07-01": [
            {
                "userId": "5e8f0c74b50aa9656c34789b",
                "moduleId": "billing",
                "reportStartDate": "2022-06-01",
                "reportEndDate": "2022-06-30",
                "user": {
                    "id": "5e8f0c74b50aa9656c34789b",
                    "updateDateTime": "2022-06-30T10:00:00.000000Z",
                    "createDateTime": "1970-01-19T08:40:22.340000Z",
                    "lastSubmitDateTime": "2022-06-30T10:00:00.000000Z",
                    "givenName": "test",
                    "familyName": "test",
                    "gender": None,
                    "biologicalSex": None,
                    "ethnicity": None,
                    "dateOfBirth": "1988-02-20",
                    "email": "<EMAIL>",
                    "phoneNumber": "+380999999999",
                    "primaryAddress": None,
                    "race": None,
                    "bloodGroup": None,
                    "emergencyPhoneNumber": None,
                    "height": None,
                    "additionalContactDetails": None,
                    "familyMedicalHistory": None,
                    "pastHistory": None,
                    "presentSymptoms": None,
                    "operationHistory": None,
                    "chronicIllness": None,
                    "allergyHistory": None,
                    "pregnancy": None,
                    "dateOfLastPhysicalExam": None,
                    "extraCustomFields": None,
                    "surgeryDetails": None,
                    "fenlandCohortId": None,
                    "nhsId": "nhs_id",
                    "insuranceNumber": None,
                    "wechatId": None,
                    "kardiaId": None,
                    "pamThirdPartyIdentifier": None,
                    "timezone": "UTC",
                    "labels": None,
                    "surgeryDateTime": "1970-01-19",
                    "preferredUnits": None,
                    "addressComponent": None,
                    "personalDocuments": None,
                    "enrollmentId": None,
                    "enrollmentNumber": None,
                    "deployments": None,
                    "deploymentsInfo": None,
                    "boardingStatus": {
                        "status": 0,
                        "updateDateTime": "2021-02-09T14:08:05.997000Z",
                        "submitterId": None,
                        "reasonOffBoarded": None,
                        "detailsOffBoarded": None,
                    },
                    "language": None,
                    "finishedOnboarding": None,
                    "stats": None,
                    "ragScore": None,
                    "flags": None,
                    "unseenFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "recentFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "lastLoginDateTime": None,
                    "badges": None,
                    "onboardingConfigs": None,
                    "researchTeamActions": None,
                    "hasAssignedManagers": None,
                    "componentsData": {
                        "billing": {
                            "status": 1,
                            "billingProviderName": "someName",
                            "diagnosis": {
                                "order": 1,
                                "icd10Code": "ICD",
                                "description": "diagnosis description",
                            },
                        }
                    },
                    "consent": None,
                    "econsent": None,
                    "age": 34,
                },
                "billing": {
                    "cpt99453_4": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590158",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "cpt99457_8": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590159",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "icd10Code": "S06.0X",
                    "description": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                    "billingFirstReading": "2023-09-07",
                    "primaryInsuranceCarrier": "testGroupName1",
                    "secondaryInsuranceCarrier": "testGroupName2",
                    "primaryInsuranceCarrierPayerId": "testPayer1",
                    "secondaryInsuranceCarrierPayerId": "testPayer2",
                },
                "cptCodesDetails": {
                    "cpt99453": {
                        "billingStartDate": "2022-06-01",
                        "billingEndDate": "2022-06-30",
                        "countOfSubmissions": 1,
                        "countOfSubmissionDays": 1,
                        "earliestBillingDate": None,
                        "totalBillable": 0,
                    },
                    "cpt99454": {
                        "billingStartDate_0": "2022-05-03",
                        "billingEndDate_0": "2022-06-01",
                        "countOfSubmissions_0": 170,
                        "countOfSubmissionDays_0": 17,
                        "earliestBillingDate_0": "2022-05-18",
                        "billingStartDate_1": "2022-06-02",
                        "billingEndDate_1": "2022-06-31",
                        "countOfSubmissions_1": 131,
                        "countOfSubmissionDays_1": 19,
                        "earliestBillingDate_1": "2022-06-22",
                        "totalBillable": 2,
                    },
                },
            }
        ]
    }
}

sample_huma_billing_3 = {
    "5e8f0c74b50aa9656c34789b": {
        "EHRMonthlyBilling_2022-04-01_2022-07-01": [
            {
                "userId": "5e8f0c74b50aa9656c34789b",
                "moduleId": "billing",
                "reportStartDate": "2023-02-01",
                "reportEndDate": "2023-02-28",
                "user": {
                    "id": "5e8f0c74b50aa9656c34789b",
                    "updateDateTime": None,
                    "createDateTime": "1970-01-19T08:40:22.340000Z",
                    "lastSubmitDateTime": None,
                    "givenName": "test",
                    "familyName": "test",
                    "gender": None,
                    "biologicalSex": None,
                    "ethnicity": None,
                    "dateOfBirth": "1988-02-20",
                    "email": "<EMAIL>",
                    "phoneNumber": "+380999999999",
                    "primaryAddress": None,
                    "race": None,
                    "bloodGroup": None,
                    "emergencyPhoneNumber": None,
                    "height": None,
                    "additionalContactDetails": None,
                    "familyMedicalHistory": None,
                    "pastHistory": None,
                    "presentSymptoms": None,
                    "operationHistory": None,
                    "chronicIllness": None,
                    "allergyHistory": None,
                    "pregnancy": None,
                    "dateOfLastPhysicalExam": None,
                    "extraCustomFields": None,
                    "surgeryDetails": None,
                    "fenlandCohortId": None,
                    "nhsId": "nhs_id",
                    "insuranceNumber": None,
                    "wechatId": None,
                    "kardiaId": None,
                    "pamThirdPartyIdentifier": None,
                    "timezone": "UTC",
                    "labels": None,
                    "surgeryDateTime": "1970-01-19",
                    "preferredUnits": None,
                    "addressComponent": None,
                    "personalDocuments": None,
                    "enrollmentId": None,
                    "enrollmentNumber": None,
                    "deployments": None,
                    "deploymentsInfo": None,
                    "boardingStatus": {
                        "status": 0,
                        "updateDateTime": "2021-02-09T14:08:05.997000Z",
                        "submitterId": None,
                        "reasonOffBoarded": None,
                        "detailsOffBoarded": None,
                    },
                    "language": None,
                    "finishedOnboarding": None,
                    "stats": None,
                    "ragScore": None,
                    "flags": None,
                    "unseenFlags": None,
                    "recentFlags": None,
                    "lastLoginDateTime": None,
                    "badges": None,
                    "onboardingConfigs": None,
                    "researchTeamActions": None,
                    "hasAssignedManagers": None,
                    "componentsData": {
                        "billing": {
                            "status": 1,
                            "billingProviderName": "someName",
                            "diagnosis": {
                                "order": 1,
                                "icd10Code": "ICD",
                                "description": "diagnosis description",
                            },
                        }
                    },
                    "consent": None,
                    "econsent": None,
                    "age": 35,
                },
                "billing": {
                    "billingProviderName": "someName",
                    "icd10Code": "ICD",
                    "description": "diagnosis description",
                },
                "cptCodesDetails": {
                    "cpt99457": {
                        "billingStartDate": "2023-02-01",
                        "billingEndDate": "2023-02-28",
                        "totalBillable": 1,
                    },
                    "cpt99458": {
                        "billingStartDate": "2023-02-01",
                        "billingEndDate": "2023-02-28",
                        "totalBillable": 2,
                    },
                    "monitoringTimeMins": 60,
                    "numberOfCalls": 1,
                },
            }
        ]
    }
}

sample_huma_billing_4 = {
    "5e8f0c74b50aa9656c34789b": {
        "EHRMonthlyBilling_2022-04-01_2022-07-01": [
            {
                "userId": "5e8f0c74b50aa9656c34789b",
                "moduleId": "billing",
                "reportStartDate": "2023-02-01",
                "reportEndDate": "2023-02-28",
                "user": {
                    "id": "5e8f0c74b50aa9656c34789b",
                    "updateDateTime": None,
                    "createDateTime": "1970-01-19T08:40:22.340000Z",
                    "lastSubmitDateTime": None,
                    "givenName": "test",
                    "familyName": "test",
                    "gender": None,
                    "biologicalSex": None,
                    "ethnicity": None,
                    "dateOfBirth": "1988-02-20",
                    "email": "<EMAIL>",
                    "phoneNumber": "+380999999999",
                    "primaryAddress": None,
                    "race": None,
                    "bloodGroup": None,
                    "emergencyPhoneNumber": None,
                    "height": None,
                    "additionalContactDetails": None,
                    "familyMedicalHistory": None,
                    "pastHistory": None,
                    "presentSymptoms": None,
                    "operationHistory": None,
                    "chronicIllness": None,
                    "allergyHistory": None,
                    "pregnancy": None,
                    "dateOfLastPhysicalExam": None,
                    "extraCustomFields": None,
                    "surgeryDetails": None,
                    "fenlandCohortId": None,
                    "nhsId": "nhs_id",
                    "insuranceNumber": None,
                    "wechatId": None,
                    "kardiaId": None,
                    "pamThirdPartyIdentifier": None,
                    "timezone": "UTC",
                    "labels": None,
                    "surgeryDateTime": "1970-01-19",
                    "preferredUnits": None,
                    "addressComponent": None,
                    "personalDocuments": None,
                    "enrollmentId": None,
                    "enrollmentNumber": None,
                    "deployments": None,
                    "deploymentsInfo": None,
                    "boardingStatus": {
                        "status": 0,
                        "updateDateTime": "2021-02-09T14:08:05.997000Z",
                        "submitterId": None,
                        "reasonOffBoarded": None,
                        "detailsOffBoarded": None,
                    },
                    "language": None,
                    "finishedOnboarding": None,
                    "stats": None,
                    "ragScore": None,
                    "flags": None,
                    "unseenFlags": None,
                    "recentFlags": None,
                    "lastLoginDateTime": None,
                    "badges": None,
                    "onboardingConfigs": None,
                    "researchTeamActions": None,
                    "hasAssignedManagers": None,
                    "componentsData": {
                        "billing": {
                            "status": 1,
                            "billingProviderName": "someName",
                            "diagnosis": {
                                "order": 1,
                                "icd10Code": "ICD",
                                "description": "diagnosis description",
                            },
                        }
                    },
                    "consent": None,
                    "econsent": None,
                    "age": 35,
                },
                "billing": {
                    "billingProviderName": "someName",
                    "icd10Code": "ICD",
                    "description": "diagnosis description",
                },
                "cptCodesDetails": {
                    "cpt99457": {
                        "billingStartDate": "2023-02-01",
                        "billingEndDate": "2023-02-28",
                        "totalBillable": 0,
                    },
                    "cpt99458": {
                        "billingStartDate": "2023-02-01",
                        "billingEndDate": "2023-02-28",
                        "totalBillable": 2,
                    },
                    "monitoringTimeMins": 60,
                    "numberOfCalls": 1,
                },
            }
        ]
    }
}

# example with RTM codes
sample_huma_billing_5 = {
    "5e8f0c74b50aa9656c34789b": {
        "EHRMonthlyBilling_2022-04-01_2022-07-01": [
            {
                "userId": "5e8f0c74b50aa9656c34789b",
                "moduleId": "billing",
                "reportStartDate": "2023-02-01",
                "reportEndDate": "2023-02-28",
                "user": {
                    "id": "5e8f0c74b50aa9656c34789b",
                    "updateDateTime": None,
                    "createDateTime": "1970-01-19T08:40:22.340000Z",
                    "lastSubmitDateTime": None,
                    "givenName": "test",
                    "familyName": "test",
                    "gender": None,
                    "biologicalSex": None,
                    "ethnicity": None,
                    "dateOfBirth": "1988-02-20",
                    "email": "<EMAIL>",
                    "phoneNumber": "+380999999999",
                    "primaryAddress": None,
                    "race": None,
                    "bloodGroup": None,
                    "emergencyPhoneNumber": None,
                    "height": None,
                    "additionalContactDetails": None,
                    "familyMedicalHistory": None,
                    "pastHistory": None,
                    "presentSymptoms": None,
                    "operationHistory": None,
                    "chronicIllness": None,
                    "allergyHistory": None,
                    "pregnancy": None,
                    "dateOfLastPhysicalExam": None,
                    "extraCustomFields": None,
                    "surgeryDetails": None,
                    "fenlandCohortId": None,
                    "nhsId": "nhs_id",
                    "insuranceNumber": None,
                    "wechatId": None,
                    "kardiaId": None,
                    "pamThirdPartyIdentifier": None,
                    "timezone": "UTC",
                    "labels": None,
                    "surgeryDateTime": "1970-01-19",
                    "preferredUnits": None,
                    "addressComponent": None,
                    "personalDocuments": None,
                    "enrollmentId": None,
                    "enrollmentNumber": None,
                    "deployments": None,
                    "deploymentsInfo": None,
                    "boardingStatus": {
                        "status": 0,
                        "updateDateTime": "2021-02-09T14:08:05.997000Z",
                        "submitterId": None,
                        "reasonOffBoarded": None,
                        "detailsOffBoarded": None,
                    },
                    "language": None,
                    "finishedOnboarding": None,
                    "stats": None,
                    "ragScore": None,
                    "flags": None,
                    "unseenFlags": None,
                    "recentFlags": None,
                    "lastLoginDateTime": None,
                    "badges": None,
                    "onboardingConfigs": None,
                    "researchTeamActions": None,
                    "hasAssignedManagers": None,
                    "componentsData": {
                        "billing": {
                            "status": 1,
                            "billingProviderName": "someName",
                            "diagnosis": {
                                "order": 1,
                                "icd10Code": "ICD",
                                "description": "diagnosis description",
                            },
                        }
                    },
                    "consent": None,
                    "econsent": None,
                    "age": 35,
                },
                "billing": {
                    "billingProviderName": "someName",
                    "icd10Code": "ICD",
                    "description": "diagnosis description",
                },
                "cptCodesDetails": {
                    "cpt98980": {
                        "billingStartDate": "2023-02-01",
                        "billingEndDate": "2023-02-28",
                        "totalBillable": 0,
                    },
                    "cpt98981": {
                        "billingStartDate": "2023-02-01",
                        "billingEndDate": "2023-02-28",
                        "totalBillable": 2,
                    },
                    "monitoringTimeMins": 60,
                    "numberOfCalls": 1,
                },
            }
        ]
    }
}

sample_huma_billing_onboarding_missing = {
    "5e8f0c74b50aa9656c34789b": {
        "EHRMonthlyBilling_2022-04-01_2022-07-01": [
            {
                "userId": "5e8f0c74b50aa9656c34789b",
                "moduleId": "billing",
                "reportStartDate": "2022-06-01",
                "reportEndDate": "2022-06-30",
                "user": {
                    "id": "5e8f0c74b50aa9656c34789b",
                    "updateDateTime": "2022-06-30T10:00:00.000000Z",
                    "createDateTime": "1970-01-19T08:40:22.340000Z",
                    "lastSubmitDateTime": "2022-06-30T10:00:00.000000Z",
                    "givenName": "test",
                    "familyName": "test",
                    "gender": None,
                    "biologicalSex": None,
                    "ethnicity": None,
                    "dateOfBirth": "1988-02-20",
                    "email": "<EMAIL>",
                    "phoneNumber": "+380999999999",
                    "primaryAddress": None,
                    "race": None,
                    "bloodGroup": None,
                    "emergencyPhoneNumber": None,
                    "height": None,
                    "additionalContactDetails": None,
                    "familyMedicalHistory": None,
                    "pastHistory": None,
                    "presentSymptoms": None,
                    "operationHistory": None,
                    "chronicIllness": None,
                    "allergyHistory": None,
                    "pregnancy": None,
                    "dateOfLastPhysicalExam": None,
                    "extraCustomFields": None,
                    "surgeryDetails": None,
                    "fenlandCohortId": None,
                    "nhsId": "nhs_id",
                    "insuranceNumber": None,
                    "wechatId": None,
                    "kardiaId": None,
                    "pamThirdPartyIdentifier": None,
                    "timezone": "UTC",
                    "labels": None,
                    "surgeryDateTime": "1970-01-19",
                    "preferredUnits": None,
                    "addressComponent": None,
                    "personalDocuments": None,
                    "enrollmentId": None,
                    "enrollmentNumber": None,
                    "deployments": None,
                    "deploymentsInfo": None,
                    "boardingStatus": {
                        "status": 0,
                        "updateDateTime": "2021-02-09T14:08:05.997000Z",
                        "submitterId": None,
                        "reasonOffBoarded": None,
                        "detailsOffBoarded": None,
                    },
                    "language": None,
                    "finishedOnboarding": None,
                    "stats": None,
                    "ragScore": None,
                    "flags": None,
                    "unseenFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "recentFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "lastLoginDateTime": None,
                    "badges": None,
                    "onboardingConfigs": None,
                    "researchTeamActions": None,
                    "hasAssignedManagers": None,
                    "componentsData": {"billing": {}},
                    "consent": None,
                    "econsent": None,
                    "age": 34,
                },
                "billing": {
                    "billingProviderName": "someName",
                    "icd10Code": "ICD",
                    "description": "diagnosis description",
                },
                "cptCodesDetails": {
                    "cpt99453": {
                        "billingStartDate": "2022-06-01",
                        "billingEndDate": "2022-06-30",
                        "countOfSubmissions": 1,
                        "countOfSubmissionDays": 1,
                        "earliestBillingDate": None,
                        "totalBillable": 0,
                    },
                    "cpt99454": {
                        "billingStartDate_0": "2022-06-01",
                        "billingEndDate_0": "2022-06-30",
                        "countOfSubmissions_0": 1,
                        "countOfSubmissionDays_0": 1,
                        "earliestBillingDate_0": None,
                        "totalBillable": 0,
                    },
                },
            }
        ]
    }
}

sample_huma_billing_6 = {
    "5e8f0c74b50aa9656c34789b": {
        "EHRMonthlyBilling_2022-04-01_2022-07-01": [
            {
                "userId": "5e8f0c74b50aa9656c34789b",
                "moduleId": "billing",
                "reportStartDate": "2022-06-01",
                "reportEndDate": "2022-06-30",
                "user": {
                    "id": "5e8f0c74b50aa9656c34789b",
                    "updateDateTime": "2022-06-30T10:00:00.000000Z",
                    "createDateTime": "1970-01-19T08:40:22.340000Z",
                    "lastSubmitDateTime": "2022-06-30T10:00:00.000000Z",
                    "givenName": "test",
                    "familyName": "test",
                    "gender": None,
                    "biologicalSex": None,
                    "ethnicity": None,
                    "dateOfBirth": "1988-02-20",
                    "email": "<EMAIL>",
                    "phoneNumber": "+380999999999",
                    "primaryAddress": None,
                    "race": None,
                    "bloodGroup": None,
                    "emergencyPhoneNumber": None,
                    "height": None,
                    "additionalContactDetails": None,
                    "familyMedicalHistory": None,
                    "pastHistory": None,
                    "presentSymptoms": None,
                    "operationHistory": None,
                    "chronicIllness": None,
                    "allergyHistory": None,
                    "pregnancy": None,
                    "dateOfLastPhysicalExam": None,
                    "extraCustomFields": None,
                    "surgeryDetails": None,
                    "fenlandCohortId": None,
                    "nhsId": "nhs_id",
                    "insuranceNumber": None,
                    "wechatId": None,
                    "kardiaId": None,
                    "pamThirdPartyIdentifier": None,
                    "timezone": "UTC",
                    "labels": None,
                    "surgeryDateTime": "1970-01-19",
                    "preferredUnits": None,
                    "addressComponent": None,
                    "personalDocuments": None,
                    "enrollmentId": None,
                    "enrollmentNumber": None,
                    "deployments": None,
                    "deploymentsInfo": None,
                    "boardingStatus": {
                        "status": 0,
                        "updateDateTime": "2021-02-09T14:08:05.997000Z",
                        "submitterId": None,
                        "reasonOffBoarded": None,
                        "detailsOffBoarded": None,
                    },
                    "language": None,
                    "finishedOnboarding": None,
                    "stats": None,
                    "ragScore": None,
                    "flags": None,
                    "unseenFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "recentFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "lastLoginDateTime": None,
                    "badges": None,
                    "onboardingConfigs": None,
                    "researchTeamActions": None,
                    "hasAssignedManagers": None,
                    "componentsData": {
                        "billing": {
                            "status": 1,
                            "billingProviderName": "someName",
                            "diagnosis": {
                                "order": 1,
                                "icd10Code": "ICD",
                                "description": "diagnosis description",
                            },
                        }
                    },
                    "consent": None,
                    "econsent": None,
                    "age": 34,
                },
                "billing": {
                    "cpt99453_4": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590158",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "cpt99457_8": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590159",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "icd10Code": "S06.0X",
                    "description": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                    "billingFirstReading": "2023-09-07",
                    "primaryInsuranceCarrier": "testGroupName1",
                    "secondaryInsuranceCarrier": "testGroupName2",
                    "primaryInsuranceCarrierPayerId": "testPayer1",
                    "secondaryInsuranceCarrierPayerId": "testPayer2",
                },
                "cptCodesDetails": {
                    "cpt99453": {
                        "billingStartDate": "2022-06-01",
                        "billingEndDate": "2022-06-30",
                        "countOfSubmissions": 1,
                        "countOfSubmissionDays": 1,
                        "earliestBillingDate": None,
                        "totalBillable": 0,
                    },
                    "cpt99454": {
                        "billingStartDate_0": "2022-05-03",
                        "billingEndDate_0": "2022-06-01",
                        "countOfSubmissions_0": 170,
                        "countOfSubmissionDays_0": 17,
                        "earliestBillingDate_0": "2022-05-18",
                        "billingStartDate_1": "",
                        "billingEndDate_1": "",
                        "countOfSubmissions_1": "",
                        "countOfSubmissionDays_1": "",
                        "earliestBillingDate_1": "",
                        "totalBillable": 1,
                    },
                },
            }
        ]
    }
}

sample_huma_billing_7 = {
    "5e8f0c74b50aa9656c34789b": {
        "EHRMonthlyBilling_2022-04-01_2022-07-01": [
            {
                "userId": "5e8f0c74b50aa9656c34789b",
                "moduleId": "billing",
                "reportStartDate": "2022-06-01",
                "reportEndDate": "2022-06-30",
                "user": {
                    "id": "5e8f0c74b50aa9656c34789b",
                    "updateDateTime": "2022-06-30T10:00:00.000000Z",
                    "createDateTime": "1970-01-19T08:40:22.340000Z",
                    "lastSubmitDateTime": "2022-06-30T10:00:00.000000Z",
                    "givenName": "test",
                    "familyName": "test",
                    "gender": None,
                    "biologicalSex": None,
                    "ethnicity": None,
                    "dateOfBirth": "1988-02-20",
                    "email": "<EMAIL>",
                    "phoneNumber": "+380999999999",
                    "primaryAddress": None,
                    "race": None,
                    "bloodGroup": None,
                    "emergencyPhoneNumber": None,
                    "height": None,
                    "additionalContactDetails": None,
                    "familyMedicalHistory": None,
                    "pastHistory": None,
                    "presentSymptoms": None,
                    "operationHistory": None,
                    "chronicIllness": None,
                    "allergyHistory": None,
                    "pregnancy": None,
                    "dateOfLastPhysicalExam": None,
                    "extraCustomFields": None,
                    "surgeryDetails": None,
                    "fenlandCohortId": None,
                    "nhsId": "nhs_id",
                    "insuranceNumber": None,
                    "wechatId": None,
                    "kardiaId": None,
                    "pamThirdPartyIdentifier": None,
                    "timezone": "UTC",
                    "labels": None,
                    "surgeryDateTime": "1970-01-19",
                    "preferredUnits": None,
                    "addressComponent": None,
                    "personalDocuments": None,
                    "enrollmentId": None,
                    "enrollmentNumber": None,
                    "deployments": None,
                    "deploymentsInfo": None,
                    "boardingStatus": {
                        "status": 0,
                        "updateDateTime": "2021-02-09T14:08:05.997000Z",
                        "submitterId": None,
                        "reasonOffBoarded": None,
                        "detailsOffBoarded": None,
                    },
                    "language": None,
                    "finishedOnboarding": None,
                    "stats": None,
                    "ragScore": None,
                    "flags": None,
                    "unseenFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "recentFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "lastLoginDateTime": None,
                    "badges": None,
                    "onboardingConfigs": None,
                    "researchTeamActions": None,
                    "hasAssignedManagers": None,
                    "componentsData": {
                        "billing": {
                            "status": 1,
                            "billingProviderName": "someName",
                            "diagnosis": {
                                "order": 1,
                                "icd10Code": "ICD",
                                "description": "diagnosis description",
                            },
                        }
                    },
                    "consent": None,
                    "econsent": None,
                    "age": 34,
                },
                "billing": {
                    "billingProviderName": "someName",
                    "icd10Code": "ICD",
                    "description": "diagnosis description",
                },
                "cptCodesDetails": {
                    "cpt99453": {
                        "billingStartDate": "2022-06-01",
                        "billingEndDate": "2022-06-30",
                        "countOfSubmissions": 1,
                        "countOfSubmissionDays": 1,
                        "earliestBillingDate": None,
                        "totalBillable": 0,
                    },
                    "cpt99454": {
                        "billingStartDate_0": "",
                        "billingEndDate_0": "",
                        "countOfSubmissions_0": "",
                        "countOfSubmissionDays_0": "",
                        "earliestBillingDate_0": "",
                        "totalBillable": 0,
                    },
                },
            }
        ]
    }
}

new_sample_huma_billings = {
    "6548d173b5a8c292d0944fe5": {
        "EHRMonthlyBilling_2023-10-01_2023-10-31": [
            {
                "userId": "6548d173b5a8c292d0944fe5",
                "deploymentId": "6548cd17b5a8c292d09416e0",
                "moduleId": "billing",
                "reportStartDate": "2023-10-01",
                "reportEndDate": "2023-10-31",
                "user": {
                    "id": "6548d173b5a8c292d0944fe5",
                    "updateDateTime": "2023-11-06T11:44:14.607000Z",
                    "createDateTime": "2023-08-23T10:44:00.503000Z",
                    "lastSubmitDateTime": "2023-11-06T11:44:14.577000Z",
                    "givenName": "Timothy",
                    "familyName": "Bixby",
                    "gender": "MALE",
                    "biologicalSex": "MALE",
                    "ethnicity": "ASIAN_OR_ASIAN_BRITISH",
                    "dateOfBirth": "2008-01-06",
                    "email": "<EMAIL>",
                    "phoneNumber": None,
                    "primaryAddress": None,
                    "race": None,
                    "bloodGroup": None,
                    "emergencyPhoneNumber": None,
                    "height": 156.0,
                    "additionalContactDetails": None,
                    "familyMedicalHistory": None,
                    "pastHistory": None,
                    "presentSymptoms": None,
                    "operationHistory": None,
                    "chronicIllness": None,
                    "allergyHistory": None,
                    "pregnancy": None,
                    "dateOfLastPhysicalExam": None,
                    "extraCustomFields": None,
                    "surgeryDetails": None,
                    "fenlandCohortId": None,
                    "nhsId": "**********",
                    "insuranceNumber": None,
                    "wechatId": None,
                    "kardiaId": None,
                    "pamThirdPartyIdentifier": None,
                    "medicalRecordNumber": None,
                    "timezone": "Asia/Kolkata",
                    "labels": None,
                    "surgeryDateTime": None,
                    "preferredUnits": {
                        "Temperature": "oC",
                        "BloodGlucose": "mmol/L",
                        "BodyMeasurement": "cm",
                        "Height": "cm",
                        "Weight": "kg",
                    },
                    "addressComponent": None,
                    "personalDocuments": None,
                    "enrollmentId": 7,
                    "enrollmentNumber": None,
                    "deployments": None,
                    "deploymentsInfo": None,
                    "boardingStatus": {
                        "status": 0,
                        "updateDateTime": "2023-08-23T10:44:00.503000Z",
                        "submitterId": None,
                        "reasonOffBoarded": None,
                        "detailsOffBoarded": None,
                    },
                    "language": "en",
                    "finishedOnboarding": True,
                    "stats": {
                        "taskCompliance": {
                            "current": 0,
                            "total": 0,
                            "due": 0,
                            "updateDateTime": "2023-11-08T15:14:14.856103Z",
                            "percentage": None,
                        },
                        "studyProgress": {
                            "currentPeriod": 0,
                            "totalPeriods": 0,
                            "totalCompliance": None,
                            "periodProgress": [],
                            "totalOpenQueriesAmount": None,
                        },
                    },
                    "ragScore": None,
                    "flags": None,
                    "unseenFlags": {"red": 0, "amber": 0, "green": None, "gray": 47},
                    "recentFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "lastLoginDateTime": "2023-11-06T11:43:47.509934Z",
                    "badges": None,
                    "onboardingConfigs": None,
                    "researchTeamActions": None,
                    "hasAssignedManagers": None,
                    "componentsData": {
                        "billing": {
                            "status": 1,
                            "insuranceCarriers": [
                                {
                                    "order": 1,
                                    "groupName": "testGroupName1",
                                    "payerId": "testPayer1",
                                    "updateDateTime": "2023-11-06T11:44:01.418903Z",
                                    "createDateTime": "2023-11-06T11:44:01.418903Z",
                                },
                                {
                                    "order": 2,
                                    "groupName": "testGroupName2",
                                    "payerId": "testPayer2",
                                    "updateDateTime": "2023-11-06T11:44:01.418903Z",
                                    "createDateTime": "2023-11-06T11:44:01.418903Z",
                                },
                            ],
                            "billingProviderName": "BillingProviderOne",
                            "diagnosis": {
                                "order": 1,
                                "icd10Code": "S06.0X",
                                "description": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                                "updateDateTime": "2023-11-06T11:44:01.418903Z",
                                "createDateTime": "2023-11-06T11:44:01.418903Z",
                            },
                            "file": {},
                            "updateDateTime": "2023-11-06T11:44:01.418903Z",
                        },
                        "surgicalForms": {"stats": {"toBeFilledAmount": 0, "earliestDueDate": None}},
                    },
                    "consent": None,
                    "econsent": None,
                    "age": 15,
                },
                "billing": {
                    "cpt99453_4": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590158",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "cpt99457_8": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590159",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "icd10Code": "S06.0X",
                    "description": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                    "billingFirstReading": "2023-09-07",
                    "primaryInsuranceCarrier": "testGroupName1",
                    "secondaryInsuranceCarrier": "testGroupName2",
                    "primaryInsuranceCarrierPayerId": "testPayer1",
                    "secondaryInsuranceCarrierPayerId": "testPayer2",
                },
                "cptCodesDetails": {
                    "cpt99453": {
                        "billingStartDate": "2023-09-07",
                        "billingEndDate": "2023-10-06",
                        "countOfSubmissions": 1,
                        "countOfSubmissionDays": 1,
                        "earliestBillingDate": "",
                        "totalBillable": 0,
                    },
                    "cpt99454": {
                        "billingStartDate_0": "2023-09-07",
                        "billingEndDate_0": "2023-10-06",
                        "countOfSubmissions_0": 1,
                        "countOfSubmissionDays_0": 1,
                        "earliestBillingDate_0": "",
                        "totalBillable": 0,
                    },
                    "cpt99457": {
                        "billingStartDate": "2023-10-01",
                        "billingEndDate": "2023-10-31",
                        "totalBillable": 1,
                    },
                    "cpt99458": {
                        "billingStartDate": "2023-10-01",
                        "billingEndDate": "2023-10-31",
                        "totalBillable": 2,
                    },
                    "monitoringTimeMins": 60,
                    "numberOfCalls": 1,
                },
            }
        ]
    },
    "6548d173b5a8c292d0944fe6": {
        "EHRMonthlyBilling_2023-10-01_2023-10-31": [
            {
                "userId": "6548d173b5a8c292d0944fe6",
                "deploymentId": "6548cd17b5a8c292d09416e0",
                "moduleId": "billing",
                "reportStartDate": "2023-10-01",
                "reportEndDate": "2023-10-31",
                "user": {
                    "id": "6548d173b5a8c292d0944fe5",
                    "updateDateTime": "2023-11-06T11:44:14.607000Z",
                    "createDateTime": "2023-08-23T10:44:00.503000Z",
                    "lastSubmitDateTime": "2023-11-06T11:44:14.577000Z",
                    "givenName": "Timothy",
                    "familyName": "Bixby",
                    "gender": "MALE",
                    "biologicalSex": "MALE",
                    "ethnicity": "ASIAN_OR_ASIAN_BRITISH",
                    "dateOfBirth": "2008-01-06",
                    "email": "<EMAIL>",
                    "phoneNumber": None,
                    "primaryAddress": None,
                    "race": None,
                    "bloodGroup": None,
                    "emergencyPhoneNumber": None,
                    "height": 156.0,
                    "additionalContactDetails": None,
                    "familyMedicalHistory": None,
                    "pastHistory": None,
                    "presentSymptoms": None,
                    "operationHistory": None,
                    "chronicIllness": None,
                    "allergyHistory": None,
                    "pregnancy": None,
                    "dateOfLastPhysicalExam": None,
                    "extraCustomFields": None,
                    "surgeryDetails": None,
                    "fenlandCohortId": None,
                    "nhsId": "**********",
                    "insuranceNumber": None,
                    "wechatId": None,
                    "kardiaId": None,
                    "pamThirdPartyIdentifier": None,
                    "medicalRecordNumber": None,
                    "timezone": "Asia/Kolkata",
                    "labels": None,
                    "surgeryDateTime": None,
                    "preferredUnits": {
                        "Temperature": "oC",
                        "BloodGlucose": "mmol/L",
                        "BodyMeasurement": "cm",
                        "Height": "cm",
                        "Weight": "kg",
                    },
                    "addressComponent": None,
                    "personalDocuments": None,
                    "enrollmentId": 7,
                    "enrollmentNumber": None,
                    "deployments": None,
                    "deploymentsInfo": None,
                    "boardingStatus": {
                        "status": 0,
                        "updateDateTime": "2023-08-23T10:44:00.503000Z",
                        "submitterId": None,
                        "reasonOffBoarded": None,
                        "detailsOffBoarded": None,
                    },
                    "language": "en",
                    "finishedOnboarding": True,
                    "stats": {
                        "taskCompliance": {
                            "current": 0,
                            "total": 0,
                            "due": 0,
                            "updateDateTime": "2023-11-08T15:14:14.856103Z",
                            "percentage": None,
                        },
                        "studyProgress": {
                            "currentPeriod": 0,
                            "totalPeriods": 0,
                            "totalCompliance": None,
                            "periodProgress": [],
                            "totalOpenQueriesAmount": None,
                        },
                    },
                    "ragScore": None,
                    "flags": None,
                    "unseenFlags": {"red": 0, "amber": 0, "green": None, "gray": 47},
                    "recentFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "lastLoginDateTime": "2023-11-06T11:43:47.509934Z",
                    "badges": None,
                    "onboardingConfigs": None,
                    "researchTeamActions": None,
                    "hasAssignedManagers": None,
                    "componentsData": {
                        "billing": {
                            "status": 1,
                            "insuranceCarriers": [
                                {
                                    "order": 1,
                                    "groupName": "testGroupName1",
                                    "payerId": "testPayer1",
                                    "updateDateTime": "2023-11-06T11:44:01.418903Z",
                                    "createDateTime": "2023-11-06T11:44:01.418903Z",
                                },
                                {
                                    "order": 2,
                                    "groupName": "testGroupName2",
                                    "payerId": "testPayer2",
                                    "updateDateTime": "2023-11-06T11:44:01.418903Z",
                                    "createDateTime": "2023-11-06T11:44:01.418903Z",
                                },
                            ],
                            "billingProviderName": "BillingProviderOne",
                            "diagnosis": {
                                "order": 1,
                                "icd10Code": "S06.0X",
                                "description": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                                "updateDateTime": "2023-11-06T11:44:01.418903Z",
                                "createDateTime": "2023-11-06T11:44:01.418903Z",
                            },
                            "file": {},
                            "updateDateTime": "2023-11-06T11:44:01.418903Z",
                        },
                        "surgicalForms": {"stats": {"toBeFilledAmount": 0, "earliestDueDate": None}},
                    },
                    "consent": None,
                    "econsent": None,
                    "age": 15,
                },
                "billing": {
                    "cpt99453_4": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590158",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "cpt99457_8": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590159",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "icd10Code": "S06.0X",
                    "description": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                    "billingFirstReading": "2023-09-07",
                    "primaryInsuranceCarrier": "testGroupName1",
                    "secondaryInsuranceCarrier": "testGroupName2",
                    "primaryInsuranceCarrierPayerId": "testPayer1",
                    "secondaryInsuranceCarrierPayerId": "testPayer2",
                },
                "cptCodesDetails": {
                    "cpt99453": {
                        "billingStartDate": "2023-09-07",
                        "billingEndDate": "2023-10-06",
                        "countOfSubmissions": 1,
                        "countOfSubmissionDays": 1,
                        "earliestBillingDate": "",
                        "totalBillable": 0,
                    },
                    "cpt99454": {
                        "billingStartDate": "2024-03-01",
                        "billingEndDate": "2024-03-31",
                        "countOfSubmissions": 29,
                        "countOfSubmissionDays": 29,
                        "earliestBillingDate": "2024-03-16",
                        "totalBillable": 1,
                    },
                    "cpt99457": {
                        "billingStartDate": "2023-10-01",
                        "billingEndDate": "2023-10-31",
                        "totalBillable": 1,
                    },
                    "cpt99458": {
                        "billingStartDate": "2023-10-01",
                        "billingEndDate": "2023-10-31",
                        "totalBillable": 2,
                    },
                    "monitoringTimeMins": 60,
                    "numberOfCalls": 1,
                },
            }
        ]
    },
    "6548d1760d0e02bbb45157b3": {
        "EHRMonthlyBilling_2023-10-01_2023-10-31": [
            {
                "userId": "6548d1760d0e02bbb45157b3",
                "deploymentId": "6548cd17b5a8c292d09416e0",
                "moduleId": "billing",
                "reportStartDate": "2023-10-01",
                "reportEndDate": "2023-10-31",
                "user": {
                    "id": "6548d1760d0e02bbb45157b3",
                    "updateDateTime": "2023-11-06T11:44:26.971000Z",
                    "createDateTime": "2023-08-23T10:44:00.661000Z",
                    "lastSubmitDateTime": "2023-11-06T11:44:26.940000Z",
                    "givenName": "Timothy",
                    "familyName": "Bixby",
                    "gender": "MALE",
                    "biologicalSex": "MALE",
                    "ethnicity": "ASIAN_OR_ASIAN_BRITISH",
                    "dateOfBirth": "2008-01-06",
                    "email": "<EMAIL>",
                    "phoneNumber": None,
                    "primaryAddress": None,
                    "race": None,
                    "bloodGroup": None,
                    "emergencyPhoneNumber": None,
                    "height": 156.0,
                    "additionalContactDetails": None,
                    "familyMedicalHistory": None,
                    "pastHistory": None,
                    "presentSymptoms": None,
                    "operationHistory": None,
                    "chronicIllness": None,
                    "allergyHistory": None,
                    "pregnancy": None,
                    "dateOfLastPhysicalExam": None,
                    "extraCustomFields": None,
                    "surgeryDetails": None,
                    "fenlandCohortId": None,
                    "nhsId": "**********",
                    "insuranceNumber": None,
                    "wechatId": None,
                    "kardiaId": None,
                    "pamThirdPartyIdentifier": None,
                    "medicalRecordNumber": None,
                    "timezone": "Asia/Kolkata",
                    "labels": None,
                    "surgeryDateTime": None,
                    "preferredUnits": {
                        "Temperature": "oC",
                        "BloodGlucose": "mmol/L",
                        "BodyMeasurement": "cm",
                        "Height": "cm",
                        "Weight": "kg",
                    },
                    "addressComponent": None,
                    "personalDocuments": None,
                    "enrollmentId": 8,
                    "enrollmentNumber": None,
                    "deployments": None,
                    "deploymentsInfo": None,
                    "boardingStatus": {
                        "status": 0,
                        "updateDateTime": "2023-08-23T10:44:00.661000Z",
                        "submitterId": None,
                        "reasonOffBoarded": None,
                        "detailsOffBoarded": None,
                    },
                    "language": "en",
                    "finishedOnboarding": True,
                    "stats": {
                        "taskCompliance": {
                            "current": 0,
                            "total": 0,
                            "due": 0,
                            "updateDateTime": "2023-11-08T15:14:14.835713Z",
                            "percentage": None,
                        },
                        "studyProgress": {
                            "currentPeriod": 0,
                            "totalPeriods": 0,
                            "totalCompliance": None,
                            "periodProgress": [],
                            "totalOpenQueriesAmount": None,
                        },
                    },
                    "ragScore": None,
                    "flags": None,
                    "unseenFlags": {"red": 0, "amber": 0, "green": None, "gray": 47},
                    "recentFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "lastLoginDateTime": "2023-11-06T11:43:51.427561Z",
                    "badges": None,
                    "onboardingConfigs": None,
                    "researchTeamActions": None,
                    "hasAssignedManagers": None,
                    "componentsData": {
                        "billing": {
                            "status": 1,
                            "insuranceCarriers": [
                                {
                                    "order": 1,
                                    "groupName": "testGroupName1",
                                    "payerId": "testPayer1",
                                    "updateDateTime": "2023-11-06T11:44:01.885426Z",
                                    "createDateTime": "2023-11-06T11:44:01.885426Z",
                                },
                                {
                                    "order": 2,
                                    "groupName": "testGroupName2",
                                    "payerId": "testPayer2",
                                    "updateDateTime": "2023-11-06T11:44:01.885426Z",
                                    "createDateTime": "2023-11-06T11:44:01.885426Z",
                                },
                            ],
                            "billingProviderName": "BillingProviderOne",
                            "diagnosis": {
                                "order": 1,
                                "icd10Code": "S06.0X",
                                "description": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                                "updateDateTime": "2023-11-06T11:44:01.885426Z",
                                "createDateTime": "2023-11-06T11:44:01.885426Z",
                            },
                            "file": {},
                            "updateDateTime": "2023-11-06T11:44:01.885426Z",
                        },
                        "surgicalForms": {"stats": {"toBeFilledAmount": 0, "earliestDueDate": None}},
                    },
                    "consent": None,
                    "econsent": None,
                    "age": 15,
                },
                "billing": {
                    "cpt99453_4": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590158",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "cpt99457_8": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590159",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "icd10Code": "S06.0X",
                    "description": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                    "billingFirstReading": "2023-09-07",
                    "primaryInsuranceCarrier": "testGroupName1",
                    "secondaryInsuranceCarrier": "testGroupName2",
                    "primaryInsuranceCarrierPayerId": "testPayer1",
                    "secondaryInsuranceCarrierPayerId": "testPayer2",
                },
                "cptCodesDetails": {
                    "cpt99453": {
                        "billingStartDate": "2023-09-07",
                        "billingEndDate": "2023-10-06",
                        "countOfSubmissions": 1,
                        "countOfSubmissionDays": 1,
                        "earliestBillingDate": "",
                        "totalBillable": 0,
                    },
                    "cpt99454": {
                        "billingStartDate_0": "2023-09-07",
                        "billingEndDate_0": "2023-10-06",
                        "countOfSubmissions_0": 1,
                        "countOfSubmissionDays_0": 1,
                        "earliestBillingDate_0": "",
                        "totalBillable": 0,
                    },
                    "cpt99457": {
                        "billingStartDate": "2023-10-01",
                        "billingEndDate": "2023-10-31",
                        "totalBillable": 1,
                    },
                    "cpt99458": {
                        "billingStartDate": "2023-10-01",
                        "billingEndDate": "2023-10-31",
                        "totalBillable": 2,
                    },
                    "monitoringTimeMins": 60,
                    "numberOfCalls": 1,
                },
            }
        ]
    },
    "6548d3e6b5a8c292d09461be": {
        "EHRMonthlyBilling_2023-10-01_2023-10-31": [
            {
                "userId": "6548d3e6b5a8c292d09461be",
                "deploymentId": "6548cd17b5a8c292d09416e0",
                "moduleId": "billing",
                "reportStartDate": "2023-10-01",
                "reportEndDate": "2023-10-31",
                "user": {
                    "id": "6548d3e6b5a8c292d09461be",
                    "updateDateTime": "2023-11-06T11:54:42.533000Z",
                    "createDateTime": "2023-08-23T10:54:24.180000Z",
                    "lastSubmitDateTime": "2023-11-06T11:54:42.505000Z",
                    "givenName": "Timothy",
                    "familyName": "Bixby",
                    "gender": "MALE",
                    "biologicalSex": "MALE",
                    "ethnicity": "ASIAN_OR_ASIAN_BRITISH",
                    "dateOfBirth": "2008-01-06",
                    "email": "<EMAIL>",
                    "phoneNumber": None,
                    "primaryAddress": None,
                    "race": None,
                    "bloodGroup": None,
                    "emergencyPhoneNumber": None,
                    "height": 156.0,
                    "additionalContactDetails": None,
                    "familyMedicalHistory": None,
                    "pastHistory": None,
                    "presentSymptoms": None,
                    "operationHistory": None,
                    "chronicIllness": None,
                    "allergyHistory": None,
                    "pregnancy": None,
                    "dateOfLastPhysicalExam": None,
                    "extraCustomFields": None,
                    "surgeryDetails": None,
                    "fenlandCohortId": None,
                    "nhsId": "**********",
                    "insuranceNumber": None,
                    "wechatId": None,
                    "kardiaId": None,
                    "pamThirdPartyIdentifier": None,
                    "medicalRecordNumber": None,
                    "timezone": "Asia/Kolkata",
                    "labels": None,
                    "surgeryDateTime": None,
                    "preferredUnits": {
                        "Temperature": "oC",
                        "BloodGlucose": "mmol/L",
                        "BodyMeasurement": "cm",
                        "Height": "cm",
                        "Weight": "kg",
                    },
                    "addressComponent": None,
                    "personalDocuments": None,
                    "enrollmentId": 9,
                    "enrollmentNumber": None,
                    "deployments": None,
                    "deploymentsInfo": None,
                    "boardingStatus": {
                        "status": 0,
                        "updateDateTime": "2023-08-23T10:54:24.180000Z",
                        "submitterId": None,
                        "reasonOffBoarded": None,
                        "detailsOffBoarded": None,
                    },
                    "language": "en",
                    "finishedOnboarding": True,
                    "stats": {
                        "taskCompliance": {
                            "current": 0,
                            "total": 0,
                            "due": 0,
                            "updateDateTime": "2023-11-08T15:14:14.830431Z",
                            "percentage": None,
                        },
                        "studyProgress": {
                            "currentPeriod": 0,
                            "totalPeriods": 0,
                            "totalCompliance": None,
                            "periodProgress": [],
                            "totalOpenQueriesAmount": None,
                        },
                    },
                    "ragScore": None,
                    "flags": None,
                    "unseenFlags": {"red": 0, "amber": 0, "green": None, "gray": 47},
                    "recentFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "lastLoginDateTime": "2023-11-06T11:54:15.053927Z",
                    "badges": None,
                    "onboardingConfigs": None,
                    "researchTeamActions": None,
                    "hasAssignedManagers": None,
                    "componentsData": {
                        "billing": {
                            "status": 1,
                            "insuranceCarriers": [
                                {
                                    "order": 1,
                                    "groupName": "testGroupName1",
                                    "payerId": "testPayer1",
                                    "updateDateTime": "2023-11-06T11:54:25.235906Z",
                                    "createDateTime": "2023-11-06T11:54:25.235906Z",
                                },
                                {
                                    "order": 2,
                                    "groupName": "testGroupName2",
                                    "payerId": "testPayer2",
                                    "updateDateTime": "2023-11-06T11:54:25.235906Z",
                                    "createDateTime": "2023-11-06T11:54:25.235906Z",
                                },
                            ],
                            "billingProviderName": "BillingProviderOne",
                            "diagnosis": {
                                "order": 1,
                                "icd10Code": "S06.0X",
                                "description": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                                "updateDateTime": "2023-11-06T11:54:25.235906Z",
                                "createDateTime": "2023-11-06T11:54:25.235906Z",
                            },
                            "file": {},
                            "updateDateTime": "2023-11-06T11:54:25.235906Z",
                        },
                        "surgicalForms": {"stats": {"toBeFilledAmount": 0, "earliestDueDate": None}},
                    },
                    "consent": None,
                    "econsent": None,
                    "age": 15,
                },
                "billing": {
                    "cpt99453_4": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590158",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "cpt99457_8": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590159",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "icd10Code": "S06.0X",
                    "description": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                    "billingFirstReading": "2023-09-07",
                    "primaryInsuranceCarrier": "testGroupName1",
                    "secondaryInsuranceCarrier": "testGroupName2",
                    "primaryInsuranceCarrierPayerId": "testPayer1",
                    "secondaryInsuranceCarrierPayerId": "testPayer2",
                },
                "cptCodesDetails": {
                    "cpt99453": {
                        "billingStartDate": "2023-09-07",
                        "billingEndDate": "2023-10-06",
                        "countOfSubmissions": 1,
                        "countOfSubmissionDays": 1,
                        "earliestBillingDate": "",
                        "totalBillable": 0,
                    },
                    "cpt99454": {
                        "billingStartDate_0": "2023-09-07",
                        "billingEndDate_0": "2023-10-06",
                        "countOfSubmissions_0": 1,
                        "countOfSubmissionDays_0": 1,
                        "earliestBillingDate_0": "",
                        "totalBillable": 0,
                    },
                    "cpt99457": {
                        "billingStartDate": "2023-10-01",
                        "billingEndDate": "2023-10-31",
                        "totalBillable": 1,
                    },
                    "cpt99458": {
                        "billingStartDate": "2023-10-01",
                        "billingEndDate": "2023-10-31",
                        "totalBillable": 2,
                    },
                    "monitoringTimeMins": 60,
                    "numberOfCalls": 1,
                },
            }
        ]
    },
    "6548d3ea0d0e02bbb451724a": {
        "EHRMonthlyBilling_2023-10-01_2023-10-31": [
            {
                "userId": "6548d3ea0d0e02bbb451724a",
                "deploymentId": "6548cd17b5a8c292d09416e0",
                "moduleId": "billing",
                "reportStartDate": "2023-10-01",
                "reportEndDate": "2023-10-31",
                "user": {
                    "id": "6548d3ea0d0e02bbb451724a",
                    "updateDateTime": "2023-11-06T11:54:55.096000Z",
                    "createDateTime": "2023-08-23T10:54:24.340000Z",
                    "lastSubmitDateTime": "2023-11-06T11:54:55.064000Z",
                    "givenName": "Timothy",
                    "familyName": "Bixby",
                    "gender": "MALE",
                    "biologicalSex": "MALE",
                    "ethnicity": "ASIAN_OR_ASIAN_BRITISH",
                    "dateOfBirth": "2008-01-06",
                    "email": "<EMAIL>",
                    "phoneNumber": None,
                    "primaryAddress": None,
                    "race": None,
                    "bloodGroup": None,
                    "emergencyPhoneNumber": None,
                    "height": 156.0,
                    "additionalContactDetails": None,
                    "familyMedicalHistory": None,
                    "pastHistory": None,
                    "presentSymptoms": None,
                    "operationHistory": None,
                    "chronicIllness": None,
                    "allergyHistory": None,
                    "pregnancy": None,
                    "dateOfLastPhysicalExam": None,
                    "extraCustomFields": None,
                    "surgeryDetails": None,
                    "fenlandCohortId": None,
                    "nhsId": "**********",
                    "insuranceNumber": None,
                    "wechatId": None,
                    "kardiaId": None,
                    "pamThirdPartyIdentifier": None,
                    "medicalRecordNumber": None,
                    "timezone": "Asia/Kolkata",
                    "labels": None,
                    "surgeryDateTime": None,
                    "preferredUnits": {
                        "Temperature": "oC",
                        "BloodGlucose": "mmol/L",
                        "BodyMeasurement": "cm",
                        "Height": "cm",
                        "Weight": "kg",
                    },
                    "addressComponent": None,
                    "personalDocuments": None,
                    "enrollmentId": 10,
                    "enrollmentNumber": None,
                    "deployments": None,
                    "deploymentsInfo": None,
                    "boardingStatus": {
                        "status": 0,
                        "updateDateTime": "2023-08-23T10:54:24.340000Z",
                        "submitterId": None,
                        "reasonOffBoarded": None,
                        "detailsOffBoarded": None,
                    },
                    "language": "en",
                    "finishedOnboarding": True,
                    "stats": {
                        "taskCompliance": {
                            "current": 0,
                            "total": 0,
                            "due": 0,
                            "updateDateTime": "2023-11-08T15:14:14.895349Z",
                            "percentage": None,
                        },
                        "studyProgress": {
                            "currentPeriod": 0,
                            "totalPeriods": 0,
                            "totalCompliance": None,
                            "periodProgress": [],
                            "totalOpenQueriesAmount": None,
                        },
                    },
                    "ragScore": None,
                    "flags": None,
                    "unseenFlags": {"red": 0, "amber": 0, "green": None, "gray": 47},
                    "recentFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "lastLoginDateTime": "2023-11-06T11:54:19.038667Z",
                    "badges": None,
                    "onboardingConfigs": None,
                    "researchTeamActions": None,
                    "hasAssignedManagers": None,
                    "componentsData": {
                        "billing": {
                            "status": 1,
                            "insuranceCarriers": [
                                {
                                    "order": 1,
                                    "groupName": "testGroupName1",
                                    "payerId": "testPayer1",
                                    "updateDateTime": "2023-11-06T11:54:25.675247Z",
                                    "createDateTime": "2023-11-06T11:54:25.675247Z",
                                },
                                {
                                    "order": 2,
                                    "groupName": "testGroupName2",
                                    "payerId": "testPayer2",
                                    "updateDateTime": "2023-11-06T11:54:25.675247Z",
                                    "createDateTime": "2023-11-06T11:54:25.675247Z",
                                },
                            ],
                            "billingProviderName": "BillingProviderOne",
                            "diagnosis": {
                                "order": 1,
                                "icd10Code": "S06.0X",
                                "description": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                                "updateDateTime": "2023-11-06T11:54:25.675247Z",
                                "createDateTime": "2023-11-06T11:54:25.675247Z",
                            },
                            "file": {},
                            "updateDateTime": "2023-11-06T11:54:25.675247Z",
                        },
                        "surgicalForms": {"stats": {"toBeFilledAmount": 0, "earliestDueDate": None}},
                    },
                    "consent": None,
                    "econsent": None,
                    "age": 15,
                },
                "billing": {
                    "cpt99453_4": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590158",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "cpt99457_8": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590159",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "icd10Code": "S06.0X",
                    "description": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                    "billingFirstReading": "2023-09-07",
                    "primaryInsuranceCarrier": "testGroupName1",
                    "secondaryInsuranceCarrier": "testGroupName2",
                    "primaryInsuranceCarrierPayerId": "testPayer1",
                    "secondaryInsuranceCarrierPayerId": "testPayer2",
                },
                "cptCodesDetails": {
                    "cpt99453": {
                        "billingStartDate": "2023-09-07",
                        "billingEndDate": "2023-10-06",
                        "countOfSubmissions": 1,
                        "countOfSubmissionDays": 1,
                        "earliestBillingDate": "",
                        "totalBillable": 0,
                    },
                    "cpt99454": {
                        "billingStartDate_0": "2023-09-07",
                        "billingEndDate_0": "2023-10-06",
                        "countOfSubmissions_0": 1,
                        "countOfSubmissionDays_0": 1,
                        "earliestBillingDate_0": "",
                        "totalBillable": 0,
                    },
                    "cpt99457": {
                        "billingStartDate": "2023-10-01",
                        "billingEndDate": "2023-10-31",
                        "totalBillable": 1,
                    },
                    "cpt99458": {
                        "billingStartDate": "2023-10-01",
                        "billingEndDate": "2023-10-31",
                        "totalBillable": 2,
                    },
                    "monitoringTimeMins": 60,
                    "numberOfCalls": 1,
                },
            }
        ]
    },
    "6548f4700d0e02bbb45369e8": {
        "EHRMonthlyBilling_2023-10-01_2023-10-31": [
            {
                "userId": "6548f4700d0e02bbb45369e8",
                "deploymentId": "6548cd17b5a8c292d09416e0",
                "moduleId": "billing",
                "reportStartDate": "2023-10-01",
                "reportEndDate": "2023-10-31",
                "user": {
                    "id": "6548f4700d0e02bbb45369e8",
                    "updateDateTime": "2023-11-06T14:13:26.201000Z",
                    "createDateTime": "2023-08-23T13:13:13.526000Z",
                    "lastSubmitDateTime": "2023-11-06T14:13:26.173000Z",
                    "givenName": "Timothy",
                    "familyName": "Bixby",
                    "gender": "MALE",
                    "biologicalSex": "MALE",
                    "ethnicity": "ASIAN_OR_ASIAN_BRITISH",
                    "dateOfBirth": "2008-01-06",
                    "email": "<EMAIL>",
                    "phoneNumber": None,
                    "primaryAddress": None,
                    "race": None,
                    "bloodGroup": None,
                    "emergencyPhoneNumber": None,
                    "height": 156.0,
                    "additionalContactDetails": None,
                    "familyMedicalHistory": None,
                    "pastHistory": None,
                    "presentSymptoms": None,
                    "operationHistory": None,
                    "chronicIllness": None,
                    "allergyHistory": None,
                    "pregnancy": None,
                    "dateOfLastPhysicalExam": None,
                    "extraCustomFields": None,
                    "surgeryDetails": None,
                    "fenlandCohortId": None,
                    "nhsId": "**********",
                    "insuranceNumber": None,
                    "wechatId": None,
                    "kardiaId": None,
                    "pamThirdPartyIdentifier": None,
                    "medicalRecordNumber": None,
                    "timezone": "Asia/Kolkata",
                    "labels": None,
                    "surgeryDateTime": None,
                    "preferredUnits": {
                        "Temperature": "oC",
                        "BloodGlucose": "mmol/L",
                        "BodyMeasurement": "cm",
                        "Height": "cm",
                        "Weight": "kg",
                    },
                    "addressComponent": None,
                    "personalDocuments": None,
                    "enrollmentId": 11,
                    "enrollmentNumber": None,
                    "deployments": None,
                    "deploymentsInfo": None,
                    "boardingStatus": {
                        "status": 0,
                        "updateDateTime": "2023-08-23T13:13:13.526000Z",
                        "submitterId": None,
                        "reasonOffBoarded": None,
                        "detailsOffBoarded": None,
                    },
                    "language": "en",
                    "finishedOnboarding": True,
                    "stats": {
                        "taskCompliance": {
                            "current": 0,
                            "total": 0,
                            "due": 0,
                            "updateDateTime": "2023-11-08T15:14:16.644875Z",
                            "percentage": None,
                        },
                        "studyProgress": {
                            "currentPeriod": 0,
                            "totalPeriods": 0,
                            "totalCompliance": None,
                            "periodProgress": [],
                            "totalOpenQueriesAmount": None,
                        },
                    },
                    "ragScore": None,
                    "flags": None,
                    "unseenFlags": {"red": 0, "amber": 0, "green": None, "gray": 47},
                    "recentFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "lastLoginDateTime": "2023-11-06T14:13:05.413612Z",
                    "badges": None,
                    "onboardingConfigs": None,
                    "researchTeamActions": None,
                    "hasAssignedManagers": None,
                    "componentsData": {
                        "billing": {
                            "status": 1,
                            "insuranceCarriers": [
                                {
                                    "order": 1,
                                    "groupName": "testGroupName1",
                                    "payerId": "testPayer1",
                                    "updateDateTime": "2023-11-06T14:13:14.371822Z",
                                    "createDateTime": "2023-11-06T14:13:14.371822Z",
                                },
                                {
                                    "order": 2,
                                    "groupName": "testGroupName2",
                                    "payerId": "testPayer2",
                                    "updateDateTime": "2023-11-06T14:13:14.371822Z",
                                    "createDateTime": "2023-11-06T14:13:14.371822Z",
                                },
                            ],
                            "billingProviderName": "BillingProviderOne",
                            "diagnosis": {
                                "order": 1,
                                "icd10Code": "S06.0X",
                                "description": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                                "updateDateTime": "2023-11-06T14:13:14.371822Z",
                                "createDateTime": "2023-11-06T14:13:14.371822Z",
                            },
                            "file": {},
                            "updateDateTime": "2023-11-06T14:13:14.371822Z",
                        },
                        "surgicalForms": {"stats": {"toBeFilledAmount": 0, "earliestDueDate": None}},
                    },
                    "consent": None,
                    "econsent": None,
                    "age": 15,
                },
                "billing": {
                    "cpt99453_4": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590158",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "cpt99457_8": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590159",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "icd10Code": "S06.0X",
                    "description": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                    "billingFirstReading": "2023-09-07",
                    "primaryInsuranceCarrier": "testGroupName1",
                    "secondaryInsuranceCarrier": "testGroupName2",
                    "primaryInsuranceCarrierPayerId": "testPayer1",
                    "secondaryInsuranceCarrierPayerId": "testPayer2",
                },
                "cptCodesDetails": {
                    "cpt99453": {
                        "billingStartDate": "2023-09-07",
                        "billingEndDate": "2023-10-06",
                        "countOfSubmissions": 1,
                        "countOfSubmissionDays": 1,
                        "earliestBillingDate": "",
                        "totalBillable": 0,
                    },
                    "cpt99454": {
                        "billingStartDate_0": "2023-09-07",
                        "billingEndDate_0": "2023-10-06",
                        "countOfSubmissions_0": 1,
                        "countOfSubmissionDays_0": 1,
                        "earliestBillingDate_0": "",
                        "totalBillable": 0,
                    },
                    "cpt99457": {
                        "billingStartDate": "2023-10-01",
                        "billingEndDate": "2023-10-31",
                        "totalBillable": 1,
                    },
                    "cpt99458": {
                        "billingStartDate": "2023-10-01",
                        "billingEndDate": "2023-10-31",
                        "totalBillable": 2,
                    },
                    "monitoringTimeMins": 60,
                    "numberOfCalls": 1,
                },
            }
        ]
    },
    "6548f474b5a8c292d096aa36": {
        "EHRMonthlyBilling_2023-10-01_2023-10-31": [
            {
                "userId": "6548f474b5a8c292d096aa36",
                "deploymentId": "6548cd17b5a8c292d09416e0",
                "moduleId": "billing",
                "reportStartDate": "2023-10-01",
                "reportEndDate": "2023-10-31",
                "user": {
                    "id": "6548f474b5a8c292d096aa36",
                    "updateDateTime": "2023-11-06T14:13:37.106000Z",
                    "createDateTime": "2023-08-23T13:13:13.661000Z",
                    "lastSubmitDateTime": "2023-11-06T14:13:37.078000Z",
                    "givenName": "Timothy",
                    "familyName": "Bixby",
                    "gender": "MALE",
                    "biologicalSex": "MALE",
                    "ethnicity": "ASIAN_OR_ASIAN_BRITISH",
                    "dateOfBirth": "2008-01-06",
                    "email": "<EMAIL>",
                    "phoneNumber": None,
                    "primaryAddress": None,
                    "race": None,
                    "bloodGroup": None,
                    "emergencyPhoneNumber": None,
                    "height": 156.0,
                    "additionalContactDetails": None,
                    "familyMedicalHistory": None,
                    "pastHistory": None,
                    "presentSymptoms": None,
                    "operationHistory": None,
                    "chronicIllness": None,
                    "allergyHistory": None,
                    "pregnancy": None,
                    "dateOfLastPhysicalExam": None,
                    "extraCustomFields": None,
                    "surgeryDetails": None,
                    "fenlandCohortId": None,
                    "nhsId": "**********",
                    "insuranceNumber": None,
                    "wechatId": None,
                    "kardiaId": None,
                    "pamThirdPartyIdentifier": None,
                    "medicalRecordNumber": None,
                    "timezone": "Asia/Kolkata",
                    "labels": None,
                    "surgeryDateTime": None,
                    "preferredUnits": {
                        "Temperature": "oC",
                        "BloodGlucose": "mmol/L",
                        "BodyMeasurement": "cm",
                        "Height": "cm",
                        "Weight": "kg",
                    },
                    "addressComponent": None,
                    "personalDocuments": None,
                    "enrollmentId": 12,
                    "enrollmentNumber": None,
                    "deployments": None,
                    "deploymentsInfo": None,
                    "boardingStatus": {
                        "status": 0,
                        "updateDateTime": "2023-08-23T13:13:13.661000Z",
                        "submitterId": None,
                        "reasonOffBoarded": None,
                        "detailsOffBoarded": None,
                    },
                    "language": "en",
                    "finishedOnboarding": True,
                    "stats": {
                        "taskCompliance": {
                            "current": 0,
                            "total": 0,
                            "due": 0,
                            "updateDateTime": "2023-11-08T15:14:16.720755Z",
                            "percentage": None,
                        },
                        "studyProgress": {
                            "currentPeriod": 0,
                            "totalPeriods": 0,
                            "totalCompliance": None,
                            "periodProgress": [],
                            "totalOpenQueriesAmount": None,
                        },
                    },
                    "ragScore": None,
                    "flags": None,
                    "unseenFlags": {"red": 0, "amber": 0, "green": None, "gray": 47},
                    "recentFlags": {"red": 0, "amber": 0, "green": None, "gray": 1},
                    "lastLoginDateTime": "2023-11-06T14:13:09.071163Z",
                    "badges": None,
                    "onboardingConfigs": None,
                    "researchTeamActions": None,
                    "hasAssignedManagers": None,
                    "componentsData": {
                        "billing": {
                            "status": 1,
                            "insuranceCarriers": [
                                {
                                    "order": 1,
                                    "groupName": "testGroupName1",
                                    "payerId": "testPayer1",
                                    "updateDateTime": "2023-11-06T14:13:14.915493Z",
                                    "createDateTime": "2023-11-06T14:13:14.915493Z",
                                },
                                {
                                    "order": 2,
                                    "groupName": "testGroupName2",
                                    "payerId": "testPayer2",
                                    "updateDateTime": "2023-11-06T14:13:14.915493Z",
                                    "createDateTime": "2023-11-06T14:13:14.915493Z",
                                },
                            ],
                            "billingProviderName": "BillingProviderOne",
                            "diagnosis": {
                                "order": 1,
                                "icd10Code": "S06.0X",
                                "description": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                                "updateDateTime": "2023-11-06T14:13:14.915493Z",
                                "createDateTime": "2023-11-06T14:13:14.915493Z",
                            },
                            "file": {},
                            "updateDateTime": "2023-11-06T14:13:14.915493Z",
                        },
                        "surgicalForms": {"stats": {"toBeFilledAmount": 0, "earliestDueDate": None}},
                    },
                    "consent": None,
                    "econsent": None,
                    "age": 15,
                },
                "billing": {
                    "cpt99453_4": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590158",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "cpt99457_8": {
                        "billingProviderName": "Billing Provider1",
                        "billingProviderId": "214590159",
                        "billingProviderLocationDepartment": "someLocation",
                        "billingProviderLocationAbbreviation": "someAbbreviation1",
                        "billingProviderPlaceOfServiceAbbreviation": "someAbbreviation2",
                    },
                    "icd10Code": "S06.0X",
                    "description": "Concussion with loss of consciousness of 30 minutes or less, initial encounter",
                    "billingFirstReading": "2023-09-07",
                    "primaryInsuranceCarrier": "testGroupName1",
                    "secondaryInsuranceCarrier": "testGroupName2",
                    "primaryInsuranceCarrierPayerId": "testPayer1",
                    "secondaryInsuranceCarrierPayerId": "testPayer2",
                },
                "cptCodesDetails": {
                    "cpt99453": {
                        "billingStartDate": "2023-09-07",
                        "billingEndDate": "2023-10-06",
                        "countOfSubmissions": 1,
                        "countOfSubmissionDays": 1,
                        "earliestBillingDate": "",
                        "totalBillable": 0,
                    },
                    "cpt99454": {
                        "billingStartDate_0": "2023-09-07",
                        "billingEndDate_0": "2023-10-06",
                        "countOfSubmissions_0": 1,
                        "countOfSubmissionDays_0": 1,
                        "earliestBillingDate_0": "",
                        "totalBillable": 0,
                    },
                    "cpt99457": {
                        "billingStartDate": "2023-10-01",
                        "billingEndDate": "2023-10-31",
                        "totalBillable": 1,
                    },
                    "cpt99458": {
                        "billingStartDate": "2023-10-01",
                        "billingEndDate": "2023-10-31",
                        "totalBillable": 2,
                    },
                    "monitoringTimeMins": 60,
                    "numberOfCalls": 1,
                },
            }
        ]
    },
}
