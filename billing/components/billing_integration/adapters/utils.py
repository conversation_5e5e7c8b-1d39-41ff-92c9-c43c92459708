import json
import logging
import os
import uuid

from jinja2 import Template

from billing.components.export.models.billing_models import (
    BillingGeneralReportCPTCodes,
    MonthlyReportSubmissionCptCodeMapping1,
    MonthlyReportSubmissionCptCodeMapping2,
    TimeTrackingCPTCodeDetails,
)
from billing.components.export.use_case.billing_monthly_export import BillingMonthlyEHRExportableUseCase

logger = logging.getLogger(__name__)

EHR_MONTHLY_BILLING = BillingMonthlyEHRExportableUseCase.MODULE_NAME
CPT_CODE_DETAILS = "cptCodesDetails"
BILLING = "billing"
COMPONENTS_DATA = "componentsData"
ICD10_CODE = "icd10Code"
DESCRIPTION = "description"
USER = "user"
DIAGNOSIS = "diagnosis"
BILLING_PROVIDER_ID = "billingProviderId"
BILLING_PROVIDER_ABBR = "billingProviderAbbreviation"
BILLING_PROVIDER_LOCATION_DEPARTMENT = "billingProviderLocationDepartment"
DEFAULT_LOCATION_DEPARTMENT = "RCCSM"
BILLING_PROVIDER_LOCATION_ABBREVIATION = "billingProviderLocationAbbreviation"
DEFAULT_LOCATION_ABBREVIATION = "MD"
BILLING_PROVIDER_PLACE_OF_SERVICE_ABBREVIATION = "billingProviderPlaceOfServiceAbbreviation"
DEFAULT_PLACE_OF_SERVICE_ABBREVIATION = "Home"

CPT_99453_4 = "cpt99453_4"
CPT_99457_8 = "cpt99457_8"
CPT_98975_6 = "cpt98975_6"
CPT_98980_1 = "cpt98980_1"

ONCE_PER_MONTH = 1
TWICE_PER_MONTH = 2


def convert_huma_billing_report_to_redox(huma_billing_report: dict) -> dict:
    """convert huma billing data to list of redox transactions input format"""
    financials_report = {}

    for user in huma_billing_report:
        huma_billing_per_user = huma_billing_report[user]

        key = get_billing_key(huma_billing_per_user, user)
        if not key:
            continue

        transactions = convert_huma_billing_to_redox_transactions(huma_billing_per_user, user, key)
        financials_report[user] = transactions

    return financials_report


def convert_huma_billing_to_redox_transactions(huma_billing: dict, user: str, key: str) -> dict:
    """convert huma billing data per user to redox transactions input format"""
    with open(os.path.dirname(__file__) + "/generic/transaction_template.jinja2") as f:
        data_read = f.read()

    json_template = Template(data_read)

    # todo can we have multiple monthly billing?
    if CPT_CODE_DETAILS not in huma_billing[key][0]:
        logger.error("There is financial report for the user: {}, but no CPT code details".format(user))

        return []

    cpt_code_details = huma_billing[key][0][CPT_CODE_DETAILS]

    if DIAGNOSIS not in huma_billing[key][0][USER][COMPONENTS_DATA][BILLING]:
        logger.error("There is financial report for the user: {}, but no user diagnosis data".format(user))

        return []

    diagnose_code = huma_billing[key][0][USER][COMPONENTS_DATA][BILLING][DIAGNOSIS].get(ICD10_CODE, "")
    diagnose_name = huma_billing[key][0][USER][COMPONENTS_DATA][BILLING][DIAGNOSIS].get(DESCRIPTION, "")

    billing_provider_ids = {}
    location = {}

    provider_cpt_code_list = [CPT_99453_4, CPT_99457_8, CPT_98975_6, CPT_98980_1]

    for provider_cpt_code in provider_cpt_code_list:
        if provider_cpt_code in huma_billing[key][0][BILLING]:
            billing_provider_ids[provider_cpt_code] = huma_billing[key][0][BILLING][provider_cpt_code].get(
                BILLING_PROVIDER_ABBR, ""
            )

            location_department = huma_billing[key][0][BILLING][provider_cpt_code].get(
                BILLING_PROVIDER_LOCATION_DEPARTMENT, DEFAULT_LOCATION_DEPARTMENT
            )
            location_abbr = huma_billing[key][0][BILLING][provider_cpt_code].get(
                BILLING_PROVIDER_LOCATION_ABBREVIATION, DEFAULT_LOCATION_ABBREVIATION
            )
            pos_abbr = huma_billing[key][0][BILLING][provider_cpt_code].get(
                BILLING_PROVIDER_PLACE_OF_SERVICE_ABBREVIATION,
                DEFAULT_PLACE_OF_SERVICE_ABBREVIATION,
            )

            location = {
                "locationDepartment": location_department or DEFAULT_LOCATION_DEPARTMENT,
                "locationAbr": location_abbr or DEFAULT_LOCATION_ABBREVIATION,
                "placeOfServiceAbr": pos_abbr or DEFAULT_PLACE_OF_SERVICE_ABBREVIATION,
            }

        else:
            billing_provider_ids[provider_cpt_code] = ""

    transactions = []

    for cpt_code in cpt_code_details:
        # check if it is monitoring codes
        check_if_monitoring_codes(
            cpt_code,
            cpt_code_details,
            billing_provider_ids,
            diagnose_code,
            diagnose_name,
            json_template,
            transactions,
        )

        # check if it is submission codes
        check_if_submission_codes(
            cpt_code,
            cpt_code_details,
            billing_provider_ids,
            diagnose_code,
            diagnose_name,
            json_template,
            transactions,
        )

        # check if it is submission codes
        check_if_submission_codes_2(
            cpt_code,
            cpt_code_details,
            billing_provider_ids,
            diagnose_code,
            diagnose_name,
            json_template,
            transactions,
        )

        # location is the same for all transactions apparently because it goes to visit parameter
        for transaction in transactions:
            transaction["location"] = location

    return transactions


def convert_huma_billing_monitoring_to_transaction(
    cpt_code_details, cpt_code, diagnose_code, diagnose_name, billing_provider_id
):
    transaction = {}

    transaction["id"] = uuid.uuid4()
    transaction["startDate"] = cpt_code_details[cpt_code][MonthlyReportSubmissionCptCodeMapping1.BILLING_START_DATE]
    transaction["endDate"] = cpt_code_details[cpt_code][MonthlyReportSubmissionCptCodeMapping1.BILLING_END_DATE]

    if not transaction["startDate"] or not transaction["endDate"]:
        return None

    transaction["code"] = cpt_code.removeprefix("cpt")
    transaction["quantity"] = cpt_code_details[cpt_code][TimeTrackingCPTCodeDetails.TOTAL_BILLABLE]
    transaction["diagnoseCode"] = diagnose_code
    transaction["diagnoseName"] = diagnose_name
    transaction["billingProviderAbr"] = billing_provider_id

    return transaction


def convert_huma_billing_submission_53_75_to_transaction(
    cpt_code_details, cpt_code, diagnose_code, diagnose_name, billing_provider_id
):
    transaction = {}

    transaction["id"] = uuid.uuid4()
    transaction["startDate"] = cpt_code_details[cpt_code][MonthlyReportSubmissionCptCodeMapping1.BILLING_START_DATE]
    transaction["endDate"] = cpt_code_details[cpt_code][MonthlyReportSubmissionCptCodeMapping1.BILLING_END_DATE]
    if not transaction["startDate"] or not transaction["endDate"]:
        return None

    transaction["code"] = cpt_code.removeprefix("cpt")
    transaction["quantity"] = 1
    transaction["diagnoseCode"] = diagnose_code
    transaction["diagnoseName"] = diagnose_name
    transaction["billingProviderAbr"] = billing_provider_id

    return transaction


def convert_huma_billing_submission_54_76_to_transaction(
    cpt_code_details, cpt_code, diagnose_code, diagnose_name, billing_provider_id
):
    transaction = {}

    transaction["id"] = uuid.uuid4()
    transaction["startDate"] = cpt_code_details[cpt_code][MonthlyReportSubmissionCptCodeMapping2.BILLING_START_DATE_0]
    transaction["endDate"] = cpt_code_details[cpt_code][MonthlyReportSubmissionCptCodeMapping2.BILLING_END_DATE_0]
    if not transaction["startDate"] or not transaction["endDate"]:
        return None

    transaction["code"] = cpt_code.removeprefix("cpt")
    transaction["quantity"] = 1
    transaction["diagnoseCode"] = diagnose_code
    transaction["diagnoseName"] = diagnose_name
    transaction["billingProviderAbr"] = billing_provider_id

    return transaction


def convert_huma_billing_submission_54_76_to_transaction_1(
    cpt_code_details, cpt_code, diagnose_code, diagnose_name, billing_provider_id
):
    transaction = {}

    transaction["id"] = uuid.uuid4()
    transaction["startDate"] = cpt_code_details[cpt_code][MonthlyReportSubmissionCptCodeMapping2.BILLING_START_DATE_1]
    transaction["endDate"] = cpt_code_details[cpt_code][MonthlyReportSubmissionCptCodeMapping2.BILLING_END_DATE_1]
    if not transaction["startDate"] or not transaction["endDate"]:
        return None

    transaction["code"] = cpt_code.removeprefix("cpt")
    transaction["quantity"] = 1
    transaction["diagnoseCode"] = diagnose_code
    transaction["diagnoseName"] = diagnose_name
    transaction["billingProviderAbr"] = billing_provider_id

    return transaction


def convert_huma_billing_submission_54_76_to_transaction_2(
    cpt_code_details, cpt_code, diagnose_code, diagnose_name, billing_provider_id
):
    transaction = {}

    transaction["id"] = uuid.uuid4()

    if MonthlyReportSubmissionCptCodeMapping1.BILLING_START_DATE in cpt_code_details[cpt_code]:
        transaction["startDate"] = cpt_code_details[cpt_code][MonthlyReportSubmissionCptCodeMapping1.BILLING_START_DATE]
        transaction["endDate"] = cpt_code_details[cpt_code][MonthlyReportSubmissionCptCodeMapping1.BILLING_END_DATE]

    elif MonthlyReportSubmissionCptCodeMapping2.BILLING_START_DATE_0 in cpt_code_details[cpt_code]:
        transaction["startDate"] = cpt_code_details[cpt_code][
            MonthlyReportSubmissionCptCodeMapping2.BILLING_START_DATE_0
        ]
        transaction["endDate"] = cpt_code_details[cpt_code][MonthlyReportSubmissionCptCodeMapping2.BILLING_END_DATE_0]
    if not transaction["startDate"] or not transaction["endDate"]:
        return None

    transaction["code"] = cpt_code.removeprefix("cpt")
    transaction["quantity"] = 1
    transaction["diagnoseCode"] = diagnose_code
    transaction["diagnoseName"] = diagnose_name
    transaction["billingProviderAbr"] = billing_provider_id

    return transaction


def check_if_monitoring_codes(
    cpt_code,
    cpt_code_details,
    billing_provider_ids,
    diagnose_code,
    diagnose_name,
    json_template,
    transactions,
):
    if (
        cpt_code
        in [
            BillingGeneralReportCPTCodes.CPT_99457,
            BillingGeneralReportCPTCodes.CPT_99458,
            BillingGeneralReportCPTCodes.CPT_98980,
            BillingGeneralReportCPTCodes.CPT_98981,
        ]
    ) and cpt_code_details[cpt_code][TimeTrackingCPTCodeDetails.TOTAL_BILLABLE] != 0:
        billing_provider_id = ""

        if cpt_code in [
            BillingGeneralReportCPTCodes.CPT_99457,
            BillingGeneralReportCPTCodes.CPT_99458,
        ]:
            billing_provider_id = billing_provider_ids[CPT_99457_8]
        if cpt_code in [
            BillingGeneralReportCPTCodes.CPT_98980,
            BillingGeneralReportCPTCodes.CPT_98981,
        ]:
            billing_provider_id = billing_provider_ids[CPT_98980_1]

        transaction = convert_huma_billing_monitoring_to_transaction(
            cpt_code_details,
            cpt_code,
            diagnose_code,
            diagnose_name,
            billing_provider_id,
        )
        if not transaction:
            return

        json_output = json_template.render(
            transaction=transaction,
        )
        transactions.append(json.loads(json_output))


def check_if_submission_codes(
    cpt_code,
    cpt_code_details,
    billing_provider_ids,
    diagnose_code,
    diagnose_name,
    json_template,
    transactions,
):
    if (
        (
            cpt_code
            in [
                BillingGeneralReportCPTCodes.CPT_99453,
                BillingGeneralReportCPTCodes.CPT_98975,
            ]
        )
        and MonthlyReportSubmissionCptCodeMapping1.TOTAL_BILLABLE in cpt_code_details[cpt_code]
        and cpt_code_details[cpt_code][MonthlyReportSubmissionCptCodeMapping1.TOTAL_BILLABLE] == ONCE_PER_MONTH
    ):
        billing_provider_id = choose_billing_provider_id(cpt_code, billing_provider_ids)

        transaction = convert_huma_billing_submission_53_75_to_transaction(
            cpt_code_details,
            cpt_code,
            diagnose_code,
            diagnose_name,
            billing_provider_id,
        )
        if not transaction:
            return

        json_output = json_template.render(
            transaction=transaction,
        )
        transactions.append(json.loads(json_output))


def check_if_submission_codes_2(
    cpt_code,
    cpt_code_details,
    billing_provider_ids,
    diagnose_code,
    diagnose_name,
    json_template,
    transactions,
):
    if cpt_code in [
        BillingGeneralReportCPTCodes.CPT_99454,
        BillingGeneralReportCPTCodes.CPT_98976,
    ]:
        if (MonthlyReportSubmissionCptCodeMapping1.TOTAL_BILLABLE in cpt_code_details[cpt_code]) and (
            cpt_code_details[cpt_code][MonthlyReportSubmissionCptCodeMapping1.TOTAL_BILLABLE] == TWICE_PER_MONTH
        ):
            billing_provider_id = choose_billing_provider_id(cpt_code, billing_provider_ids)

            transaction = convert_huma_billing_submission_54_76_to_transaction(
                cpt_code_details,
                cpt_code,
                diagnose_code,
                diagnose_name,
                billing_provider_id,
            )

            if transaction:
                json_output = json_template.render(
                    transaction=transaction,
                )
                transactions.append(json.loads(json_output))

            transaction = convert_huma_billing_submission_54_76_to_transaction_1(
                cpt_code_details,
                cpt_code,
                diagnose_code,
                diagnose_name,
                billing_provider_id,
            )

            if transaction:
                json_output = json_template.render(
                    transaction=transaction,
                )
                transactions.append(json.loads(json_output))
        elif (MonthlyReportSubmissionCptCodeMapping1.TOTAL_BILLABLE in cpt_code_details[cpt_code]) and (
            cpt_code_details[cpt_code][MonthlyReportSubmissionCptCodeMapping1.TOTAL_BILLABLE] == ONCE_PER_MONTH
        ):
            billing_provider_id = choose_billing_provider_id(cpt_code, billing_provider_ids)

            transaction = convert_huma_billing_submission_54_76_to_transaction_2(
                cpt_code_details,
                cpt_code,
                diagnose_code,
                diagnose_name,
                billing_provider_id,
            )
            if not transaction:
                return

            json_output = json_template.render(
                transaction=transaction,
            )
            transactions.append(json.loads(json_output))


def choose_billing_provider_id(cpt_code, billing_provider_ids):
    billing_provider_id = ""

    if cpt_code == BillingGeneralReportCPTCodes.CPT_99454:
        billing_provider_id = billing_provider_ids[CPT_99453_4]
    if cpt_code == BillingGeneralReportCPTCodes.CPT_98976:
        billing_provider_id = billing_provider_ids[CPT_98975_6]
    if cpt_code == BillingGeneralReportCPTCodes.CPT_99453:
        billing_provider_id = billing_provider_ids[CPT_99453_4]
    if cpt_code == BillingGeneralReportCPTCodes.CPT_98975:
        billing_provider_id = billing_provider_ids[CPT_98975_6]

    return billing_provider_id


def get_billing_key(huma_billing_per_user, user):
    matching_keys = [key for key in huma_billing_per_user.keys() if EHR_MONTHLY_BILLING in key]

    if len(matching_keys) != 1:
        logger.error("There is unexpected data in the billing report for user {}".format(user))

        return None

    key = matching_keys[0]

    return key
