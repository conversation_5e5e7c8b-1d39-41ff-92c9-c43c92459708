{
    "Meta": {
        "DataModel": "Financial",
        "EventType": "Transaction",
        "EventDateTime": "{{ datetime }}",
        "Test": {{ destination.is_test | tojson }},
        "Source": {
            "ID": "{{ generic_ehr.source_id }}",
            "Name": "{{ generic_ehr.source_name }}"
        },
        "Destinations": [
            {"ID": "{{ destination.destination_id }}", "Name": "{{ destination.destination_name }}"}
        ],
        "Logs": [
            {
                "ID": "{{ generic_ehr.logs_id }}",
                "AttemptID": "{{ generic_ehr.logs_attempt_id }}"
            }
        ]
    },
    "Visit": {
        "Location": {
            "Type": "{{ data.Location.locationAbr }}",
            "Facility": "{{ data.Location.placeOfServiceAbr }}",
            "Department": "{{ data.Location.locationDepartment }}"
        }
    },
    "Patient": {
        "Identifiers": {{ data.Patient.Identifiers|tojson }},
        "Demographics": {
            "FirstName": "{{ data.Patient.Demographics.FirstName }}",
            "LastName": "{{ data.Patient.Demographics.LastName }}"
        }
    },
    "Transactions": {{ data.Transactions|tojson }}
}
