import json
import logging
import os
from datetime import datetime

from jinja2 import Template

from huma_plugins.components.integration.adapters.generic.ehr_api import EHRApi
from huma_plugins.components.integration.dtos.generic_ehr import GenericEHRDTO

logger = logging.getLogger(__name__)


class EHRBillingApi(EHRApi):
    def send_financial_transactions(self, data: dict):
        """The Financial data model conveys when the patient owes something for a medication,
        lab, test, or other care (e.g., bed charges). The data model contains details about the product or service, as
        well as details like date, time, amounts, and participating organizations and persons. Healthcare organizations
        typically use the Financial data model for their billing process and internal cost allocation..
        """

        self._path = "query"

        if not self.authenticate():
            return None

        query = self.create_financial_query(data)
        if not query:
            logger.error("send_financial_transactions destination not registered")
            return None

        headers = {
            "Authorization": "Bearer " + self._access_token,
        }
        session = self.setup_session()

        response = session.post(self._url + "/" + self._path, json=query, headers=headers)

        if response.ok:
            logger.info("sending financial transaction/s result: {}".format(response.text))
        else:
            logger.error("sending financial transaction/s failed: {}".format(response.text))
        return response.json()

    def create_financial_query(self, data: dict):
        with open(os.path.dirname(__file__) + "/financial_transaction_template.jinja2") as f:
            data_read = f.read()

        json_template = Template(data_read)

        destination = None
        # find the right destination
        for dt in self._integration.generic_ehr.destination:
            if dt.destination_func == GenericEHRDTO.Destination.DestinationFunc.send_financial_transactions:
                destination = dt
                break

        if not destination:
            # it means there is no endpoint for this function
            return None

        # Render the template with the data
        json_output = json_template.render(
            datetime=datetime.now().isoformat(),
            generic_ehr=self._integration.generic_ehr,
            data=data,
            destination=destination,
        )
        query = json.loads(json_output)

        return query
