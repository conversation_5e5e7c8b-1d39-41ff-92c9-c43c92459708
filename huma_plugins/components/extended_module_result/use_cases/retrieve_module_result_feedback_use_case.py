import logging
import typing as t

from huma_plugins.components.cms.dtos import CMS<PERSON>ontentD<PERSON>
from huma_plugins.components.cms.library import RECOMMENDATIONS_COLLECTION_NAME
from huma_plugins.components.cms.use_case import (
    AdvancedSearchContentRequestObject,
    AdvancedSearchContentUseCase,
)
from huma_plugins.components.extended_module_result.dtos.feedback_model import (
    FeedbackDTO,
)
from huma_plugins.components.extended_module_result.exceptions import (
    Feedback<PERSON>otConfigured,
    FeedbackRagsNotConfigured,
    FeedbackTextsNotConfigured,
    ModuleResultDoesNotExist,
    PrimitiveValueNotAssigned,
)
from huma_plugins.components.extended_module_result.repository.module_feedback_repository import (
    FeedbackRepository,
)
from huma_plugins.components.extended_module_result.routers.app_request_objects import (
    RetrieveFeedbackRequestObject,
)
from huma_plugins.components.extended_module_result.routers.rpm_module_result_response import (
    FeedbackTextResponse,
    RetrieveFeedbackResponseObject,
)
from sdk.common.adapter.feature_toggle import FeatureToggleAdapter
from sdk.common.common_models.sort import Sort<PERSON>ield
from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils import inject
from sdk.common.utils.convertible import ConvertibleClassValidationError
from sdk.common.utils.inject import autoparams
from sdk.common.utils.validators import must_be_at_least_one_of, must_be_present
from sdk.deployment.dtos.deployment import DeploymentDTO
from sdk.module_result.dtos.module_config import (
    FeedBacksGroupedByRag,
    FeedbackConfig,
    FeedbackRag,
    FeedbackText,
    ModuleConfig,
    VariabilityFilter,
)
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.dtos.primitives.primitive_dto import MeasureUnit
from sdk.module_result.dtos.primitives.primitives_with_preferable_units_dto import (
    PrimitivesWithPreferableUnitsDTO,
)
from sdk.module_result.modules.modules_manager import ModulesManager
from sdk.module_result.service.module_result_service import ModuleResultService

logger = logging.getLogger(__name__)


class RetrieveFeedbackUseCase(UseCase):
    request_object: RetrieveFeedbackRequestObject
    DEFAULT_SEVERITY = 1

    @autoparams()
    def __init__(self, feedback_repo: FeedbackRepository):
        self.feedback_repo = feedback_repo

    def process_request(self, request_object: RetrieveFeedbackRequestObject):
        deployment = self.request_object.deployment
        module_config = deployment.find_module_config(self.request_object.moduleId)
        self.validate_module_config(module_config)

        current_result, comparing_result = self.get_module_results(deployment, module_config)
        severity, include_rags = self.get_severity_from_module_result(current_result)
        rag = FeedbackDTO.get_rag_colour_by_severity(severity)
        feedbacks = module_config.feedback.feedbacks

        latest_feedback = self.get_latest_feedback_by_rag(feedbacks, rag, module_config)
        user_feedback = self.create_user_feedback(latest_feedback, module_config, rag, current_result, feedbacks)
        resp_obj = RetrieveFeedbackResponseObject
        response_data = {
            resp_obj.CURRENT_VALUE: self.get_result_value(current_result),
            resp_obj.COMPARING_VALUE: (self.get_result_value(comparing_result) if comparing_result else None),
            resp_obj.UPPER_BOUND: module_config.feedback.upperBound,
            resp_obj.LOWER_BOUND: module_config.feedback.lowerBound,
            resp_obj.CURRENT_VALUE_SEVERITY: severity,
            resp_obj.FEEDBACK: {
                FeedbackTextResponse.FEEDBACK_TEXT_ID: user_feedback.feedbackTextId,
                FeedbackTextResponse.TEXT: user_feedback.text,
            },
            resp_obj.RAG_THRESHOLDS: (module_config.ragThresholds if include_rags else None),
        }

        return RetrieveFeedbackResponseObject(**response_data)

    def validate_module_config(self, module_config: t.Optional[ModuleConfig]):
        if not module_config:
            raise InvalidRequestException(f"Module config for module {self.request_object.moduleId} not found")

        if not module_config.feedback or not module_config.feedback.enabled:
            raise FeedbackNotConfigured(f"Feedback is not configured for module {self.request_object.moduleId}")

        features: FeatureToggleAdapter = inject.instance(FeatureToggleAdapter)
        if not features.is_enabled("module-feedback"):
            must_be_present(
                upperBound=module_config.feedback.upperBound,
                lowerBound=module_config.feedback.lowerBound,
                feedbacks=module_config.feedback.feedbacks,
            )

    def get_module_results(
        self, deployment: DeploymentDTO, module_config: ModuleConfig
    ) -> tuple[PrimitiveDTO, t.Optional[PrimitiveDTO]]:
        module_id = self.request_object.moduleId
        module_service = ModuleResultService()
        options = {
            "user_id": self.request_object.userId,
            "module_id": module_id,
            "module_config_id": module_config.id,
            "deployment_id": deployment.id,
            "direction": SortField.Direction.DESC,
            "role": self.request_object.submitter.get_role().id,
            "limit": 1,
            "skip": 0,
            "from_date_time": None,
            "to_date_time": None,
            "filters": None,
        }
        current = module_service.retrieve_module_results(
            module_result_id=self.request_object.moduleResultId, **options
        ).get(module_id)

        if not current:
            raise ModuleResultDoesNotExist(f"User has not yet submitted the module result for module {module_id}")

        options["to_date_time"] = current[0].startDateTime
        comparing = module_service.retrieve_module_results(**options).get(module_id)

        return current[0], comparing[0] if comparing else None

    def get_severity_from_module_result(self, module_result: PrimitiveDTO) -> tuple[int, bool]:
        severity = module_result.get_severity()
        return (severity, True) if severity is not None else (self.DEFAULT_SEVERITY, False)

    def get_latest_feedback_by_rag(
        self,
        feedbacks: list[FeedBacksGroupedByRag],
        rag: FeedbackRag,
        module_config: ModuleConfig,
    ) -> FeedbackDTO:
        rag_option = []
        for feedback in feedbacks:
            if rag in feedback.rag:
                rag_option.extend(feedback.rag)
                break

        if not rag_option:
            raise FeedbackRagsNotConfigured(
                f"Feedback rags are not configured in {self.request_object.moduleId} module config"
            )

        latest_feedback = self.feedback_repo.get_latest_user_feedback_by_rag(
            self.request_object.userId, module_config.id, rag_option
        )
        return latest_feedback

    def create_user_feedback(
        self,
        latest_feedback: FeedbackDTO,
        module_config: ModuleConfig,
        rag: FeedbackRag,
        latest_module_result: PrimitiveDTO,
        feedbacks: list[FeedBacksGroupedByRag],
    ) -> FeedbackDTO:
        if not latest_feedback:
            user_feedback_text = self.pull_texts_from_feedback_config(rag, feedbacks)[0]
        elif latest_feedback.moduleResultId == latest_module_result.moduleResultId:
            return latest_feedback
        else:
            user_feedback_text = self.pull_next_feedback_by_rag(latest_feedback, rag, feedbacks)

        feedback = FeedbackDTO.from_dict(
            {
                FeedbackDTO.MODULE_CONFIG_ID: module_config.id,
                FeedbackDTO.MODULE_RESULT_ID: latest_module_result.moduleResultId,
                FeedbackDTO.FEEDBACK_TEXT_ID: user_feedback_text.id,
                FeedbackDTO.TEXT: user_feedback_text.text,
                FeedbackDTO.RAG: rag,
                **self.request_object.to_dict(
                    include_none=False,
                    ignored_fields=[RetrieveFeedbackRequestObject.SUBMITTER],
                ),
            }
        )
        created_feedback = self.feedback_repo.create_feedback(feedback)

        return created_feedback

    def pull_texts_from_feedback_config(
        self, rag: FeedbackRag, feedbacks: list[FeedBacksGroupedByRag]
    ) -> list[FeedbackText]:
        texts = []
        for feedback in feedbacks:
            if rag in feedback.rag:
                texts.extend(feedback.texts)

        if not texts:
            raise FeedbackTextsNotConfigured(
                f"Feedback rags are not configured in {self.request_object.moduleId} module config"
            )

        return texts

    def pull_next_feedback_by_rag(
        self,
        latest_feedback: FeedbackDTO,
        rag: FeedbackRag,
        feedbacks: list[FeedBacksGroupedByRag],
    ) -> FeedbackText:
        texts = self.pull_texts_from_feedback_config(rag, feedbacks)
        index = next(
            (i for i, text in enumerate(texts) if text.id == latest_feedback.feedbackTextId),
            -1,
        )
        next_index = (index + 1) % len(texts)
        user_feedback_text = texts[next_index]
        return user_feedback_text

    @staticmethod
    def get_result_value(module_result: PrimitiveDTO) -> list[int]:
        value = module_result.get_primitive_value
        if not value:
            raise PrimitiveValueNotAssigned(f"Primitive value is not assigned for {module_result.moduleId}")
        return [value]


class RetrieveFeedbackFromCMSUseCase(UseCase):
    request_object: RetrieveFeedbackRequestObject
    DEFAULT_SEVERITY = 1

    @autoparams()
    def __init__(self, feedback_repo: FeedbackRepository):
        self.feedback_repo = feedback_repo

    def process_request(self, request_object: RetrieveFeedbackRequestObject):
        deployment = self.request_object.deployment
        module_config = deployment.find_module_config(self.request_object.moduleId)
        self.validate_module_config(module_config)

        current_result, comparing_result = self.get_module_results(deployment, module_config)
        severity, include_rags = self.get_severity_from_module_result(current_result)
        rag = FeedbackDTO.get_rag_colour_by_severity(severity)
        direction = self.compare_results(current_result, comparing_result)
        feedbacks = self.get_feedbacks(module_config.feedback, severity, direction)

        latest_feedback = self.feedback_repo.get_latest_user_feedback(
            self.request_object.userId,
            module_config.id,
            rag if module_config.feedback.severityFilter else None,
        )
        user_feedback = self.create_user_feedback(latest_feedback, module_config, rag, current_result, feedbacks)
        resp_obj = RetrieveFeedbackResponseObject
        response_data = {
            resp_obj.CURRENT_VALUE: self.get_result_value(current_result),
            resp_obj.COMPARING_VALUE: (self.get_result_value(comparing_result) if comparing_result else None),
            resp_obj.UPPER_BOUND: module_config.feedback.upperBound,
            resp_obj.LOWER_BOUND: module_config.feedback.lowerBound,
            resp_obj.CURRENT_VALUE_SEVERITY: severity,
            resp_obj.FEEDBACK: self.build_feedback_response(user_feedback),
            resp_obj.RAG_THRESHOLDS: (module_config.ragThresholds if include_rags else None),
        }

        return RetrieveFeedbackResponseObject(**response_data)

    def validate_module_config(self, module_config: t.Optional[ModuleConfig]):
        if not module_config:
            raise InvalidRequestException(f"Module config for module {self.request_object.moduleId} not found")

        if not module_config.feedback or not module_config.feedback.enabled:
            raise FeedbackNotConfigured(f"Feedback is not configured for module {self.request_object.moduleId}")

    def get_module_results(
        self, deployment: DeploymentDTO, module_config: ModuleConfig
    ) -> tuple[PrimitiveDTO, t.Optional[PrimitiveDTO]]:
        module_id = self.request_object.moduleId
        module_service = ModuleResultService()

        manager = inject.instance(ModulesManager)
        module = manager.find_module(module_id)
        primitive_name = module.primitives[0].get_primitive_name()

        options = {
            "user_id": self.request_object.userId,
            "module_id": module_id,
            "module_config_id": module_config.id,
            "deployment_id": deployment.id,
            "direction": SortField.Direction.DESC,
            "role": self.request_object.submitter.get_role().id,
            "limit": 1,
            "skip": 0,
            "from_date_time": None,
            "to_date_time": None,
            "filters": None,
        }
        current = module_service.retrieve_module_results(
            module_result_id=self.request_object.moduleResultId, **options
        ).get(primitive_name)

        if not current:
            raise ModuleResultDoesNotExist(f"User has not yet submitted the module result for module {module_id}")

        options["to_date_time"] = current[0].startDateTime
        comparing = module_service.retrieve_module_results(**options).get(primitive_name)

        return current[0], comparing[0] if comparing else None

    def get_severity_from_module_result(self, module_result: PrimitiveDTO) -> tuple[int, bool]:
        severity = module_result.get_severity()
        return (severity, True) if severity is not None else (self.DEFAULT_SEVERITY, False)

    def compare_results(self, current: PrimitiveDTO, comparing: PrimitiveDTO | None) -> str:
        if comparing is None:
            return VariabilityFilter.UNAVAILABLE

        current_value = self.get_result_value(current)
        comparing_value = self.get_result_value(comparing)

        if current_value > comparing_value:
            return VariabilityFilter.INCREASED
        elif current_value < comparing_value:
            return VariabilityFilter.DECREASED

        return VariabilityFilter.NO_CHANGE

    def get_feedbacks(self, feedback_config: FeedbackConfig, severity: int, direction: str) -> list[CMSContentDTO]:
        try:
            must_be_at_least_one_of(
                query=feedback_config.query,
                tags=feedback_config.tags,
                cmsContentIds=feedback_config.cmsContentIds,
                severityFilter=feedback_config.severityFilter,
                variabilityFilter=feedback_config.variabilityFilter,
            )
        except ConvertibleClassValidationError:
            logger.warning("Feedback filters not specified")
            return []

        tags = feedback_config.get_tags(severity, direction)
        req_obj = AdvancedSearchContentRequestObject(
            collection=RECOMMENDATIONS_COLLECTION_NAME,
            submitter=self.request_object.submitter,
            query=feedback_config.query,
            tags=tags,
            cmsContentIds=feedback_config.cmsContentIds,
        )
        recommendations: list[CMSContentDTO] = AdvancedSearchContentUseCase().execute(req_obj).items
        if not recommendations:
            logger.warning("Recommendations with specified filters are not found")
        return sorted(recommendations, key=lambda c: (c.data or c.draftData or {}).get("title"))

    def create_user_feedback(
        self,
        latest_feedback: FeedbackDTO | None,
        module_config: ModuleConfig,
        rag: FeedbackRag,
        latest_module_result: PrimitiveDTO,
        feedbacks: list[CMSContentDTO],
    ) -> FeedbackDTO | None:
        if latest_feedback and latest_feedback.moduleResultId == latest_module_result.moduleResultId:
            return latest_feedback

        if not feedbacks:
            return None

        if not latest_feedback:
            user_feedback = feedbacks[0]
        else:
            user_feedback = self.pull_next_feedback(latest_feedback, feedbacks)

        feedback = FeedbackDTO.from_dict(
            {
                FeedbackDTO.MODULE_CONFIG_ID: module_config.id,
                FeedbackDTO.MODULE_RESULT_ID: latest_module_result.moduleResultId,
                FeedbackDTO.FEEDBACK_TEXT_ID: user_feedback.id,
                FeedbackDTO.TITLE: user_feedback.data.get("title"),
                FeedbackDTO.TEXT: user_feedback.data.get("content"),
                FeedbackDTO.RAG: rag,
                **self.request_object.to_dict(
                    include_none=False,
                    ignored_fields=[RetrieveFeedbackRequestObject.SUBMITTER],
                ),
            }
        )
        created_feedback = self.feedback_repo.create_feedback(feedback)

        return created_feedback

    def pull_next_feedback(
        self,
        latest_feedback: FeedbackDTO,
        feedbacks: list[CMSContentDTO],
    ) -> CMSContentDTO:
        index = next(
            (i for i, text in enumerate(feedbacks) if text.id == latest_feedback.feedbackTextId),
            -1,
        )
        next_index = (index + 1) % len(feedbacks)
        user_feedback = feedbacks[next_index]
        return user_feedback

    def get_result_value(self, module_result: PrimitiveDTO) -> list[int | float]:
        if issubclass(type(module_result), PrimitivesWithPreferableUnitsDTO):
            unit = self._preferred_unit(module_result)
            value = module_result.get_value_by_unit(unit)
        else:
            value = module_result.get_primitive_value

        if not value:
            raise PrimitiveValueNotAssigned(f"Primitive value is not assigned for {module_result.moduleId}")
        return [value] if isinstance(value, (int, float)) else value

    @staticmethod
    def build_feedback_response(user_feedback: FeedbackDTO | None) -> dict | None:
        if not user_feedback:
            return None

        return {
            FeedbackTextResponse.FEEDBACK_TEXT_ID: user_feedback.feedbackTextId,
            FeedbackTextResponse.TITLE: user_feedback.title,
            FeedbackTextResponse.TEXT: user_feedback.text,
        }

    def _preferred_unit(self, primitive: PrimitiveDTO) -> MeasureUnit:
        """Get the preferred unit for the current primitive_cls."""
        user = self.request_object.submitter.user
        unit = user.get_preferred_units_for_module(primitive.get_primitive_name())
        return MeasureUnit(unit) if unit else None
