from datetime import datetime
from unittest import TestCase
from unittest.mock import <PERSON>Mock, patch

from freezegun import freeze_time

from huma_plugins.components.third_party_app.dtos.third_party_app import (
    ThirdPartyAppType,
)
from huma_plugins.components.third_party_app.repository import ThirdPartyAppRepository
from huma_plugins.components.third_party_app.router.request_objects import (
    DisconnectAppRequestObject,
    FitbitSchedulerRequestObject,
)
from huma_plugins.components.third_party_app.use_cases.fitbit.fitbit_scheduler_use_case import (
    FitbitSchedulerUseCase,
)
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.common.common_models.sort import SortField
from sdk.common.utils import inject
from sdk.module_result.dtos.primitives import PrimitiveDTO
from ._utils import (
    EXPECTED_HEART_RATE_FROM_DATETIME,
    MOCK_PRE_PATH,
    MockAuthRepo,
    <PERSON>ck<PERSON><PERSON><PERSON><PERSON><PERSON>,
    MockModuleResultService,
    MockThirdPartyAppRepo,
    deployment_with_fitbit,
)
from .samples import SAMPLE_DEPLOYMENT_ID, SAMPLE_ID

MOCK_PATH = f"{MOCK_PRE_PATH}.fitbit_scheduler_use_case"


@patch(f"{MOCK_PATH}.ModuleResultService", MockModuleResultService)
class FitbitSchedulerUseCaseTestCase(TestCase):
    def setUp(self) -> None:
        self._repo = MockThirdPartyAppRepo()
        self._repo.create_third_party_app_log.return_value = SAMPLE_ID
        self._auth_repo = MockAuthRepo()

        def configure_binder(binder: inject.Binder):
            binder.bind(ThirdPartyAppRepository, self._repo)
            binder.bind(AuthorizationRepository, self._auth_repo)

        inject.clear_and_configure(configure_binder)

    def tearDown(self) -> None:
        MockModuleResultService.retrieve_module_results.reset_mock()

    @staticmethod
    def _request_object(module: str = "Sleep") -> FitbitSchedulerRequestObject:
        return FitbitSchedulerRequestObject.from_dict(
            {
                FitbitSchedulerRequestObject.USER_ID: SAMPLE_ID,
                FitbitSchedulerRequestObject.MODULES: [module],
            }
        )

    @freeze_time("2023-01-20T09:48:15.000Z")
    @patch(f"{MOCK_PATH}.AuthorizedUser", MockAuthzUser())
    @patch(f"{MOCK_PATH}.pull_user_data_from_fitbit")
    def test_success_fitbit_scheduler(self, mock_sync_user_data_func):
        FitbitSchedulerUseCase().execute(self._request_object())
        expected_sync_time = "2023-01-20T09:48:15.000000Z"
        expected_schedule_dt = datetime(2023, 1, 20, 10, 0, 0)

        mock_sync_user_data_func.apply_async.assert_called_with(
            args=[
                SAMPLE_ID,
                expected_sync_time,
                {"Sleep": "2022-12-21T09:48:16.000000Z"},
                ["sleep"],
                ["Sleep"],
                SAMPLE_ID,
            ],
            eta=expected_schedule_dt,
        )

    @freeze_time("2023-01-20T09:48:15.000Z")
    @patch(f"{MOCK_PATH}.AuthorizedUser", MockAuthzUser())
    @patch(f"{MOCK_PATH}.pull_user_data_from_fitbit")
    @patch(f"{MOCK_PATH}.RetrieveThirdPartyAppsUseCase.execute", return_value=[])
    def test_success_fitbit_scheduler_none_app(self, mock_execute, mock_sync_user_data_func):
        FitbitSchedulerUseCase().execute(self._request_object())

        mock_sync_user_data_func.apply_async.assert_not_called()

    @freeze_time("2023-01-20T10:01:15.000Z")
    @patch(f"{MOCK_PATH}.AuthorizedUser", MockAuthzUser())
    @patch(f"{MOCK_PATH}.pull_user_data_from_fitbit")
    def test_success_fitbit_scheduler_heart_rate_module_latest_date_hrv(self, mock_sync_user_data_func):
        FitbitSchedulerUseCase().execute(self._request_object("HeartRate"))
        expected_sync_time = "2023-01-20T10:01:15.000000Z"
        expected_schedule_dt = datetime(2023, 1, 20, 10, 15, 0)

        MockModuleResultService.retrieve_module_results.assert_called_once_with(
            SAMPLE_ID,
            "HeartRate",
            skip=0,
            limit=1,
            direction=SortField.Direction.DESC,
            from_date_time=datetime(2022, 12, 21, 10, 1, 15),
            to_date_time=datetime(2023, 1, 20, 10, 1, 15),
            filters={PrimitiveDTO.SOURCE: "Fitbit"},
            deployment_id=SAMPLE_DEPLOYMENT_ID,
            role=MockAuthzUser.get_role().id,
            module_config_id=SAMPLE_ID,
        )
        mock_sync_user_data_func.apply_async.assert_called_with(
            args=[
                SAMPLE_ID,
                expected_sync_time,
                {"HeartRate": EXPECTED_HEART_RATE_FROM_DATETIME},
                ["heart_rate"],
                ["HeartRate"],
                SAMPLE_ID,
            ],
            eta=expected_schedule_dt,
        )

    @patch(f"{MOCK_PATH}.is_fitbit_enabled")
    @patch(f"{MOCK_PATH}.pull_user_data_from_fitbit")
    @patch.object(FitbitSchedulerUseCase, "deployment", MagicMock())
    def test_fitbit_scheduler_doesnt_pull_fitbit_disabled(self, pull_user_data, is_fitbit_enabled):
        is_fitbit_enabled.return_value = False

        FitbitSchedulerUseCase().execute(self._request_object())
        pull_user_data.assert_not_called()

    @patch(f"{MOCK_PATH}.pull_user_data_from_fitbit")
    @patch.object(FitbitSchedulerUseCase, "deployment")
    def test_fitbit_scheduler_doesnt_pull_modules_not_configured(self, deployment, pull_user_data):
        deployment.return_value = deployment_with_fitbit()
        deployment.find_module_config.return_value = None
        FitbitSchedulerUseCase().execute(self._request_object())
        pull_user_data.assert_not_called()

    @patch(f"{MOCK_PATH}.pull_user_data_from_fitbit")
    @patch(f"{MOCK_PATH}.DisconnectThirdPartyAppUseCase")
    @patch.object(FitbitSchedulerUseCase, "fitbit_modules", MagicMock())
    @patch.object(FitbitSchedulerUseCase, "deployment")
    @patch.object(FitbitSchedulerUseCase, "user")
    def test_fitbit_scheduler_user_off_boarded(self, user, deployment, disconnect_use_case, pull_user_data):
        user.id = SAMPLE_ID
        deployment.return_value = deployment_with_fitbit()
        user.is_offboarded.return_value = True
        FitbitSchedulerUseCase().execute(self._request_object())
        expected_req_obj = DisconnectAppRequestObject(userId=SAMPLE_ID, type=ThirdPartyAppType.FITBIT)
        disconnect_use_case().execute.assert_called_once_with(expected_req_obj)
        pull_user_data.assert_not_called()

    def test_fit(self):
        test_date = (
            (datetime(2023, 1, 20, 11, 50, 52), datetime(2023, 1, 20, 12, 0, 0)),
            (datetime(2023, 1, 20, 12, 2, 20), datetime(2023, 1, 20, 12, 15, 0)),
            (datetime(2023, 1, 20, 12, 20, 32), datetime(2023, 1, 20, 12, 30, 0)),
            (datetime(2023, 1, 20, 12, 40, 9), datetime(2023, 1, 20, 12, 45, 0)),
            (datetime(2023, 1, 20, 12, 55, 35), datetime(2023, 1, 20, 13, 0, 0)),
        )

        for date, expected in test_date:
            use_case = FitbitSchedulerUseCase(MagicMock(), MagicMock())
            use_case.request_object = FitbitSchedulerRequestObject(dateTime=date)
            self.assertEqual(expected, use_case.schedule_time, f"Submitted date {date}")
