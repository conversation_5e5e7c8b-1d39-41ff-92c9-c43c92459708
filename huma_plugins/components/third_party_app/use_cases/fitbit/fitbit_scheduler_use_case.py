from datetime import datetime, timedelta
from functools import cached_property

from huma_plugins.components.third_party_app.adapters.fitbit import (
    FitbitModuleType,
    MODULE_TO_FITBIT_MODULE,
)
from huma_plugins.components.third_party_app.dtos.third_party_app import (
    ThirdPartyAppDTO,
    ThirdPartyAppLogDTO,
    ThirdPartyAppStatus,
    ThirdPartyAppType,
)
from huma_plugins.components.third_party_app.repository.third_party_app_repository import (
    ThirdPartyAppRepository,
)
from huma_plugins.components.third_party_app.router.request_objects import (
    Account,
    FitbitSchedulerRequestObject,
)
from huma_plugins.components.third_party_app.router.request_objects.base_third_party_app_request_object import (
    DisconnectAppRequestObject,
    RetrieveThirdPartyAppsRequestObject,
)
from huma_plugins.components.third_party_app.tasks import pull_user_data_from_fitbit
from huma_plugins.components.third_party_app.use_cases.disconnect_third_party_app_use_case import (
    DisconnectThirdPartyAppUseCase,
)
from huma_plugins.components.third_party_app.use_cases.fitbit.utils import (
    is_fitbit_enabled,
)
from huma_plugins.components.third_party_app.use_cases.retrieve_third_party_apps_use_case import (
    RetrieveThirdPartyAppsUseCase,
)
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.dtos.user import UserDTO
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.calendar.utils import no_seconds
from sdk.common.common_models.sort import SortField
from sdk.common.exceptions.exceptions import ObjectDoesNotExist
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams
from sdk.common.utils.validators import remove_none_values, utc_str_field_to_val
from sdk.deployment.dtos.deployment import DeploymentDTO
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.modules.heart_rate.primitives import FitbitHRVDTO, HeartRateDTO
from sdk.module_result.service.module_result_service import ModuleResultService

FROM_DAYS = 30


class FitbitSchedulerUseCase(UseCase):
    request_object: FitbitSchedulerRequestObject

    @autoparams()
    def __init__(
        self,
        repo: ThirdPartyAppRepository,
        auth_repo: AuthorizationRepository,
    ):
        self._repo = repo
        self._auth_repo = auth_repo
        self._module_service = ModuleResultService()

    def process_request(self, request_object: FitbitSchedulerRequestObject):
        if not self.fitbit_app:
            return
        if not is_fitbit_enabled(self.deployment):
            return
        if not self.fitbit_modules:
            return
        if self.user.is_off_boarded():
            return self._disconnect(self.user.id)
        pull_user_data_from_fitbit.apply_async(
            args=[
                self.account.user.id,
                utc_str_field_to_val(request_object.dateTime),
                self.account.fromDates,
                self.modules_str,
                request_object.modules,
                self.account.logId or None,
            ],
            eta=self.schedule_time,
        )

    @cached_property
    def deployment(self) -> DeploymentDTO:
        return self.authz_user.deployment

    @cached_property
    def authz_user(self) -> AuthorizedUser:
        return AuthorizedUser(self.user)

    @cached_property
    def user(self) -> UserDTO:
        return self._auth_repo.retrieve_simple_user_profile(user_id=self.fitbit_app.userId)

    @cached_property
    def fitbit_app(self) -> ThirdPartyAppDTO | None:
        req_dict = remove_none_values(
            {
                RetrieveThirdPartyAppsRequestObject.STATUS: ThirdPartyAppStatus.CONNECTED,
                RetrieveThirdPartyAppsRequestObject.TYPE: ThirdPartyAppType.FITBIT,
                RetrieveThirdPartyAppsRequestObject.USER_ID: self.request_object.userId,
            }
        )
        req_obj = RetrieveThirdPartyAppsRequestObject.from_dict(req_dict)
        res = RetrieveThirdPartyAppsUseCase().execute(req_obj)
        return res[0] if res else None

    @cached_property
    def fitbit_modules(self) -> list[FitbitModuleType]:
        modules = []
        for module_id in self.request_object.modules:
            config = self.deployment.find_module_config(module_id)
            if not config:
                continue
            modules.append(MODULE_TO_FITBIT_MODULE[module_id])
        return modules

    @cached_property
    def account(self) -> Account:
        return Account(
            app=self.fitbit_app,
            user=self.user,
            log_id=self.app_log.id if self.app_log else None,
            modules=self.fitbit_modules,
            from_dates=self.from_dates,
        )

    @cached_property
    def app_log(self) -> ThirdPartyAppLogDTO | None:
        query = {
            ThirdPartyAppDTO.TYPE: ThirdPartyAppType.FITBIT.value,
            ThirdPartyAppDTO.USER_ID: self.fitbit_app.userId,
        }
        try:
            log = self._repo.retrieve_third_party_app_log(**query)
        except ObjectDoesNotExist:
            log = None
        return log

    @cached_property
    def from_dates(self) -> dict[str, str]:
        from_dates = {}
        role_id = self.authz_user.get_role().id

        for module_id in self.request_object.modules:
            config = self.deployment.find_module_config(module_id)
            if not config:
                continue
            last_date = self._retrieve_last_module_result_date(module_id, role_id, config.id)
            if last_date < self.fitbit_app.createDateTime:
                last_date = self.fitbit_app.createDateTime
            from_dates[module_id] = utc_str_field_to_val(last_date)
        return from_dates

    @cached_property
    def schedule_time(self):
        """schedule_time == next 15min mark from now, a.k. 0, 15, 30, 45"""
        schedule_time = no_seconds(self.request_object.dateTime)
        minutes_to_add = (schedule_time.minute // 15 + 1) * 15
        minute = minutes_to_add % 60
        hour = minutes_to_add // 60
        schedule_time += timedelta(hours=hour)
        return schedule_time.replace(minute=minute)

    @cached_property
    def modules_str(self):
        return [module.value for module in self.account.modules]

    @staticmethod
    def _disconnect(user_id):
        app = ThirdPartyAppType.FITBIT
        req_obj = DisconnectAppRequestObject.from_dict(
            {
                DisconnectAppRequestObject.USER_ID: user_id,
                DisconnectAppRequestObject.TYPE: app,
            }
        )
        return DisconnectThirdPartyAppUseCase().execute(req_obj)

    def _retrieve_last_module_result_date(self, module_id: str, role_id: str, module_config_id: str) -> datetime:
        from_date_time = self.request_object.dateTime - timedelta(days=FROM_DAYS)
        result = self._module_service.retrieve_module_results(
            self.user.id,
            module_id,
            skip=0,
            limit=1,
            direction=SortField.Direction.DESC,
            from_date_time=from_date_time,
            to_date_time=self.request_object.dateTime,
            filters={PrimitiveDTO.SOURCE: "Fitbit"},
            deployment_id=self.deployment.id,
            role=role_id,
            module_config_id=module_config_id,
        )
        if primitives := result[module_id]:
            primitive = primitives[0]
            from_date_time = primitive.startDateTime
            if primitive.endDateTime:
                from_date_time = primitive.endDateTime
            if module_id == HeartRateDTO.get_primitive_name():
                hrv_primitives = result.get(FitbitHRVDTO.get_primitive_name())
                if hrv_primitives:
                    hrv_last_date = hrv_primitives[0].startDateTime
                    if hrv_last_date > from_date_time:
                        from_date_time = hrv_last_date

        return from_date_time + timedelta(seconds=1)
