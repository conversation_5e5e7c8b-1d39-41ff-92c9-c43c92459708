from huma_plugins.components.diary.callbacks.diary_callback import diary_module_result_callback
from sdk.module_result.modules.sleep import SleepDTO
from ._base import BaseDiaryTestCase, mock_utcnow
from .samples import (
    sleep_diaries_response_all_values,
    sleep_diaries_response_only_duration,
    sleep_primitive_dict_all_values,
    sleep_primitive_dict_only_duration,
)


class SleepTestCase(BaseDiaryTestCase):
    @mock_utcnow
    def test_create_and_search_sleep_diary_all_values(self):
        sleep_primitive = self._create_primitive(sleep_primitive_dict_all_values(), SleepDTO.get_primitive_name())
        event = self._post_create_module_result_batch_event([sleep_primitive])
        diary_module_result_callback(event, False)

        diary_item_rsp = self._retrieve_single_created_diary(expected_diaries_count=5)
        diary_item_rsp.sort(key=lambda x: (x["startDateTime"], x["title"]))

        expected_rsp = sleep_diaries_response_all_values()
        expected_rsp.sort(key=lambda x: (x["startDateTime"], x["title"]))
        self.assertEqual(expected_rsp, diary_item_rsp)

    @mock_utcnow
    def test_create_and_search_sleep_diary_only_duration(self):
        sleep_primitive = self._create_primitive(sleep_primitive_dict_only_duration(), SleepDTO.get_primitive_name())
        event = self._post_create_module_result_batch_event([sleep_primitive])
        diary_module_result_callback(event, False)

        diary_item_rsp = self._retrieve_single_created_diary()
        expected_rsp = sleep_diaries_response_only_duration()
        self.assertEqual(expected_rsp, diary_item_rsp)
