from huma_plugins.components.diary.callbacks.diary_callback import diary_module_result_callback
from sdk.module_result.modules.koos import KOOSDTO
from sdk.module_result.modules.questionnaire import QuestionnaireDTO
from ._base import BaseDiaryTestCase, mock_utcnow
from .samples import koos_diaries_response, koos_primitive_dict, koos_questionnaire_dict


class KOOSTestCase(BaseDiaryTestCase):
    @mock_utcnow
    def test_create_and_search_koos_diary(self):
        self.maxDiff = None
        koos_primitive = self._create_primitive(koos_primitive_dict(), KOOSDTO.get_primitive_name())
        questionnaire_primitive = self._create_primitive(
            koos_questionnaire_dict(), QuestionnaireDTO.get_primitive_name()
        )
        event = self._post_create_module_result_batch_event(
            [questionnaire_primitive, koos_primitive, questionnaire_primitive]
        )
        diary_module_result_callback(event, False)

        diary_item_rsp = self._retrieve_single_created_diary(expected_diaries_count=5)
        diary_item_rsp.sort(key=lambda x: (x["startDateTime"], x["title"]))

        expected_rsp = koos_diaries_response()
        expected_rsp.sort(key=lambda x: (x["startDateTime"], x["title"]))
        self.assertEqual(expected_rsp, diary_item_rsp)
