from datetime import datetime, timedelta, timezone
from pathlib import Path
from unittest import main
from unittest.mock import ANY, MagicMock, patch
from uuid import uuid4

import i18n
from flask import url_for
from freezegun import freeze_time

from huma_plugins.components.medication.component import MedicationComponent
from huma_plugins.components.medication.dtos.medication import MedicationChangeType
from huma_plugins.components.medication.dtos.medication_history import (
    MedicationHistoryV2DTO,
)
from huma_plugins.components.medication.dtos.medication_log import MedicationLogDTO
from huma_plugins.components.medication.dtos.medication_v2 import (
    AdditionalFields,
    Frequency,
    Indication,
    InhalerInfo,
    MedicationMealSchedule,
    MedicationNotificationAction,
    MedicationReasonUpdated,
    MedicationV2DTO,
    MedicationV2Schedule,
    SubmitterUserType,
)
from huma_plugins.components.medication.models import (
    MedicationHistoryV2,
    MedicationLog,
    MedicationV2,
)
from huma_plugins.components.medication.module import MedicationsV2Module
from huma_plugins.components.medication.router.medication_request_v2 import RetrieveMedicationsRequestObjectV2
from huma_plugins.components.medication.router.medication_response_v2 import (
    MedicationGroup,
    MedicationResponseGroupedV2,
)
from huma_plugins.components.medication.service.medication_v2_service import (
    MedicationV2Service,
)
from huma_plugins.components.medication.use_case.base_medication_use_case import (
    medication_notification_body_keys,
)
from huma_plugins.tests.plugin_test_case import PluginsTestCase
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.authorization.dtos.user import BoardingStatus, UnseenFlags, UserDTO
from sdk.authorization.exceptions import UserErrorCodes
from sdk.authorization.models import User
from sdk.authorization.router.user_profile_request import MoveUserDetails
from sdk.calendar.component import CalendarComponent
from sdk.calendar.dtos.calendar_event import CalendarEventDTO
from sdk.calendar.models.calendar_event import CalendarEvent
from sdk.calendar.service.calendar_service import CalendarService
from sdk.common.adapter.email_adapter import PercentageTemplate
from sdk.common.exceptions.exceptions import ErrorCodes
from sdk.common.utils.common_functions_utils import deep_get, find
from sdk.common.utils.validators import remove_none_values, utc_str_field_to_val
from sdk.deployment.component import DeploymentComponent
from sdk.deployment.dtos.deployment import MoveUserReasonDetails
from sdk.key_action.component import KeyActionComponent
from sdk.key_action.models.key_action_log import KeyAction
from sdk.module_result.component import ModuleResultComponent
from sdk.module_result.dtos.primitives import MealType
from sdk.module_result.dtos.unseen_result import UnseenResultDTO
from sdk.module_result.modules import BloodGlucoseModule
from sdk.notification.component import NotificationComponent
from sdk.organization.component import OrganizationComponent
from sdk.tests.extension_test_case import ExtensionTestCase

VALID_DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
VALID_DEPLOYMENT_2_ID = "5d386cc6ff885918d96edb2d"
VALID_USER_ID = "5e84b0dab8dfa268b1180536"
VALID_USER_3_ID = "5e84b0dab8dfa268b1280539"
VALID_PROXY_USER_ID = "5e8f0c74b50aa9656c342220"
VALID_USER_2_ID = "5e8f0c74b50aa9656c34789c"
VALID_MANAGER_ID = "5e8f0c74b50aa9656c34789d"
INVALID_USER_ID = "6e84b0dab8dfa268b1180532"
VALID_SUPER_ADMIN_ID = "5e8f0c74b50aa9656c34789b"

MEDICATION_CONFIG_ID = "5f1824ba504787d8d89ebeca"

AS_NEEDED_MEDICATION_ID = "62fca83e19af8839979d65bc"
VALID_MEDICATION_ID_1 = "5e8f0c74b50aa9656c347777"
VALID_MEDICATION_ID_2 = "62fca83e19af8839979d65bc"
VALID_MEDICATION_ID_3 = "62fca83e19af8839979d65ad"
INVALID_MEDICATION_ID = "2e8cc88d0e8f49bbe59d11ba"
MEAL_EVENT_ID = "630ccbe044e6f4aef43faadc"
MEAL_MEDICATION_ID = "5e8f0c74b50aa9656c347778"

VALID_MEDICATION_NAME = "Paracetamol"
VALID_MEDICATION_ID_1_NAME = "Ampicillin"
VALID_MEDICATION_ID_2_NAME = "Aspirin"
VALID_MEDICATION_ID_3_NAME = "Ibuprofen"
SEND_NOTIFICATION_PATH = (
    "huma_plugins.components.medication.use_case.base_medication_use_case.prepare_and_send_push_notification"
)
MEDICATION_GROUP_IDS = [
    "e62bb95c-46e7-4b66-b907-a539b06d0e81",
    "e62bb95c-46e7-4b66-b907-a539b06d0e82",
    "e62bb95c-46e7-4b66-b907-a539b06d0e83",
    "e62bb95c-46e7-4b66-b907-a539b06d0e84",
]


def medication_history(
    id_: str,
    timestamp: str | None = None,
    change_type: str = MedicationChangeType.MEDICATION_CREATE.value,
    name: str = VALID_MEDICATION_NAME,
    user_type: str = SubmitterUserType.MANAGER.value,
) -> dict:
    return remove_none_values(
        {
            "userId": VALID_USER_ID,
            "deploymentId": VALID_DEPLOYMENT_ID,
            "medicationId": id_,
            "medicationName": name,
            "changeType": change_type,
            "userType": user_type,
            "seen": False,
            "updateDateTime": timestamp,
            "createDateTime": timestamp,
        }
    )


def sample_medication():
    return {
        "name": VALID_MEDICATION_NAME,
        "dosage": 200.0,
        "unit": "mg",
        "enabled": True,
        "isNotificationEnabled": False,
        "schedule": {
            "asNeeded": False,
            "isoDuration": "P1W",
            "timesOfReadings": ["PT18H0M", "PT10H30M"],
            "endTimes": ["PT22H0M", "PT14H30M"],
            "specificWeekDays": ["MON", "FRI"],
            "timeRanges": [
                {"endTime": "PT22H0M", "startTime": "PT18H0M"},
                {"endTime": "PT14H30M", "startTime": "PT10H30M"},
            ],
        },
        "coding": [
            {
                "system": "http://snomed.info/sct",  # optional
                "code": "1234567823",  # optional
                "display": "Paracetamol",
            }
        ],
    }


def sample_meal_based_medication():
    return {
        "name": VALID_MEDICATION_NAME,
        "dosage": 200.0,
        "unit": "mg",
        "enabled": True,
        "isNotificationEnabled": False,
        "connectedModules": ["BloodGlucose"],
        "mealSchedule": [
            {
                "asNeeded": False,
                "meal": MealType.AFTER_LUNCH.value,
                "isoDuration": "P1D",
                "timeOfReading": "PT14H0M",
                "dosage": 200.0,
                "unit": "mg",
            },
            {
                "asNeeded": False,
                "meal": MealType.AFTER_LUNCH_2_HOURS.value,
                "isoDuration": "P1D",
                "timeOfReading": "PT15H0M",
                "dosage": 200.0,
                "unit": "mg",
            },
        ],
    }


def sample_medication_log(as_needed=False):
    if as_needed:
        return {
            "eventId": "630ccbe044e6f4aef43fccdb",
            "status": "TAKEN",
            "startDateTime": "2022-07-26T08:00:00.000Z",
            "logsCount": 3,
        }
    return {
        "eventId": "630ccbe044e6f4aef43fccdb",
        "status": "TAKEN",
        "startDateTime": "2022-07-26T08:00:00.000Z",
        "device": {
            "uuid": "STRING",
            "serialNumber": "STRING",
            "modelName": "STRING",
            "manufacturer": "STRING",
        },
        "source": "HumaDeviceKit; version: X.X",
    }


class BaseMedicationTestCaseV2(PluginsTestCase):
    components = [
        AuthComponent(),
        AuthorizationComponent(),
        CalendarComponent(),
        DeploymentComponent(),
        MedicationComponent(),
        ModuleResultComponent(additional_modules=[MedicationsV2Module(), BloodGlucoseModule()]),
        NotificationComponent(),
        OrganizationComponent(),
        KeyActionComponent(),
    ]
    fixtures = [
        Path(__file__).parent.joinpath("fixtures/deployments_dump.json"),
        Path(__file__).parent.joinpath("fixtures/medication_dump_v2.json"),
    ]

    def setUp(self):
        super().setUp()

        self.user_headers = self.get_headers_for_token(VALID_USER_ID)
        self.headers = self.get_headers_for_token(VALID_MANAGER_ID)
        self.proxy_headers = self.get_headers_for_token(VALID_PROXY_USER_ID)
        self.medication_route = f"/api/extensions/v1/user/{VALID_USER_ID}/medication"
        self.module_config_id = MEDICATION_CONFIG_ID
        self.soon = datetime.utcnow() + timedelta(days=2)
        self.sooner = datetime.utcnow() + timedelta(days=1)

    def _assert_notification(self, send_notification, action, medication_name):
        send_notification.assert_called_once()
        send_push_notification_args = send_notification.call_args.kwargs
        self.assertEqual(VALID_USER_ID, send_push_notification_args.get("user_id"))
        expected_notification_template = {
            "title": i18n.t("Medication.Notification.title", locale="en"),
            "body": PercentageTemplate(i18n.t(medication_notification_body_keys[action], locale="en")).safe_substitute(
                name=medication_name
            ),
        }
        self.assertEqual(
            expected_notification_template,
            send_push_notification_args.get("notification_template"),
        )
        expected_notification_data = {
            "action": action.value,
            MedicationV2DTO.MODULE_ID: "MedicationsV2",
            MedicationV2DTO.MODULE_CONFIG_ID: self.module_config_id,
        }
        self.assertDictEqual(
            expected_notification_data,
            send_push_notification_args.get("notification_data"),
        )

    def _update_medication(
        self,
        medication_id,
        body: dict,
        as_: str = VALID_MANAGER_ID,
        for_: str = VALID_USER_ID,
        code: str = 200,
    ) -> dict:
        response = self.flask_client.put(
            f"/api/extensions/v1/user/{for_}/medication/{medication_id}",
            json=body,
            headers=self.get_headers_for_token(as_),
        )
        self.assertEqual(code, response.status_code)
        self.assertEqual(medication_id, response.json["id"])
        return response.json

    def _off_board_user(self, user_id: str):
        user = User.objects.filter(mongoId=user_id).first()
        self.assertIsNotNone(user)
        user.boardingStatus = user.boardingStatus or {}
        user.boardingStatus["status"] = BoardingStatus.Status.OFF_BOARDED
        user.save()

    def _create_medication(self, body: dict, user_id: str = VALID_USER_ID, headers=None):
        body[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        response = self.flask_client.post(
            url_for("medication_v2.create_medication", user_id=user_id),
            json=body,
            headers=headers or self.headers,
        )
        self.assertEqual(201, response.status_code)
        return response

    def _retrieve_medications(self, grouped: bool = False) -> dict:
        url = url_for("medication_v2.retrieve_medications", user_id=VALID_USER_ID)
        query_params = {RetrieveMedicationsRequestObjectV2.GROUPED: grouped}
        response = self.flask_client.get(url, headers=self.headers, query_string=query_params)
        self.assertEqual(200, response.status_code)
        return response.json

    @staticmethod
    def _find_db_event_for(user: str, **kwargs) -> CalendarEvent:
        return CalendarEvent.objects.filter(userId=user, **kwargs).first()

    def _retrieve_and_assert_medication_history(
        self,
        medication_id: str,
        expected_history: dict | list[dict],
        user_id: str = VALID_USER_ID,
        deployment_id: str = VALID_DEPLOYMENT_ID,
        expected_count: int = 1,
        compare_dt: bool = True,
        compare_only_last: bool = True,
    ):
        history, count = MedicationV2Service().retrieve_medication_history(
            medication_id=medication_id, user_id=user_id, deployment_id=deployment_id
        )
        self.assertEqual(expected_count, count)
        history_dicts = [h.to_dict(include_none=False) for h in history]
        for h in history_dicts:
            h.pop("id")
            if not compare_dt:
                h.pop("createDateTime")
                h.pop("updateDateTime")
        if compare_only_last:
            return self.assertEqual(expected_history, history_dicts[-1])
        return self.assertEqual(expected_history, history_dicts)


class CreateMedicationTestCase(BaseMedicationTestCaseV2):
    override_config = {"server.deployment.offBoarding": "true"}

    @patch(SEND_NOTIFICATION_PATH)
    def test_success_create_medication(self, send_notification: MagicMock):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id

        frozen_time = "2023-05-07T10:00:00.000000Z"
        with freeze_time(frozen_time):
            events = self._retrieve_key_actions()
            self.assertEqual(1, len(events))
            self.assertTrue(events[0].enabled)

            response = self.flask_client.post(
                self.medication_route,
                json=req_dict,
                headers=self.get_headers_for_token(VALID_MANAGER_ID),
            )
            self.assertEqual(201, response.status_code)
            events = self._retrieve_key_actions()
            self.assertEqual(1, len(events))
            self.assertFalse(events[0].enabled)

        expected_history = medication_history(response.json["id"], frozen_time)
        self._retrieve_and_assert_medication_history(response.json["id"], expected_history)
        self._assert_notification(
            send_notification,
            MedicationNotificationAction.MEDICATION_ADDED,
            VALID_MEDICATION_NAME,
        )
        self.assertIsNotNone(response.json["id"])

    def test_success_create_take_disable_medication_tz(self):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id

        with freeze_time("2023-07-23T12:00:00"):
            header = self.get_headers_for_token(VALID_USER_3_ID)
            req_dict[MedicationV2DTO.SCHEDULE] = {
                "isoDuration": "P1D",
                "timesOfReadings": ["PT10H0M"],
                "asNeeded": False,
            }
            User.objects.filter(mongoId=VALID_USER_3_ID).update(**{UserDTO.TIMEZONE: "America/Denver"})
            response = self.flask_client.post(
                f"/api/extensions/v1/user/{VALID_USER_3_ID}/medication",
                json=req_dict,
                headers=header,
            )
            self.assertEqual(201, response.status_code, response.text)
            med_id = response.json["id"]

            cal_event = self._find_db_event_for(VALID_USER_3_ID)
            self.assertIsNotNone(cal_event)

            response = self.flask_client.put(
                f"/api/extensions/v1/user/{VALID_USER_3_ID}/medication/{med_id}",
                json={MedicationV2DTO.ENABLED: False},
                headers=header,
            )
            self.assertEqual(200, response.status_code, response.text)
            cal_event = self._find_db_event_for(VALID_USER_3_ID)
            self.assertIsNotNone(cal_event)
            self.assertEqual(
                "2023-07-23T00:00:00",
                cal_event.startDateTime.isoformat(),
                "start datetime is set badly",
            )

    @patch(SEND_NOTIFICATION_PATH)
    def test_success_create_medication_with_proxy(self, send_notification: MagicMock):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.proxy_headers)
        self.assertEqual(201, response.status_code)
        send_notification.assert_not_called()
        self.assertIsNotNone(response.json["id"])

        medication = MedicationV2.objects.get(mongoId=response.json["id"])
        self.assertEqual(medication.submitterUserType, SubmitterUserType.USER.value)

    @patch(SEND_NOTIFICATION_PATH, MagicMock())
    def test_failure_create_medication_for_proxy(self):
        req_dict = sample_medication()
        medication_route = f"/api/extensions/v1/user/{VALID_PROXY_USER_ID}/medication"
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        response = self.flask_client.post(medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(403, response.status_code)

    def test_failure_create_medication_long_dosage_number(self):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.DOSAGE] = 123456789012345678901234567890
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(403, response.status_code)

    def test_failure_create_medication_wrong_payload_type(self):
        response = self.flask_client.post(self.medication_route, json=[], headers=self.headers)
        self.assertEqual(403, response.status_code)

    def test_failure_create_medication_no_name(self):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        del req_dict[MedicationV2DTO.NAME]
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(403, response.status_code)

    def test_failure_create_medication_old_start_date(self):
        req_dict = sample_medication()

        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        req_dict[MedicationV2DTO.START_DATE] = "2021-09-13"
        del req_dict[MedicationV2DTO.NAME]
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(403, response.status_code)

    def test_failure_create_medication_off_boarded_user(self):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id

        self._off_board_user(VALID_USER_ID)
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(403, response.status_code)
        self.assertEqual("User is not active", response.json["message"])

    @patch(SEND_NOTIFICATION_PATH)
    def test_success_create_medication_with_schedule_as_needed_max_dosage(self, send_notification: MagicMock):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.SCHEDULE] = {MedicationV2Schedule.AS_NEEDED: True}
        req_dict[MedicationV2DTO.MAX_DOSAGE] = 200.0
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(201, response.status_code)
        send_notification.assert_called_once()

    def test_failure_create_medication_with_schedule_scheduled_max_dosage(self):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.SCHEDULE][MedicationV2Schedule.AS_NEEDED] = False
        req_dict[MedicationV2DTO.MAX_DOSAGE] = 200.0
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(403, response.status_code)
        self.assertEqual(ErrorCodes.DATA_VALIDATION_ERROR, response.json["code"])
        self.assertEqual(
            "Only AsNeeded medications or inhalers should have maxDosage",
            response.json["message"],
        )

    @patch(SEND_NOTIFICATION_PATH)
    def test_success_create_medication_with_schedule_as_needed_no_max_dosage(self, send_notification: MagicMock):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.SCHEDULE] = {MedicationV2Schedule.AS_NEEDED: True}
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(201, response.status_code)
        send_notification.assert_called_once()

    @patch(SEND_NOTIFICATION_PATH)
    def test_success_create_medication_with_schedule_as_needed_null_max_dosage(self, send_notification: MagicMock):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.SCHEDULE] = {MedicationV2Schedule.AS_NEEDED: True}
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        req_dict[MedicationV2DTO.MAX_DOSAGE] = None
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(201, response.status_code)
        send_notification.assert_called_once()

    @patch(SEND_NOTIFICATION_PATH)
    def test_success_create_medication_without_specificWeekDays(self, send_notification: MagicMock):
        req_dict = sample_medication()
        schedule = req_dict.pop(MedicationV2DTO.SCHEDULE)
        schedule.pop(MedicationV2Schedule.SPECIFIC_WEEK_DAYS)
        req_dict[MedicationV2DTO.SCHEDULE] = schedule
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(201, response.status_code)
        send_notification.assert_called_once()

    @patch(SEND_NOTIFICATION_PATH)
    def test_success_user_create_medication(self, send_notification: MagicMock):
        req_dict = sample_medication()
        headers = self.get_headers_for_token(VALID_USER_ID)
        schedule = req_dict.pop(MedicationV2DTO.SCHEDULE)
        schedule.pop(MedicationV2Schedule.SPECIFIC_WEEK_DAYS)
        req_dict[MedicationV2DTO.SCHEDULE] = schedule
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=headers)
        self.assertEqual(201, response.status_code)
        send_notification.assert_not_called()

    def test_success_dct_medication_submission(self):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.INDICATION] = {
            Indication.NAME: "headache",
            Indication.CODING: [],
        }
        req_dict[MedicationV2DTO.START_DATE] = self.sooner.strftime("%Y-%m-%d")
        req_dict[MedicationV2DTO.END_DATE] = self.soon.strftime("%Y-%m-%d")
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(201, response.status_code)
        self.assertIsNotNone(response.json["id"])

    def test_failure_dct_medication_submission_with_wrong_end_date(self):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.INDICATION] = {
            Indication.NAME: "headache",
            Indication.CODING: [],
        }
        req_dict[MedicationV2DTO.START_DATE] = "2022-07-26"
        req_dict[MedicationV2DTO.END_DATE] = "wrong date"
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(403, response.status_code)

    def test_failure_dct_medication_submission_with_empty_end_date(self):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.INDICATION] = {
            Indication.NAME: "headache",
            Indication.CODING: [],
        }
        req_dict[MedicationV2DTO.START_DATE] = "2022-07-26"
        req_dict[MedicationV2DTO.END_DATE] = ""
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(201, response.status_code)

    def test_failure_dct_medication_submission(self):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.INDICATION] = {
            Indication.NAME: "headache",
            Indication.CODING: [],
        }
        req_dict[MedicationV2DTO.START_DATE] = self.soon.strftime("%Y-%m-%d")
        req_dict[MedicationV2DTO.END_DATE] = self.sooner.strftime("%Y-%m-%d")
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(403, response.status_code)

    def test_failure_dct_medication_submission_without_start_date(self):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.INDICATION] = {
            Indication.NAME: "headache",
            Indication.CODING: [],
        }
        req_dict[MedicationV2DTO.END_DATE] = self.soon.strftime("%Y-%m-%d")
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(403, response.status_code)

    def test_success_dct_medication_submission_with_just_start_date(self):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.INDICATION] = {
            Indication.NAME: "headache",
            Indication.CODING: [],
        }
        req_dict[MedicationV2DTO.START_DATE] = self.soon.strftime("%Y-%m-%d")
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(403, response.status_code)

    def test_success_dct_medication_submission_with_equal_start_date_and_end_date(self):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.INDICATION] = {
            Indication.NAME: "headache",
            Indication.CODING: [],
        }
        req_dict[MedicationV2DTO.START_DATE] = self.soon.strftime("%Y-%m-%d")
        req_dict[MedicationV2DTO.END_DATE] = self.soon.strftime("%Y-%m-%d")
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(403, response.status_code)

    def test_success_medication_submission_with_additional_inhaler(self):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        req_dict[MedicationV2DTO.UNIT] = "puffs"
        req_dict[MedicationV2DTO.DOSAGE] = 8
        req_dict[MedicationV2DTO.ADDITIONAL_FIELDS] = {
            AdditionalFields.INHALER: {InhalerInfo.FREQUENCY: Frequency.RELIEVE.name}
        }
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(201, response.status_code)

    def test_medication_submission_with_additional_inhaler_schedule(self):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        req_dict[MedicationV2DTO.UNIT] = "puffs"
        req_dict[MedicationV2DTO.DOSAGE] = 8
        req_dict[MedicationV2DTO.ADDITIONAL_FIELDS] = {
            AdditionalFields.INHALER: {InhalerInfo.FREQUENCY: Frequency.MAINTAIN.name}
        }
        req_dict[MedicationV2DTO.SCHEDULE].pop(MedicationV2Schedule.TIME_RANGES)
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(403, response.status_code)  # Missing timeRanges

        req_dict[MedicationV2DTO.ADDITIONAL_FIELDS] = {
            AdditionalFields.INHALER: {InhalerInfo.FREQUENCY: Frequency.RELIEVE_AND_MAINTAIN.name}
        }
        req_dict[MedicationV2DTO.DOSAGE] = 18
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(403, response.status_code)  # Missing timeRanges

        req_dict[MedicationV2DTO.SCHEDULE] = {
            MedicationV2Schedule.AS_NEEDED: True,
            MedicationV2Schedule.TIME_RANGES: [{"startTime": "PT2H0M", "endTime": "PT1H0M"}],
        }
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(403, response.status_code)  # endTime < startTime

        req_dict[MedicationV2DTO.SCHEDULE] = {
            MedicationV2Schedule.AS_NEEDED: True,
            MedicationV2Schedule.TIME_RANGES: [
                {"startTime": "PT2H0M", "endTime": "PT5H0M"},
                {"startTime": "PT12H0M", "endTime": "PT15H0M"},
            ],
        }
        req_dict[MedicationV2DTO.DOSAGE] = 360
        req_dict[MedicationV2DTO.MAX_DOSAGE] = 400
        frozen_time = "2023-05-07T10:00:00.000000Z"
        with freeze_time(frozen_time):
            response = self.flask_client.post(
                self.medication_route,
                json=req_dict,
                headers=self.get_headers_for_token(VALID_MANAGER_ID),
            )
        self.assertEqual(201, response.status_code)
        expected_history = medication_history(response.json["id"], frozen_time)
        expected_history["regimen"] = {"frequency": {"old": "", "new": Frequency.RELIEVE_AND_MAINTAIN.name}}
        self._retrieve_and_assert_medication_history(response.json["id"], expected_history)

    def test_failure_medication_submission_with_additional_inhaler(self):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        req_dict[MedicationV2DTO.ADDITIONAL_FIELDS] = {
            AdditionalFields.INHALER: {InhalerInfo.FREQUENCY: Frequency.RELIEVE.name}
        }
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.user_headers)
        self.assertEqual(403, response.status_code)
        self.assertEqual("unit [mg] must be one of ['puffs']", response.json["message"])

        req_dict[MedicationV2DTO.UNIT] = "puffs"
        req_dict[MedicationV2DTO.DOSAGE] = 2

        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.user_headers)
        self.assertEqual(403, response.status_code)
        self.assertEqual("dosage value should be in range of [5-10]", response.json["message"])

        req_dict[MedicationV2DTO.DOSAGE] = 20

        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.user_headers)
        self.assertEqual(403, response.status_code)
        self.assertEqual("dosage value should be in range of [5-10]", response.json["message"])

    def test_failure_medication_submission_without_coding(self):
        req_dict = sample_medication()
        req_dict.pop(MedicationV2DTO.CODING)
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(403, response.status_code)

    def test_medication_disabled_after_off_boarding(self):
        user_id_filter = {MedicationV2DTO.USER_ID: VALID_USER_ID}
        enabled_med_filter = user_id_filter.copy()
        enabled_med_filter.update({MedicationV2DTO.ENABLED: True})

        self.assertEqual(4, MedicationV2.objects.filter(**enabled_med_filter).count())
        events = CalendarEvent.objects.filter(userId=VALID_USER_ID)
        [self.assertIsNone(ev.endDateTime) for ev in events]

        off_board_route = f"/api/extensions/v1/user/{VALID_USER_ID}/offboard"
        data = {"detailsOffBoarded": "recovered"}
        response = self.flask_client.post(
            off_board_route,
            headers=self.get_headers_for_token(VALID_MANAGER_ID),
            json=data,
        )
        self.assertEqual(200, response.status_code)

        self.assertEqual(0, MedicationV2.objects.filter(**enabled_med_filter).count())
        med = MedicationV2.objects.filter(**user_id_filter).first()
        self.assertEqual(
            med.reason["reason"],
            "This medication was deleted as part of patient offboarding",
        )
        self.assertEqual(med.reason["clinicianId"], VALID_MANAGER_ID)
        events = CalendarEvent.objects.filter(userId=VALID_USER_ID)
        [self.assertIsNotNone(ev.endDateTime) for ev in events]

    def test_medication_not_disabled_after_moving_user(self):
        user_id_filter = {MedicationV2DTO.USER_ID: VALID_USER_ID}
        enabled_med_filter = user_id_filter.copy()
        enabled_med_filter.update({MedicationV2DTO.ENABLED: True})

        self.assertEqual(4, MedicationV2.objects.filter(**enabled_med_filter).count())
        events = CalendarEvent.objects.filter(userId=VALID_USER_ID)
        [self.assertIsNone(ev.endDateTime) for ev in events]

        move_route = f"/api/extensions/v1/user/{VALID_USER_ID}/move/{VALID_DEPLOYMENT_2_ID}"
        move_details = {
            MoveUserDetails.REASON: MoveUserReasonDetails.DIFFERENTIAL_DIAGNOSIS,
            MoveUserDetails.SITUATION: "Nothing Special",
            MoveUserDetails.BACKGROUND: "Was Healthy",
            MoveUserDetails.ASSESSMENT: "Should be fine",
            MoveUserDetails.MESSAGE: "You were moved to the new deployment",
            MoveUserDetails.RECOMMENDATION: "Stay Healthy",
        }
        response = self.flask_client.post(
            move_route,
            headers=self.get_headers_for_token(VALID_MANAGER_ID),
            json={"details": move_details},
        )
        self.assertEqual(200, response.status_code)

        self.assertEqual(4, MedicationV2.objects.filter(**enabled_med_filter).count())
        events = CalendarEvent.objects.filter(userId=VALID_USER_ID)
        [self.assertIsNotNone(ev.endDateTime) for ev in events]

    @patch(SEND_NOTIFICATION_PATH)
    def test_create_meal_based_medication(self, notification_mock: MagicMock):
        req_dict = sample_meal_based_medication()  # medication for two meals
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(201, response.status_code)

        notification_mock.assert_called_once_with(
            user_id=VALID_USER_ID,
            action="OPEN_MODULE_DETAILS",
            notification_template=ANY,
            notification_data=ANY,
            run_async=ANY,
        )

        num_events = CalendarEvent.objects.filter(extraFields__medication__id=response.json["id"]).count()
        self.assertEqual(2, num_events)

    @patch(SEND_NOTIFICATION_PATH, MagicMock())
    def test_create_meal_based_medication_with_custom_units(self):
        req_dict = sample_meal_based_medication()
        req_dict["unit"] = "custom"
        req_dict[MedicationV2DTO.CUSTOM_UNIT] = "quark"
        req_dict[MedicationV2DTO.MEAL_SCHEDULE][0]["unit"] = "custom"
        req_dict[MedicationV2DTO.MEAL_SCHEDULE][0][MedicationMealSchedule.CUSTOM_UNIT] = "quark"

        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.headers)
        self.assertEqual(201, response.status_code)

    def _retrieve_key_actions(self) -> list[CalendarEventDTO]:
        extra_key_name = KeyAction.EXTRA_FIELDS
        query = {
            KeyAction.MODEL: KeyAction.__name__,
            KeyAction.USER_ID: VALID_USER_ID,
            f"{extra_key_name}.{KeyAction.MODULE_ID}": MedicationsV2Module.moduleId,
            f"{extra_key_name}.{KeyAction.MODULE_CONFIG_ID}": self.module_config_id,
        }
        return CalendarService().retrieve_calendar_events(
            compare_dt=datetime.utcnow(),
            timezone="UTC",
            allow_past_events=False,
            **query,
        )


class RetrieveMedicationTestCase(BaseMedicationTestCaseV2):
    def test_retrieve_medications(self):
        with freeze_time(datetime(2022, 9, 12, hour=12, minute=30)):
            headers = self.get_headers_for_token(VALID_MANAGER_ID)

            options = {"skip": 0, "limit": 10}
            response = self.flask_client.get(
                f"{self.medication_route}/search",
                headers=headers,
                query_string=options,
            )
            self.assertEqual(200, response.status_code)

        rsp_dict = response.json
        self.assertEqual(dict, type(rsp_dict))
        self.assertIn("items", rsp_dict)
        self.assertEqual(6, rsp_dict["total"])
        self.assertGreater(
            response.json["items"][0][MedicationV2DTO.UPDATE_DATE_TIME],
            response.json["items"][1][MedicationV2DTO.UPDATE_DATE_TIME],
        )

        test_event = rsp_dict["items"][1]
        self.assertIsNotNone(test_event["adherence"])
        self.assertIsNotNone(test_event[MedicationV2DTO.CODING])
        self.assertEqual(14, test_event["adherence"]["value"])
        self.assertEqual("%", test_event["adherence"]["unit"])
        times_of_readings = test_event["schedule"]["timesOfReadings"]
        self.assertEqual(2, len(times_of_readings))
        self.assertEqual("PT10H30M", times_of_readings[0])
        self.assertEqual("PT18H0M", times_of_readings[1])

    def test_retrieve_medications_pagination(self):
        options = {"skip": 0, "limit": 1}
        response = self.flask_client.get(
            f"{self.medication_route}/search",
            headers=self.get_headers_for_token(VALID_USER_ID),
            query_string=options,
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(1, len(response.json["items"]))
        self.assertEqual(6, response.json["total"])

    def test_retrieve_medications_with_proxy(self):
        response = self.flask_client.get(
            f"{self.medication_route}/search",
            headers=self.get_headers_for_token(VALID_USER_ID),
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(6, response.json["total"])

    def test_failure_retrieve_medications_permission_denied(self):
        options = {"skip": 0, "limit": 1, "enabled": True}
        response = self.flask_client.get(
            f"{self.medication_route}/search",
            headers=self.get_headers_for_token(VALID_USER_2_ID),
            query_string=options,
        )
        self.assertEqual(403, response.status_code)

    def test_failure_retrieve_medication_with_invalid_query(self):
        options = {"skip": 0, "limit": 0, "enabled": True}
        response = self.flask_client.get(
            f"{self.medication_route}/search",
            headers=self.headers,
            query_string=options,
        )
        self.assertEqual(403, response.status_code)

    def test_retrieve_enabled_medications(self):
        headers = self.get_headers_for_token(VALID_MANAGER_ID)

        options = {"skip": 0, "limit": 10, "enabled": True}
        response = self.flask_client.get(
            f"{self.medication_route}/search",
            headers=headers,
            query_string=options,
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(4, response.json["total"])

    def test_retrieve_disabled_medications(self):
        headers = self.get_headers_for_token(VALID_MANAGER_ID)

        options = {"skip": 0, "limit": 10, "enabled": False}
        response = self.flask_client.get(
            f"{self.medication_route}/search",
            headers=headers,
            query_string=options,
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(2, response.json["total"])
        self.assertGreater(
            response.json["items"][0][MedicationV2DTO.UPDATE_DATE_TIME],
            response.json["items"][1][MedicationV2DTO.UPDATE_DATE_TIME],
        )
        self.assertEqual("Fastum Gel", response.json["items"][0]["name"])

    @freeze_time("2022-07-07 14:00:00")
    def test_retrieve_medication_events_by_connected_module(self):
        medication = sample_meal_based_medication()
        medication[MedicationV2DTO.CONNECTED_MODULES] = [BloodGlucoseModule.moduleId]
        headers = self.get_headers_for_token(VALID_USER_3_ID)
        rsp = self._create_medication(medication, VALID_USER_3_ID, headers)

        self._retrieve_and_assert_medication_history(
            rsp.json["id"],
            expected_history=[],
            expected_count=0,
            compare_only_last=False,
        )
        rsp = self._retrieve_bg_medication_events(headers)

        events = rsp.json["items"]
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(2, len(events))
        self.assertIn(BloodGlucoseModule.moduleId, events[0]["medication"]["connectedModules"])

        rsp = self._retrieve_bg_medication_events(headers, hide_meals=True)
        events = rsp.json["items"]
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(0, len(events))

    @freeze_time("2022-07-07 14:00:00")
    def test_only_enabled_medication_events_retrieved_by_connected_module(self):
        medication = sample_meal_based_medication()
        medication[MedicationV2DTO.CONNECTED_MODULES] = [BloodGlucoseModule.moduleId]
        headers = self.get_headers_for_token(VALID_USER_3_ID)
        rsp = self._create_medication(medication, VALID_USER_3_ID, headers)
        with freeze_time("2022-07-07 15:00:00"):
            update_url = url_for(
                "medication_v2.update_medication",
                user_id=VALID_USER_3_ID,
                medication_id=rsp.json["id"],
            )
            self.flask_client.put(
                update_url,
                json={MedicationV2DTO.ENABLED: False},
                headers=headers,
            )

        rsp = self._retrieve_bg_medication_events(headers)

        events = rsp.json["items"]
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(0, len(events))

    def _retrieve_bg_medication_events(self, headers, hide_meals=False):
        now = datetime.now(timezone.utc)
        url = url_for("medication_v2.retrieve_medication_calendar", user_id=VALID_USER_3_ID)
        query_string = {
            "connectedModule": BloodGlucoseModule.moduleId,
            "hideMealBased": hide_meals,
            "startDateTime": utc_str_field_to_val(now),
            "endDateTime": utc_str_field_to_val(now + timedelta(days=1)),
        }
        return self.flask_client.get(
            url,
            headers=headers,
            query_string=query_string,
        )

    def test_retrieve_grouped_medications(self):
        medication = sample_medication()
        random_uuid = str(uuid4())
        MEDICATION_GROUP_IDS.append(random_uuid)
        for g_id in MEDICATION_GROUP_IDS:
            medication[MedicationV2DTO.GROUP_ID] = g_id
            self._create_medication(body=medication)

        medications = self._retrieve_medications(grouped=True)
        med_groups = medications[MedicationResponseGroupedV2.MEDICATION_GROUPS]
        types_dict = {t[MedicationGroup.NAME]: t[MedicationGroup.ITEMS] for t in med_groups}
        self.assertEqual(5, len(med_groups))
        self.assertEqual(7, len(types_dict["Unknown"]))
        self.assertEqual(1, len(types_dict["Rescue"]))
        self.assertEqual(1, len(types_dict["Biologic"]))
        self.assertEqual(1, len(types_dict["Controller"]))
        self.assertEqual(1, len(types_dict["Allergy"]))

    def test_retrieve_grouped_medications_with_no_group(self):
        medications = self._retrieve_medications(grouped=True)
        med_groups = medications[MedicationResponseGroupedV2.MEDICATION_GROUPS]
        self.assertEqual(1, len(med_groups))
        self.assertEqual(6, len(med_groups[0][MedicationGroup.ITEMS]))
        self.assertEqual("Unknown", med_groups[0][MedicationGroup.NAME])


class UpdateMedicationTestCase(BaseMedicationTestCaseV2):
    REASON = "reason"

    @patch(SEND_NOTIFICATION_PATH)
    def test_success_update_medication(self, send_notification: MagicMock):
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.REASON] = self.REASON
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.headers,
        )
        self.assertEqual(200, response.status_code)
        expected_history = medication_history(
            response.json["id"],
            change_type=MedicationChangeType.MEDICATION_UPDATE.value,
            name=VALID_MEDICATION_ID_1_NAME,
        )
        expected_history.update(
            {
                MedicationHistoryV2DTO.REASON: {
                    MedicationReasonUpdated.REASON: self.REASON,
                    MedicationReasonUpdated.CLINICIAN_ID: VALID_MANAGER_ID,
                    MedicationReasonUpdated.CLINICIAN_NAME: "Joe MrClinician",
                }
            }
        )
        self._retrieve_and_assert_medication_history(
            response.json["id"], expected_history, expected_count=3, compare_dt=False
        )
        self._assert_notification(
            send_notification,
            MedicationNotificationAction.MEDICATION_UPDATED,
            VALID_MEDICATION_ID_1_NAME,
        )
        self.assertEqual(VALID_MEDICATION_ID_1, response.json["id"])
        self._assert_medication_reason_updated(self.REASON)

    @patch(SEND_NOTIFICATION_PATH)
    def test_success_update_medication_with_no_changes(self, send_notification: MagicMock):
        req_dict = self._prepare_request_dict()
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.user_headers,
        )
        self.assertEqual(200, response.status_code)
        send_notification.assert_not_called()
        self.assertEqual(VALID_MEDICATION_ID_1, response.json["id"])

    @patch(SEND_NOTIFICATION_PATH)
    def test_success_update_medication_with_end_date(self, send_notification: MagicMock):
        start_date = "2023-01-09"
        end_date = self.soon.strftime("%Y-%m-%d")
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.START_DATE] = start_date
        req_dict[MedicationV2DTO.END_DATE] = end_date
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.user_headers,
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(VALID_MEDICATION_ID_1, response.json["id"])
        self._assert_start_date_and_end_date_updated(start_date, end_date)
        send_notification.assert_not_called()

    @patch(SEND_NOTIFICATION_PATH)
    def test_success_update_medication_with_end_date_empty(self, send_notification: MagicMock):
        start_date = "2023-01-09"
        end_date = ""
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.START_DATE] = start_date
        req_dict[MedicationV2DTO.END_DATE] = end_date
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.user_headers,
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(VALID_MEDICATION_ID_1, response.json["id"])
        self._assert_start_date_and_end_date_updated(start_date, end_date)
        send_notification.assert_not_called()

    @patch(SEND_NOTIFICATION_PATH)
    def test_success_update_medication_with_empty_indication(self, send_notification: MagicMock):
        start_date = "2023-01-09"
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.START_DATE] = start_date
        req_dict[MedicationV2DTO.INDICATION] = {}
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.user_headers,
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(VALID_MEDICATION_ID_1, response.json["id"])
        send_notification.assert_not_called()

    def test_failure_update_medication_with_indication_coding_no_name(self):
        start_date = "2023-01-09"
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.START_DATE] = start_date
        req_dict[MedicationV2DTO.INDICATION] = {
            "coding": [
                {
                    "system": "http://snomed.info/sct",
                    "code": "123456",
                    "display": "Some display",
                }
            ]
        }
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.user_headers,
        )
        self.assertEqual(403, response.status_code)
        self.assertEqual(
            "Indication name is required when coding is provided",
            response.json["message"],
        )

    def test_failure_update_medication_with_wrong_end_date_format(self):
        start_date = "2023-01-09"
        end_date = "wrong date"
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.START_DATE] = start_date
        req_dict[MedicationV2DTO.END_DATE] = end_date
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.user_headers,
        )
        self.assertEqual(403, response.status_code)

    def test_failure_update_medication_with_wrong_dates(self):
        start_date = "2023-01-09"
        end_date = "2022-01-09"
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.START_DATE] = start_date
        req_dict[MedicationV2DTO.END_DATE] = end_date
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.user_headers,
        )
        self.assertEqual(403, response.status_code)

    def test_failure_update_medication_off_boarded_user(self):
        start_date = "2023-01-09"
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.START_DATE] = start_date

        self._off_board_user(VALID_USER_ID)
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.user_headers,
        )
        self.assertEqual(403, response.status_code)
        self.assertEqual("User is not active", response.json["message"])

    @patch(SEND_NOTIFICATION_PATH)
    def test_success_update_medication_with_as_needed_no_max_dosage(self, send_notification: MagicMock):
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.REASON] = self.REASON
        req_dict[MedicationV2DTO.SCHEDULE] = {"asNeeded": True}
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.headers,
        )
        self.assertEqual(200, response.status_code)
        self._assert_notification(
            send_notification,
            MedicationNotificationAction.MEDICATION_UPDATED,
            VALID_MEDICATION_ID_1_NAME,
        )
        self.assertEqual(VALID_MEDICATION_ID_1, response.json["id"])
        self._assert_medication_reason_updated(self.REASON)

        req_dict[MedicationV2DTO.MAX_DOSAGE] = None
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.headers,
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(VALID_MEDICATION_ID_1, response.json["id"])
        self._assert_medication_reason_updated(self.REASON)

    @patch(SEND_NOTIFICATION_PATH)
    def test_success_disable_a_medication_max_dosage(self, send_notification: MagicMock):
        req_dict = self._prepare_request_dict()
        req_dict.pop(MedicationV2DTO.SCHEDULE)
        req_dict[MedicationV2DTO.REASON] = self.REASON
        req_dict[MedicationV2DTO.ENABLED] = False
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_2}",
            json=req_dict,
            headers=self.headers,
        )
        self.assertEqual(200, response.status_code)
        self._assert_notification(
            send_notification,
            MedicationNotificationAction.MEDICATION_REMOVED,
            VALID_MEDICATION_ID_2_NAME,
        )
        self.assertEqual(VALID_MEDICATION_ID_2, response.json["id"])
        self._assert_medication_reason_updated(self.REASON, VALID_MEDICATION_ID_2, disabled=True)

    @patch(SEND_NOTIFICATION_PATH)
    def test_success_change_schedule_removes_medication_max_dosage(self, send_notification: MagicMock):
        original_medication = self._get_medication_from_db(VALID_MEDICATION_ID_2)
        self.assertIsNotNone(original_medication.maxDosage)
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.REASON] = self.REASON
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_2}",
            json=req_dict,
            headers=self.headers,
        )
        self.assertEqual(200, response.status_code)
        self._assert_notification(
            send_notification,
            MedicationNotificationAction.MEDICATION_UPDATED,
            VALID_MEDICATION_ID_2_NAME,
        )
        self.assertEqual(VALID_MEDICATION_ID_2, response.json["id"])
        self._assert_medication_reason_updated(self.REASON, VALID_MEDICATION_ID_2)
        updated_medication = self._get_medication_from_db(VALID_MEDICATION_ID_2)
        self.assertIsNone(updated_medication.maxDosage)

    @patch(SEND_NOTIFICATION_PATH)
    def test_cannot_replace_schedule_with_meal_schedule(self, send_notification):
        medication_with_schedule = self._get_medication_from_db(VALID_MEDICATION_ID_2)
        self.assertIsNotNone(medication_with_schedule.schedule)

        req_dict = sample_meal_based_medication()
        req_dict[MedicationV2DTO.REASON] = self.REASON
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        url = f"{self.medication_route}/{VALID_MEDICATION_ID_2}"
        response = self.flask_client.put(url, json=req_dict, headers=self.headers)

        self.assertEqual(403, response.status_code)

    @patch(SEND_NOTIFICATION_PATH)
    def test_cannot_update_meal_schedule_to_regular_schedule(self, send_notification):
        req_dict = sample_meal_based_medication()
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        rsp = self._create_medication(req_dict)
        med_id = rsp.json["id"]

        url = f"{self.medication_route}/{med_id}"
        updated_med = sample_medication()
        updated_med[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        updated_med[MedicationV2DTO.REASON] = self.REASON
        response = self.flask_client.put(url, json=updated_med, headers=self.headers)

        self.assertEqual(403, response.status_code)

    @patch(SEND_NOTIFICATION_PATH)
    def test_failure_update_medication_with_max_dosage_less_than_dosage(self, send_notification: MagicMock):
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.REASON] = self.REASON
        req_dict[MedicationV2DTO.MAX_DOSAGE] = 10
        req_dict.pop(MedicationV2DTO.DOSAGE, None)
        req_dict[MedicationV2DTO.SCHEDULE] = {"asNeeded": True}
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.headers,
        )
        self.assertEqual(403, response.status_code)
        send_notification.assert_not_called()

    @patch(SEND_NOTIFICATION_PATH)
    def test_failure_update_medication_with_no_schedule(self, send_notification: MagicMock):
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.REASON] = self.REASON
        req_dict[MedicationV2DTO.SCHEDULE] = {"asNeeded": False}
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.headers,
        )
        self.assertEqual(403, response.status_code)
        send_notification.assert_not_called()

    @patch(SEND_NOTIFICATION_PATH)
    def test_failure_update_medication_for_proxy(self, send_notification: MagicMock):
        medication_route = f"/api/extensions/v1/user/{VALID_PROXY_USER_ID}/medication"
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.DOSAGE] = 10
        response = self.flask_client.put(
            f"{medication_route}/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.proxy_headers,
        )
        self.assertEqual(403, response.status_code)

    @patch(SEND_NOTIFICATION_PATH)
    def test_success_update_medication_by_proxy(self, send_notification: MagicMock):
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.DOSAGE] = 10
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.proxy_headers,
        )
        self.assertEqual(200, response.status_code)
        send_notification.assert_not_called()
        self.assertEqual(VALID_MEDICATION_ID_1, response.json["id"])

        medication = MedicationV2.objects.get(mongoId=VALID_MEDICATION_ID_1)
        self.assertIsNotNone(medication)
        self.assertIsNone(medication.reason)
        self.assertEqual(10, medication.dosage)

    def test_failure_update_medication(self):
        req_dict = self._prepare_request_dict()
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.headers,
        )
        self.assertEqual(403, response.status_code)
        self.assertEqual(response.json["message"], "Missing keys: reason")

    @patch(SEND_NOTIFICATION_PATH)
    def test_failure_update_medication_wrong_user(self, send_notification: MagicMock):
        req_dict = self._prepare_request_dict()
        response = self.flask_client.put(
            f"/api/extensions/v1/user/{VALID_USER_2_ID}/medication/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.headers,
        )
        self.assertEqual(403, response.status_code)

    def test_success_update_medication_with_regimen_frequency(self):
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.REASON] = self.REASON
        req_dict[MedicationV2DTO.UNIT] = "puffs"
        req_dict[MedicationV2DTO.ADDITIONAL_FIELDS] = {
            AdditionalFields.INHALER: {InhalerInfo.FREQUENCY: Frequency.MAINTAIN.name}
        }
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_2}",
            json=req_dict,
            headers=self.headers,
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(VALID_MEDICATION_ID_2, response.json["id"])
        medication = self._get_medication_from_db(response.json["id"])
        self.assertEqual(Frequency.MAINTAIN.name, medication.additionalFields["inhaler"]["frequency"])

    @patch(SEND_NOTIFICATION_PATH)
    def test_success_disable_medication(self, send_notification: MagicMock):
        with freeze_time(datetime(2022, 9, 1, hour=18)):
            req_dict = self._prepare_request_dict()
            req_dict[MedicationV2DTO.ENABLED] = False
            req_dict[MedicationV2DTO.REASON] = "Wrong medication"
            response = self.flask_client.put(
                f"{self.medication_route}/{VALID_MEDICATION_ID_3}",
                json=req_dict,
                headers=self.get_headers_for_token(VALID_MANAGER_ID),
            )
            self.assertEqual(200, response.status_code)
            self._assert_notification(
                send_notification,
                MedicationNotificationAction.MEDICATION_REMOVED,
                VALID_MEDICATION_ID_3_NAME,
            )
            self.assertEqual(VALID_MEDICATION_ID_3, response.json["id"])
            self._assert_medication_reason_updated("Wrong medication", VALID_MEDICATION_ID_3, True)

            event = self._find_db_event_for(VALID_USER_ID, extraFields__medication__id=VALID_MEDICATION_ID_3)
            self.assertIsNotNone(event)
            self.assertFalse(event.extraFields["medication"]["enabled"])

    def test_success_disable_medication_by_patient(self):
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.ENABLED] = False
        response = self.flask_client.put(
            f"/api/extensions/v1/user/{VALID_USER_ID}/medication/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.get_headers_for_token(VALID_USER_ID),
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(VALID_MEDICATION_ID_1, response.json["id"])
        expected_history = medication_history(
            VALID_MEDICATION_ID_1,
            change_type=MedicationChangeType.MEDICATION_DELETE.value,
            name=VALID_MEDICATION_ID_1_NAME,
            user_type=SubmitterUserType.USER.value,
        )
        self._retrieve_and_assert_medication_history(
            VALID_MEDICATION_ID_1, expected_history, expected_count=3, compare_dt=False
        )
        medication = MedicationV2.objects.get(mongoId=VALID_MEDICATION_ID_1)
        self.assertIsNotNone(medication)
        self.assertIsNone(medication.reason)

    def test_failure_disable_medication_by_manager_without_reason(self):
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.ENABLED] = False
        response = self.flask_client.put(
            f"/api/extensions/v1/user/{VALID_USER_ID}/medication/{VALID_MEDICATION_ID_1}",
            json=req_dict,
            headers=self.headers,
        )
        self.assertEqual(403, response.status_code)

    def test_ignore_name_in_update_medication_request(self):
        data = sample_medication()
        data.update({"reason": "test"})
        self.assertNotEqual(data["name"], VALID_MEDICATION_ID_1)

        update_rsp = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_1}",
            json=data,
            headers=self.headers,
        )
        self.assertEqual(200, update_rsp.status_code)

        get_medication_rsp = self.flask_client.get(f"{self.medication_route}/search", headers=self.headers)
        self.assertEqual(200, get_medication_rsp.status_code)
        items = get_medication_rsp.json["items"]
        medication = find(lambda i: i["id"] == VALID_MEDICATION_ID_1, items)
        self.assertEqual(VALID_MEDICATION_ID_1_NAME, medication["name"])

    def test_failure_update_medication_invalid_id(self):
        req_dict = self._prepare_request_dict()
        req_dict[MedicationV2DTO.REASON] = "Changed diet"
        response = self.flask_client.put(
            f"{self.medication_route}/{INVALID_MEDICATION_ID}",
            json=req_dict,
            headers=self.headers,
        )
        self.assertEqual(404, response.status_code)
        self.assertEqual(400011, response.json["code"])

    def test_failure_update_medication_invalid_user_id(self):
        response = self.flask_client.post(
            f"api/extensions/v1/user/{INVALID_USER_ID}/medication/{VALID_MEDICATION_ID_1}",
            json=self._prepare_request_dict(),
            headers=self.headers,
        )
        self.assertEqual(404, response.status_code)
        self.assertEqual(UserErrorCodes.INVALID_USER_ID, response.json["code"])

    def test_success_get_medication_notes_by_manager(self):
        response = self.flask_client.get(
            f"/api/extensions/v1/user/{VALID_USER_ID}/medication/{VALID_MEDICATION_ID_1}/notes",
            headers=self.headers,
        )
        self.assertEqual(200, response.status_code)
        self.assertIn("items", response.json)
        self.assertEqual(1, len(response.json["items"]))

    def test_failure_get_medication_notes_by_manager(self):
        response = self.flask_client.get(
            f"/api/extensions/v1/user/{VALID_USER_ID}/medication/{INVALID_MEDICATION_ID}/notes",
            headers=self.headers,
        )
        self.assertEqual(200, response.status_code)
        self.assertIn("items", response.json)
        self.assertEqual(0, len(response.json["items"]))

    def test_failure_get_medication_notes_by_manager_with_invalid_medication_id(self):
        invalid_medication_id = "47dgsjsddff"
        response = self.flask_client.get(
            f"/api/extensions/v1/user/{VALID_USER_ID}/medication/{invalid_medication_id}/notes",
            headers=self.headers,
        )
        self.assertEqual(403, response.status_code)
        self.assertEqual(100050, response.json["code"])

    def test_failure_updating_connected_module(self):
        update = self._prepare_request_dict()
        update[MedicationV2DTO.REASON] = "Reason"
        update[MedicationV2DTO.CONNECTED_MODULES] = [BloodGlucoseModule.moduleId]
        response = self.flask_client.put(
            f"{self.medication_route}/{VALID_MEDICATION_ID_1}",
            json=update,
            headers=self.headers,
        )
        self.assertEqual(403, response.status_code)

    def _prepare_request_dict(self):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        req_dict.pop(MedicationV2DTO.NAME)
        return req_dict

    def _get_medication_from_db(self, medication_id: str = VALID_MEDICATION_ID_1) -> MedicationV2:
        medication = MedicationV2.objects.get(mongoId=medication_id)
        self.assertIsNotNone(medication)
        return medication

    def _assert_start_date_and_end_date_updated(
        self, start_date: str, end_date: str, medication_id: str = VALID_MEDICATION_ID_1
    ) -> MedicationV2:
        medication = self._get_medication_from_db(medication_id)
        medication_start_date = medication.startDate
        self.assertIsNotNone(medication_start_date)
        self.assertEqual(start_date, datetime.strftime(medication_start_date, "%Y-%m-%d"))
        self.assertEqual(end_date, medication.endDate)
        return medication

    def _assert_medication_reason_updated(
        self,
        reason_str,
        medication_id: str = VALID_MEDICATION_ID_1,
        disabled: bool = False,
    ) -> MedicationV2:
        medication = self._get_medication_from_db(medication_id)
        self.assertIsNotNone(medication.reason)
        reason = medication.reason
        self.assertEqual(VALID_MANAGER_ID, str(reason[MedicationReasonUpdated.CLINICIAN_ID]))
        self.assertEqual(reason_str, reason[MedicationReasonUpdated.REASON])
        self.assertIsNotNone(reason[MedicationReasonUpdated.CLINICIAN_NAME])
        if disabled:
            self.assertIsNotNone(medication.adherence)
        else:
            self.assertIsNone(medication.adherence)
        return medication


class CreateOrUpdateMedicationLogTestCase(BaseMedicationTestCaseV2):
    def test_success_create_medication_log(self):
        req_dict = sample_medication_log()
        response = self._create_update_log(req_dict)

        self.assertEqual(201, response.status_code)
        self.assertIsNotNone(response.json["ids"])

    def test_success_create_meal_based_medication_log(self):
        req_dict = sample_medication_log()
        req_dict[MedicationLogDTO.EVENT_ID] = "630ccbe044e6f4aef43faadc"
        req_dict[MedicationLogDTO.MEAL] = meal = "AFTER_LUNCH_2_HOURS"
        response = self._create_update_log(req_dict, "5e8f0c74b50aa9656c347778")

        self.assertEqual(201, response.status_code)
        log_id = response.json["ids"][0]
        log = MedicationLog.objects.get(mongoId=log_id)
        self.assertEqual(meal, log.meal)

    def test_success_create_medication_log_with_proxy(self):
        req_dict = sample_medication_log()
        self.headers = self.proxy_headers
        response = self._create_update_log(req_dict)

        self.assertEqual(201, response.status_code)
        self.assertIsNotNone(response.json["ids"])

    def test_event_does_not_match_medication(self):
        req_dict = sample_medication_log()
        response = self._create_update_log(req_dict, medication_id=VALID_MEDICATION_ID_1)

        self.assertEqual(400, response.status_code)

    def test_failure_create_medication_log_for_future_date(self):
        req_dict = sample_medication_log()
        future_start_time = datetime.now() + timedelta(days=1)
        req_dict[MedicationLogDTO.START_DATE_TIME] = datetime.strftime(future_start_time, "%Y-%m-%dT%H:%M:%S.%fZ")
        response = self._create_update_log(req_dict)

        self.assertEqual(403, response.status_code)

    def test_failure_create_medication_log(self):
        req_dict = sample_medication_log()
        response = self._create_update_log(req_dict, medication_id=INVALID_MEDICATION_ID)

        self.assertEqual(404, response.status_code)

    def test_failure_create_medication_with_invalid_log_count(self):
        req_dict = sample_medication_log()
        req_dict[MedicationLogDTO.LOGS_COUNT] = -1
        response = self._create_update_log(req_dict)

        self.assertEqual(403, response.status_code)

    def test_success_create_medication_with_log_count(self):
        req_dict = sample_medication_log(as_needed=True)
        req_dict[MedicationLogDTO.EVENT_ID] = AS_NEEDED_MEDICATION_ID
        response = self._create_update_log(req_dict, medication_id=AS_NEEDED_MEDICATION_ID)

        self.assertEqual(201, response.status_code)
        self.assertIsNotNone(response.json["ids"])

    def test_failure_create_medication_without_log_count(self):
        MEDICATION_ID = "62fca83e19af8839979d65bc"
        req_dict = sample_medication_log()
        response = self._create_update_log(req_dict, medication_id=MEDICATION_ID)

        self.assertEqual(403, response.status_code)

    def test_success_update_medication_log(self):
        EXPECTED_MEDICATION_LOG_ID = "630759a996aa2480c30361ec"
        req_dict = sample_medication_log()
        req_dict[MedicationLogDTO.EVENT_ID] = "630ccbe044e6f4aef43fccdb"
        req_dict[MedicationLogDTO.START_DATE_TIME] = "2022-09-06T16:30:00.000Z"

        response = self._create_update_log(req_dict)

        self.assertEqual(201, response.status_code)
        self.assertEqual(response.json["ids"], [EXPECTED_MEDICATION_LOG_ID])

    def test_success_create_update_medication_log_as_needed(self):
        req_dict = sample_medication_log()
        req_dict[MedicationLogDTO.EVENT_ID] = AS_NEEDED_MEDICATION_ID
        req_dict[MedicationLogDTO.LOGS_COUNT] = 6
        req_dict[MedicationLogDTO.START_DATE_TIME] = "2022-09-06T00:00:00.000Z"

        response = self._create_update_log(req_dict, medication_id=AS_NEEDED_MEDICATION_ID)
        self.assertEqual(201, response.status_code)

        response = self.flask_client.get(
            f"{self.medication_route}/unseen",
            headers=self.headers,
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(1, len(response.json["items"]))
        unseen_result = response.json["items"][0]
        flags = unseen_result[UnseenResultDTO.FLAGS]
        self.assertEqual(1, flags[UnseenFlags.RED])
        self.assertEqual(0, flags[UnseenFlags.AMBER])
        self.assertEqual(0, flags[UnseenFlags.GRAY])
        self.assertEqual(1200, unseen_result[UnseenResultDTO.EXTRA]["logsCount"])

        req_dict[MedicationLogDTO.LOGS_COUNT] = 10
        response = self._create_update_log(req_dict, medication_id=AS_NEEDED_MEDICATION_ID)

        self.assertEqual(201, response.status_code)

        response = self.flask_client.get(
            f"{self.medication_route}/unseen",
            headers=self.headers,
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(2, len(response.json["items"]))
        unseen_result = response.json["items"][0]
        self.assertEqual(2000, unseen_result[UnseenResultDTO.EXTRA]["logsCount"])

    def test_success_delete_medication_log(self):
        EXPECTED_MEDICATION_LOG_ID = "630759a996aa2480c30361ec"
        req_dict = sample_medication_log()
        req_dict[MedicationLogDTO.EVENT_ID] = "630ccbe044e6f4aef43fccdb"
        req_dict[MedicationLogDTO.START_DATE_TIME] = "2022-09-06T16:30:00.000Z"
        req_dict.pop(MedicationLogDTO.STATUS)

        response = self._create_update_log(req_dict)

        self.assertEqual(201, response.status_code)
        self.assertEqual([EXPECTED_MEDICATION_LOG_ID], response.json["ids"])

        medication_log = MedicationLog.objects.filter(mongoId=EXPECTED_MEDICATION_LOG_ID).first()
        self.assertIsNone(medication_log)

    def _create_update_log(self, body: dict, medication_id: str = VALID_MEDICATION_ID_3):
        return self.flask_client.post(
            f"{self.medication_route}/{medication_id}/log",
            json=body,
            headers=self.headers,
        )


class RetrieveMedicationLogsTestCase(BaseMedicationTestCaseV2):
    module_config_id = "5f1824ba504787d8d89ebecb"

    def test_retrieve_logs(self):
        url = url_for("medication_v2.retrieve_medication_logs", user_id=VALID_USER_ID)
        response = self.flask_client.get(
            url,
            headers=self.headers,
            query_string={"moduleId": BloodGlucoseModule.moduleId, "status": "TAKEN"},
        )
        self.assertEqual(200, response.status_code)
        self.assertEqual(1, len(response.json["items"]))
        self.assertEqual("TAKEN", response.json["items"][0]["status"])


class RetrieveMedicationHistoryTestCase(BaseMedicationTestCaseV2):
    def _get_history(self, query: dict, _as=VALID_MANAGER_ID):
        url = url_for("medication_v2.retrieve_medications_history", user_id=VALID_USER_ID)
        response = self.flask_client.get(url, headers=self.get_headers_for_token(_as), query_string=query)
        self.assertEqual(200, response.status_code)
        return response.json

    def test_success_retrieve_medications_history(self):
        response_dict = self._get_history({})
        self.assertIn("total", response_dict)
        self.assertEqual(4, response_dict["total"])

    def test_success_retrieve_unseen_medication_history(self):
        response_dict = self._get_history({"seen": False})
        self.assertIn("total", response_dict)
        self.assertEqual(3, response_dict["total"])

    def test_success_retrieve_user_medication_history(self):
        response_dict = self._get_history({"searchWithUserType": "MANAGER"}, _as=VALID_USER_ID)

        self.assertIn("total", response_dict)
        self.assertEqual(2, response_dict["total"])

    def test_success_retrieve_frequency_changes_in_medication_history(self):
        def _update_body(frequency: str) -> dict:
            return {
                MedicationV2DTO.ADDITIONAL_FIELDS: {AdditionalFields.INHALER: {InhalerInfo.FREQUENCY: frequency}},
                MedicationV2DTO.ENABLED: True,
                MedicationV2DTO.REASON: "Test reason",
                MedicationV2DTO.SCHEDULE: {
                    "asNeeded": True,
                    "timeRanges": [{"endTime": "PT22H0M", "startTime": "PT18H0M"}],
                },
            }

        self._update_medication(VALID_MEDICATION_ID_1, _update_body(Frequency.MAINTAIN.name))
        self._assert_regimen_frequency_change(old="", new=Frequency.MAINTAIN.name)

        self._update_medication(VALID_MEDICATION_ID_1, _update_body(Frequency.RELIEVE.name))
        self._assert_regimen_frequency_change(old=Frequency.MAINTAIN.name, new=Frequency.RELIEVE.name)

        self._update_medication(VALID_MEDICATION_ID_1, _update_body(Frequency.RELIEVE_AND_MAINTAIN.name))
        self._assert_regimen_frequency_change(old=Frequency.RELIEVE.name, new=Frequency.RELIEVE_AND_MAINTAIN.name)

    def _assert_regimen_frequency_change(self, old, new):
        response_dict = self._get_history({})
        self.assertIn("total", response_dict)
        self.assertTrue(response_dict["total"] > 0)
        medication_history = response_dict["items"][-1]

        self.assertIsNotNone(deep_get(medication_history, "regimen"))
        self.assertEqual(deep_get(medication_history, "regimen.frequency.old"), old)
        self.assertEqual(deep_get(medication_history, "regimen.frequency.new"), new)


class UnseenResultsRouterTestCase(BaseMedicationTestCaseV2):
    @patch(SEND_NOTIFICATION_PATH, MagicMock())
    def test_success_retrieve_unseen_medications(self):
        self._create_medication()
        response = self.flask_client.get(f"{self.medication_route}/unseen", headers=self.user_headers)
        self.assertEqual(200, response.status_code)

        response_dict = response.json
        self.assertIn("items", response_dict)
        self.assertEqual(1, len(response_dict["items"]))

    def _create_medication(self):
        req_dict = sample_medication()
        req_dict[MedicationV2DTO.MODULE_CONFIG_ID] = self.module_config_id
        response = self.flask_client.post(self.medication_route, json=req_dict, headers=self.user_headers)
        self.assertEqual(201, response.status_code)
        return response.json["id"]


class DebugCalendarRouteTestCaseDebugOff(ExtensionTestCase):
    components = [CalendarComponent(), MedicationComponent()]
    path = "/api/extensions/v1/medication"

    def test_failure_html_page(self):
        response = self.flask_client.get(f"{self.path}/render/test")
        self.assertEqual(404, response.status_code)

    def test_failure_export_route(self):
        response = self.flask_client.get(f"{self.path}/user/{VALID_USER_ID}/export")
        self.assertEqual(404, response.status_code)


class DebugCalendarRouteTestCaseDebugOn(ExtensionTestCase):
    components = [CalendarComponent(), MedicationComponent()]
    override_config = {"server.debugRouter": "true"}
    path = "/api/extensions/v1/medication"

    def test_success_html_page(self):
        response = self.flask_client.get(f"{self.path}/render/test")
        self.assertEqual(200, response.status_code)
        self.assertEqual("text/html; charset=utf-8", response.content_type)

    @patch("huma_plugins.components.medication.router.debug_router.ExportMedicationsCalendarUseCase")
    def test_success_export_route(self, use_case: MagicMock):
        response = self.flask_client.get(
            f"{self.path}/user/{VALID_USER_ID}/export",
            query_string={
                "start": "2022-01-01T00:00:00.000Z",
                "end": "2022-01-31T00:00:00.000Z",
            },
        )
        self.assertEqual(200, response.status_code)


class DeleteUserMedicationV2OnUserDelete(BaseMedicationTestCaseV2):
    def _check_document_count(self, model, query, expected_count):
        count = model.objects.filter(**query).count()
        self.assertEqual(expected_count, count)
        return count

    def _delete_user(self, user_id):
        super_admin_headers = self.get_headers_for_token(VALID_SUPER_ADMIN_ID)
        del_user_path = f"/api/auth/v1/private/user/{user_id}/delete-user"
        rsp = self.flask_client.delete(del_user_path, headers=super_admin_headers)
        self.assertEqual(rsp.status_code, 200)
        return rsp.json

    def test_success_delete_user_medication(self):
        query = {MedicationV2DTO.USER_ID: VALID_USER_ID}
        model, table_name = MedicationV2, MedicationV2._meta.db_table
        count = self._check_document_count(model, query, 6)
        rsp_dict = self._delete_user(VALID_USER_ID)
        self._check_document_count(model, query, 0)
        self.assertIn(table_name, rsp_dict)
        self.assertEqual(count, rsp_dict[table_name])

    def test_success_delete_user_medication_log(self):
        query = {MedicationLogDTO.USER_ID: VALID_USER_ID}
        model, table_name = MedicationLog, MedicationLog._meta.db_table
        count = self._check_document_count(model, query, 1)
        rsp_dict = self._delete_user(VALID_USER_ID)
        self._check_document_count(model, query, 0)
        self.assertIn(table_name, rsp_dict)
        self.assertEqual(count, rsp_dict[table_name])

    def test_success_delete_user_medication_history(self):
        query = {MedicationHistoryV2DTO.USER_ID: VALID_USER_ID}
        model, table_name = MedicationHistoryV2, MedicationHistoryV2._meta.db_table
        count = self._check_document_count(model, query, 4)
        rsp_dict = self._delete_user(VALID_USER_ID)
        self._check_document_count(model, query, 0)
        self.assertIn(table_name, rsp_dict)
        self.assertEqual(count, rsp_dict[table_name])


if __name__ == "__main__":
    main()
