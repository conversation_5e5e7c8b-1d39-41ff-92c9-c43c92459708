{"deployment": [{"_id": {"$oid": "5d386cc6ff885918d96edb2c"}, "name": "Medication Test Deployment", "status": "DEPLOYED", "color": "0x007AFF", "moduleConfigs": [{"id": {"$oid": "5f1824ba504787d8d89ebeca"}, "moduleId": "MedicationsV2", "moduleName": "Medications", "notificationData": {"title": "Medications", "body": "Take your {$time} ${medicationName}"}, "about": "Medications are an important part of your care plan. You can use the medications list to keep track of, and refer to dosages as and when you need to. Your care team will have access to this list and can add medications when needed. Please note, the list is not a prescription.", "status": "ENABLED", "configBody": {"types": {"inhaler": {"medicationNames": ["Symbicort"], "units": ["puffs"]}}, "flows": {"inhaler": {"frequencies": {"relieve": {"lowerBound": 5, "upperBound": 10}, "maintain": {"lowerBound": 1, "upperBound": 10}, "relieveMaintain": {"lowerBound": 10, "upperBound": 300}}, "scheduleBuffer": "PT1H"}}, "groups": [{"id": "e62bb95c-46e7-4b66-b907-a539b06d0e81", "name": "Rescue", "tags": ["Rescue"]}, {"id": "e62bb95c-46e7-4b66-b907-a539b06d0e82", "name": "Controller", "tags": ["Controller"]}, {"id": "e62bb95c-46e7-4b66-b907-a539b06d0e83", "name": "Allergy", "tags": ["Allergy"]}, {"id": "e62bb95c-46e7-4b66-b907-a539b06d0e84", "name": "Biologic", "tags": ["Biologic"]}]}, "connectableModules": ["BloodGlucose"]}, {"id": {"$oid": "5f1824ba504787d8d89ebecb"}, "moduleId": "BloodGlucose", "status": "ENABLED"}], "updateDateTime": {"$date": {"$numberLong": "1586433217042"}}, "createDateTime": {"$date": {"$numberLong": "1586433217042"}}, "integrationDevices": [{"displayName": "Turbu+", "moduleIds": ["MedicationsV2"], "model": "NF0097", "backgroundSync": true, "supportedMedications": {"placeholder": "Symbicort Turbuhaler", "type": "inhaler"}}]}, {"_id": {"$oid": "5d386cc6ff885918d96edb2d"}, "name": "Deployment A", "status": "DEPLOYED", "color": "0x007AFF", "code": "AU15"}], "huma_auth_user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "status": 1, "emailVerified": true, "email": "<EMAIL>"}, {"_id": {"$oid": "5e84b0dab8dfa268b1180536"}, "status": 1, "emailVerified": true, "email": "<EMAIL>"}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "status": 1, "emailVerified": true, "email": "<EMAIL>"}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789d"}, "status": 1, "email": "<EMAIL>", "emailVerified": true}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789e"}, "status": 1, "email": "<EMAIL>", "emailVerified": true}, {"_id": {"$oid": "5e8f0c74b50aa9656c342220"}, "status": 1, "emailVerified": true, "email": "<EMAIL>"}, {"_id": {"$oid": "5e84b0dab8dfa268b1280539"}, "status": 1, "emailVerified": true, "email": "<EMAIL>"}], "user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "email": "<EMAIL>", "phoneNumber": "+380999999999", "masterKey": "88888888", "roles": [{"roleId": "SuperAdmin", "resource": "deployment/*", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": {"$numberLong": "1642495679877"}}}, {"_id": {"$oid": "5e84b0dab8dfa268b1180536"}, "email": "<EMAIL>", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": {"$numberLong": "1662850800"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "email": "<EMAIL>", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": {"$numberLong": "1663196400"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789d"}, "email": "<EMAIL>", "givenName": "<PERSON>", "familyName": "MrClini<PERSON>", "roles": [{"roleId": "Manager", "resource": "deployment/5d386cc6ff885918d96edb2c", "isActive": true}, {"roleId": "Manager", "resource": "deployment/5d386cc6ff885918d96edb2d", "isActive": true}], "timezone": "UTC"}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789e"}, "email": "<EMAIL>", "roles": [{"roleId": "DataManager", "resource": "deployment/5d386cc6ff885918d96edb2c", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": {"$numberLong": "1663110000"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c342220"}, "email": "<EMAIL>", "roles": [{"roleId": "Proxy", "resource": "deployment/5d386cc6ff885918d96edb2c", "isActive": true}, {"roleId": "Proxy", "resource": "user/5e84b0dab8dfa268b1180536", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": {"$numberLong": "1663196400"}}}, {"_id": {"$oid": "5e84b0dab8dfa268b1280539"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "phoneNumber": "+380989999989", "masterKey": "88888888", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "isActive": true}], "timezone": "UTC"}]}