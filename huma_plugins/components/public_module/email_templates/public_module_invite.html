<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Serif+TC&display=swap"
      rel="stylesheet"
    />
    <meta content="width=device-width" name="viewport" />
    <meta name="color-scheme" content="light dark" />
    <meta name="supported-color-schemes" content="light dark" />
    <!--[if !mso]><!-->
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <!--<![endif]-->
    <title></title>
    <!--[if !mso]><!-->
    <!--<![endif]-->
    <style type="text/css">
      @font-face {
        font-family: "Noto Serif TC";
        src: url("https://static.cdn.huma.com/notoseriftc-light.otf")
          format("truetype");
      }
      @font-face {
        font-family: "Noto Sans", sans-serif;
        src: url("https://static.cdn.huma.com/notosans-regular.ttf")
          format("truetype");
      }
      @font-face {
        font-family: "Noto Sans-Medium";
        src: url("https://static.cdn.huma.com/notosans-medium.ttf")
          format("truetype");
      }
      :root {
        color-scheme: light dark;
        supported-color-schemes: light dark;
      }
      body {
        margin: 0;
        padding: 0;
      }

      table,
      td,
      tr {
        vertical-align: top;
        border-collapse: collapse;
      }

      * {
        line-height: inherit;
      }

      a[x-apple-data-detectors="true"] {
        color: inherit !important;
        text-decoration: none !important;
      }

      .card-radius {
        overflow: hidden;
        border-radius: 32px;
      }
    </style>
    <style id="media-query" type="text/css">
      @media (prefers-color-scheme: dark) {
        table,
        tr,
        td,
        .dark-theme-text {
          background-color: #2f3033 !important;
          color: #ffffff !important;
        }
        .dark-theme-link {
          color: #ffffff !important;
        }
        .dark-button {
          background-color: #ebebeb !important;
          color: #2f3033 !important;
        }
        .dark-mode-container,
        .divider table {
          border-color: #6a6d72 !important;
        }
      }
      @media (max-width: 705px) {
        .block-grid,
        .col {
          min-width: 320px !important;
          max-width: 100% !important;
          display: block !important;
        }

        .block-grid {
          width: 100% !important;
        }

        .col {
          width: 100% !important;
        }

        .col > div {
          margin: 0 auto;
        }

        img.fullwidth,
        img.fullwidthOnMobile {
          max-width: 100% !important;
        }

        .no-stack .col {
          min-width: 0 !important;
          display: table-cell !important;
        }

        .no-stack.two-up .col {
          width: 50% !important;
        }

        .no-stack .col.num2 {
          width: 16.6% !important;
        }

        .no-stack .col.num3 {
          width: 25% !important;
        }

        .no-stack .col.num4 {
          width: 33% !important;
        }

        .no-stack .col.num5 {
          width: 41.6% !important;
        }

        .no-stack .col.num6 {
          width: 50% !important;
        }

        .no-stack .col.num7 {
          width: 58.3% !important;
        }

        .no-stack .col.num8 {
          width: 66.6% !important;
        }

        .no-stack .col.num9 {
          width: 75% !important;
        }

        .no-stack .col.num10 {
          width: 83.3% !important;
        }

        .video-block {
          max-width: none !important;
        }

        .mobile_hide {
          min-height: 0px;
          max-height: 0px;
          max-width: 0px;
          display: none;
          overflow: hidden;
          font-size: 0px;
        }

        .desktop_hide {
          display: block !important;
          max-height: none !important;
        }

        .card-radius {
          border-radius: 16px;
        }
      }
    </style>
    <style id="icon-media-query" type="text/css">
      @media (max-width: 705px) {
        .icons-inner {
          text-align: center;
        }

        .icons-inner td {
          margin: 0 auto;
        }
      }
    </style>
  </head>
  <body
    class="clean-body"
    dir="%textDirection"
    style="
      margin: 0;
      padding: 15px;
      -webkit-text-size-adjust: 100%;
      background-color: #f8f8f8;
      display: flex;
      justify-content: center;
    "
  >
    <!--[if IE]><div class="ie-browser"><![endif]-->
    <table
      bgcolor="#FFFFFF"
      cellpadding="0"
      cellspacing="0"
      class="nl-container card-radius"
      role="presentation"
      style="
        table-layout: fixed;
        vertical-align: top;
        min-width: 320px;
        border-spacing: 0;
        border-collapse: collapse;
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
        background-color: #ffffff;
        width: auto;
      "
      valign="top"
      width="100%"
    >
      <tbody>
        <tr style="vertical-align: top" valign="top">
          <td style="word-break: break-word; vertical-align: top" valign="top">
            <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color:#FFFFFF"><![endif]-->
            <div style="background-color: transparent; overflow: hidden">
              <div
                class="block-grid"
                style="
                  min-width: 320px;
                  max-width: 685px;
                  overflow-wrap: break-word;
                  word-wrap: break-word;
                  word-break: break-word;
                  margin: 0 auto;
                  width: 100%;
                  background-color: transparent;
                "
              >
                <div
                  style="
                    border-collapse: collapse;
                    display: table;
                    width: 100%;
                    background-color: transparent;
                  "
                >
                  <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:685px"><tr class="layout-full-width" style="background-color:transparent"><![endif]-->
                  <!--[if (mso)|(IE)]><td align="center" width="685" style="background-color:transparent;width:685px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
                  <div
                    class="col num12"
                    style="
                      min-width: 320px;
                      max-width: 685px;
                      display: table-cell;
                      vertical-align: top;
                      width: 685px;
                    "
                  >
                    <div style="width: 100% !important">
                      <!--[if (!mso)&(!IE)]><!-->
                      <!--<![endif]-->
                      <table
                        cellpadding="0"
                        cellspacing="0"
                        role="presentation"
                        style="
                          table-layout: fixed;
                          vertical-align: top;
                          border-spacing: 0;
                          border-collapse: collapse;
                          mso-table-lspace: 0pt;
                          mso-table-rspace: 0pt;
                        "
                        valign="top"
                        width="100%"
                      >
                        <tr style="vertical-align: top" valign="top">
                          <td
                            text-align="%contentAlign"
                            style="
                              word-break: break-word;
                              vertical-align: top;
                              padding-top: 0px;
                              padding-right: 0px;
                              padding-bottom: 0px;
                              padding-left: 0px;
                            "
                            valign="top"
                          >
                            <!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:NaNpx;padding-right:NaNpx;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
                            <!--[if !vml]><!-->
                            <table
                              cellpadding="0"
                              cellspacing="0"
                              class="icons-inner"
                              role="presentation"
                              style="
                                table-layout: fixed;
                                vertical-align: top;
                                border-spacing: 0;
                                border-collapse: collapse;
                                mso-table-lspace: 0pt;
                                mso-table-rspace: 0pt;
                                display: inline-block;
                                margin-right: -4px;
                                padding-left: NaNpx;
                                padding-right: NaNpx;
                              "
                              valign="top"
                            >
                              <!--<![endif]-->
                              <tr style="vertical-align: top" valign="top">
                                <td
                                  align="center"
                                  style="
                                    word-break: break-word;
                                    vertical-align: top;
                                    text-align: center;
                                    padding-top: 32px;
                                    padding-bottom: 0;
                                    padding-left: 24px;
                                    padding-right: 24px;
                                  "
                                  valign="top"
                                >
                                  <img
                                    align="center"
                                    class="icon"
                                    height="40"
                                    src="https://static.cdn.huma.com/fill_8_dark.png"
                                    style="border: 0"
                                  />
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                      </table>

                      <div
                        style="
                          border-top: 0px solid transparent;
                          border-left: 0px solid transparent;
                          border-bottom: 0px solid transparent;
                          border-right: 0px solid transparent;
                          padding-top: 10px;
                          padding-bottom: 10px;
                          padding-right: 0px;
                          padding-left: 0px;
                        "
                      >
                        <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Georgia, 'Times New Roman', serif"><![endif]-->
                        <div
                          style="
                            color: #555555;
                            line-height: 1.2;
                            padding-top: 5px;
                            padding-right: 24px;
                            padding-bottom: 0px;
                            padding-left: 24px;
                          "
                        >
                          <div
                            class="dark-theme-text"
                            style="
                              line-height: 1.2;
                              font-size: 32px;
                              font-family: 'Noto Serif TC', sans-serif;
                              color: #2f3033;
                              mso-line-height-alt: 14px;
                            "
                          >
                            <p
                              style="
                                font-size: 32px;
                                line-height: 36px;
                                font-family: 'Noto Serif TC', sans-serif;
                                word-break: break-word;
                                mso-line-height-alt: 36px;
                                margin: 0;
                                font-weight: 300;
                              "
                            >
                              <span
                                style="font-size: 32px; letter-spacing: -1.28px"
                              >
                                %subtitle
                                <!-- Welcome to Huma -->
                              </span>
                            </p>
                          </div>
                        </div>
                      </div>
                      <!--[if mso]></td></tr></table><![endif]-->
                      <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Verdana, sans-serif"><![endif]-->
                      <div
                        style="
                          color: #2f3033;
                          font-family: 'Noto Sans', sans-serif;
                          line-height: 1.2;
                          padding-top: 0;
                          padding-right: 24px;
                          padding-bottom: 5px;
                          padding-left: 24px;
                        "
                      >
                        <div
                          class="dark-theme-text"
                          style="
                            line-height: 1.2;
                            font-size: 16px;
                            font-family: 'Noto Sans', sans-serif;
                            color: #2f3033;
                            mso-line-height-alt: 14px;
                          "
                        >
                          <p
                            style="
                              font-size: 16px;
                              line-height: 24px;
                              font-family: 'Noto Sans', sans-serif;
                              word-break: break-word;
                              margin: 0;
                            "
                          >
                            <span
                              style="font-size: 16px; letter-spacing: 0.5px"
                            >
                              %body
                              <!-- You're invited to Huma, where we help you take
                              charge of your health. Start today to get insights
                              for your condition. -->
                            </span>
                          </p>
                        </div>
                      </div>
                      <div style="padding: 12px 24px">
                        <div
                          class="dark-mode-container"
                          style="
                            /*border: 1px solid #ebebeb;*/
                            border-radius: 12px;
                            text-align: center;
                            padding: 24px;
                          "
                        >
                          <div class="mobile_hide">
                            <div
                              align="center"
                              class="button-container"
                              style="/*padding-top: 19px; */ padding-bottom: 0"
                            >
                              <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="border-spacing: 0; border-collapse: collapse; mso-table-lspace:0pt; mso-table-rspace:0pt;"><tr><td style="padding-top: 10px; padding-right: 10px; padding-bottom: 10px; padding-left: 10px" align="center"><v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="%buttonLink" style="height:36pt; width:286.5pt; v-text-anchor:middle;" arcsize="50%" stroke="false" fillcolor="#424347"><w:anchorlock/><v:textbox inset="0,0,0,0"><center style="color:#ffffff; font-family:Verdana, sans-serif; font-size:16px"><!
                              [endif]--><a
                                href="%buttonLink"
                                class="dark-button"
                                style="
                                  -webkit-text-size-adjust: none;
                                  text-decoration: none;
                                  display: block;
                                  color: #ffffff;
                                  background-color: #424347;
                                  border-radius: 24px;
                                  -webkit-border-radius: 24px;
                                  -moz-border-radius: 24px;
                                  width: auto;
                                  max-width: 295px;
                                  padding-top: 10px;
                                  padding-bottom: 10px;
                                  font-family: 'Noto Sans', sans-serif;
                                  text-align: center;
                                  mso-border-alt: none;
                                  word-break: keep-all;
                                "
                                target="_blank"
                                ><span
                                  style="
                                    padding-left: 10px;
                                    padding-right: 10px;
                                    font-size: 16px;
                                    display: inline-block;
                                  "
                                  ><span
                                    style="
                                      font-size: 16px;
                                      line-height: 1.8;
                                      font-family: 'Noto Sans', sans-serif;
                                      word-break: break-word;
                                      mso-line-height-alt: 29px;
                                      letter-spacing: 0.5px;
                                    "
                                  >
                                    %buttonText
                                    <!--Get started with Huma-->
                                  </span></span
                                ></a
                              >
                              <!--[if mso]></center></v:textbox></v:roundrect></td></tr></table><![endif]-->
                            </div>
                          </div>
                          <!--[if !mso]><!-->
                          <div
                            class="desktop_hide"
                            style="
                              mso-hide: all;
                              display: none;
                              max-height: 0px;
                              overflow: hidden;
                            "
                          >
                            <div
                              align="center"
                              class="button-container"
                              style="
                                /*padding-top: 30px;*/
                                padding-right: 10px;
                                /*padding-bottom: 10px;*/
                                padding-left: 10px;
                              "
                            >
                              <a
                                href="%buttonLink"
                                class="dark-button"
                                style="
                                  -webkit-text-size-adjust: none;
                                  text-decoration: none;
                                  display: block;
                                  color: #ffffff;
                                  background-color: #424347;
                                  border-radius: 24px;
                                  -webkit-border-radius: 24px;
                                  -moz-border-radius: 24px;
                                  width: auto;
                                  max-width: 295px;
                                  padding-top: 10px;
                                  padding-bottom: 10px;
                                  font-family: 'Noto Sans', sans-serif;
                                  text-align: center;
                                  mso-border-alt: none;
                                  word-break: keep-all;
                                "
                                target="_blank"
                                ><span
                                  style="
                                    padding-left: 10px;
                                    padding-right: 10px;
                                    font-size: 16px;
                                    display: inline-block;
                                  "
                                  ><span
                                    style="
                                      font-size: 16px;
                                      line-height: 1.8;
                                      font-family: 'Noto Sans', sans-serif;
                                      word-break: break-word;
                                      mso-line-height-alt: 29px;
                                    "
                                  >
                                    %buttonText
                                    <!--Get started with Huma-->
                                  </span></span
                                ></a
                              >
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        style="
                          color: #2f3033;
                          font-family: 'Noto Sans', sans-serif;
                          line-height: 1.2;
                          padding-top: 1px;
                          padding-right: 24px;
                          padding-bottom: 5px;
                          padding-left: 24px;
                        "
                      >
                        <div
                          class="dark-theme-text"
                          style="
                            line-height: 1.2;
                            font-size: 16px;
                            font-family: 'Noto Sans', sans-serif;
                            color: #2f3033;
                            mso-line-height-alt: 14px;
                          "
                        >
                          <p
                            style="
                              font-size: 16px;
                              line-height: 24px;
                              font-family: 'Noto Sans', sans-serif;
                              word-break: break-word;
                              mso-line-height-alt: 19px;
                              margin: 0;
                            "
                          >
                            <span
                              style="font-size: 16px; letter-spacing: 0.4px"
                            >
                              %secondBody
                              <!--Once you open the Huma app, you will be asked to
                              create a password for your account.-->
                            </span>
                          </p>
                        </div>
                      </div>
                      <div
                        style="border-top: 1px solid #e8e8e8; margin: 11px 24px"
                      ></div>
                      <div
                        style="
                          color: #2f3033;
                          font-family: 'Noto Sans', sans-serif;
                          line-height: 1.2;
                          padding-top: 5px;
                          padding-right: 24px;
                          padding-left: 24px;
                        "
                      >
                        <div
                          class="dark-theme-text"
                          style="
                            line-height: 1.2;
                            font-size: 16px;
                            font-family: 'Noto Sans', sans-serif;
                            color: #2f3033;
                            mso-line-height-alt: 14px;
                          "
                        >
                          <p
                            style="
                              font-size: 16px;
                              line-height: 24px;
                              font-family: 'Noto Sans', sans-serif;
                              word-break: break-word;
                              mso-line-height-alt: 19px;
                              margin-bottom: 24px;
                              margin-top: 0;
                            "
                          >
                            <span
                              style="
                                font-size: 16px;
                                font-style: normal;
                                line-height: 24px;
                                margin-bottom: 24px;
                                letter-spacing: 0.2px;
                              "
                            >
                              %mainBodyAlternativeText
                              <!--Having issues with the button above or signing up
                              to Huma? Click or copy-paste the following URL into your
                              mobile phone's internet browser:-->
                            </span>
                          </p>
                        </div>
                      </div>
                      <div
                        style="
                          color: #2f3033;
                          font-family: 'Noto Sans', sans-serif;
                          line-height: 1.2;
                          padding-right: 24px;
                          padding-bottom: 5px;
                          padding-left: 24px;
                        "
                      >
                        <div
                          class="dark-theme-text"
                          style="
                            line-height: 1.2;
                            font-size: 16px;
                            font-family: 'Noto Sans', sans-serif;
                            color: #2f3033;
                            mso-line-height-alt: 14px;
                          "
                        >
                          <p
                            style="
                              font-size: 16px;
                              line-height: 24px;
                              font-family: 'Noto Sans', sans-serif;
                              word-break: break-word;
                              mso-line-height-alt: 19px;
                              margin: 0;
                              text-decoration-line: underline;
                            "
                          >
                          <a href="%buttonLink" target="_blank">
                            <span style="font-size: 16px">
                              %buttonLink
                            </span>
                            </a>
                          </p>
                        </div>
                      </div>
                      <!-- Backup text and shortenedCode link ending -->
                    </div>
                  </div>
                  <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                  <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
                </div>
              </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
          </td>
        </tr>
      </tbody>
    </table>
    <!--[if (IE)]></div><![endif]-->
  </body>
</html>
