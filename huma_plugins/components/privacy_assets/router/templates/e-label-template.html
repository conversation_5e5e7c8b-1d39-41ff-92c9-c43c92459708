<!DOCTYPE html>
<html lang="en">
  <head>
    <title><PERSON><PERSON></title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;700&family=Source+Serif+Pro:wght@200&display=swap"
      rel="stylesheet"
    />
    <style>
      @font-face {
        font-family: "VictorSerifSmooth-Regular";
        src: url("https://static.cdn.huma.com/victorserifsmooth-regular.otf");
      }
      @font-face {
        font-family: "Noto Sans-Medium";
        src: url("https://static.cdn.huma.com/notosans-medium.ttf");
      }
      @font-face {
        font-family: "NotoSans-Light";
        src: url("https://static.cdn.huma.com/notosans-light.ttf");
      }
      @font-face {
        font-family: "Arial";
        src: url("https://static.cdn.huma.com/arial.ttf");
      }

      body {
        color: #2f3033;
        font-family: "NotoSans-Light";
      }
      .container {
        width: 80%;
        max-width: 716px;
        margin: 0 auto;
      }
      .font-size-basic {
        font-size: 16px;
        line-height: 26px;
      }
      .font-size-second {
        font-size: 16px;
        line-height: 24px;
      }
      .font-size-version {
        font-size: 16px;
        line-height: 24px;
      }
      .bold-text {
        font-family: "Noto Sans-Medium";
      }
      .flex-column {
        display: flex;
        flex-direction: column;
      }
      .row {
        display: flex;
        align-items: flex-start;
        width: 100%;
        margin-top: 72px;
      }
      .row-centered {
        align-items: center;
      }
      .for-img {
        width: 173px;
      }
      .for-img-small {
        display: none;
      }
      .for-text {
        align-self: center;
        padding-left: 69px;
        width: 224px;
      }
      .subtitle {
        padding-bottom: 8px;
      }
      .version {
        text-align: center;
      }
      .build-number {
        text-align: center;
      }
      .footer-images {
        align-items: center;
        padding-bottom: 113px;
      }
      .text2 {
        padding: 11px 0px 75px 0px;
      }
      .intended-use {
        font-family: "VictorSerifSmooth-Regular", sans-serif;
        font-size: 24px;
        line-height: 28px;
        padding-top: 83px;
        text-align: center;
      }
      .IFU-link {
        text-decoration: underline;
        color: #000;
        font-size: 18px;
        line-height: 26px;
        font-family: "Arial", sans-serif;
      }
      .pt-0297 {
        padding-top: 19px;
      }
      .not-found-container {
        display: none;
        margin-top: 240px;
      }
      .text-column {
        margin-left: 108px;
      }
      .text-column-title {
        font-family: "VictorSerifSmooth-Regular", sans-serif;
        font-size: 30px;
        line-height: 40px;
        margin-bottom: 24px;
      }
      .text-column-subtitle {
        font-size: 20px;
        line-height: 28px;
        margin-bottom: 24px;
      }
      .template-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .empty-box {
        width: 40px;
      }
      .language-box {
        width: 40px;
        height: 40px;
        box-sizing: border-box;
        border: 2px solid black;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      @media (max-width: 500px) {
        .font-size-basic {
          font-size: 10px;
          line-height: 16px;
        }
        .font-size-second {
          font-size: 10px;
          line-height: 16px;
        }
        .font-size-version {
          font-size: 14px;
          line-height: 22px;
        }
        .row {
          margin-top: 55px;
        }
        .for-text {
          padding-left: 30px;
          width: 186px;
        }
        .subtitle {
          padding-bottom: 4px;
        }
        .version {
          padding-top: 47px;
        }
        .footer-images {
          padding-bottom: 69px;
        }
        .text2 {
          padding: 17px 0px 57px 0px;
          font-size: 14px;
          line-height: 22px;
        }
        .intended-use {
          line-height: 32px;
          padding-top: 63px;
        }
        .for-img {
          width: 82px;
        }
        .for-img-big {
          display: none;
        }
        .for-img-small {
          display: block;
        }
        .pt-0297 {
          padding-top: 14px;
        }
      }
      .nz-tiny-scrollbar::-webkit-scrollbar {
        width: 2px;
        background-color: transparent;
      }
      .nz-tiny-scrollbar::-webkit-scrollbar-thumb {
        background-color: #6a6d72;
        border-radius: 14px;
      }
      .language {
        float: right;
        margin-right: 0;
        text-align: right;
      }
    </style>
  </head>

  <body class="nz-tiny-scrollbar">
    {% for key, value in language_dict.items() %}
    <div class="container">
      <div class="version font-size-version">
        <div class="template-header">
          <div class="empty-box"></div>
          <div><span class="bold-text">Huma |</span> Ver No: <span id="version"></span>{{ version }}</div>

          <div class="language-box">
            <span class="bold-text" id="language">{{ key }}</span>
          </div>
        </div>
        {% if show_mobile_build_number %}
        <div id="buildNumberSection" class="build-number">
          <span class="bold-text">Build number : </span> <span id="buildNumber">{{ mobile_build_number }}</span>
        </div>
        {% endif %}
      </div>
      <div class="row">
        <div class="for-img">
          <img
            class="for-img-big"
            width="173"
            alt="date"
            onerror="handleConnectionError()"
            src="https://static.cdn.huma.com/e-label/elabel1.png"
          />
          <img
            class="for-img-small"
            width="82"
            alt="date"
            src="https://static.cdn.huma.com/e-label/elabel1.png"
          />
        </div>
        <div class="flex-column for-text">
          <div class="subtitle bold-text font-size-basic" id="SomeId">
            {{ release_date }}
          </div>
        </div>
      </div>

      <div class="row row-centered">
        <div class="for-img">
          <img
            class="for-img-big"
            width="173"
            alt="Therapeutics Limited"
            onerror="handleConnectionError()"
            src="https://static.cdn.huma.com/e-label/elabel2.png"
          />
          <img
            class="for-img-small"
            width="82"
            alt="Therapeutics Limited"
            src="https://static.cdn.huma.com/e-label/elabel2.png"
          />
        </div>
        <div class="flex-column for-text">
          <div class="subtitle bold-text font-size-second">
            Huma Therapeutics Limited
          </div>
          <div class="font-size-second">
            13th Floor, Millbank Tower<br />
            21-24 Millbank <br />
            London, United Kingdom<br />
            SW1P 4QP
          </div>
        </div>
      </div>

      <div class="row row-centered">
        <div class="for-img">
          <img
            class="for-img-big"
            width="173"
            alt="Advena Limited"
            onerror="handleConnectionError()"
            src="https://static.cdn.huma.com/e-label/elabelrep.png"
          />
          <img
            class="for-img-small"
            width="82"
            alt="Advena Limited"
            src="https://static.cdn.huma.com/e-label/elabelrep.png"
          />
        </div>
        <div class="flex-column for-text">
          <div class="subtitle bold-text font-size-second">
            Advena Limited
          </div>
          <div class="font-size-second">
            Tower Business Centre,<br>2nd Flr., Tower Street, Swatar,<br>BKR 401 Malta
          </div>
        </div>
      </div>

      {% if authorised_rep_enabled %}
      <div id="authorisedRepSection" class="row row-centered">
        <div class="for-img">
          <img
            class="for-img-big"
            width="173"
            alt="Therapeutics Limited"
            onerror="handleConnectionError()"
            src="{{ authorised_rep_icon_url }}"
          />
          <img
            class="for-img-small"
            width="82"
            alt="Therapeutics Limited"
            src="{{ authorised_rep_icon_url }}"
          />
        </div>
        <div class="flex-column for-text">
          <div class="subtitle bold-text font-size-second">
            {{ authorised_rep_address_title }}
          </div>
          <div class="font-size-second">
            {{ authorised_rep_address }}
          </div>
        </div>
      </div>
      {% endif %}

      <div class="row">
        <div class="for-img">
          <img
            class="for-img-big"
            alt="MD"
            onerror="handleConnectionError()"
            src="https://static.cdn.huma.com/e-label/elabelmd.png"
          />
          <img
            class="for-img-small"
            width="65"
            alt="MD"
            src="https://static.cdn.huma.com/e-label/elabelmd.png"
          />
        </div>
        <div class="flex-column for-text">
          <img
            class="for-img-big"
            width="139"
            alt="Electronic IFU"
            onerror="handleConnectionError()"
            src="https://static.cdn.huma.com/e-label/elabeli.png"
          />
          <img
            class="for-img-small"
            width="60"
            alt="Electronic IFU"
            src="https://static.cdn.huma.com/e-label/elabeli.png"
          />

          <a
            href="{{ ifu_url }}"
            target="_blank"
            title="Electronic IFU"
            class="font-size-basic IFU-link"
            rel="noopener"
            >Electronic IFU
          </a>
        </div>
      </div>
      <div class="row">
        <div class="for-img">
          <img
            class="for-img-big"
            alt="UDI"
            onerror="handleConnectionError()"
            src="https://static.cdn.huma.com/e-label/elabeludi.png"
          />
          <img
            class="for-img-small"
            width="65"
            alt="UDI"
            src="https://static.cdn.huma.com/e-label/elabeludi.png"
          />
        </div>
        <div class="flex-column for-text">
          <img
            class="for-img-big"
            width="217"
            alt="Barcode"
            onerror="handleConnectionError()"
            src="{{ barcode_url }}"
          />
          <img
            class="for-img-small"
            width="110"
            alt="Barcode"
            src="{{ barcode_url }}"
          />
        </div>
      </div>

      <div>
        <div class="intended-use">Intended Use</div>
        <div class="font-size-basic text2">
          <p>{{ value["paragraph1"] }}</p>
          <p>{{ value["paragraph2"] }}</p>
          <p>{{ value["paragraph3"] }}</p>
          <p>{{ value["paragraph4"] }}</p>
          <p>{{ value["paragraph5"] }}</p>
          <p>{{ value["paragraph6"] }}</p>
        </div>
      </div>

      <div class="flex-column footer-images">
        <img
          class="for-img-big"
          width="104"
          alt="CE"
          onerror="handleConnectionError()"
          src="https://static.cdn.huma.com/e-label/elabelce.png"
        />
        <img
          class="for-img-big pt-0297"
          width="72"
          alt="0297"
          onerror="handleConnectionError()"
          src="https://static.cdn.huma.com/e-label/0297.png"
        />

        <img
          class="for-img-small"
          width="65"
          alt="CE"
          src="https://static.cdn.huma.com/e-label/elabelce.png"
        />
        <img
          class="for-img-small pt-0297"
          width="45"
          alt="0297"
          src="https://static.cdn.huma.com/e-label/0297.png"
        />
      </div>
    </div>
    {% endfor %}

    <div class="not-found-container">
      <div class="text-column">
        <div class="text-column-title">Something went wrong</div>
        <div class="text-column-subtitle">
          Oops. Unfortunately an error has occured. Please sign out and back in
          to continue.
        </div>
      </div>
    </div>
  </body>
  <script>
    document.fonts.onloadingdone = function (fontFaceSetEvent) {
      if (fontFaceSetEvent.fontfaces.length < 4) {
        handleConnectionError();
      }
    };

    function handleConnectionError() {
      if (window.screen.width > 500) {
        let handleConnectionErrorElem = document.querySelector(
          ".not-found-container"
        );
        handleConnectionErrorElem.style.display = "flex";
        let containerElem = document.querySelector(".container");
        containerElem.style.display = "none";
      }
    }

    //This variable must be set with BE
    let shouldBeRed = "{{ is_approved }}";

    let text = document.getElementById("SomeId");
    text.style.color = shouldBeRed === "True" ? "red" : "#2f3033";
  </script>
</html>
