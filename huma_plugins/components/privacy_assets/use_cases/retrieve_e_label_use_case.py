import json
from functools import cached_property
from pathlib import Path

from flask import url_for
from jinja2 import Environment, PackageLoader, select_autoescape

from huma_plugins.components.config.config import ExtensionServerConfig
from huma_plugins.components.privacy_assets.privacy_assets_manager import (
    PrivacyAssetsManager,
)
from huma_plugins.components.privacy_assets.router.request_object import (
    RetrieveELabelRequestObject,
)
from huma_plugins.components.privacy_assets.router.response_object import (
    RetrieveELabelResponseObject,
)
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams
from sdk.deployment.repository.deployment_repository import DeploymentRepository
from sdk.organization.dtos import OrganizationDTO
from sdk.organization.repository.organization_repository import (
    OrganizationRepository,
)
from sdk.phoenix.config.server_config import PhoenixServerConfig
from sdk.storage.use_case.storage_request_objects import GetSignedUrlRequestObject
from sdk.storage.use_case.storage_use_cases import GetSignedUrlUseCase
from sdk.versioning.models.version import Version


class RetrieveELabelUseCase(UseCase):
    request_object: RetrieveELabelRequestObject
    template_name = "e-label-template.html"
    language_path = Path(__file__).parent.joinpath("assets/elabel_language.json")

    @autoparams()
    def __init__(
        self,
        version: Version,
        server_config: PhoenixServerConfig,
        privacy_assets_manager: PrivacyAssetsManager,
        organization_repo: OrganizationRepository,
        deployment_repo: DeploymentRepository,
    ):
        self.version = version
        self.server_config: ExtensionServerConfig = server_config  # type: ignore
        self.jinja_env = Environment(
            loader=PackageLoader(privacy_assets_manager.resources_path),
            autoescape=select_autoescape(),
        )
        self.template = self.jinja_env.get_template("e-label-template.html")
        self.organization_repo = organization_repo
        self.deployment_repo = deployment_repo
        self.privacy_manager = privacy_assets_manager

    def process_request(self, request_object: RetrieveELabelRequestObject):
        html = self.template.render(
            version=self.version.server.without_micro_version,
            release_date=self.server_config.server.releaseDate,
            is_approved=False,
            language_dict=self._languages,
            ifu_url=self._ifu_url,
            barcode_url=self._barcode_url,
            authorised_rep_enabled=self._authorised_rep_enabled,
            authorised_rep_icon_url=self._icon_url,
            authorised_rep_address_title=self._authorised_rep_address_title,
            authorised_rep_address=self._authorised_rep_address,
            show_mobile_build_number=self._show_mobile_build_number,
            mobile_build_number=request_object.buildNumber,
        )
        return RetrieveELabelResponseObject(html)

    @cached_property
    def _organization(self) -> OrganizationDTO | None:
        if self.request_object.organizationId:
            return self.organization_repo.retrieve_organization(organization_id=self.request_object.organizationId)

        return self.organization_repo.retrieve_organization_by_deployment_id(
            deployment_id=self.request_object.deploymentId
        )

    @property
    def _barcode_url(self) -> str:
        if url := self.server_config.server.privacyAssets.barcodeUrl:
            return url
        return "https://static.cdn.huma.com/e-label/udi-di humaii barcode g128 1.png"

    @property
    def _show_mobile_build_number(self) -> bool:
        if (org := self._organization) and org.authorisedRep and org.authorisedRep.showBuildNumber:
            return self.request_object.buildNumber and self.request_object.is_mobile

        return False

    @property
    def _ifu_url(self) -> str:
        pdf_type = self.request_object.type.value
        return url_for("privacy_assets.instructions_for_use", type=pdf_type)

    @property
    def _authorised_rep_enabled(self) -> bool:
        if (org := self._organization) and org.authorisedRep:
            return True

        return False

    @property
    def _icon_url(self) -> str:
        if not self._authorised_rep_enabled:
            return ""

        icon_object = self._organization.authorisedRep.iconObject
        req = GetSignedUrlRequestObject.from_dict(
            {
                GetSignedUrlRequestObject.BUCKET: icon_object.bucket,
                GetSignedUrlRequestObject.FILENAME: icon_object.key,
                GetSignedUrlRequestObject.HOST: self.request_object.host,
            }
        )
        rsp = GetSignedUrlUseCase().execute(req)
        return rsp.value.url

    @property
    def _authorised_rep_address_title(self) -> str:
        if self._authorised_rep_enabled:
            return self._organization.authorisedRep.addressTitle

        return ""

    @property
    def _authorised_rep_address(self) -> str:
        if self._authorised_rep_enabled:
            return self._organization.authorisedRep.address

        return ""

    @property
    def _languages(self) -> dict:
        with open(self.privacy_manager.language_path, "r") as file:
            return json.loads(file.read())
