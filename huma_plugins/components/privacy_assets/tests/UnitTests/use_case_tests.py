import unittest
from dataclasses import field
from unittest.mock import MagicMock

from huma_plugins.components.privacy_assets.config.config import PrivacyAssetsConfig
from huma_plugins.components.privacy_assets.privacy_assets_manager import (
    PrivacyAssetsManager,
)
from huma_plugins.components.privacy_assets.router.request_object import (
    RetrieveELabelRequestObject,
)
from huma_plugins.components.privacy_assets.use_cases.retrieve_e_label_use_case import (
    RetrieveELabelUseCase,
)
from sdk import convertibleclass
from sdk.phoenix.config.server_config import Server, PhoenixServerConfig
from sdk.versioning.models.version import Version

BARCODE_URL = "https://image.server/barcode.png"


@convertibleclass
class TestExtensionServer(Server):
    privacyAssets: PrivacyAssetsConfig = field(default_factory=PrivacyAssetsConfig)


class RetrieveELabelUseCaseTestCase(unittest.TestCase):
    def setUp(self):
        version = Version(server="1.0.0", api="1.0.0", build="1.0.0")
        self._config = PhoenixServerConfig(
            server=TestExtensionServer(
                releaseDate="2021-01-01",
                privacyAssets=PrivacyAssetsConfig(barcodeUrl=BARCODE_URL),
            )
        )
        self.use_case = RetrieveELabelUseCase(
            version,
            self._config,
            PrivacyAssetsManager(self._config.server.privacyAssets),
            MagicMock(),
            MagicMock(),
        )

    def test_barcode_url(self):
        self.use_case.request_object = self._request_object()
        self.assertEqual(BARCODE_URL, self.use_case._barcode_url)

    def test_default_barcode_url(self):
        self.use_case.request_object = self._request_object()
        self._config.server.privacyAssets.barcodeUrl = None
        self.assertEqual(
            "https://static.cdn.huma.com/e-label/udi-di humaii barcode g128 1.png",
            self.use_case._barcode_url,
        )

    @staticmethod
    def _request_object():
        return RetrieveELabelRequestObject(host="test.com")
