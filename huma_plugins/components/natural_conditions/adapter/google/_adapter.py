import logging
from datetime import datetime as dt
from typing import Optional

import googlemaps
from requests import Response

from huma_plugins.components.location.adapter.config import GoogleMapsConfig
from huma_plugins.components.natural_conditions.adapter._adapter import (
    NaturalConditionsAdapter,
)
from huma_plugins.components.natural_conditions.models import (
    AirQuality,
    AqiByHour,
    AqiData,
    HeatmapTileImage,
    Index,
    IndexData,
    PlantInfo,
    PollenData,
    Type,
)
from sdk.common.exceptions.exceptions import GoogleEnvironmentException
from sdk.common.localization.utils import Language
from sdk.common.utils.common_functions_utils import convert_rgb_to_hex
from sdk.common.utils.json_utils import camelize

logger = logging.getLogger(__name__)

INDEXES_KEY = "indexes"
HEALTH_RECOMMENDATIONS_KEY = "healthRecommendations"
DATETIME_KEY = "dateTime"
AQI_KEY = "aqi"
COLOR_KEY = "color"
CATEGORY_KEY = "category"
POLLEN_INDEX = "indexInfo"
DAILY_INFO = "dailyInfo"


def _get_aqi_model_data(data: dict) -> dict:
    return {
        AqiByHour.DATETIME: data[DATETIME_KEY],
        AqiByHour.INDEXES: {
            IndexData.AQI: {
                AqiData.AQI: data[INDEXES_KEY][0][AQI_KEY],
                AqiData.COLOR: data[INDEXES_KEY][0][COLOR_KEY],
                AqiData.CATEGORY: data[INDEXES_KEY][0][CATEGORY_KEY],
            }
        },
        AqiByHour.HEALTH_RECOMMENDATIONS: data.get(HEALTH_RECOMMENDATIONS_KEY),
    }


class GoogleNaturalConditionsAdapter(NaturalConditionsAdapter):
    def __init__(self, config: GoogleMapsConfig):
        self.config = config
        self.client = googlemaps.Client(key=self.config.apiKey)
        self.domain_aqi_api = "https://airquality.googleapis.com"
        self.domain_pollen_api = "https://pollen.googleapis.com"
        self.air_quality_path = "/v1/currentConditions:lookup"
        self.pollen_path = "/v1/forecast:lookup"

    def _environment_request(self, path: str, base_url: str, params: dict = None, json_body: dict = None) -> Response:
        options = {
            "url": path,
            "params": params or {},
            "post_json": json_body,
            "base_url": base_url,
            "extract_body": lambda x: x,
        }
        return self.client._request(**options)

    def retrieve_air_quality_data(self, latitude: float, longitude: float) -> AirQuality:
        params = {"location": {"latitude": latitude, "longitude": longitude}}
        if self.config.includeHealthRecommendations:
            params.update({"extraComputations": ["HEALTH_RECOMMENDATIONS"]})
        response = self._environment_request(path=self.air_quality_path, base_url=self.domain_aqi_api, json_body=params)
        if response.status_code != 200 or not response.json():
            raise GoogleEnvironmentException("No AQI data available")

        data = response.json()
        index = data.get(INDEXES_KEY, [{}])[0]
        rgb_color = index.get(COLOR_KEY)

        if rgb_color:
            data[INDEXES_KEY][0][COLOR_KEY] = convert_rgb_to_hex(rgb_color)

        current = _get_aqi_model_data(data)
        return AirQuality.from_dict({AirQuality.CURRENT: current})

    def retrieve_pollen_data(
        self,
        latitude: float,
        longitude: float,
        language: str = Language.EN,
        days: int = 1,
    ) -> Optional[PollenData]:
        """
        Supported Plants
        https://developers.google.com/maps/documentation/pollen/reference/rest/v1/forecast/lookup#plant
        """
        params = {
            "location.latitude": latitude,
            "location.longitude": longitude,
            "days": days,
            "languageCode": language,
            "plantsDescription": True,
        }
        response = self._environment_request(path=self.pollen_path, base_url=self.domain_pollen_api, params=params)
        if response.status_code != 200 or not response.json():
            return None

        data = response.json()
        date = dt.strftime(dt(**data[DAILY_INFO][0]["date"]), "%Y-%m-%d")
        pollen_types = data[DAILY_INFO][0]["pollenTypeInfo"]
        plants_info = data[DAILY_INFO][0]["plantInfo"]

        types = {}
        for pollen_type in pollen_types:
            if POLLEN_INDEX not in pollen_type:
                continue
            rgb_color = pollen_type[POLLEN_INDEX][COLOR_KEY]
            pollen_type[POLLEN_INDEX][COLOR_KEY] = convert_rgb_to_hex(rgb_color)
            type_name = pollen_type["code"]
            pollen = Type(
                displayName=pollen_type["displayName"].capitalize(),
                index=Index.from_dict(pollen_type[POLLEN_INDEX]),
                plants=self._generate_plants(type_name, plants_info),
            )
            types.update({type_name.lower(): pollen})
        return PollenData(date=date, types=types)

    @staticmethod
    def _generate_plants(type_name: str, plants_info: dict) -> dict[str, PlantInfo]:
        plant_info = filter(
            lambda x: x.get("plantDescription", {}).get("type") == type_name,
            plants_info,
        )
        plants = {}
        for info in plant_info:
            rgb_color = info[POLLEN_INDEX][COLOR_KEY]
            info[POLLEN_INDEX][COLOR_KEY] = convert_rgb_to_hex(rgb_color)
            info[PlantInfo.INDEX] = info[POLLEN_INDEX]
            plant = PlantInfo.from_dict(camelize(info))
            plants.update({info["code"].lower(): plant})
        return plants

    def heatmap_tile(self, zoom: int, x: float, y: float) -> HeatmapTileImage:
        map_type = "UAQI_RED_GREEN"
        path = f"/v1/mapTypes/{map_type}/heatmapTiles/{zoom}/{x}/{y}"
        domain = self.domain_aqi_api
        response = self._environment_request(path=path, base_url=domain)
        if not response.ok:
            raise GoogleEnvironmentException("No Heat map data available")

        return HeatmapTileImage.from_dict(
            {
                HeatmapTileImage.CONTENT: response.content,
                HeatmapTileImage.CONTENT_TYPE: response.headers.get("content-type"),
            }
        )
