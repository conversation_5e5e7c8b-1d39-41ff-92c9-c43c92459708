{"deployment": [{"_id": {"$oid": "5d386cc6ff885918d96edb2c"}, "name": "X", "status": "DEPLOYED", "moduleConfigs": [{"about": "", "configBody": {}, "id": "5e94b2007773091c9a592660", "moduleId": "Weight", "moduleName": null, "order": 2, "ragThresholds": [], "status": "ENABLED", "version": 0}, {"id": {"$oid": "5fe5c31a6d3a9649567ae0ad"}, "configBody": {}, "moduleId": "HighFrequencyHeartRate", "about": "HighFrequencyHeartRate config", "moduleName": "HeartRate", "status": "ENABLED"}], "features": {"messaging": {"enabled": true, "messages": ["text_message", "hu_sample_message"]}, "labels": true, "customAppConfig": {"billing": {"enabled": true, "productType": "RPM", "files": [{"type": "insuranceCarriers", "name": "insurance_carrier_test_file.csv", "uploadDateTime": "2023-02-10T15:21:09.090Z", "fileId": "63e660e27ab4034a57aa8e55"}, {"type": "billingProviders", "name": "BillingProviders.csv", "uploadDateTime": "2023-02-10T15:21:09.091Z", "fileId": "63e660e37ab4034a57aa8e56"}, {"type": "icd10Codes", "name": "ICD-10-codes.csv", "uploadDateTime": "2023-02-10T15:21:09.091Z", "fileId": "63e660e4985eb95f05b21e70"}], "submissionReminder": {"enabled": true, "schedule": {"syncThreshold": 2, "interval": 2, "count": 3}}}}}, "updateDateTime": {"$date": {"$numberLong": "1586433217042"}}, "createDateTime": {"$date": {"$numberLong": "1586433217042"}}, "localizations": {"en": {"hu_sample_message": "Some EN translated message"}}, "labels": [{"text": "New label", "authorId": {"$oid": "5e8f0c74b50aa9656c34789d"}, "createDateTime": "2023-08-15T08:37:09.807713Z", "id": {"$oid": "6525cc1a3c410036d6f587a7"}}, {"text": "Second label", "authorId": {"$oid": "5e8f0c74b50aa9656c34789d"}, "createDateTime": "2023-09-22T08:37:09.807713Z", "id": {"$oid": "6525cc1a3c410036d6f587a8"}}]}, {"_id": {"$oid": "5d386cc6ff885918d96edb2b"}, "name": "Y", "status": "DEPLOYED", "moduleConfigs": [{"about": "", "configBody": {}, "id": "5e94b2007773091c9a592660", "moduleId": "Weight", "moduleName": null, "order": 2, "ragThresholds": [], "status": "ENABLED", "version": 0}, {"configBody": {}, "id": "5e94b2007773091c9a592661", "moduleId": "RespiratoryRate", "order": 2, "status": "ENABLED", "version": 0}], "features": {"messaging": {"enabled": true, "messages": ["text_message", "hu_sample_message"]}, "customAppConfig": {"billing": {"enabled": true, "productType": "RTM", "files": [{"type": "insuranceCarriers", "name": "insurance_carrier_test_file.csv", "uploadDateTime": "2023-02-10T15:21:09.090Z", "fileId": "63e660e27ab4034a57aa8e55"}, {"type": "billingProviders", "name": "BillingProviders.csv", "uploadDateTime": "2023-02-10T15:21:09.091Z", "fileId": "63e660e37ab4034a57aa8e56"}, {"type": "icd10Codes", "name": "ICD-10-codes.csv", "uploadDateTime": "2023-02-10T15:21:09.091Z", "fileId": "63e660e4985eb95f05b21e70"}]}}}, "updateDateTime": {"$date": {"$numberLong": "1586433217042"}}, "createDateTime": {"$date": {"$numberLong": "1586433217042"}}, "localizations": {"en": {"hu_sample_message": "Some EN translated message"}}}], "huma_auth_user": [{"_id": {"$oid": "64e76adedf047d493ba356c3"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "manager"}, {"_id": {"$oid": "64e76adedf047d493ba356c4"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "administrator"}, {"_id": {"$oid": "651145013681ba0f8b9fb67f"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "clin"}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "testUser"}, {"_id": {"$oid": "5e8f0c74b50ac9656c34789d"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "manager"}, {"_id": {"$oid": "5e8f0c74b50aa9656c34129d"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "manager"}, {"_id": {"$oid": "5e8f0c74b50aa9656c347890"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "managerrtm"}, {"_id": {"$oid": "63ce44193d6527f1c3ceafe3"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "administator"}, {"_id": {"$oid": "63ce44193d6527f1c3ceafe4"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "administator"}, {"_id": {"$oid": "5e8f0c74b50aa9656c347891"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "testUser"}], "user": [{"_id": {"$oid": "5f0496ab82a641a9725336d6"}, "givenName": "Seraf", "familyName": "Fla", "gender": "FEMALE", "dateOfBirth": "1990-02-20", "email": "<EMAIL>", "nhsId": "nhs_id", "verificationStatus": 1, "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}, "roles": []}, {"_id": {"$oid": "5f0496ab82a641a9625336d6"}, "givenName": "Test", "familyName": "User", "email": "<EMAIL>", "gender": "FEMALE", "nhsId": "nhs_id", "dateOfBirth": "1990-02-20", "verificationStatus": 1, "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}, "roles": []}, {"_id": {"$oid": "63ce44193d6527f1c3ceadf3"}, "givenName": "Temp", "familyName": "Document", "email": "<EMAIL>", "gender": "FEMALE", "nhsId": "nhs_id", "dateOfBirth": "1990-02-20", "verificationStatus": 1, "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}, "roles": []}, {"_id": {"$oid": "5f0496ab82a63219725332d5"}, "givenName": "<PERSON>", "familyName": "<PERSON>", "email": "<EMAIL>", "gender": "FEMALE", "nhsId": "nhs_id", "dateOfBirth": "1990-02-20", "verificationStatus": 1, "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}, "roles": []}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789e"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "nhsId": "nhs_id", "dateOfBirth": "1988-02-20", "verificationStatus": 1, "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}, "surgeryDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "givenName": "<PERSON>", "familyName": "<PERSON>", "email": "<EMAIL>", "nhsId": "nhs_id", "gender": "FEMALE", "dateOfBirth": "1988-02-20", "verificationStatus": 1, "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}, "surgeryDateTime": {"$date": {"$numberInt": "**********"}}, "labels": [{"assignedBy": {"$oid": "5e8f0c74b50aa9656c34789d"}, "assignDateTime": "2023-08-15T08:45:09.807713Z", "labelId": {"$oid": "6525cc1a3c410036d6f587a7"}}, {"assignedBy": {"$oid": "5e8f0c74b50aa9656c34789d"}, "assignDateTime": "2023-10-05T08:45:09.807713Z", "labelId": {"$oid": "6525cc1a3c410036d6f587a8"}}]}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "givenName": "<PERSON>", "familyName": "<PERSON>", "email": "<EMAIL>", "nhsId": "nhs_id", "dateOfBirth": "1988-02-20", "gender": "FEMALE", "verificationStatus": 1, "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "timezone": "America/New_York", "createDateTime": {"$date": {"$numberInt": "**********"}}, "surgeryDateTime": {"$date": {"$numberInt": "**********"}}, "boardingStatus": {"status": 0, "updateDateTime": "2020-04-09T12:52:20.000Z"}, "componentsData": {"billing": {"status": 1, "billingProviderName": "someName", "diagnosis": {"order": 1, "icd10Code": "ICD", "description": "diagnosis description"}}}, "labels": [{"assignedBy": {"$oid": "5e8f0c74b50aa9656c34789d"}, "assignDateTime": "2023-08-18T08:45:09.807713Z", "labelId": {"$oid": "6525cc1a3c410036d6f587a8"}}]}, {"_id": {"$oid": "5e8f0c74b50ac9656c34789d"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "nhsId": "nhs_id", "dateOfBirth": "1988-02-20", "verificationStatus": 1, "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}, "surgeryDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "64e76adedf047d493ba356c3"}, "givenName": "manager", "familyName": "manager", "email": "<EMAIL>", "roles": [{"roleId": "Clinician", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "Manager", "isActive": true}, {"roleId": "Clinician", "resource": "deployment/5d386cc6ff885918d96edb2b", "userType": "Manager", "isActive": true}], "timezone": "Asia/Tehran", "createDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "64e76adedf047d493ba356c4"}, "givenName": "administrator", "familyName": "administrator", "email": "<EMAIL>", "roles": [{"roleId": "Administrator", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "Manager", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "651145013681ba0f8b9fb67f"}, "givenName": "clin", "familyName": "clin", "email": "<EMAIL>", "roles": [{"roleId": "Clinician", "resource": "deployment/5d386cc6ff885918d96edb2b", "userType": "Manager", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34129d"}, "givenName": "manager12", "familyName": "manager", "email": "<EMAIL>", "roles": [{"roleId": "Admin", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "Manager", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c347890"}, "givenName": "managerrtm", "familyName": "managerrtm", "email": "<EMAIL>", "roles": [{"roleId": "Admin", "resource": "deployment/5d386cc6ff885918d96edb2b", "userType": "Manager", "isActive": true}], "timezone": "UTC"}, {"_id": {"$oid": "5e8f0c74b50aa9656c347891"}, "givenName": "user", "familyName": "user", "email": "<EMAIL>", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2b", "userType": "User", "isActive": true}], "timezone": "UTC", "boardingStatus": {"status": 0, "updateDateTime": "2020-04-09T12:52:20.000Z"}}, {"_id": {"$oid": "63ce44193d6527f1c3ceafe3"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "timezone": "UTC", "roles": [{"roleId": "Administrator", "resource": "organization/63b67ff40d4b124707c3d29b", "userType": "Manager", "isActive": true}], "createDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "63ce44193d6527f1c3ceafe4"}, "givenName": "<PERSON><PERSON>", "familyName": "<PERSON>", "gender": "FEMALE", "dateOfBirth": "1990-02-20", "email": "<EMAIL>", "timezone": "UTC", "roles": [{"roleId": "Administrator", "resource": "organization/63b67ff40d4b124707c3d29b", "userType": "Manager", "isActive": true}]}], "billing_alert": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "_cls": "MongoBillingAlertDocument", "user_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "monitoringMinutes": 24.0, "createDateTime": "2023-04-25T16:50:19.456Z", "updateDateTime": "2023-05-14T08:00:00.000Z", "nextSubmissionDoS": "2023-05-01T08:00:00.000Z", "lastMonitoringDate": {"$date": "2023-05-05T08:00:00.000Z"}, "submissionDates": [], "callDateTime": {"$date": "2023-05-05T08:00:00.000Z"}, "nextMonitoringDoS": "2023-05-24T08:00:00.000Z", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}}, {"_id": {"$oid": "5f0496ab82a63019725332d5"}, "_cls": "MongoBillingAlertDocument", "user_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "monitoringMinutes": 48.0, "createDateTime": "2023-04-25T16:50:19.456Z", "updateDateTime": "2023-04-25T16:50:19.456Z", "nextSubmissionDoS": {"$date": "2023-05-01T08:00:00.000Z"}, "lastMonitoringDate": {"$date": "2023-05-05T08:00:00.000Z"}, "submissionDates": [], "callDateTime": {"$date": "2023-03-05T08:00:00.000Z"}, "nextMonitoringDoS": "2023-05-24T08:00:00.000Z", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}}, {"_id": {"$oid": "5f0496ab82a63219725332d5"}, "_cls": "MongoBillingAlertDocument", "user_id": {"$oid": "5f0496ab82a63219725332d5"}, "monitoringMinutes": 48.0, "createDateTime": "2023-04-25T16:50:19.456Z", "updateDateTime": "2023-04-25T16:50:19.456Z", "nextSubmissionDoS": {"$date": "2023-05-01T08:00:00.000Z"}, "lastMonitoringDate": {"$date": "2023-05-02T08:00:00.000Z"}, "submissionDates": [{"$date": "2023-03-29T08:00:00.000Z"}, {"$date": "2023-04-01T08:00:00.000Z"}], "callDateTime": {"$date": "2023-03-05T08:00:00.000Z"}, "nextMonitoringDoS": "2023-05-31T08:00:00.000Z", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}}, {"_id": {"$oid": "5f0496bb82a630a9725336d6"}, "_cls": "MongoBillingAlertDocument", "user_id": {"$oid": "5e8f0c74b50aa9656c34129d"}, "monitoringMinutes": 31.9, "createDateTime": "2023-04-25T16:50:19.456Z", "updateDateTime": "2023-04-25T16:50:19.456Z", "nextSubmissionDoS": {"$date": "2023-05-01T08:00:00.000Z"}, "lastMonitoringDate": {"$date": "2023-05-05T08:00:00.000Z"}, "submissionDates": [], "callDateTime": {"$date": "2023-05-05T08:00:00.000Z"}, "nextMonitoringDoS": "2023-05-24T08:00:00.000Z", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}}, {"_id": {"$oid": "63ce44193d6527f1c3ceafe3"}, "_cls": "MongoBillingAlertDocument", "user_id": {"$oid": "63ce44193d6527f1c3ceafe3"}, "monitoringMinutes": 14.2, "createDateTime": "2023-04-25T16:50:19.456Z", "updateDateTime": "2023-04-25T16:50:19.456Z", "nextSubmissionDoS": {"$date": "2023-05-01T08:00:00.000Z"}, "lastMonitoringDate": {"$date": "2023-05-05T08:00:00.000Z"}, "submissionDates": [{"$date": "2023-05-01T08:00:00.000Z"}], "callDateTime": {"$date": "2023-05-05T08:00:00.000Z"}, "nextMonitoringDoS": "2023-05-24T08:00:00.000Z", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}}, {"_id": {"$oid": "5f0496ab82a631a9725336d6"}, "_cls": "MongoBillingAlertDocument", "user_id": {"$oid": "5e8f0c74b50aa9656c347890"}, "monitoringMinutes": 3.1, "createDateTime": "2023-04-25T16:50:19.456Z", "updateDateTime": "2023-04-25T16:50:19.456Z", "nextSubmissionDoS": {"$date": "2023-05-01T08:00:00.000Z"}, "lastMonitoringDate": "2023-04-24T08:00:00.000Z", "submissionDates": [], "callDateTime": {"$date": "2023-05-05T08:00:00.000Z"}, "nextMonitoringDoS": "2023-05-24T08:00:00.000Z", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}}, {"_id": {"$oid": "5f0496ab82a641a9725336d6"}, "_cls": "MongoBillingAlertDocument", "user_id": {"$oid": "5f0496ab82a641a9725336d6"}, "monitoringMinutes": 3.1, "createDateTime": "2023-04-25T16:50:19.456Z", "updateDateTime": "2023-04-25T16:50:19.456Z", "nextSubmissionDoS": {"$date": "2023-05-01T08:00:00.000Z"}, "lastMonitoringDate": "2023-04-24T08:00:00.000Z", "submissionDates": [], "callDateTime": {"$date": "2023-03-05T08:00:00.000Z"}, "nextMonitoringDoS": "2023-05-24T08:00:00.000Z", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2b"}}, {"_id": {"$oid": "5f0496ab82a641a9625336d6"}, "_cls": "MongoBillingAlertDocument", "user_id": {"$oid": "5f0496ab82a641a9625336d6"}, "monitoringMinutes": 3.1, "createDateTime": "2023-04-25T16:50:19.456Z", "updateDateTime": "2023-04-25T16:50:19.456Z", "nextSubmissionDoS": {"$date": "2023-05-01T08:00:00.000Z"}, "lastMonitoringDate": "2023-04-24T08:00:00.000Z", "submissionDates": [], "callDateTime": {"$date": "2023-05-05T08:00:00.000Z"}, "nextMonitoringDoS": "2023-05-24T08:00:00.000Z", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2b"}}, {"_id": {"$oid": "5f0496ab22a630a9725336d6"}, "_cls": "MongoBillingAlertDocument", "user_id": {"$oid": "63ce44193d6527f1c3ceafe4"}, "monitoringMinutes": 54.2, "createDateTime": "2023-04-25T16:50:19.456Z", "updateDateTime": "2023-04-25T16:50:19.456Z", "nextSubmissionDoS": {"$date": "2023-05-01T08:00:00.000Z"}, "lastMonitoringDate": {"$date": "2023-05-05T08:00:00.000Z"}, "submissionDates": [], "callDateTime": null, "nextMonitoringDoS": "2023-05-24T08:00:00.000Z", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}}, {"_id": {"$oid": "5e8f0c74b50aa9656c347891"}, "user_id": {"$oid": "5e8f0c74b50aa9656c347891"}, "nextSubmissionDoS": null, "submissionDates": [], "deploymentId": {"$oid": "5d386cc6ff885918d96edb2b"}, "createDateTime": "2023-04-25T16:50:19.456Z", "updateDateTime": "2023-04-25T16:50:19.456Z"}], "billing_monitoring_log": [{"_id": {"$oid": "64b7b85fb024d4faa61d9f23"}, "_cls": "MongoBillingMonitoringLogDocument", "deploymentId": {"$oid": "63b6bba50d4b124707c3f50b"}, "createdById": {"$oid": "64e76adedf047d493ba356c3"}, "lastModifiedById": {"$oid": "64e76adedf047d493ba356c3"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789c"}, "timeTrackingId": {"$oid": "64b7b85fb024d4faa61d9f22"}, "timeSpent": 300, "status": 0, "action": "hu_billing_log_action_8", "startDateTime": {"$date": "2023-05-19T10:12:06.889Z"}, "endDateTime": {"$date": "2023-05-19T10:17:06.889Z"}, "createDateTime": {"$date": "2023-05-15T10:18:07.664Z"}, "initialCreateDateTime": {"$date": "2023-05-15T10:18:07.664Z"}, "updateDateTime": {"$date": "2023-05-15T10:18:07.664Z"}}], "billing_profile_log_provider": [{"_id": {"$oid": "64a43c73b77e7d2ce0155850"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "createDateTime": {"$date": "2023-04-10T11:30:31.985Z"}}], "video_call": [{"_id": {"$oid": "5f0496ab82a630a9725336c1"}, "_cls": "MongoVideoCall", "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "managerId": {"$oid": "64e76adedf047d493ba356c3"}, "roomStatus": "completed", "startDateTime": {"$date": "2023-02-09T14:08:03.997Z"}, "endDateTime": {"$date": "2023-02-09T14:08:05.997Z"}, "updateDateTime": {"$date": "2023-02-09T14:08:03.997Z"}, "createDateTime": {"$date": "2023-02-09T14:08:03.997Z"}, "duration": {"$numberInt": "2"}, "status": "ANSWERED"}], "organization": [{"_id": {"$oid": "63b67ff40d4b124707c3d29b"}, "termAndConditionUrl": "https://goole.com", "privacyPolicyUrl": "https://goole.com", "eulaUrl": "https://goole.com", "name": "rpmdevbillingorganization", "status": "DRAFT", "enrollmentTarget": 0, "studyCompletionTarget": 0, "viewType": "RPM", "dashboardId": "OrganizationOverview", "features": {"newRolesSupport": true}, "deploymentIds": ["5d386cc6ff885918d96edb2c"], "targetConsented": 0}], "weight": [{"_id": {"$oid": "5f7dd1f0e03d4a97e8007a91"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "moduleId": "Weight", "moduleConfigId": {"$oid": "5e94b2007773091c9a592660"}, "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "version": 0, "deviceName": "iOS", "startDateTime": {"$date": "2020-10-07T17:12:00.000Z"}, "createDateTime": {"$date": "2020-10-07T14:34:24.851Z"}, "submitterId": {"$oid": "5e8f0c74b50aa9656c34789c"}, "value": 99}]}