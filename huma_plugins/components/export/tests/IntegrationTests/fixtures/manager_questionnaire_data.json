{"deployment": [{"_id": {"$oid": "5d386cc6ff885918d96edb2e"}, "name": "Test", "status": "DRAFT", "color": "0x007AFF", "consent": {"id": {"$oid": "61926cc89cb844829c967fd2"}, "createDateTime": {"$date": "2021-11-15T14:20:56.341Z"}, "enabled": "ENABLED", "revision": 1, "sections": [{"type": "DATA_GATHERING", "title": "Your data", "details": "Mocked details"}], "instituteName": "string", "instituteFullName": "string", "instituteTextDetails": "string"}, "onboardingConfigs": [{"id": {"$oid": "61926cc89cb844829c967fe0"}, "onboardingId": "Consent", "status": "DISABLED", "configBody": {}, "order": 1, "version": 0, "userTypes": ["User"]}], "moduleConfigs": [{"id": {"$oid": "5f0d64a47fe6637a82dcbea8"}, "updateDateTime": {"$date": "2021-09-06T20:50:05.084Z"}, "createDateTime": {"$date": "2021-09-06T20:50:05.084Z"}, "moduleId": "Questionnaire", "moduleName": "Short Codes", "status": "ENABLED", "order": 20, "configBody": {"name": "Manager Questionnaire", "id": "6d4ea71a-7554-4315-8e67-53bfbc0760b1", "isForManager": false, "scoreAvailable": true, "filledBy": "Manager", "publisherName": "AB", "trademarkText": "", "pages": [{"type": "INFO", "id": "10dcb781-990a-4c04-94ae-3a37aa93ecf0", "order": 1, "text": "Anxiety level", "description": "Another desc"}, {"type": "QUESTION", "items": [{"logic": {"isEnabled": false}, "description": "Some desc", "id": "9f0c0b00-8541-4a0a-a8ba-c8eb4a629af7", "required": true, "format": "TEXTCHOICE", "order": 1, "exportShortCode": "question_with_short_code", "text": "Over the last 2 weeks", "options": [{"label": "Ok", "value": "0", "weight": 0}, {"label": "Bad", "value": "1", "weight": 1}, {"label": "Fine", "value": "2", "weight": 2}, {"label": "Often true", "value": "3", "weight": 3}], "selectionCriteria": "SINGLE"}], "order": 2}, {"type": "QUESTION", "items": [{"logic": {"isEnabled": false}, "description": "How often have you been bothered by not being able to stop or control worrying?", "id": "12ed5202-e636-4929-8d31-6ecdec107cee", "required": true, "format": "TEXTCHOICE", "order": 1, "text": "Over the last 2 weeks", "options": [{"label": "Not at all", "value": "0", "weight": 0}, {"label": "Several days", "value": "1", "weight": 1}, {"label": "More than half the days", "value": "2", "weight": 2}, {"label": "Sometimes true", "value": "3", "weight": 3}], "selectionCriteria": "SINGLE"}], "order": 3}]}, "about": "Sample for short codes", "schedule": {"timesPerDuration": 0, "friendlyText": "AS NEEDED", "timesOfDay": []}, "version": 0}, {"id": {"$oid": "5f0d64a47fe6637a82dcbea7"}, "updateDateTime": {"$date": "2021-09-06T20:50:05.084Z"}, "createDateTime": {"$date": "2021-09-06T20:50:05.084Z"}, "moduleId": "Questionnaire", "moduleName": "Short Codes", "status": "ENABLED", "order": 20, "configBody": {"name": "User Questionnaire", "id": "6d4ea71a-7554-4315-8e67-53bfbc0760b2", "isForManager": false, "scoreAvailable": true, "publisherName": "AB", "trademarkText": "", "pages": [{"type": "INFO", "id": "10dcb781-990a-4c04-94ae-3a37aa93ecf0", "order": 1, "text": "Anxiety level", "description": "Another desc"}, {"type": "QUESTION", "items": [{"logic": {"isEnabled": false}, "description": "Some desc", "id": "9f0c0b00-8541-4a0a-a8ba-c8eb4a629af7", "required": true, "format": "TEXTCHOICE", "order": 1, "exportShortCode": "question_with_short_code", "text": "Over the last 2 weeks", "options": [{"label": "Ok", "value": "0", "weight": 0}, {"label": "Bad", "value": "1", "weight": 1}, {"label": "Fine", "value": "2", "weight": 2}, {"label": "Often true", "value": "3", "weight": 3}], "selectionCriteria": "SINGLE"}], "order": 2}, {"type": "QUESTION", "items": [{"logic": {"isEnabled": false}, "description": "How often have you been bothered by not being able to stop or control worrying?", "id": "12ed5202-e636-4929-8d31-6ecdec107cee", "required": true, "format": "TEXTCHOICE", "order": 1, "text": "Over the last 2 weeks", "options": [{"label": "Not at all", "value": "0", "weight": 0}, {"label": "Several days", "value": "1", "weight": 1}, {"label": "More than half the days", "value": "2", "weight": 2}, {"label": "Sometimes true", "value": "3", "weight": 3}], "selectionCriteria": "SINGLE"}], "order": 3}]}, "about": "Sample for short codes", "schedule": {"timesPerDuration": 0, "friendlyText": "AS NEEDED", "timesOfDay": []}, "version": 0}, {"id": {"$oid": "624bf345a6f6e9939a284ed4"}, "updateDateTime": {"$date": {"$numberLong": "1675168744972"}}, "createDateTime": {"$date": {"$numberLong": "1649144645810"}}, "moduleId": "Breathlessness", "moduleName": "Breathlessness", "status": "ENABLED", "order": 33, "configBody": {"calculatedQuestionnaire": false, "id": "d4c83ab1-ea14-46fb-9ea9-45871dcdb439", "isForManager": false, "isHorizontal": false, "isOnboarding": false, "name": "Breathlessness", "pages": [{"items": [{"description": "", "format": "TEXTCHOICE", "id": "c1246f03-f89e-469e-8d4e-a03ae5abcbdb", "options": [{"label": "None", "value": "1", "weight": 1}, {"label": "Mild", "value": "2", "weight": 2}, {"label": "Moderate", "value": "3", "weight": 3}, {"label": "Severe", "value": "4", "weight": 4}, {"label": "Very Severe", "value": "5", "weight": 5}], "order": 1, "required": true, "selectionCriteria": "SINGLE", "text": "How breathless are you when you are walking around or walking up stairs?"}], "order": 1, "type": "QUESTION"}], "publisherName": "RT", "scoreAvailable": true}, "about": "This is a quick assessment to understand the degree of breathlessness you are experiencing. The information you provide will allow your care team to keep an eye on your lung function during the isolation period. Please complete this activity three times a day for your care team to be able to monitor trends over time.", "schedule": {"isoDuration": "P1D", "timesPerDuration": 3, "friendlyText": "friendlyText"}, "ragThresholds": [{"type": "VALUE", "severity": 3, "thresholdRange": [{"minValue": 4, "maxValue": 5}], "color": "#FBCCD7", "fieldName": "value", "enabled": true}, {"type": "VALUE", "severity": 2, "thresholdRange": [{"minValue": 3, "maxValue": 3.9}], "color": "#FFDA9F", "fieldName": "value", "enabled": true}, {"type": "VALUE", "severity": 1, "thresholdRange": [{"minValue": 1, "maxValue": 2.9}], "color": "#CBEBF0", "fieldName": "value", "enabled": true}], "version": 0, "footnote": {"enabled": false}}], "learn": {"id": {"$oid": "5e8eeae1b707216625ca4202"}, "updateDateTime": {"$date": {"$numberLong": "1586433217042"}}, "createDateTime": {"$date": {"$numberLong": "1586433217042"}}}, "updateDateTime": {"$date": {"$numberLong": "1586433217042"}}, "createDateTime": {"$date": {"$numberLong": "1586433217042"}}}], "questionnaire": [{"_id": {"$oid": "6286211c13404ff039539016"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789c"}, "moduleId": "Breathlessness", "moduleConfigId": {"$oid": "624bf345a6f6e9939a284ed4"}, "moduleResultId": "7318315fa09349b0a6e5e67d9b6cb233", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2e"}, "version": 0, "deviceName": "Android", "isAggregated": false, "startDateTime": {"$date": {"$numberLong": "1652957466845"}}, "createDateTime": {"$date": {"$numberLong": "1652957468371"}}, "submitterId": {"$oid": "628610db13404ff039538fb8"}, "server": {"hostUrl": "qaapi.humaapp.io", "server": "1.21.0", "api": "V1"}, "ragThreshold": {"Breathlessness": {"value": {"color": "#FBCCD7", "severity": 3, "isCustom": false}}}, "flags": {"red": 1, "amber": 0, "gray": 0}, "answers": [{"answerText": "Severe", "questionId": "c1246f03-f89e-469e-8d4e-a03ae5abcbdb", "question": "How breathless are you when you are walking around or walking up stairs?", "format": "TEXTCHOICE", "choices": ["None", "Mild", "Moderate", "Severe", "Very Severe"], "selectedChoices": ["4"], "selectionCriteria": "SINGLE"}], "questionnaireId": "d4c83ab1-ea14-46fb-9ea9-45871dcdb439", "questionnaireName": "Breathlessness", "value": 4}, {"_id": {"$oid": "687c61c058fdf4e58c9a9488"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789c"}, "moduleId": "Questionnaire", "moduleConfigId": "5f0d64a47fe6637a82dcbea8", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2e"}, "version": 0, "deviceName": "iOS", "isAggregated": false, "startDateTime": {"$date": "2020-10-12T08:50:28.321Z"}, "createDateTime": {"$date": "2020-10-12T08:50:28.442Z"}, "submitterId": {"$oid": "5f834cd9952675f3bfaca997"}, "answers": [{"id": "6310b1ed324673eb4e43a7c1", "answerText": "Often true", "questionId": "9f0c0b00-8541-4a0a-a8ba-c8eb4a629af7", "question": "I/We worried whether my/our food would run out before I/we got money to buy more."}, {"id": "6310b1ed324673eb4e43a7c2", "answerText": "Sometimes true", "questionId": "7a23cb87-b7ad-4083-bb33-417b9ae3ceae", "question": "The food that I/we bought just didn’t last, and I/we didn’t have money to get more."}], "questionnaireId": "6d4ea71a-7554-4315-8e67-53bfbc0760b1", "questionnaireName": "Test 1"}, {"_id": {"$oid": "5f8418d4c9608e67c8cf859b"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789c"}, "moduleId": "Questionnaire", "moduleConfigId": "5f0d64a47fe6637a82dcbea7", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2e"}, "version": 0, "deviceName": "iOS", "isAggregated": false, "startDateTime": {"$date": "2020-10-12T08:50:28.321Z"}, "createDateTime": {"$date": "2020-10-12T08:50:28.442Z"}, "submitterId": {"$oid": "5f834cd9952675f3bfaca997"}, "answers": [{"id": "6310b1ed324673eb4e43a7c1", "answerText": "Often true", "questionId": "9f0c0b00-8541-4a0a-a8ba-c8eb4a629af7", "question": "I/We worried whether my/our food would run out before I/we got money to buy more."}, {"id": "6310b1ed324673eb4e43a7c2", "answerText": "Sometimes true", "questionId": "7a23cb87-b7ad-4083-bb33-417b9ae3ceae", "question": "The food that I/we bought just didn’t last, and I/we didn’t have money to get more."}], "questionnaireId": "6d4ea71a-7554-4315-8e67-53bfbc0760b2", "questionnaireName": "Test 2"}], "huma_auth_user": [{"_id": {"$oid": "5ed803dd5f2f99da73655134"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "exporter", "userAttributes": {"familyName": "exporter", "givenName": "exporter", "dob": "1998-04-20", "gender": "MALE"}}], "user": [{"_id": {"$oid": "5ed803dd5f2f99da73655134"}, "email": "<EMAIL>", "givenName": "exporter", "familyName": "exporter", "gender": "MALE", "roles": [{"roleId": "Exporter", "resource": "deployment/5d386cc6ff885918d96edb2e", "userType": "ServiceAccount", "isActive": true}], "timezone": "UTC"}]}