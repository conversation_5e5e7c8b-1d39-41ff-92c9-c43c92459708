{"bmi": [{"_id": {"$oid": "5ff4b14e0b3791bf7278abcd"}, "userId": {"$oid": "5e8f0c74b50aa9656c34788a"}, "moduleId": "BMI", "moduleConfigId": {"$oid": "5f1824ba504787d8d89eb78d"}, "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "version": 0, "deviceName": "iOS", "isAggregated": false, "startDateTime": {"$date": "2021-01-05T18:34:54.000Z"}, "createDateTime": {"$date": "2021-01-05T18:34:54.658Z"}, "submitterId": {"$oid": "5e8f0c74b50aa9656c34788a"}, "value": 35, "originalValue": 41}], "huma_auth_user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34788a"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380991999990", "displayName": "testUser", "userAttributes": {"familyName": "testUser", "givenName": "testUser", "dob": "1933-02-20"}, "createDateTime": {"$date": {"$numberInt": "1586422340"}}, "updateDateTime": {"$date": {"$numberInt": "1586422340"}}}], "user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34788a"}, "givenName": "testUser", "familyName": "testUser", "email": "<EMAIL>", "phoneNumber": "+380991999990", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "isActive": true}], "recentModuleResults": {}, "timezone": "UTC", "height": 220, "gender": "AGENDER_OR_TRANSGENDER", "dateOfBirth": "1933-02-20"}], "consent_log": [{"_id": {"$oid": "614c57a392896a5b886daa12"}, "userId": {"$oid": "5e8f0c74b50aa9656c34788a"}, "consentId": {"$oid": "5e9443789911c97c0b639374"}, "revision": 1, "createDateTime": {"$date": "2021-09-23T10:32:03.686Z"}, "agreement": true, "deploymentId": "5d386cc6ff885918d96edb2c"}], "econsent_log": [{"_id": {"$oid": "614c57a392896a5b886daa71"}, "userId": {"$oid": "5e8f0c74b50aa9656c34788a"}, "econsentId": {"$oid": "5e9443789911c97c0b639444"}, "revision": 1, "createDateTime": {"$date": "2021-09-23T10:32:03.686Z"}, "agreement": true, "deploymentId": "5d386cc6ff885918d96edb2c"}]}