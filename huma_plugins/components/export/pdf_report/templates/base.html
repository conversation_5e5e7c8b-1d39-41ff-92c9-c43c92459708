<!DOCTYPE html>
<html lang="en">
  <head>
    <title>{{ title }}</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;700&family=Source+Serif+Pro:wght@200&display=swap"
      rel="stylesheet"
    />

    <script src="https://cdn.amcharts.com/lib/version/5.2.12/index.js"></script>
    <script src="https://cdn.amcharts.com/lib/version/5.2.12/xy.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.15/lodash.min.js"></script>
    <script>
      // a global tool to track the progress on loading pages and
      // creates an element with id="pageready" when all pages are loaded

      let previousLoadedPages = 0;
      var loadedPages = 0;

      document.onreadystatechange = function () {
        if (document.readyState === "complete") {
          let interval = setInterval(() => {
            if (loadedPages !== 0 && loadedPages === previousLoadedPages) {
              clearInterval(interval);
              let pageReady = document.createElement("div");
              pageReady.id = "pageready";
              document.body.appendChild(pageReady);
            } else {
              previousLoadedPages = loadedPages;
            }
          }, 1000);
        }
      };
    </script>
    <script>
      function setIndentation(
        selector,
        distance,
        indentation,
        current,
        expected
      ) {
        const body = document.getElementById("main_body");
        if (body.dir === "rtl") {
          let list = document.querySelectorAll(selector);
          list.forEach((elem) => {
            elem.style.setProperty(indentation + "-" + expected, distance);
            elem.style.setProperty(indentation + "-" + current, "0");
          });
        }
      }

      var rtlLabelPadding;
      if (navigator.userAgent.indexOf("Win") !== -1) {
        rtlLabelPadding = 2.5;
      } else {
        rtlLabelPadding = 5.5;
      }

      function isHexColor(str) {
        return /^#[0-9A-F]{6}$/i.test(str);
      }

      function colourNameToHex(colour) {
        const colours = {
          aliceblue: "#f0f8ff",
          antiquewhite: "#faebd7",
          aqua: "#00ffff",
          aquamarine: "#7fffd4",
          azure: "#f0ffff",
          beige: "#f5f5dc",
          bisque: "#ffe4c4",
          black: "#000000",
          blanchedalmond: "#ffebcd",
          blue: "#0000ff",
          blueviolet: "#8a2be2",
          brown: "#a52a2a",
          burlywood: "#deb887",
          cadetblue: "#5f9ea0",
          chartreuse: "#7fff00",
          chocolate: "#d2691e",
          coral: "#ff7f50",
          cornflowerblue: "#6495ed",
          cornsilk: "#fff8dc",
          crimson: "#dc143c",
          cyan: "#00ffff",
          darkblue: "#00008b",
          darkcyan: "#008b8b",
          darkgoldenrod: "#b8860b",
          darkgray: "#a9a9a9",
          darkgreen: "#006400",
          darkkhaki: "#bdb76b",
          darkmagenta: "#8b008b",
          darkolivegreen: "#556b2f",
          darkorange: "#ff8c00",
          darkorchid: "#9932cc",
          darkred: "#8b0000",
          darksalmon: "#e9967a",
          darkseagreen: "#8fbc8f",
          darkslateblue: "#483d8b",
          darkslategray: "#2f4f4f",
          darkturquoise: "#00ced1",
          darkviolet: "#9400d3",
          deeppink: "#ff1493",
          deepskyblue: "#00bfff",
          dimgray: "#696969",
          dodgerblue: "#1e90ff",
          firebrick: "#b22222",
          floralwhite: "#fffaf0",
          forestgreen: "#228b22",
          fuchsia: "#ff00ff",
          gainsboro: "#dcdcdc",
          ghostwhite: "#f8f8ff",
          gold: "#ffd700",
          goldenrod: "#daa520",
          gray: "#808080",
          green: "#008000",
          greenyellow: "#adff2f",
          honeydew: "#f0fff0",
          hotpink: "#ff69b4",
          "indianred ": "#cd5c5c",
          indigo: "#4b0082",
          ivory: "#fffff0",
          khaki: "#f0e68c",
          lavender: "#e6e6fa",
          lavenderblush: "#fff0f5",
          lawngreen: "#7cfc00",
          lemonchiffon: "#fffacd",
          lightblue: "#add8e6",
          lightcoral: "#f08080",
          lightcyan: "#e0ffff",
          lightgoldenrodyellow: "#fafad2",
          lightgrey: "#d3d3d3",
          lightgreen: "#90ee90",
          lightpink: "#ffb6c1",
          lightsalmon: "#ffa07a",
          lightseagreen: "#20b2aa",
          lightskyblue: "#87cefa",
          lightslategray: "#778899",
          lightsteelblue: "#b0c4de",
          lightyellow: "#ffffe0",
          lime: "#00ff00",
          limegreen: "#32cd32",
          linen: "#faf0e6",
          magenta: "#ff00ff",
          maroon: "#800000",
          mediumaquamarine: "#66cdaa",
          mediumblue: "#0000cd",
          mediumorchid: "#ba55d3",
          mediumpurple: "#9370d8",
          mediumseagreen: "#3cb371",
          mediumslateblue: "#7b68ee",
          mediumspringgreen: "#00fa9a",
          mediumturquoise: "#48d1cc",
          mediumvioletred: "#c71585",
          midnightblue: "#191970",
          mintcream: "#f5fffa",
          mistyrose: "#ffe4e1",
          moccasin: "#ffe4b5",
          navajowhite: "#ffdead",
          navy: "#000080",
          oldlace: "#fdf5e6",
          olive: "#808000",
          olivedrab: "#6b8e23",
          orange: "#ffa500",
          orangered: "#ff4500",
          orchid: "#da70d6",
          palegoldenrod: "#eee8aa",
          palegreen: "#98fb98",
          paleturquoise: "#afeeee",
          palevioletred: "#d87093",
          papayawhip: "#ffefd5",
          peachpuff: "#ffdab9",
          peru: "#cd853f",
          pink: "#ffc0cb",
          plum: "#dda0dd",
          powderblue: "#b0e0e6",
          purple: "#800080",
          rebeccapurple: "#663399",
          red: "#ff0000",
          rosybrown: "#bc8f8f",
          royalblue: "#4169e1",
          saddlebrown: "#8b4513",
          salmon: "#fa8072",
          sandybrown: "#f4a460",
          seagreen: "#2e8b57",
          seashell: "#fff5ee",
          sienna: "#a0522d",
          silver: "#c0c0c0",
          skyblue: "#87ceeb",
          slateblue: "#6a5acd",
          slategray: "#708090",
          snow: "#fffafa",
          springgreen: "#00ff7f",
          steelblue: "#4682b4",
          tan: "#d2b48c",
          teal: "#008080",
          thistle: "#d8bfd8",
          tomato: "#ff6347",
          turquoise: "#40e0d0",
          violet: "#ee82ee",
          wheat: "#f5deb3",
          white: "#ffffff",
          whitesmoke: "#f5f5f5",
          yellow: "#ffff00",
          yellowgreen: "#9acd32",
        };

        if (typeof colours[colour.toLowerCase()] != "undefined")
          return colours[colour.toLowerCase()];

        return colour;
      }
    </script>

    <style>
      @font-face {
        font-family: "VictorSerifSmooth-Regular";
        src: url("../pdf_report/static/fonts/VictorSerifSmooth-Regular.otf");
      }

      .for-body {
        margin: 0;
      }

      .for-subtitle {
        font-weight: 400;
      }

      .container {
        width: 816px;
      }

      .general {
        width: 776px;
        margin: 20px 20px 0;
        border: 1px solid #ebebeb;
      }

      .general-container {
        font-family: "Noto Sans", sans-serif;
        border-bottom: 1px solid #ebebeb;
        box-sizing: border-box;
        border-radius: 8px;
      }

      .charts-container {
        background: #fbfbfb;
      }

      .for-charts {
        display: flex;
      }

      .font-size-12 {
        font-size: 12px;
        line-height: 16px;
      }

      .chartDiv {
        width: 100%;
        height: 180px;
        margin-top: 5px;
      }

      .containers-align {
        display: flex;
        align-items: center;
      }

      .for-bottom-markers {
        display: flex;
        align-items: center;
        margin-right: 20px;
      }

      .title {
        padding: 14px 14px 10px;
        margin: 0;
        font-size: 14px;
        line-height: 16px;
        color: #2f3033;
        align-items: center;
        display: flex;
      }

      .title span {
        color: #6a6d72;
        font-weight: normal;
        padding: 0 4px;
      }

      .superscripts {
        position: relative;
        bottom: 0.3ex;
        font-size: 11px;
        color: #6a6d72;
      }

      .superscripts span {
        position: relative;
        bottom: 1.2ex;
        font-size: 7px;
      }

      .bottom-markers {
        width: 32px;
        height: 20px;
        border: 1px solid #ebebeb;
        box-sizing: border-box;
        border-radius: 4px;
        margin-right: 8px;
      }

      .dark-dot {
        width: 5px;
        height: 5px;
        background: #424347;
        border-radius: 50px;
      }

      .white-dot {
        width: 2px;
        height: 2px;
        border-radius: 50px;
        border: 1px solid #424347;
      }

      .margin-7-13 {
        margin: 7px 13px;
      }

      .column {
        max-width: 794px;
        column-count: 2;
      }

      .page {
        height: 1054px;
        position: relative;
      }

      .page + .page {
        margin-top: 3px;
      }

      .display-none {
        display: none;
      }

      .generic-data {
        display: flex;
        padding: 3px 14px 5px;
      }

      .generic-row {
        display: flex;
        flex-direction: row;
        padding-bottom: 2px;
        padding-right: 70px;
      }

      .generic-values {
        padding: 0 6px;
        color: #2f3033;
      }

      .generic-titles {
        color: #6a6d72;
      }

      .generic-footer {
        padding: 8px 10px 8px;
        font-size: 8px;
        line-height: 16px;
        color: #2f3033;
        display: flex;
      }

      .generic-column-padding {
        padding-right: 70px;
      }

      .generic-6month-chart {
        display: block;
        width: 70%;
      }

      .generic-week-chart {
        display: block;
        width: 40%;
        margin-left: -25px;
        margin-right: -10px;
      }

      .monthly-mean-value {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .line {
        margin: 0 -1px;
        height: 1px;
        width: 16px;
        background: #424347;
      }

      .standard-deviation-line {
        height: 100%;
        width: 5px;
        background: #909499;
        opacity: 0.3;
      }

      .standard-deviation {
        display: inline-flex;
        justify-content: space-around;
      }

      .superscripts-footer {
        position: relative;
        font-size: 9px;
      }

      .superscripts-footer span {
        position: relative;
        bottom: 1.1ex;
        font-size: 6px;
      }

      .for-body-measurements {
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
      }

      .dot-margin {
        margin: 2px 8px 0;
      }

      .title-dot-margin {
        margin: 7px 13px;
      }

      .margin-20 {
        margin-top: 20px;
      }

      .page-logo {
        height: 34px;
        width: 100%;
        border-bottom: 1px solid #ebebeb;
      }

      .page-logo img {
        float: right;
        padding: 8px 20px 7px;
        height: 19px;
        object-fit: cover;
      }

      .footer {
        position: absolute;
        bottom: 0;
        border-top: 1px solid #ebebeb;
        display: flex;
        justify-content: space-between;
        color: #6a6d72;
        font-weight: 400;
        font-size: 10px;
        line-height: 10px;
        height: 34px;
        width: 100%;
        align-items: center;
        font-family: "Noto Sans", sans-serif;
      }

      .footer-img {
        display: flex;
        align-items: center;
        padding: 0 10px;
      }

      .footer-img img {
        height: 11px;
        width: 11px;
        margin: 0 10px;
      }

      .for-page {
        padding: 0 20px;
      }

      .for-page span {
        font-weight: 600;
      }

      .footnote {
        font-family: "Noto Sans", sans-serif;
        color: #424347;
        font-size: 10px;
        line-height: 150%;
        font-weight: 400;
        padding: 0 20px;
        position: absolute;
        bottom: 34px;
      }

      .footnote-item {
        display: flex;
        align-items: baseline;
      }

      .footnote-item-mark {
        padding-right: 6px;
        font-size: 8px;
        line-height: 16px;
      }

      .page-direction {
        direction: ltr;
      }
    </style>
  </head>
  <body class="for-body" id="main_body" dir="{{ textDirection }}">
    <div class="page containers-align">{{ pages[0].render_html() | safe }}</div>
    <div id="pages-list">
      {% for page in pages[1:] %} {% set result = page.render_html() %} {% if
      result %} {{ result | safe }} {% endif %} {% endfor %}
    </div>
    <div id="pageFooterTemplate" class="footer" style="display: none">
      <div class="footer-img">
        <img src="https://static.cdn.huma.com/fill_8.png" alt="" />
        <div>{{ title }}</div>
      </div>
      <div class="for-page">
        {{ pageTitle }}
        <span>$currentPage</span> / $totalPageCount
      </div>
    </div>
    <script>
      const PDF_PAGE_SIZE = 1054 - 35 - 35 - 40 - 14; // page_height - header - footer - margins - safe_margin
      // main
      var iconUrl = "{{ icon | safe }}";

      let allChartsNodes = document.getElementById('pages-list');
      let allCharts = [];
      allChartsNodes.childNodes.forEach((container) => {
        if (container.tagName === "DIV") {
          container.childNodes.forEach((child) => {
            if ((child.className || "").includes("flexible-height")) {
              container.className = "flexible-height";
            }
          })
          allCharts.push(container);
        }
      });

      let pageItems = [];
      let pageIndex = 0
      allCharts.forEach(chart => {
        let pageContentSize = 0;
        for (let el of pageItems) {
          pageContentSize += el.clientHeight;
        }
        if (pageContentSize + chart.clientHeight >= PDF_PAGE_SIZE) {
          pageIndex++;
          postPage(pageItems, pageIndex);
          pageItems = [];
        }
        let isFlexibleContainer = chart.className.includes("flexible-height");
        if (isFlexibleContainer) {
          let newHeight = pageContentSize + chart.clientHeight;
          if (pageItems.length > 0 && newHeight >= PDF_PAGE_SIZE) {
            // post current page
            pageIndex++;
            postPage(pageItems, pageIndex);
            pageItems = [chart];
            // post new full size page
            pageIndex++;
            postPage(pageItems, pageIndex);
            pageItems = [];
            return;
          }
        }
        pageItems.push(chart);
      });

      if (pageItems.length > 0) {
        pageIndex++;
        postPage(pageItems, pageIndex);
        pageItems = [];
      }

      window.addEventListener("load", () => {
        createFootNote();
        let pageNumbers = document.getElementsByClassName("for-page");
        let pages = document.getElementsByClassName("page");
        for (let el of pageNumbers) {
          const pageCountWithoutCoverPage = pages.length - 1;
          el.innerHTML = el.innerHTML.replace("$totalPageCount", pageCountWithoutCoverPage.toString());
        }
        allChartsNodes.remove();
        setIndentation('.footnote-item-mark', '6px','padding', 'right', 'left');
      });
      // end main

      // utils
      function createPageFooter(number) {
        let footer = document.getElementById("pageFooterTemplate").cloneNode(true);
        footer.innerHTML = footer.innerHTML.replace("$currentPage", number.toString());
        footer.style.display = "flex";
        return footer;
      }

      function createFootNote() {
        let pages = document.getElementsByClassName("page");
        if (pages.length <= 1) return;
        let lastPage = pages[pages.length - 1];
        let lastPageContentSize = 0;
        let showAtTheTopOfThePage = false;
        for (let el of lastPage.childNodes) {
          lastPageContentSize += el.clientHeight;
        }
        if (lastPageContentSize + 34 > PDF_PAGE_SIZE) {
          showAtTheTopOfThePage = true;
          postPage([document.createElement("dev")], pageIndex + 1)
          let pages = document.getElementsByClassName("page");
          lastPage = pages[pages.length - 1];
        }
        let footNote = document.createElement("div");
        footNote.className = "footnote"
        if (showAtTheTopOfThePage)
          footNote.style.top = "54px";

        const footNotes = [
          {% for annotation in annotations %}
            "<span class=\"footnote-item-mark\">*{{ loop.index }}</span><p>{{ annotation.title }}</p>",
          {% endfor %}
        ];
        for (let text of footNotes) {
          let footNoteItem = document.createElement("div");
          footNoteItem.className = "footnote-item";
          footNoteItem.innerHTML = text;
          footNote.appendChild(footNoteItem);
        }
        lastPage.appendChild(footNote);
      }

      function postPage(pageItems, pageNumber) {
        let page = document.createElement("div");
        page.className = "page";

        let imgContainer = document.createElement("div");
        imgContainer.className = "page-logo";
        if (iconUrl !== "None") {
          let image = document.createElement("img");
          image.src = iconUrl;
          imgContainer.appendChild(image);
          const body = document.getElementById('main_body');
          if (body.dir === "rtl"){
            image.style.float = 'left';
          }
        }
        page.appendChild(imgContainer)

        for (let item of pageItems) {
          page.appendChild(item);
        }

        const footer = createPageFooter(pageNumber);
        page.appendChild(footer)
        document.body.appendChild(page);
      }
    </script>
  </body>
</html>
