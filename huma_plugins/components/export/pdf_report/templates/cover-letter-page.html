<div>

  <style>
    .for-title {
      overflow: hidden;
      text-align: center;
      color: #2f3033;
      padding-bottom: 28px;
      display: flex;
    }

    .for-title span {
      font-family: "VictorSerifSmooth-Regular", sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 28px;
      line-height: 28px;
      padding: 0 10px;
    }

    .before-title {
      width: 33px;
    }

    .before-after {
      height: 1px;
      background-color: #909499;
      align-self: center;
    }

    .general-section {
      font-family: 'Noto Sans', sans-serif;
      display: flex;
      padding: 0 38px;
      font-weight: 400;
      font-size: 14px;
      line-height: 28px;
      padding-bottom: 8px;
    }

    .left-section {
      color: #6a6d72;
      margin-right: 14px;
      text-align: end;
    }

    .right-section {
      color: #2f3033;
    }

    .for-img {
      padding: 20px 40px;
    }

    .for-img img {
      height: 19px;
      width: 19px;
    }
  </style>
  <div class="container" dir="{{ textDirection }}">
    <div class="for-img">
      <img
          align="center"
          class="icon"
          height="32"
          src="https://static.cdn.huma.com/fill_8.png"
          style="border: 0"
      />
    </div>
    <div class="for-title">
      <div class="before-after before-title"></div>
      <span id="generalTitle">{{ title }}</span>
      <div class="before-after after-title"></div>
    </div>

    {% for field in fields %}
      {% if field.value %}
        <div class="general-section">
          <div class="left-section">{{ field.title }}</div>
          <div class="right-section">{{ field.value }}</div>
        </div>
      {% endif %}
    {% endfor %}
  </div>
  <script>
    function setWidths() {
      const titleElement = document.getElementById('generalTitle');
      let afterElement = document.querySelector('.after-title');
      // 816 - general width of the page; 33 - before element width;
      afterElement.style.width = 816 - 33 - titleElement.clientWidth + 'px';
      const sections = document.querySelectorAll('.left-section');
      const widths = [ ...sections ].map(i => i.clientWidth)
      const maxWidth = Math.max(...widths)
      sections.forEach((elem) => {
        elem.style.width = maxWidth + 'px';
      });
    }
    window.addEventListener("load", () => {
      setWidths();
      setIndentation('.left-section', '14px', 'margin', 'right', 'left');
      loadedPages ++;  // this variable is a global counter
    });
  </script>
</div>
