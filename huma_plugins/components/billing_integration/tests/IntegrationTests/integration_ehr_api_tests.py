from copy import copy
from pathlib import Path

from huma_plugins.components.billing_integration.adapters.generic.ehr_billing_api import EHRBillingApi
from huma_plugins.components.billing_integration.adapters.utils import convert_huma_billing_report_to_redox
from huma_plugins.components.billing_integration.tests.IntegrationTests.sample_data import (
    new_sample_huma_billings,
    sample_financial_data,
    sample_generic_ehr,
    sample_huma_billing_0,
    sample_huma_billing_1,
    sample_huma_billing_2,
    sample_huma_billing_3,
    sample_huma_billing_4,
    sample_huma_billing_5,
    sample_integration,
    sample_wrong_financial_data,
)
from huma_plugins.components.export.component import ExportComponent
from huma_plugins.components.integration.component import IntegrationComponent
from huma_plugins.components.integration.dtos.generic_ehr import GenericEHRDTO
from huma_plugins.tests.plugin_test_case import PluginsTestCase
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.deployment.component import DeploymentComponent
from sdk.module_result.component import ModuleResultComponent
from sdk.module_result.modules import BloodPressureModule
from sdk.organization.component import OrganizationComponent
from sdk.versioning.component import VersionComponent

USER_ID = "5e8f0c74b50aa9656c34789b"


class IntegrationEHRApiTestCase(PluginsTestCase):
    components = [
        AuthComponent(),
        AuthorizationComponent(),
        DeploymentComponent(),
        IntegrationComponent(),
        ExportComponent(),
        OrganizationComponent(),
        ModuleResultComponent(additional_modules=[BloodPressureModule()]),
        VersionComponent(server_version="1.0.0", api_version="1.0.0"),
    ]
    fixtures = [Path(__file__).parent.joinpath("fixtures/data.json")]
    migration_path: str = str(Path(__file__).parent.parent.parent) + "/migrations"

    def setUp(self):
        super().setUp()

        self.data = sample_financial_data
        self.wrong_data = sample_wrong_financial_data

        generic_ehr = GenericEHRDTO.from_dict(sample_generic_ehr)
        sample_integration.generic_ehr = generic_ehr

        # send data to redox sandbox and check if we get a successful response
        self.ehr_api = EHRBillingApi(sample_integration)

    def test_financial_transaction_with_legacy_authType(self):
        self.ehr_api._integration.generic_ehr.authType = GenericEHRDTO.GenericEHRAuthType.LEGACY

        response = self.ehr_api.send_financial_transactions(self.data)

        self.assertFalse("Errors" in response["Meta"])
        self.assertIsNotNone(response["Meta"]["Source"]["ID"])

    def test_financial_transaction_with_legacy_authType_required_field_missing(self):
        self.ehr_api._integration.generic_ehr.authType = GenericEHRDTO.GenericEHRAuthType.LEGACY

        response = self.ehr_api.send_financial_transactions(self.wrong_data)

        self.assertTrue("Errors" in response["Meta"])

    def test_financial_transaction_with_oauth2_basic_authType(self):
        from unittest.mock import patch

        self.ehr_api._integration.generic_ehr.authType = GenericEHRDTO.GenericEHRAuthType.OAUTH2_BASIC
        self.ehr_api._integration.generic_ehr.client_id = "test_client_id"
        self.ehr_api._integration.generic_ehr.client_secret = "test_client_secret"

        with patch(
            "huma_plugins.components.integration.adapters.generic.ehr_api.EHRApi.login_basic", return_value=True
        ):
            with patch("requests.Session.post") as mock_post:
                from unittest.mock import MagicMock

                mock_response = MagicMock()
                mock_response.ok = True
                mock_response.json.return_value = {"Meta": {"Source": {"ID": "test_source_id"}}}
                mock_post.return_value = mock_response

                response = self.ehr_api.send_financial_transactions(self.data)

                self.assertFalse("Errors" in response["Meta"])
                self.assertIsNotNone(response["Meta"]["Source"]["ID"])

    def test_convert_huma_billing_report_send_to_redox(self):
        self.ehr_api._integration.generic_ehr.authType = GenericEHRDTO.GenericEHRAuthType.LEGACY

        financials_report = convert_huma_billing_report_to_redox(sample_huma_billing_0)
        transactions = financials_report[USER_ID]

        data = copy(self.data)
        data["Transactions"] = transactions

        response = self.ehr_api.send_financial_transactions(data)

        self.assertFalse("Errors" in response["Meta"])
        self.assertIsNotNone(response["Meta"]["Source"]["ID"])

        financials_report = convert_huma_billing_report_to_redox(sample_huma_billing_1)
        transactions = financials_report[USER_ID]

        data = copy(self.data)
        data["Transactions"] = transactions

        response = self.ehr_api.send_financial_transactions(data)

        self.assertFalse("Errors" in response["Meta"])
        self.assertIsNotNone(response["Meta"]["Source"]["ID"])

        financials_report = convert_huma_billing_report_to_redox(sample_huma_billing_2)
        transactions = financials_report[USER_ID]

        data = copy(self.data)
        data["Transactions"] = transactions

        response = self.ehr_api.send_financial_transactions(data)

        self.assertFalse("Errors" in response["Meta"])
        self.assertIsNotNone(response["Meta"]["Source"]["ID"])

        financials_report = convert_huma_billing_report_to_redox(sample_huma_billing_3)
        transactions = financials_report[USER_ID]

        data = copy(self.data)
        data["Transactions"] = transactions

        response = self.ehr_api.send_financial_transactions(data)

        self.assertFalse("Errors" in response["Meta"])
        self.assertIsNotNone(response["Meta"]["Source"]["ID"])

        financials_report = convert_huma_billing_report_to_redox(sample_huma_billing_4)
        transactions = financials_report[USER_ID]

        data = copy(self.data)
        data["Transactions"] = transactions

        response = self.ehr_api.send_financial_transactions(data)

        self.assertFalse("Errors" in response["Meta"])
        self.assertIsNotNone(response["Meta"]["Source"]["ID"])

        financials_report = convert_huma_billing_report_to_redox(sample_huma_billing_5)
        transactions = financials_report[USER_ID]

        data = copy(self.data)
        data["Transactions"] = transactions

        response = self.ehr_api.send_financial_transactions(data)

        self.assertFalse("Errors" in response["Meta"])
        self.assertIsNotNone(response["Meta"]["Source"]["ID"])

    def test_convert_huma_billing_report_send_to_redox_new_changes(self):
        financials_report = convert_huma_billing_report_to_redox(new_sample_huma_billings)

        count = 0

        values = [2, 3, 2, 2, 2, 2, 2]

        for user in financials_report:
            count += 1
            transactions = financials_report[user]

            data = copy(self.data)
            data["Transactions"] = transactions
            self.assertEqual(len(transactions), values[count - 1])

            response = self.ehr_api.send_financial_transactions(data)

            self.assertFalse("Errors" in response["Meta"])
            self.assertIsNotNone(response["Meta"]["Source"]["ID"])

        self.assertEqual(count, 7)
