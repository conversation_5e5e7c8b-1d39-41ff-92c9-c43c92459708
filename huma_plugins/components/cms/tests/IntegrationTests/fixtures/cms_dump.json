{"organization": [{"_id": {"$oid": "63bfdc3e1e863fdf04752583"}, "name": "CMS Organization", "deploymentIds": ["63bfdc3e1e863fdf04752581", "63bfdc3e1e863fdf04752582", "63bfdc3e1e863fdf0475caca", "63bfdc3e1e863fdf0475cdcd", "5d386cc6ff885918d9000001"], "status": "DEPLOYED", "eulaUrl": "https://some_url.com/eulaUrl", "termAndConditionUrl": "https://some_url.com/termAndConditionUrl", "privacyPolicyUrl": "https://some_url.com/privacyPolicyUrl"}], "deployment": [{"_id": {"$oid": "63bfdc3e1e863fdf04752502"}, "name": "CMS Deployment - With bad ModuleConfig pointed to CMS ID", "status": "DRAFT"}, {"_id": {"$oid": "63bfdc3e1e863fdf04752581"}, "name": "CMS Deployment", "status": "DRAFT", "moduleConfigs": [{"id": {"$oid": "644bdea1dfb15c2fdcfd50bc"}, "about": "Sleep config", "configBody": {}, "moduleId": "Sleep", "moduleName": "Sleep", "ragThresholds": [], "schedule": {}, "status": "ENABLED", "goalEnabled": true, "feedback": {"enabled": true, "variabilityFilter": {"increased": ["tag1"], "decreased": ["tag2"], "noChange": ["tag3"], "unavailable": ["tag4"]}}, "feedbackTexts": [{"groupedBy": ["DECREASED"], "notifications": [{"order": 1, "title": "You slept less than usual", "body": "You slept less than usual. This can affect your health and well-being."}, {"order": 2, "title": "You slept less than usual", "body": "You slept less than usual. This can affect your health and well-being."}]}]}, {"id": {"$oid": "616e0156557daa4741a85d14"}, "moduleId": "Questionnaire", "status": "ENABLED", "version": 30, "order": 5, "configBody": {"cmsQuestionnaireId": "670cdd09673fc91575b30001", "questionnaireKind": "CMS", "id": "e932907c-e2c6-4fc7-88a9-72e2bba65431", "version": 2}}, {"id": {"$oid": "677bccaa0bf0350bfba41545"}, "updateDateTime": {"$date": "2025-01-06T12:29:48.132Z"}, "createDateTime": {"$date": "2025-01-06T12:29:30.520Z"}, "moduleId": "Questionnaire", "moduleName": "Questionnaire definition is in deployment", "status": "ENABLED", "order": 4, "configBody": {"pages": [{"type": "INFO", "id": "7023c90f-bdba-45c6-bb93-831a075584ce", "order": 1, "text": "Welcome to our questionnaire placed in Deployment", "description": "Please answer each question to the best of your ability."}], "submissionPage": {"description": "Scroll up to change any of your answers.", "id": "8845dcb4-8471-4944-82b6-daf624a5719c", "text": "You've completed the questionnaire", "buttonText": "Submit", "order": 10, "type": "SUBMISSION"}, "groupedQuestionnaire": false}, "about": "for event_tests.test_event_on_load_deployment_questionnaire_definition", "schedule": {"timesPerDuration": 0, "friendlyText": "AS NEEDED", "timesOfDay": []}, "version": 1}, {"id": {"$oid": "5e94b2007773091c9a592650"}, "about": "string", "configBody": {}, "moduleId": "BloodPressure", "moduleName": "hu_BloodPressure_moduleName", "shortModuleName": "hu_BloodPressure_shortModuleName", "status": "ENABLED", "learnArticleIds": ["5e8f0c74b50aa9656c34132c", "5e8c58176207e5f78023e656"]}], "keyActions": [{"id": {"$oid": "5f078582c565202bd6cb03af"}, "title": "Observation Notes", "description": "Test", "deltaFromTriggerTime": "PT0M", "durationFromTrigger": "P6M", "type": "MODULE", "trigger": "SIGN_UP", "durationIso": "P1WT9H2M", "numberOfNotifications": {"$numberInt": "0"}, "moduleId": "Questionnaire", "moduleConfigId": "5f1824ba504787d8d89ebecb", "updateDateTime": {"$date": {"$numberLong": "1594328450732"}}, "learnArticleId": "5e8f0c74b50aa9656c34132c"}, {"id": {"$oid": "5f078582c565202bd6cb03ad"}, "title": "Observation Notes", "description": "Test", "deltaFromTriggerTime": "PT0M", "durationFromTrigger": "P6M", "type": "MODULE", "trigger": "SIGN_UP", "durationIso": "P1WT9H2M", "numberOfNotifications": {"$numberInt": "0"}, "moduleId": "Questionnaire", "moduleConfigId": "5f1824ba504787d8d89ebecb", "updateDateTime": {"$date": {"$numberLong": "1594328450732"}}, "learnArticleId": "5f1824ba504787d8d89ebecb"}]}, {"_id": {"$oid": "63bfdc3e1e863fdf04752582"}, "name": "CMS Deployment 2", "status": "DEPLOYED", "moduleConfigs": [{"id": {"$oid": "644bdea1dfb15c2fdcfd50bc"}, "about": "Sleep config", "configBody": {}, "moduleId": "Sleep", "moduleName": "Sleep", "ragThresholds": [], "schedule": {}, "status": "ENABLED", "goalEnabled": true, "feedback": {"enabled": true, "variabilityFilter": {"increased": ["tag1"], "decreased": ["tag2"], "noChange": ["tag3"], "unavailable": ["tag4"]}}, "feedbackTexts": [{"groupedBy": ["DECREASED"], "notifications": [{"order": 1, "title": "You slept less than usual", "body": "You slept less than usual. This can affect your health and well-being."}, {"order": 2, "title": "You slept less than usual", "body": "You slept less than usual. This can affect your health and well-being."}]}]}], "keyActions": [{"id": {"$oid": "5f078582c565202bd6cb03af"}, "title": "Observation Notes", "description": "Test", "deltaFromTriggerTime": "PT0M", "durationFromTrigger": "P6M", "type": "MODULE", "trigger": "SIGN_UP", "durationIso": "P1WT9H2M", "numberOfNotifications": {"$numberInt": "0"}, "moduleId": "Questionnaire", "moduleConfigId": "5f1824ba504787d8d89ebecb", "updateDateTime": {"$date": {"$numberLong": "1594328450732"}}, "learnArticleId": "5e8f0c74b50aa9656c34132c"}]}, {"name": "CMS Deployment with a module config pointing not existing CMS Content", "_id": {"$oid": "63bfdc3e1e863fdf0475caca"}, "status": "DEPLOYED", "moduleConfigs": [{"id": {"$oid": "616e0156557daa4741a85d14"}, "moduleId": "Questionnaire", "status": "ENABLED", "version": 30, "order": 5, "configBody": {"cmsQuestionnaireId": "aa0cdd09673fc91575b30001", "questionnaireKind": "CMS", "id": "e932907c-e2c6-4fc7-88a9-72e2bba65431", "name": "the referred CMS Content does not exist", "version": 100}}]}, {"name": "CMS Deployment with a module config pointing to a CMS content with invalid definition", "_id": {"$oid": "63bfdc3e1e863fdf0475cdcd"}, "status": "DEPLOYED", "moduleConfigs": [{"id": {"$oid": "616e0156557daa4741a85d14"}, "moduleId": "Questionnaire", "status": "ENABLED", "version": 30, "order": 5, "configBody": {"cmsQuestionnaireId": "aa0cdd09673fc91575b50001", "questionnaireKind": "CMS", "id": "e932907c-e2c6-4fc7-88a9-72e2bba65431", "name": "the referred CMS Content does not exist", "version": 100}}]}], "huma_auth_user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789a"}, "status": 1, "emailVerified": true, "email": "<EMAIL>"}, {"email": "<EMAIL>", "_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "status": 1, "emailVerified": true}, {"email": "<EMAIL>", "_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "status": 1, "emailVerified": true}, {"email": "<EMAIL>", "_id": {"$oid": "3e8f0c74b50aa9656c34bab0"}, "status": 1, "emailVerified": true}, {"email": "<EMAIL>", "_id": {"$oid": "5e8f0c74b50aa9656c34dada"}, "status": 1, "emailVerified": true}], "user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789a"}, "email": "<EMAIL>", "masterKey": "88888888", "roles": [{"roleId": "SuperAdmin", "resource": "deployment/*", "userType": "SuperAdmin", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "1586422340"}}, "updateDateTime": {"$date": {"$numberInt": "1586422340"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "email": "<EMAIL>", "roles": [{"roleId": "OrganizationOwner", "resource": "organization/63bfdc3e1e863fdf04752583", "isActive": true}]}, {"email": "<EMAIL>", "_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "dateOfBirth": {"$date": {"$numberLong": "572392800000"}}, "biologicalSex": "NOT_SPECIFIED", "gender": "MALE", "roles": [{"roleId": "User", "resource": "deployment/63bfdc3e1e863fdf04752581", "userType": "User", "isActive": true}], "timezone": "UTC", "height": 175}, {"email": "<EMAIL>", "_id": {"$oid": "3e8f0c74b50aa9656c34bab0"}, "dateOfBirth": {"$date": {"$numberLong": "572392800000"}}, "biologicalSex": "NOT_SPECIFIED", "gender": "MALE", "roles": [{"roleId": "User", "resource": "deployment/63bfdc3e1e863fdf0475caca", "userType": "User", "isActive": true}, {"roleId": "User", "resource": "organization/63bfdc3e1e863fdf04752583", "userType": "User", "isActive": true}], "timezone": "UTC", "height": 176}, {"email": "<EMAIL>", "_id": {"$oid": "5e8f0c74b50aa9656c34dada"}, "dateOfBirth": {"$date": {"$numberLong": "572392800000"}}, "biologicalSex": "NOT_SPECIFIED", "gender": "MALE", "roles": [{"roleId": "User", "resource": "deployment/63bfdc3e1e863fdf0475caca", "userType": "User", "isActive": true}, {"roleId": "User", "resource": "organization/63bfdc3e1e863fdf04752583", "userType": "User", "isActive": true}], "timezone": "UTC", "height": 176}], "cms_content": [{"_id": {"$oid": "5e8f0c74b50aa9656c34132c"}, "schema": "articles", "status": "PUBLISHED", "data": {"localizable": [{"title": "CMS Title", "language": "en", "content": "CMS Content", "articleType": "<PERSON><PERSON>", "thumbnail": "5e8f0c74b50aa9656c34712c"}, {"title": "Deutsch title", "language": "de", "content": "Deutsch CMS Content", "articleType": "<PERSON><PERSON>", "thumbnail": "5e8f0c74b50aa9656c34712c"}]}, "tags": ["Asthma", "A"], "resources": ["deployment/63bfdc3e1e863fdf04752581"], "owner": "user/5e8f0c74b50aa9656c34789d", "editors": [], "viewers": [], "createDateTime": {"$date": "2024-04-09T10:00:00.000Z"}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34132d"}, "schema": "articles", "status": "PUBLISHED", "data": {"localizable": [{"title": "CMS Title - A", "language": "en", "content": "CMS Content", "articleType": "<PERSON><PERSON>", "thumbnail": "5e8f0c74b50aa9656c34712c"}]}, "tags": ["Asthma", "B"], "resources": ["deployment/63bfdc3e1e863fdf04752581"], "owner": "user/5e8f0c74b50aa9656c34789d", "editors": [], "viewers": [], "createDateTime": {"$date": "2024-04-09T10:00:00.000Z"}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34102a"}, "data": {}, "draftData": {"localizable": [{"title": "Second article", "thumbnail": "66fc01e3996a098786b2b1e8", "articleType": "<PERSON><PERSON>", "content": "Content to be moved from draft"}]}, "schema": "articles", "status": "DRAFT", "tags": ["apple"], "resources": ["deployment/63bfdc3e1e863fdf04752581"], "owner": "user/5e8f0c74b50aa9656c34789d", "editors": [], "viewers": [], "version": 0, "updateDateTime": {"$date": "2024-11-01T10:19:49.489Z"}, "createDateTime": {"$date": "2024-11-01T10:19:49.489Z"}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34102b"}, "data": {"localizable": [{"title": "CMS Title 1", "thumbnail": "66fc01e3996a098786b2b1e8", "articleType": "<PERSON><PERSON>", "content": "Content to be moved from draft"}]}, "draftData": {"localizable": [{"title": "Third article", "thumbnail": "66fc01e3996a098786b2b1e8", "articleType": "<PERSON><PERSON>", "content": "Content to be moved from draft"}]}, "schema": "articles", "status": "DRAFT", "tags": ["apple"], "resources": ["deployment/63bfdc3e1e863fdf04752581"], "owner": "user/5e8f0c74b50aa9656c34789d", "editors": [], "viewers": [], "version": 0, "updateDateTime": {"$date": "2024-11-01T10:19:49.489Z"}, "createDateTime": {"$date": "2024-11-01T10:19:49.489Z"}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34102c"}, "data": {}, "draftData": {"localizable": [{"title": "First recommendation", "content": "Recommendation content", "language": "en", "reviewer": "<PERSON>"}]}, "schema": "recommendations", "status": "DRAFT", "tags": ["tag1"], "resources": ["deployment/63bfdc3e1e863fdf04752581"], "owner": "user/5e8f0c74b50aa9656c34789d", "editors": [], "viewers": [], "version": 0, "updateDateTime": {"$date": "2024-11-01T10:19:49.489Z"}, "createDateTime": {"$date": "2024-11-01T10:19:49.489Z"}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34102d"}, "data": {}, "draftData": {"localizable": [{"title": "Second recommendation", "content": "Content to be moved from draft", "language": "en", "reviewer": "<PERSON>"}]}, "schema": "recommendations", "status": "DRAFT", "tags": ["tag2"], "resources": ["deployment/63bfdc3e1e863fdf04752581"], "owner": "user/5e8f0c74b50aa9656c34789d", "editors": [], "viewers": [], "version": 0, "updateDateTime": {"$date": "2024-11-01T10:19:49.489Z"}, "createDateTime": {"$date": "2024-11-01T10:19:49.489Z"}}, {"_id": {"$oid": "673605f954c230faf9a1b3d8"}, "data": {"language": "en", "title": "Health Survey", "status": "PUBLISHED", "maxScore": 100, "isHorizontalFlow": true, "trademarkText": "Health Co", "publisherName": "Health Publications", "buttons": [{"kind": "NEXT", "text": "Next"}, {"kind": "SUBMIT", "text": "Submit"}], "pages": [{"order": 1, "text": "Welcome to the Health Survey", "description": "Please answer the following questions to the best of your ability.", "media": [], "buttons": [{"kind": "NEXT", "text": "Continue"}], "type": "INFO", "image": {"key": "img-key-01", "region": "uk1", "bucket": "abc-def"}}, {"order": 2, "id": "4fa85f64-5717-4562-b3fc-2c963f66afa6", "text": "How often do you experience pain?", "description": "Choose the option that best describes your pain frequency.", "type": "QUESTION", "items": [{"order": 1, "format": "TEXTCHOICE", "id": "6fa85f64-5717-4562-b3fc-2c963f66afa6", "required": true, "text": "Frequency of Pain", "description": "", "fields": [], "logic": {"isEnabled": false}, "options": [{"label": "Never", "value": "never"}, {"label": "Occasionally", "value": "occasionally"}, {"label": "Frequently", "value": "frequently"}, {"label": "Always", "value": "always"}], "selectionCriteria": "SINGLE"}]}], "submissionPage": {"id": "9fa85f64-5717-4562-b3fc-2c963f66afa9", "order": 4, "text": "Thank you for completing the survey", "description": "Your responses have been recorded.", "type": "SUBMISSION", "buttonText": "Finish"}, "questionnaireType": "PHQ_8", "dimensions": [{"name": "Physical Health", "formula": "sum(scores)", "maxScore": 50, "showToPatient": true, "showToClinician": true}, {"name": "Mental Health", "formula": "sum(scores)", "maxScore": 50, "showToPatient": true, "showToClinician": true}]}, "draftData": {}, "schema": "questionnaires", "status": "DRAFT", "tags": ["questionnaire", "dev", "test", "pain"], "resources": ["deployment/63bfdc3e1e863fdf04752581"], "owner": "user/5e8f0c74b50aa9656c34789d", "editors": [], "viewers": [], "version": 0, "updateDateTime": {"$date": "2024-11-14T14:15:05.897Z"}, "createDateTime": {"$date": "2024-11-14T14:15:05.897Z"}}, {"_id": {"$oid": "670cdd09673fc91575bea1d7"}, "data": {"title": "Mock data", "version": 3, "about": "FROM CMS", "page": []}, "schema": "questionnaires", "status": "PUBLISHED", "version": 2, "tags": ["test", "sny"], "resources": ["deployment/*"], "owner": "user/5e8f0c74b50aa9656c34789d", "editors": [], "viewers": [], "updateDateTime": {"$date": "2025-01-01T10:00:00.000Z"}, "createDateTime": {"$date": "2024-11-01T10:19:49.489Z"}, "publishDateTime": {"$date": "2024-11-01T10:19:49.489Z"}}, {"_id": {"$oid": "670cdd09673fc91575b30001"}, "schema": "questionnaires", "version": 2, "status": "DRAFT", "data": {"name": "Questionnaire v2 in CMS Content", "pages": [{"0": "673f3a3986a3a47742861472", "id": "dac3a10d-5298-4432-8cd9-0f57da11ea91", "text": "Welcome to our questionnaire", "type": "INFO", "media": ["673f3a3986a3a47742861472"], "order": 1, "description": "Please answer each question to the best of your ability."}, {"0": "673dbc299d88c066a63c10d5", "id": "febae985-b79f-4342-aadc-ed62efd49412", "text": "What is this?", "type": "INFO", "media": ["673dbc299d88c066a63c10d5"], "order": 2, "description": "This is the 2nd info page of <i>dev</i> questionnaire"}, {"type": "QUESTION", "items": [{"id": "fd25ad1a-f895-4637-bfcd-25f1caaf0204", "text": "Do you <b>agree</b> to participate?", "logic": {"rules": [], "isEnabled": false}, "order": 3, "format": "BOOLEAN", "required": true, "description": "Do you want to continue?", "questionType": "BOOLEAN"}], "order": 3}, {"type": "QUESTION", "items": [{"id": "7cd76f1c-36d1-45c3-9f4f-f21c242a362c", "text": "Which team are you in?", "type": "SINGLECHOICE", "logic": {"rules": [{"anyOf": [{"eq": "false", "questionId": "fd25ad1a-f895-4637-bfcd-25f1caaf0204"}], "jumpToId": "144c607e-154d-492a-9ae3-41cbc19a1157"}], "isEnabled": true}, "order": 4, "format": "TEXTCHOICE", "options": [{"label": "Office", "other": {}, "value": "1"}, {"label": "Front", "other": {}, "value": "2"}, {"label": "Back", "other": {}, "value": "3"}, {"label": "Managment", "other": {}, "value": "4"}, {"label": "People", "other": {}, "value": "5"}], "required": false, "description": "What is your team?", "placeholder": "", "selectionCriteria": "SINGLE"}], "order": 4}, {"type": "QUESTION", "items": [{"id": "5a39224d-77a3-4420-8da4-2719f4cb59bc", "text": "Age", "logic": {"reuse": {"loop": {"choices": "unselected", "isEnabled": false}, "recall": {"isEnabled": false}, "questionId": ""}, "rules": [], "isEnabled": false}, "order": 5, "units": [], "format": "NUMERIC", "required": false, "lowerBound": 1, "upperBound": 195, "description": "How old are you?", "maxDecimals": 0, "normalRange": {"lowerBound": 20, "upperBound": 45, "warningMessage": ""}, "questionType": "NUMERIC", "multipleAnswers": {"enabled": false, "maxAnswers": 0}}], "order": 5}, {"0": "673dbc2d9d88c066a63c10db", "id": "144c607e-154d-492a-9ae3-41cbc19a1157", "text": "Sorry", "type": "INFO", "media": ["673dbc2d9d88c066a63c10db"], "order": 6, "description": "We are sorry that you are not interested :-("}], "isForManager": false, "publisherName": "SNY@HUMA", "scoreAvailable": false, "submissionPage": {"id": "ff1f53a0-bd0a-4596-bee2-75f43676d611", "text": "You've completed the questionnaire", "type": "SUBMISSION", "order": 7, "buttonText": "Submit", "description": "Scroll up to change any of your answers."}, "isHorizontalFlow": false, "groupedQuestionnaire": false, "calculatedQuestionnaire": false}, "resources": ["deployment/63bfdc3e1e863fdf04752581"], "owner": "user/5e8f0c74b50aa9656c34789d", "editors": [], "viewers": [], "tags": ["v2"], "createDateTime": {"$date": "2025-01-01T01:00:01.000Z"}}, {"schema": "questionnaires", "_id": {"$oid": "677bccaa0bf0350bfba41545"}, "data": {"pages": [], "name": "This CMS Content supposes to not load to any deployment"}, "draftData": {}, "owner": "user/5e8f0c74b50aa9656c34789d", "editors": [], "viewers": [], "version": 1, "tags": ["Orphan Questionnaire"]}], "cms_change_log": [{"_id": {"$oid": "670cdd09673fc91575b10002"}, "contentId": {"$oid": "670cdd09673fc91575b30001"}, "snapshot": {"data": {"title": "Version 1 in cms change log"}, "draftDate": {}, "schema": "questionnaires", "status": "PUBLISHED", "version": 1, "tags": ["test", "sny", "v1"], "resources": ["deployment/*"], "updateDateTime": {"$date": "2025-01-01T10:00:00.000Z"}, "createDateTime": {"$date": "2024-11-01T10:00:00.000Z"}, "publishDateTime": {"$date": "2025-01-01T10:00:00.000Z"}}, "change": {}, "version": 2, "createDateTime": {"$date": "2024-11-01T10:00:00.000Z"}}, {"_id": {"$oid": "670cdd09673fc91575b10001"}, "contentId": {"$oid": "670cdd09673fc91575b30001"}, "snapshot": {"data": {"title": "Version 0 in CMS Change log"}, "draftDate": {}, "schema": "questionnaires", "status": "DEPLOYED", "version": 0, "tags": ["test", "v0"], "resources": []}, "change": {}, "version": 1, "createDateTime": {"$date": "2024-10-01T10:00:00.000Z"}}]}