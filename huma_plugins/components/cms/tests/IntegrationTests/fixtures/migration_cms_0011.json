{"organization": [{"_id": {"$oid": "680000000000000000000000"}, "name": "CMS Organization", "deploymentIds": ["681000000000000000000001", "681000000000000000000002", "681000000000000000000003"], "status": "DEPLOYED", "eulaUrl": "https://some_url.com/eulaUrl", "termAndConditionUrl": "https://some_url.com/termAndConditionUrl", "privacyPolicyUrl": "https://some_url.com/privacyPolicyUrl"}], "deployment": [{"_id": {"$oid": "681000000000000000000001"}, "name": "DRAFT Deployment", "status": "DRAFT", "moduleConfigs": [{"id": {"$oid": "681010000000000000010001"}, "about": "Deployment Questionnaire in CMS", "configBody": {"cmsQuestionnaireId": "682000000000000000000001", "questionnaireKind": "CMS", "id": "e932907c-e2c6-4fc7-88a9-72e2bba65431"}, "moduleId": "Questionnaire", "ragThresholds": [], "schedule": {}, "status": "ENABLED", "feedbackTexts": []}, {"id": {"$oid": "681010000000000000010002"}, "about": "Deployment Questionnaire in CMS", "configBody": {"cmsQuestionnaireId": "682000000000000000000001", "questionnaireKind": "CMS", "id": "e932907c-e2c6-4fc7-88a9-72e2bba65431"}, "moduleId": "Questionnaire", "ragThresholds": [], "schedule": {}, "status": "ENABLED", "feedbackTexts": []}], "keyActions": []}, {"_id": {"$oid": "681000000000000000000002"}, "name": "DEPLOYED Deployment", "status": "DEPLOYED", "moduleConfigs": [{"id": {"$oid": "681010000000000000020001"}, "about": "Deployment Questionnaire in CMS", "configBody": {"cmsQuestionnaireId": "682000000000000000000001", "questionnaireKind": "CMS", "id": "e932907c-e2c6-4fc7-88a9-72e2bba65431"}, "moduleId": "Questionnaire", "ragThresholds": [], "schedule": {}, "status": "ENABLED", "feedbackTexts": []}, {"id": {"$oid": "681010000000000000020002"}, "about": "Deployment Questionnaire in CMS", "configBody": {"cmsQuestionnaireId": "682000000000000000000001", "questionnaireKind": "CMS", "id": "e932907c-e2c6-4fc7-88a9-72e2bba65431"}, "moduleId": "Questionnaire", "ragThresholds": [], "schedule": {}, "status": "ENABLED", "feedbackTexts": []}], "keyActions": []}, {"_id": {"$oid": "681000000000000000000003"}, "name": "ARCHIVED Deployment", "status": "ARCHIVED", "moduleConfigs": [{"id": {"$oid": "681010000000000000030001"}, "about": "Deployment Questionnaire in CMS", "configBody": {"cmsQuestionnaireId": "682000000000000000000001", "questionnaireKind": "CMS", "id": "e932907c-e2c6-4fc7-88a9-72e2bba65431"}, "moduleId": "Questionnaire", "ragThresholds": [], "schedule": {}, "status": "ENABLED", "feedbackTexts": []}, {"id": {"$oid": "681010000000000000030002"}, "about": "Deployment Questionnaire in CMS", "configBody": {"cmsQuestionnaireId": "682000000000000000000001", "questionnaireKind": "CMS", "id": "e932907c-e2c6-4fc7-88a9-72e2bba65431"}, "moduleId": "Questionnaire", "ragThresholds": [], "schedule": {}, "status": "ENABLED", "feedbackTexts": []}], "keyActions": []}], "cms_content": [{"_id": {"$oid": "682000000000000000000101"}, "schema": "questionnaires", "version": 0, "status": "DRAFT", "data": {"pages": [], "isForManager": false, "publisherName": "SNY@HUMA", "scoreAvailable": false, "submissionPage": {"id": "ff1f53a0-bd0a-4596-bee2-75f43676d611", "text": "in Data", "type": "SUBMISSION", "order": 7, "buttonText": "Submit", "description": "Scroll up to change any of your answers."}, "isHorizontalFlow": false, "groupedQuestionnaire": false, "calculatedQuestionnaire": false}, "draftData": {"pages": [], "isForManager": false, "publisherName": "SNY@HUMA", "scoreAvailable": false, "submissionPage": {"id": "ff1f53a0-bd0a-4596-bee2-75f43676d611", "text": "in Draft Data", "type": "SUBMISSION", "order": 7, "buttonText": "Submit", "description": "Scroll up to change any of your answers."}, "isHorizontalFlow": false, "groupedQuestionnaire": false, "calculatedQuestionnaire": false}, "tags": ["migrated_from_deployment"], "resources": ["deployment/681000000000000000000001"], "owner": "user/5e8000000000000000000001", "editors": [], "viewers": [], "createDateTime": {"$date": "2025-01-01T01:00:01.000Z"}}, {"_id": {"$oid": "682000000000000000000201"}, "schema": "questionnaires", "version": 0, "status": "DRAFT", "data": {"pages": [], "isForManager": false, "publisherName": "SNY@HUMA", "scoreAvailable": false, "submissionPage": {"id": "ff1f53a0-bd0a-4596-bee2-75f43676d611", "text": "in Data", "type": "SUBMISSION", "order": 7, "buttonText": "Submit", "description": "Scroll up to change any of your answers."}, "isHorizontalFlow": false, "groupedQuestionnaire": false, "calculatedQuestionnaire": false}, "draftData": {"pages": [], "isForManager": false, "publisherName": "SNY@HUMA", "scoreAvailable": false, "submissionPage": {"id": "ff1f53a0-bd0a-4596-bee2-75f43676d611", "text": "in Draft Data", "type": "SUBMISSION", "order": 7, "buttonText": "Submit", "description": "Scroll up to change any of your answers."}, "isHorizontalFlow": false, "groupedQuestionnaire": false, "calculatedQuestionnaire": false}, "resources": ["deployment/681000000000000000000002"], "tags": ["migrated_from_deployment"], "owner": "user/5e8000000000000000000001", "editors": [], "viewers": [], "createDateTime": {"$date": "2025-01-01T01:00:01.000Z"}}, {"_id": {"$oid": "682000000000000000000301"}, "schema": "questionnaires", "version": 0, "status": "DRAFT", "data": {"pages": [], "isForManager": false, "publisherName": "SNY@HUMA", "scoreAvailable": false, "submissionPage": {"id": "ff1f53a0-bd0a-4596-bee2-75f43676d611", "text": "in Data", "type": "SUBMISSION", "order": 7, "buttonText": "Submit", "description": "Scroll up to change any of your answers."}, "isHorizontalFlow": false, "groupedQuestionnaire": false, "calculatedQuestionnaire": false}, "draftData": {"pages": [], "isForManager": false, "publisherName": "SNY@HUMA", "scoreAvailable": false, "submissionPage": {"id": "ff1f53a0-bd0a-4596-bee2-75f43676d611", "text": "in Draft Data", "type": "SUBMISSION", "order": 7, "buttonText": "Submit", "description": "Scroll up to change any of your answers."}, "isHorizontalFlow": false, "groupedQuestionnaire": false, "calculatedQuestionnaire": false}, "resources": ["deployment/681000000000000000000003"], "tags": ["migrated_from_deployment"], "owner": "user/5e8000000000000000000001", "editors": [], "viewers": [], "createDateTime": {"$date": "2025-01-01T01:00:01.000Z"}}]}