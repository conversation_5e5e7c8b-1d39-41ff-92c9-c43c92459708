import unittest
from pathlib import Path


from huma_plugins.components.cms.models.content import CMSContent
from huma_plugins.components.cms.dtos import Status as CMSStatus
from sdk.deployment.models import Deployment
from sdk.deployment.dtos.status import Status as DeploymentStatus
from ._base import BaseCMSRouterTestCase


class CMSQuestionnaireMigrationElevenTestCase(BaseCMSRouterTestCase):
    DEPLOYMENT_ID = "681000000000000000000000"
    SUPER_USER_ID = "5e8000000000000000000001"
    CMS_ID = "682000000000000000000001"
    fixtures = [
        Path(__file__).parent.joinpath("fixtures/migration_cms_0011.json"),
    ]
    override_config = {
        "server.adapters.inMemoryFeatureToggle.keys": {
            "hu-cms-questionnaires": True,
            "cms_questionnaire": True,
        },
    }

    CMS_STATUS_TO_DEPLOYMENT_STATUS = {
        CMSStatus.DRAFT: DeploymentStatus.DRAFT,
        CMSStatus.PUBLISHED: DeploymentStatus.DEPLOYED,
        CMSStatus.ARCHIVED: DeploymentStatus.ARCHIVED,
    }

    DEPLOYMENT_STATUS_TO_CMS_STATUS = {
        DeploymentStatus.DRAFT: CMSStatus.DRAFT,
        DeploymentStatus.DEPLOYED: CMSStatus.PUBLISHED,
        DeploymentStatus.ARCHIVED: CMSStatus.ARCHIVED,
    }

    class App:
        @staticmethod
        def get_model(app_name, model_name):
            if model_name == "CMSContent":
                return CMSContent
            elif model_name == "Deployment":
                return Deployment
            raise ModuleNotFoundError()

    def setUp(self):
        super().setUp()
        self.headers.update(self.get_headers_for_token(self.SUPER_USER_ID))

        from importlib import import_module

        migration_module = import_module(
            "huma_plugins.components.cms.migrations.0011_fix_missing_draftdata_in_draft_cms"
        )

        migration_module.fill_auto_stored_cms_questionnaire_data_and_draftdata_and_status(self.App(), None)

    def check_cms_and_deployment_matches(self, deployment: Deployment, content: CMSContent):
        if content.status != CMSStatus.DRAFT:
            return

        cms_status_must_be = self.DEPLOYMENT_STATUS_TO_CMS_STATUS.get(deployment.status, None)

        self.assertEqual(
            cms_status_must_be,
            content.status,
            f"Deployment id:{deployment.id}-{deployment.mongoId}: {deployment.status} | "
            f"Content Id: {content.id}-{content.mongoId}-{content.status}: {content.status}",
        )

        if content.status == CMSStatus.PUBLISHED:
            self.assertIsNotNone(content.data)
        elif content.status == CMSStatus.DRAFT:
            self.assertIsNotNone(content.draftData)
        elif content.status == CMSStatus.ARCHIVED:
            self.assertIsNotNone(content.data)

    def test_check_all_contents(self):
        deployments = Deployment.objects.all()
        deployments_by_id = {d.mongoId: d for d in deployments}

        contents = CMSContent.objects.all()

        for content in contents:
            res: str = content.resources[0]
            self.assertIsInstance(res, str)
            deployment_id: str = res.split("/")[1]
            self.assertIsInstance(deployment_id, str)

            deployment: Deployment = deployments_by_id[deployment_id]
            self.check_cms_and_deployment_matches(
                deployment=deployment,
                content=content,
            )


if __name__ == "__main__":
    unittest.main()
