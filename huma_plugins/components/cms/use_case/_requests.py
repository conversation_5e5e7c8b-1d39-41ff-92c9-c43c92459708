from dataclasses import field
from typing import Self

import jsonschema
import marshmallow
from aenum import StrEnum
from flask import g, request

from huma_plugins.components.cms.dtos import CMSContentDTO, Status
from huma_plugins.components.cms.library import ASSET_COLLECTION_NAME, CMSLibrary
from huma_plugins.components.cms.use_case.advanced_query.validators import (
    validate_query,
)
from huma_plugins.components.cms.utils import validate_collection_exists
from huma_plugins.components.cms.utils.utils import normalize_version
from sdk import convertibleclass
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.common.common_models.sort import SortField
from sdk.common.constants import CONTAINS
from sdk.common.exceptions.exceptions import PermissionDenied
from sdk.common.usecase.request_object import RequestObject
from sdk.common.utils import inject
from sdk.common.utils.convertible import (
    default_field,
    meta,
    natural_number_field,
    positive_integer_field,
    required_field,
)
from sdk.common.utils.fields import id_field
from sdk.common.utils.validators import (
    must_be_at_least_one_of,
    not_empty,
    validate_entity_name,
    validate_len,
)


@convertibleclass
class ContentRequestObject(RequestObject):
    """A base class for content request to consume a collection from view args"""

    COLLECTION = "collection"

    collection: str = required_field(metadata=meta(validator=not_empty))

    @marshmallow.validates("collection")
    def validate_schema(self, value: str, **kwargs):
        if not inject.instance(CMSLibrary).get_schema(value):
            raise marshmallow.ValidationError(f"Collection {value} not found")

    @marshmallow.pre_load
    def add_collection(self, data, **kwargs):
        if ContentRequestObject.COLLECTION in data:
            return data

        if request.view_args.get("collection") == ASSET_COLLECTION_NAME:
            raise marshmallow.ValidationError("Collection assets not found")
        if not request.view_args.get("collection"):
            request.view_args["collection"] = ASSET_COLLECTION_NAME

        data[ContentRequestObject.COLLECTION] = request.view_args.get("collection")
        return data

    @classmethod
    def validate(cls, obj: Self) -> None:
        if not hasattr(obj, "collection") or obj.collection is None:
            return
        validate_collection_exists(obj.collection)


@convertibleclass
class CreateContentRequestObject(CMSContentDTO, RequestObject):
    @marshmallow.pre_load
    def add_schema(self, data, **kwargs):
        if request.view_args.get("collection") == ASSET_COLLECTION_NAME:
            raise marshmallow.ValidationError("Collection assets not found")
        if not request.view_args.get("collection"):
            request.view_args["collection"] = ASSET_COLLECTION_NAME

        resource_id = g.authz_user.deployment_id()
        if not resource_id or resource_id == "*":
            raise PermissionDenied("Not able to identify requested resources")

        if g.authz_user.deployment.status != Status.DRAFT.value:
            raise PermissionDenied("deployment is not in draft status")

        resources = [f"deployment/{resource_id}"]
        if data.get(CMSContentDTO.RESOURCES) and data.get(CMSContentDTO.RESOURCES) != resources:
            raise PermissionDenied("This resource is not allowed")

        data.update(
            {
                CMSContentDTO.RESOURCES: resources,
                CMSContentDTO.SCHEMA: request.view_args.get("collection"),
            }
        )
        return data

    @marshmallow.validates("schema")
    def validate_schema(self, value: str, **kwargs):
        if not inject.instance(CMSLibrary).get_schema(value):
            raise marshmallow.ValidationError(f"Collection {value} not found")

    @classmethod
    @marshmallow.validates_schema
    def validate_data(cls, value: dict, **kwargs):
        library = inject.instance(CMSLibrary)
        must_be_at_least_one_of(data=value.get("data"), draftData=value.get("draftData"))
        try:
            if data := value.get("data"):
                library.validate_content(data, value.get("schema"))
            if draft_data := value.get("draftData"):
                library.validate_content(draft_data, value.get("schema"))
        except jsonschema.exceptions.ValidationError as e:
            raise marshmallow.ValidationError(str(e))
        if Status.DRAFT == value.get(CMSContentDTO.status, None) and value.get(CMSContentDTO.DRAFT_DATA, None) is None:
            raise marshmallow.ValidationError("draftData is required when status is DRAFT")


@convertibleclass
class ContentIdRequestObject(ContentRequestObject):
    CONTENT_ID = "contentId"

    contentId: str = id_field(required=True)
    authzUser: AuthorizedUser = default_field(metadata=meta(dump_only=True))

    @marshmallow.pre_load
    def content_id(self, data, **kwargs):
        data[ContentIdRequestObject.CONTENT_ID] = data.pop("content_id")
        return data

    @marshmallow.post_load()
    def add_user(self, data, **kwargs):
        data[RequestObject.AUTHZ_USER] = g.authz_user
        return data


@convertibleclass
class ContentIdVersionRequestObject(ContentIdRequestObject):
    version: int = default_field(
        metadata=meta(
            value_to_field=normalize_version,
            field_to_value=normalize_version,
            example="latest",
        ),
        default="latest",
    )
    collection: str = required_field(
        metadata=meta(example=ASSET_COLLECTION_NAME),
    )

    @classmethod
    def validate(cls, obj: Self) -> None:
        if hasattr(obj, "collection") and obj.collection is not None:
            validate_collection_exists(obj.collection)

        if hasattr(obj, "version") and obj.version is not None:
            if obj.version not in ("latest", ""):
                try:
                    int(obj.version)
                except ValueError:
                    raise marshmallow.ValidationError(f"Version {obj.version} is not a valid integer")


@convertibleclass
class RetrieveConvertedContentRequestObject(RequestObject):
    COLLECTION = "collection"
    CONTENT_IDS = "contentIds"

    collection: str = required_field()
    contentIds: list[str] = required_field()
    authzUser: AuthorizedUser = default_field()
    language: str = default_field()
    unpublished: bool = field(default=False)


@convertibleclass
class PublishContentRequestObject(ContentIdRequestObject):
    PUBLISHER_ID = "publisherId"

    publisherId: str = id_field(required=True)

    @marshmallow.pre_load
    def publisher_id(self, data, **kwargs):
        data[PublishContentRequestObject.PUBLISHER_ID] = g.user.id
        return data


@convertibleclass
class SearchContentRequestObject(ContentRequestObject):
    FILTERS = "filters"
    SKIP = "skip"
    LIMIT = "limit"

    search: str = default_field(metadata=meta(validate_entity_name, value_to_field=str.strip))
    filters: dict = default_field()
    sort: list[SortField] = default_field()
    skip: int = positive_integer_field()
    limit: int = natural_number_field()
    unpublished: bool = default_field()  # attempts to get draft data if present
    authzUser: AuthorizedUser = default_field(metadata=meta(dump_only=True))

    @marshmallow.pre_load
    def add_default_values(self, data, **kwargs):
        resource_id = g.authz_user.deployment_id()
        if not resource_id or resource_id == "*":
            raise PermissionDenied("Not able to identify requested resources")

        filters = data.get("filters") or {}
        filters.update({CMSContentDTO.RESOURCES: {CONTAINS: f"deployment/{resource_id}"}})
        data["filters"] = filters
        return data

    @marshmallow.post_load()
    def add_user(self, data, **kwargs):
        data[RequestObject.AUTHZ_USER] = g.authz_user
        return data


@convertibleclass
class AdvancedSearchContentRequestObject(ContentRequestObject):
    class Sort(StrEnum):
        NEWEST = "NEWEST"
        OLDEST = "OLDEST"
        RANDOM = "RANDOM"

    submitter: AuthorizedUser = required_field()
    query: str = default_field()
    tags: list[str] = field(default_factory=list, metadata=meta(validate_len(0, 50)))
    cmsContentIds: list[str] = default_field()
    sort: Sort = field(default=Sort.OLDEST)

    @classmethod
    def validate(cls, search: Self) -> None:
        must_be_at_least_one_of(
            query=search.query or None,
            cmsRecommendationIds=search.cmsContentIds or None,
            tags=search.tags or None,
        )
        validate_query(search.query)

    def build_query(self) -> str:
        """Build a query string based on the configuration."""
        query = self.query or "articles"
        if self.cmsContentIds:
            query = f"filter({query}, ids='{','.join(self.cmsContentIds)}')"
        if self.tags:
            query = f"filter({query}, tags='{','.join(self.tags)}')"
        if self.sort:
            query = f"sort({query}, by='{self.sort}')"

        return query


@convertibleclass
class UpdateContentRequestObject(CreateContentRequestObject):
    id: str = id_field()
    resources: list[str] = default_field(metadata=meta(dump_only=True))

    @marshmallow.pre_load
    def add_id(self, data, **kwargs):
        data.update({CMSContentDTO.ID: request.view_args.get("content_id")})
        return data


@convertibleclass
class RetrieveCollectionTagsRequestObject(ContentRequestObject):
    """Retrieve all tags for a collection in the resource"""

    resource: str = default_field(metadata=meta(dump_only=True))

    @marshmallow.post_load()
    def add_resource(self, data, **kwargs):
        data["resource"] = f"deployment/{g.authz_user.deployment_id()}"
        return data
