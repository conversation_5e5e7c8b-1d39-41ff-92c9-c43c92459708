from huma_plugins.components.cms.repo import CMSRepository
from sdk.common.usecase.response_object import Response
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.convertible import convertibleclass, required_field
from sdk.common.utils.inject import autoparams
from ._requests import CreateContentRequestObject


@convertibleclass
class CreateContentResponseObject(Response):
    id: str = required_field()


class CreateContentUseCase(UseCase):
    """Create a content item in a chosen collection"""

    request_object: CreateContentRequestObject

    @autoparams("repo")
    def __init__(self, repo: CMSRepository):
        self._repo = repo

    def process_request(self, request_object: CreateContentRequestObject) -> Response:
        content_id = self._repo.create(content=request_object)
        return CreateContentResponseObject(id=content_id)
