# Generated by Django 5.1.10 on 2025-06-20 18:28
import logging

from django.db import migrations
from django.db.models import Q


from sdk.deployment.models import Deployment

from huma_plugins.components.cms.dtos import Status as CMSStatus
from huma_plugins.components.cms.library import (
    QUESTIONNAIRE_COLLECTION_NAME,
    TAG_CMS_QUESTIONNAIRE_AUTO_STORE,
    TAG_CMS_QUESTIONNAIRE_MIGRATED,
)
from sdk.deployment.dtos.status import Status as DeploymentStatus

MAX_BATCH_SIZE = 1000

logger = logging.getLogger(__name__)


def _extract_deployment_id(resources: list[str] | None) -> str | None:
    if not resources:
        return None
    for resource in resources:
        if resource and resource.startswith("deployment/"):
            return resource.split("/", 1)[1]
    return None


def fill_auto_stored_cms_questionnaire_data_and_draftdata_and_status(apps, schema_editor):
    """
    This data migration processes 'questionnaire' CMSContent items that were auto-stored or migrated.
    It aims to fix two potential inconsistencies:
    1. For items linked to a DRAFT deployment, it ensures `draftData` is populated by copying `data` if `draftData` is empty.
    2. It synchronizes the `status` of the CMSContent with the status of its associated Deployment.
    """
    DEPLOYMENT_STATUS_TO_CONTENT_STATUS = {
        DeploymentStatus.DRAFT: CMSStatus.DRAFT,
        DeploymentStatus.DEPLOYED: CMSStatus.PUBLISHED,
        DeploymentStatus.ARCHIVED: CMSStatus.ARCHIVED,
    }

    try:
        CMSContent = apps.get_model("cms", "CMSContent")
    except LookupError as exception:
        logger.error(f"Could not find one of the models. Skipping migration logic.{exception}")
        raise

    tags_q = Q(tags__contains=[TAG_CMS_QUESTIONNAIRE_MIGRATED]) | Q(tags__contains=[TAG_CMS_QUESTIONNAIRE_AUTO_STORE])
    contents_to_process = (
        CMSContent.objects.filter(
            schema=QUESTIONNAIRE_COLLECTION_NAME,
            status=CMSStatus.DRAFT,
        )
        .filter(tags_q)
        .distinct()
    )

    count = contents_to_process.count()
    logger.info(f"Found {count} CMSContent objects to process.")
    if count == 0:
        return

    # Cache for deployments to minimize DB hits
    deployment_cache: dict[str, Deployment] = {}
    contents_to_update = []
    updated_draft_data_count = 0
    updated_status_count = 0
    contents_updated_count = 0
    updated_data_count = 0

    # The .iterator() used to reduce memory usage for large querysets
    for content in contents_to_process.iterator():
        deployment_id = _extract_deployment_id(content.resources)

        if not deployment_id:
            logger.warning(f"CMSContent(id={content.id}) has no deployment resource, skipping.")
            continue

        deployment = deployment_cache.get(deployment_id)
        if not deployment:
            try:
                # Fetch only the 'status' field for efficiency
                deployment = Deployment.objects.only("status").get(mongoId=deployment_id)
                deployment_cache[deployment_id] = deployment
            except Deployment.DoesNotExist:
                logger.warning(f"Deployment(id={deployment_id}) for CMSContent(id={content.id}) not found, skipping.")
                continue

        has_changed = False

        # 1. Sync status
        expected_status = DEPLOYMENT_STATUS_TO_CONTENT_STATUS.get(deployment.status, None)
        if expected_status is not None and content.status != expected_status:
            content.status = expected_status
            updated_status_count += 1
            has_changed = True

        # 2. Fill draftData if needed
        if deployment.status == DeploymentStatus.DRAFT and not content.draftData:
            content.draftData = content.data
            updated_draft_data_count += 1
            has_changed = True

        # 3. Fill data if needed
        if deployment.status != DeploymentStatus.DRAFT and not content.data and content.draftData:
            content.data = content.draftData
            updated_data_count += 1
            has_changed = True

        if has_changed:
            contents_to_update.append(content)
            if len(contents_to_update) >= MAX_BATCH_SIZE:
                logger.info(f"Preparing to bulk update {len(contents_to_update)} CMSContent objects.")
                CMSContent.objects.bulk_update(contents_to_update, fields=["draftData", "data", "status"])
                contents_updated_count += len(contents_to_update)
                contents_to_update = []
                logger.info(f"Updated {contents_updated_count} CMSContent objects yet.")

    if contents_to_update:
        logger.info(f"Preparing to bulk update {len(contents_to_update)} CMSContent objects.")
        CMSContent.objects.bulk_update(contents_to_update, fields=["draftData", "data", "status"])

    logger.info("Bulk update complete.")

    logger.info(
        f"Migration finished. "
        f"Updated `draftData` for {updated_draft_data_count} contents. "
        f"Updated `data` for {updated_data_count} contents. "
        f"Updated `status` for {updated_status_count} contents. "
        f"Total content modified: {len(contents_to_update) + contents_updated_count}."
    )


class Migration(migrations.Migration):
    dependencies = [
        ("cms", "0010_add_auto_stored_in_cms_tag_to_on_store_event"),
    ]

    operations = [
        migrations.RunPython(
            code=fill_auto_stored_cms_questionnaire_data_and_draftdata_and_status,
            reverse_code=migrations.RunPython.noop,
            hints={"model_name": ["CMSContent", "Deployment"]},
            atomic=True,
            elidable=False,
        )
    ]
