[project]
name = "billing"
version = "1.0.0"
description = "A collection of Billing plugins"
requires-python = ">=3.11"
dependencies = [
    "framework",
    "huma-plugins",
    "sdk",
]

[dependency-groups]
dev = [
    "coverage",
    "freezegun",
    "packaging",
    "pre-commit",
    "pytest-cov",
    "pytest-django",
    "yamllint",
    "parameterized",
    "pytest-xdist==3.6.1"
]

[tool.uv.sources]
framework = { git = "https://github.com/huma-engineering/huma-server-platform.git", rev = "v2.4.4" }
huma_plugins = { path = "../huma-server-plugins" }
sdk = { path = "../huma-server-sdk"}
