# kotlinx.serialization
-keep class kotlinx.serialization.** { *; }
-keep @kotlinx.serialization.Serializable class * { *; }
-keepclassmembers class ** {
    @kotlinx.serialization.Serializable *;
}
-keepclassmembers @kotlinx.serialization.Serializable class * { *; }
-keep class com.huma.sdk.servier.surgicalchecklist.source.SurgicalChecklistWidgetConfig { *; }
-keep class com.huma.sdk.servier.surgicalchecklist.source.SurgicalChecklistWidgetConfig$$serializer { *; }
