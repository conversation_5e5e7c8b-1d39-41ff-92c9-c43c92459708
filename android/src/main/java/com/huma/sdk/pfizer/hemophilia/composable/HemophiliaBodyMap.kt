package com.huma.sdk.pfizer.hemophilia.composable

import androidx.annotation.Keep
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.absoluteOffset
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.huma.sdk.core.utils.ext.Helpers.dpToPx
import com.huma.sdk.pfizer.R
import com.huma.sdk.ui.components.base.Palette
import com.huma.sdk.ui.components.base.toComposeColor
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.humastyle.HumaTypeStyler
import com.huma.sdk.ui.utils.ext.throttledClickable

@Keep
val DEFAULT_BODY_MAP_ITEMS_1 = HemophiliaBodyMap12Points.entries.map {
    HemophiliaBodyMapPoint(
        it.name,
        Palette.Base.GRAY_GALLERY.toComposeColor(),
        HemophiliaBodyMapLocation(it.relativeX, it.relativeY),
    )
}

@Keep
val DEFAULT_BODY_MAP_ITEMS_2 = HemophiliaBodyMap12Points.entries.map {
    HemophiliaBodyMapPoint(
        it.name,
        Palette.Base.GRAY_SILVER_SAND.toComposeColor(),
        HemophiliaBodyMapLocation(it.relativeX, it.relativeY),
    )
}

enum class HemophiliaBodyMap12Points(
    val relativeX: Float,
    val relativeY: Float,
) {
    LEFT_SHOULDER(0.70f, 0.20f),
    RIGHT_SHOULDER(-0.70f, 0.20f),
    LEFT_ELBOW(0.80f, 0.37f),
    RIGHT_ELBOW(-0.80f, 0.37f),
    LEFT_WRIST(0.85f, 0.48f),
    RIGHT_WRIST(-0.85f, 0.48f),
    LEFT_HIP(0.45f, 0.55f),
    RIGHT_HIP(-0.45f, 0.55f),
    LEFT_KNEE(0.30f, 0.75f),
    RIGHT_KNEE(-0.30f, 0.75f),
    LEFT_ANKLE(0.20f, 0.91f),
    RIGHT_ANKLE(-0.20f, 0.91f),
}

data class HemophiliaBodyMapPoint(
    val id: String,
    val color: Color,
    val location: HemophiliaBodyMapLocation,
)

data class HemophiliaBodyMapLocation(
    val relativeX: Float,
    val relativeY: Float,
)

@Preview(showBackground = true)
@Composable
private fun PreviewHemophiliaBodyMap() {
    HemophiliaBodyMap(
        modifier = Modifier.height(500.dp),
    )
}

@Composable
fun HemophiliaBodyMap(
    modifier: Modifier = Modifier,
    bodyPoints: List<HemophiliaBodyMapPoint> = DEFAULT_BODY_MAP_ITEMS_1,
    isEnabled: Boolean = true,
    onTouchPointClick: (HemophiliaBodyMapPoint) -> Unit = {},
    selectedPointId: String? = null,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
    ) {
        var imageSize by remember {
            mutableStateOf<IntSize?>(null)
        }
        Image(
            painter = painterResource(R.drawable.hsdk_body_map),
            contentDescription = null,
            contentScale = ContentScale.FillHeight,
            modifier = Modifier
                .align(Alignment.Center)
                .fillMaxHeight()
                .onGloballyPositioned {
                    imageSize = it.size
                },
        )
        imageSize?.let {
            bodyPoints.forEach { bodyPoint ->
                val offsetX = it.width.toFloat() / 2 * bodyPoint.location.relativeX
                val offsetY = it.height.toFloat() * bodyPoint.location.relativeY

                val touchPointModifier = Modifier
                    .fillMaxHeight(0.045f)
                    .aspectRatio(1f)
                    .align(Alignment.TopCenter)
                    .absoluteOffset {
                        IntOffset(x = offsetX.toInt(), y = offsetY.toInt())
                    }
                    .clip(CircleShape)
                TouchPoint(
                    color = bodyPoint.color,
                    modifier = touchPointModifier.throttledClickable(isEnabled) {
                        onTouchPointClick(bodyPoint)
                    },
                )
                if (selectedPointId == bodyPoint.id) {
                    Image(
                        painterResource(R.drawable.hsdk_ic_touchpoint_selected),
                        contentDescription = null,
                        modifier = touchPointModifier,
                    )
                }
            }

            val offsetX =
                LocalConfiguration.current.screenWidthDp.dpToPx(LocalContext.current) / 2 * 0.65f
            val offsetY = it.height * 0.74
            // Left sign (Right on screen)
            DirectionSign(
                text = stringResource(R.string.plugin_hemophilia_body_map_left),
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .absoluteOffset {
                        IntOffset(offsetX.toInt(), offsetY.toInt())
                    }
            )

            // Right sign (Left on screen)
            DirectionSign(
                text = stringResource(R.string.plugin_hemophilia_body_map_right),
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .absoluteOffset {
                        IntOffset(-offsetX.toInt(), offsetY.toInt())
                    }
            )
        }
    }
}

@Composable
private fun DirectionSign(
    text: String,
    modifier: Modifier = Modifier,
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .size(30.dp)
            .border(1.dp, Palette.Base.SILVER_SAND.toComposeColor(), CircleShape)
    ) {
        BaseText(
            text = text,
            style = HumaTypeStyler.action1,
            textColor = Palette.Base.GRAY_OSLO,
        )
    }
}

@Composable
private fun TouchPoint(
    color: Color,
    modifier: Modifier = Modifier,
) {
    Canvas(
        modifier = modifier
            .background(Color.White, CircleShape)
    ) {
        val strokeWidth = (size.minDimension * 1.25f / 20)
        drawCircle(
            color,
            radius = size.minDimension / 2 - strokeWidth / 2,
            style = Stroke(width = strokeWidth)
        )
        drawCircle(
            color,
            radius = size.minDimension * 12.5f / 20 / 2
        )
    }
}
