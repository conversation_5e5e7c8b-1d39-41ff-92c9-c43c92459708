package com.huma.sdk.pfizer.hemophilia.data

import androidx.annotation.Keep
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Keep
@Serializable
data class HemophiliaWidgetDataResponse(
    @SerialName("title")
    val title: String? = null,
    @SerialName("description")
    val description: String? = null,
    @SerialName("primaryCTAtext")
    val primaryCTAText: String? = null,
    @SerialName("secondaryCTAtext")
    val secondaryCTAText: String? = null,
    @SerialName("state")
    val state: String? = null,
    @SerialName("legend")
    val legend: List<LegendItemResponse>? = null,
    @SerialName("bodyMapColor")
    val bodyMapColor: List<BodyMapItemResponse>? = null,
    @SerialName("hasSubmissions")
    val hasSubmissions: Boolean? = null,
    @SerialName("results")
    val results: List<HemophiliaModuleResultResponse>? = null,
) {
    @Keep
    @Serializable
    data class LegendItemResponse(
        @SerialName("color")
        val color: String? = null,
        @SerialName("label")
        val label: String? = null,
    )

    @Keep
    @Serializable
    data class BodyMapItemResponse(
        @SerialName("location")
        val location: String? = null,
        @SerialName("color")
        val color: String? = null,
    )

    @Keep
    @Serializable
    data class HemophiliaModuleResultResponse(
        @SerialName("id")
        val id: String? = null,
    )
}
