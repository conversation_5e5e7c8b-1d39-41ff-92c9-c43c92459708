package com.huma.sdk.pfizer.hemophilia.model

import androidx.annotation.Keep

@Keep
enum class HemophiliaBodyLocationType(
    val relativeX: Float,
    val relativeY: Float,
    val isHidden: Boolean = false,
) {
    LEFT_SHOULDER(0.70f, 0.20f),
    RIGHT_SHOULDER(-0.70f, 0.20f),
    LEFT_ELBOW(0.80f, 0.37f),
    RIGHT_ELBOW(-0.80f, 0.37f),
    LEFT_WRIST(0.85f, 0.48f),
    RIGHT_WRIST(-0.85f, 0.48f),
    LEFT_HIP(0.45f, 0.55f),
    RIGHT_HIP(-0.45f, 0.55f),
    LEFT_KNEE(0.30f, 0.75f),
    RIGHT_KNEE(-0.30f, 0.75f),
    LEFT_ANKLE(0.20f, 0.91f),
    RIGHT_ANKLE(-0.20f, 0.91f),
    <PERSON><PERSON><PERSON>(0f, 0f, true),
}
