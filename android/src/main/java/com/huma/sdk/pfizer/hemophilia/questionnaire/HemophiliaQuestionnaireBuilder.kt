package com.huma.sdk.pfizer.hemophilia.questionnaire

import android.content.Context
import androidx.annotation.Keep
import androidx.annotation.StringRes
import com.huma.sdk.module.medicationv2.hemophilia.builder.HemophiliaMedicationV2QuestionnaireBuilder.medicationFlow
import com.huma.sdk.module.medicationv2.hemophilia.select_medication.SelectMedicationFilter
import com.huma.sdk.module.medicationv2.hemophilia.select_medication.SelectMedicationSource
import com.huma.sdk.module.medicationv2.hemophilia.select_medication.SelectMedicationType
import com.huma.sdk.module.medicationv2.hemophilia.select_medication.selectMedication
import com.huma.sdk.pfizer.R
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjuryReason
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjurySeverity
import com.huma.sdk.questionnaire.core.domain.entities.Form
import com.huma.sdk.questionnaire.core.domain.entities.InputType
import com.huma.sdk.questionnaire.core.domain.entities.questions.metadata.MediaQuestionAnswerFormat
import com.huma.sdk.questionnaire.core.domain.source.builder.boolean
import com.huma.sdk.questionnaire.core.domain.source.builder.date
import com.huma.sdk.questionnaire.core.domain.source.builder.ext.ifFalse
import com.huma.sdk.questionnaire.core.domain.source.builder.ext.ifTrue
import com.huma.sdk.questionnaire.core.domain.source.builder.form
import com.huma.sdk.questionnaire.core.domain.source.builder.logic
import com.huma.sdk.questionnaire.core.domain.source.builder.multiChoice
import com.huma.sdk.questionnaire.core.domain.source.builder.numeric
import com.huma.sdk.questionnaire.core.domain.source.builder.options
import com.huma.sdk.questionnaire.core.domain.source.builder.page.params.FileValidationParams
import com.huma.sdk.questionnaire.core.domain.source.builder.pages
import com.huma.sdk.questionnaire.core.domain.source.builder.photo
import com.huma.sdk.questionnaire.core.domain.source.builder.scale
import com.huma.sdk.questionnaire.core.domain.source.builder.singleChoice
import com.huma.sdk.questionnaire.core.domain.source.builder.submissionPage
import com.huma.sdk.questionnaire.core.domain.source.builder.text
import com.huma.sdk.unitskit.utils.MAX_WEIGHT_LBS
import com.huma.sdk.unitskit.utils.MIN_WEIGHT_LBS
import org.threeten.bp.LocalDate

@Keep
object HemophiliaQuestionnaireBuilder {
    fun forProfile(context: Context): Form {
        return form(PROFILE_QUESTIONNAIRE_ID) {
            name = context.getString(R.string.plugin_hemophilia_profile_questionnaire_title)
            pages {
                numeric {
                    id = HEMOPHILIA_WEIGHT_ID
                    text = context.getString(R.string.module_weight_input_subtitle)
                    hint = context.getString(R.string.plugin_hemophilia_enter_your_weight)
                    validationErrorMessage = context.getString(
                        R.string.validation_height_range,
                        MIN_WEIGHT_LBS.toFloat(),
                        MAX_WEIGHT_LBS.toFloat()
                    )
                    isRequired = true
                    upperBound = MAX_WEIGHT_LBS
                    lowerBound = MIN_WEIGHT_LBS
                    units = listOf("lb")
                    maxDecimals = 1
                }

                singleChoice {
                    id = HEMOPHILIA_TYPE_ID
                    text = context.getString(
                        R.string.plugin_hemophilia_please_indicate_your_type_of_hemophilia
                    )
                    isRequired = true
                    options {
                        HemophiliaType.entries.map {
                            it.name to context.getString(it.titleRes)
                        }
                    }
                }

                singleChoice {
                    id = HEMOPHILIA_SEVERITY_ID
                    text = context.getString(
                        R.string.plugin_hemophilia_please_indicate_the_severity_of_your_hemophilia
                    )
                    description = context.getString(R.string.plugin_hemophilia_severity_description)
                    isRequired = true
                    options {
                        HemophiliaSeverity.entries.map {
                            it.name to context.getString(it.titleRes)
                        }
                    }
                }

                singleChoice {
                    id = HEMOPHILIA_ACTIVE_ANTIBODY_ID
                    text = context.getString(R.string.plugin_hemophilia_active_antibody_title)
                    description = context.getString(
                        R.string.plugin_hemophilia_active_antibody_description
                    )
                    isRequired = true
                    options {
                        HemophiliaActiveAntibody.entries.map {
                            it.name to context.getString(it.titleRes)
                        }
                    }
                }

                numeric {
                    id = HEMOPHILIA_BLEEDS_COUNT_ID
                    text = context.getString(R.string.plugin_hemophilia_bleed_count_title)
                    hint = context.getString(R.string.plugin_hemophilia_enter_number_of_bleeds)
                    isRequired = true
                    lowerBound = 0.0
                    upperBound = 15.0
                    isZeroAllowed = true
                    validationErrorMessage = context.getString(
                        R.string.plugin_hemophilia_make_sure_this_number_reflects_your_actual_bleed_count
                    )
                    allowsIntegersOnly = true
                }

                numeric {
                    id = HEMOPHILIA_TREATMENT_COUNT_ID
                    text =
                        context.getString(R.string.plugin_hemophilia_how_many_of_those_bleeds_were_treated)
                    hint =
                        context.getString(R.string.plugin_hemophilia_enter_number_of_treated_bleeds)
                    isRequired = true
                    lowerBound = 0.0
                    upperBound = 15.0
                    isZeroAllowed = true
                    validationErrorMessage = context.getString(
                        R.string.plugin_hemophilia_make_sure_this_number_not_exceed_your_actual_bleed_count
                    )
                    allowsIntegersOnly = true
                }

                boolean {
                    id = HEMOPHILIA_TARGET_JOINTS_CONDITION_ID
                    text = context.getString(R.string.plugin_hemophilia_do_you_have_target_joints)
                    description =
                        context.getString(R.string.plugin_hemophilia_target_joints_condition_description)
                    isRequired = true
                    logic {
                        ifTrue(
                            jumpId = HEMOPHILIA_TARGET_JOINTS_ID,
                            questionId = HEMOPHILIA_TARGET_JOINTS_CONDITION_ID,
                        )
                        ifFalse(
                            jumpId = HEMOPHILIA_PROPHYLACTIC_CONDITION_ID,
                            questionId = HEMOPHILIA_TARGET_JOINTS_CONDITION_ID,
                        )
                    }
                }

                multiChoice {
                    id = HEMOPHILIA_TARGET_JOINTS_ID
                    text =
                        context.getString(R.string.plugin_hemophilia_which_are_your_target_joints)
                    description =
                        context.getString(R.string.plugin_hemophilia_please_select_all_that_apply)
                    isRequired = true
                    options {
                        HemophiliaTargetJoints.entries.map {
                            it.name to context.getString(it.titleRes)
                        }
                    }
                }

                boolean {
                    id = HEMOPHILIA_PROPHYLACTIC_CONDITION_ID
                    text =
                        context.getString(R.string.plugin_hemophilia_are_you_on_prophylactic_treatment)
                    isRequired = true
                    logic {
                        ifTrue(
                            jumpId = HEMOPHILIA_PROPHYLACTIC_ID,
                            questionId = HEMOPHILIA_PROPHYLACTIC_CONDITION_ID,
                        )
                        ifFalse(
                            jumpId = HEMOPHILIA_ON_DEMAND_ID,
                            questionId = HEMOPHILIA_PROPHYLACTIC_CONDITION_ID,
                        )
                    }
                }

                selectMedication {
                    id = HEMOPHILIA_PROPHYLACTIC_ID
                    text = context.getString(R.string.plugin_hemophilia_select_prophylactic_title)
                    hint = context.getString(R.string.plugin_hemophilia_start_typing_to_search)
                    isRequired = true
                    medicationFilter = SelectMedicationFilter(
                        listOf(SelectMedicationType.Prophylactic.name),
                    )
                    medicationSource = SelectMedicationSource.CMS
                }

                medicationFlow(
                    context,
                    dosagePageId = HEMOPHILIA_PROPHYLACTIC_DOSAGE_ID,
                    frequencyPageId = HEMOPHILIA_PROPHYLACTIC_FREQUENCY_ID,
                    daysOfWeekPageId = HEMOPHILIA_PROPHYLACTIC_DAYS_OF_WEEK_ID,
                    daysIntervalPageId = HEMOPHILIA_PROPHYLACTIC_DAYS_INTERVAL_ID,
                    timeOfDayPageId = HEMOPHILIA_PROPHYLACTIC_TIME_OF_DAY_ID,
                    reminderPageId = HEMOPHILIA_PROPHYLACTIC_REMINDER_ID,
                    asNeededPageId = HEMOPHILIA_PROPHYLACTIC_AS_NEEDED_ID,
                    nextDosePageId = HEMOPHILIA_PROPHYLACTIC_NEXT_DOSAGE_ID,
                    nextStepPageId = HEMOPHILIA_ON_DEMAND_ID,
                )

                selectMedication {
                    id = HEMOPHILIA_ON_DEMAND_ID
                    text = context.getString(R.string.plugin_hemophilia_select_on_demand_title)
                    hint = context.getString(R.string.plugin_hemophilia_start_typing_to_search)
                    isRequired = true
                    medicationFilter = SelectMedicationFilter(
                        listOf(SelectMedicationType.OnDemand.name),
                    )
                    medicationSource = SelectMedicationSource.CMS
                }

                multiChoice {
                    id = HEMOPHILIA_DIAGNOSED_ID
                    text = context.getString(R.string.plugin_hemophilia_diagnosed_question_title)
                    description = context.getString(R.string.plugin_hemophilia_please_select_all_that_apply)
                    isRequired = true
                    options {
                        HemophiliaDiagnoseOptions.entries.map {
                            it.name to context.getString(it.titleRes)
                        }
                    }
                }

                submissionPage {
                    text =
                        context.getString(R.string.plugin_hemophilia_confirm_the_information_title)
                    description =
                        context.getString(R.string.plugin_hemophilia_confirm_the_information_description)
                    id = HEMOPHILIA_SUBMISSION_ID
                    addBottomExtraPadding = true
                }
            }
        }
    }

    enum class HemophiliaType(@StringRes val titleRes: Int) {
        HEMOPHILIA_A(R.string.plugin_hemophilia_type_a),
        HEMOPHILIA_B(R.string.plugin_hemophilia_type_b),
//        ACQUIRED_HEMOPHILIA(R.string.plugin_hemophilia_type_acquired),
    }

    enum class HemophiliaSeverity(@StringRes val titleRes: Int) {
        MILD(R.string.common_mild),
        MODERATE(R.string.common_moderate),
        SEVERE(R.string.common_severe),
        NOT_SURE(R.string.common_not_sure)
    }

    enum class HemophiliaActiveAntibody(@StringRes val titleRes: Int) {
        NEVER(R.string.plugin_hemophilia_never_had_an_inhibitor),
        IN_PAST(R.string.plugin_hemophilia_had_an_inhibitor_in_the_past),
        HAVE_CURRENTLY(R.string.plugin_hemophilia_have_an_active_inhibitor),
        NOT_SURE(R.string.common_not_sure),
    }

    enum class HemophiliaTargetJoints(
        @StringRes
        val titleRes: Int,
    ) {
        RIGHT_WRIST(R.string.plugin_hemophilia_right_wrist),
        LEFT_WRIST(R.string.plugin_hemophilia_left_wrist),
        RIGHT_ELBOW(R.string.plugin_hemophilia_right_elbow),
        LEFT_ELBOW(R.string.plugin_hemophilia_left_elbow),
        RIGHT_SHOULDER(R.string.plugin_hemophilia_right_shoulder),
        LEFT_SHOULDER(R.string.plugin_hemophilia_left_shoulder),
        RIGHT_HIP(R.string.plugin_hemophilia_right_hip),
        LEFT_HIP(R.string.plugin_hemophilia_left_hip),
        RIGHT_KNEE(R.string.plugin_hemophilia_right_knee),
        LEFT_KNEE(R.string.plugin_hemophilia_left_knee),
        RIGHT_ANKLE(R.string.plugin_hemophilia_right_ankle),
        LEFT_ANKLE(R.string.plugin_hemophilia_left_ankle),
    }

    enum class HemophiliaDiagnoseOptions(
        @StringRes
        val titleRes: Int,
    ) {
        ARTHROPATHY(R.string.plugin_hemophilia_arthropathy),
        CHRONIC_PAIN(R.string.plugin_hemophilia_chronic_pain),
        HIV(R.string.plugin_hemophilia_hiv),
        HEPATITIS_C(R.string.plugin_hemophilia_hepatitis_c),
        HEPATITIS_B(R.string.plugin_hemophilia_hepatitis_b),
        PREVIOUS_JOINT_REPLACEMENT(R.string.plugin_hemophilia_previous_joint_replacement),
        HEART_DISEASE(R.string.plugin_hemophilia_heart_disease),
        DIABETES(R.string.plugin_hemophilia_diabetes),
        OTHER(R.string.plugin_hemophilia_other),
        NONE(R.string.plugin_hemophilia_none),
    }

    fun forAddNewBleed(
        context: Context,
        bodyPointName: String,
        onDemandMedicationGroupId: String?,
    ): Form {
        return form(ADD_NEW_BLEED_QUESTIONNAIRE_ID) {
            name = bodyPointName
            pages {
                singleChoice {
                    id = SEVERITY_ID
                    isRequired = true
                    text = context.getString(R.string.plugin_hemophilia_severity_of_the_bleed)
                    options {
                        HemophiliaInjurySeverity.entries.map {
                            it.name to context.getString(it.titleRes)
                        }
                    }
                    summaryTitle =
                        context.getString(R.string.plugin_hemophilia_severity_of_the_bleed)
                }

                singleChoice {
                    id = REASON_ID
                    text = context.getString(R.string.plugin_hemophilia_reason_for_the_bleed_pain)
                    isRequired = true
                    options {
                        HemophiliaInjuryReason.entries.map {
                            it.name to context.getString(it.titleRes)
                        }
                    }
                    summaryTitle =
                        context.getString(R.string.plugin_hemophilia_reason_for_the_bleed)
                }

                boolean {
                    id = TREATMENT_CONDITION_ID
                    isRequired = true
                    text = context.getString(R.string.plugin_hemophilia_did_you_treat_the_bleed)
                    logic {
                        ifTrue(
                            jumpId = TREATMENT_ID,
                            questionId = TREATMENT_CONDITION_ID,
                        )
                        ifFalse(
                            jumpId = ACCIDENT_DATE_ID,
                            questionId = TREATMENT_CONDITION_ID,
                        )
                    }
                }

                selectMedication {
                    id = TREATMENT_ID
                    text = context.getString(R.string.plugin_hemophilia_what_treatment_was_used_to_treat_the_bleed)
                    hint = context.getString(R.string.plugin_hemophilia_start_typing_to_search)
                    isRequired = true
                    medicationFilter = onDemandMedicationGroupId?.let {
                        SelectMedicationFilter(listOf(onDemandMedicationGroupId))
                    }
                    medicationSource = SelectMedicationSource.Module
                    summaryTitle = context.getString(R.string.plugin_hemophilia_treatment)
                }

                numeric {
                    id = FACTOR_ID
                    isRequired = true
                    text = context.getString(R.string.plugin_hemophilia_factor_units_title)
                    hint = context.getString(R.string.plugin_hemophilia_number_of_factor_units)
                    validationErrorMessage = context.getString(R.string.plugin_hemophilia_factor_unit_validation)
                    allowsIntegersOnly = true
                    lowerBound = 1.0
                    upperBound = 1_000_000.0
                    summaryTitle =
                        context.getString(R.string.plugin_hemophilia_factor_units_summary_title)
                }

                date {
                    id = ACCIDENT_DATE_ID
                    text = context.getString(
                        R.string.plugin_hemophilia_please_confirm_date_of_the_bleed_pain
                    )
                    isRequired = true
                    isShouldInitWithDefaultDate = true
                    minDate = null
                    maxDate = LocalDate.now()
                    summaryTitle = context.getString(R.string.plugin_hemophilia_bleed_date)
                    endIcon = R.drawable.ic_date_calendar
                }

                scale {
                    id = SCALE_ID
                    isRequired = true
                    text = context.getString(R.string.plugin_hemophilia_pain_scale)
                    description =
                        context.getString(R.string.plugin_hemophilia_pain_scale_description)
                    lowerBound = 0
                    upperBound = 10
                    lowerBoundLabel = context.getString(R.string.plugin_hemophilia_no_pain)
                    upperBoundLabel = context.getString(R.string.common_severe)
                    summaryTitle =
                        context.getString(R.string.plugin_hemophilia_pain_scale_of_the_bleed)
                }

                text {
                    id = NOTE_ID
                    isRequired = false
                    inputType = InputType.LONG_TEXT
                    text =
                        context.getString(R.string.plugin_hemophilia_do_you_have_any_notes_to_add)
                    description =
                        context.getString(R.string.plugin_hemophilia_add_notes_description)
                    hint = context.getString(R.string.plugin_hemophilia_enter_your_notes_here)
                    summaryTitle = context.getString(R.string.plugin_hemophilia_note)
                }

                photo {
                    id = PHOTO_ID
                    isRequired = false
                    text = context.getString(R.string.plugin_hemophilia_add_photos_title)
                    description =
                        context.getString(R.string.plugin_hemophilia_add_photos_description)
                    fileValidationParams = FileValidationParams(
                        supportedFileFormats = MediaQuestionAnswerFormat.PHOTO.fileFormats.toList(),
                        maxFileSizeInMb = 100,
                    )
                    summaryTitle = context.getString(R.string.file_question_type_photo)
                }

                submissionPage {
                    text =
                        context.getString(R.string.plugin_hemophilia_confirm_the_information_title)
                    description =
                        context.getString(R.string.plugin_hemophilia_confirm_the_information_description)
                    isSummary = true
                }
            }
        }
    }

    private const val PROFILE_QUESTIONNAIRE_ID = "PROFILE_QUESTIONNAIRE_ID"
    const val HEMOPHILIA_WEIGHT_ID = "HEMOPHILIA_WEIGHT_ID"
    const val HEMOPHILIA_TYPE_ID = "HEMOPHILIA_TYPE_ID"
    const val HEMOPHILIA_SEVERITY_ID = "HEMOPHILIA_SEVERITY_ID"
    const val HEMOPHILIA_ACTIVE_ANTIBODY_ID = "HEMOPHILIA_ACTIVE_ANTIBODY_ID"
    const val HEMOPHILIA_BLEEDS_COUNT_ID = "HEMOPHILIA_BLEEDS_COUNT_ID"
    const val HEMOPHILIA_TREATMENT_COUNT_ID = "HEMOPHILIA_TREATMENT_COUNT_ID"
    const val HEMOPHILIA_TARGET_JOINTS_CONDITION_ID = "HEMOPHILIA_TARGET_JOINTS_CONDITION_ID"
    const val HEMOPHILIA_TARGET_JOINTS_ID = "HEMOPHILIA_TARGET_JOINTS_ID"
    const val HEMOPHILIA_PROPHYLACTIC_CONDITION_ID = "HEMOPHILIA_PROPHYLACTIC_CONDITION_ID"

    // PROPHYLACTIC
    const val HEMOPHILIA_PROPHYLACTIC_ID = "HEMOPHILIA_PROPHYLACTIC_ID"
    const val HEMOPHILIA_PROPHYLACTIC_DOSAGE_ID = "HEMOPHILIA_PROPHYLACTIC_DOSAGE_ID"
    const val HEMOPHILIA_PROPHYLACTIC_FREQUENCY_ID = "HEMOPHILIA_PROPHYLACTIC_FREQUENCY_ID"
    const val HEMOPHILIA_PROPHYLACTIC_TIME_OF_DAY_ID = "HEMOPHILIA_PROPHYLACTIC_TIME_OF_DAY_ID"
    const val HEMOPHILIA_PROPHYLACTIC_DAYS_OF_WEEK_ID = "HEMOPHILIA_PROPHYLACTIC_DAYS_OF_WEEK_ID"
    const val HEMOPHILIA_PROPHYLACTIC_AS_NEEDED_ID = "HEMOPHILIA_PROPHYLACTIC_AS_NEEDED_ID"
    const val HEMOPHILIA_PROPHYLACTIC_DAYS_INTERVAL_ID = "HEMOPHILIA_PROPHYLACTIC_DAYS_INTERVAL_ID"
    const val HEMOPHILIA_PROPHYLACTIC_REMINDER_ID = "HEMOPHILIA_PROPHYLACTIC_REMINDER_ID"
    const val HEMOPHILIA_PROPHYLACTIC_NEXT_DOSAGE_ID = "HEMOPHILIA_PROPHYLACTIC_NEXT_DOSAGE_ID"

    // ON_DEMAND
    const val HEMOPHILIA_ON_DEMAND_ID = "HEMOPHILIA_ON_DEMAND_ID"

    const val HEMOPHILIA_DIAGNOSED_ID = "HEMOPHILIA_DIAGNOSED_ID"
    private const val HEMOPHILIA_SUBMISSION_ID = "HEMOPHILIA_SUBMISSION_ID"

    private const val ADD_NEW_BLEED_QUESTIONNAIRE_ID = "ADD_NEW_BLEED_QUESTIONNAIRE_ID"
    const val ACCIDENT_DATE_ID = "ACCIDENT_DATE_ID"
    const val REASON_ID = "REASON_ID"
    const val SEVERITY_ID = "SEVERITY_ID"
    const val TREATMENT_CONDITION_ID = "TREATMENT_CONDITION_ID"
    const val TREATMENT_ID = "TREATMENT_ID"
    const val FACTOR_ID = "FACTOR_ID"
    const val SCALE_ID = "SCALE_ID"
    const val NOTE_ID = "NOTE_ID"
    const val PHOTO_ID = "PHOTO_ID"
}
