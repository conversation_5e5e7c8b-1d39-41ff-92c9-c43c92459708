package com.huma.sdk.pfizer.hemophilia.module

import android.os.Parcelable
import com.huma.sdk.module.medicationv2.core.domain.entities.MedicationV2
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaBodyLocationType
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjuryReason
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjurySeverity
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjuryType
import com.huma.sdk.pfizer.hemophilia.widget.config.HemophiliaWidgetConfig
import kotlinx.parcelize.Parcelize
import org.threeten.bp.Instant
import org.threeten.bp.LocalDate
import org.threeten.bp.format.DateTimeFormatter

@Parcelize
data class HemophiliaModuleResult(
    val creationDateTime: Instant? = null,
    val startDateTime: Instant? = null,
    val bodyLocation: HemophiliaBodyLocationType? = null,
    val bodyPartInjury: HemophiliaInjuryType,
    val customBodyPart: String? = null,
    val accidentDate: LocalDate,
    val reason: HemophiliaInjuryReason? = null,
    val note: String? = null,
    val photos: List<String>? = null,
    val scale: Int? = null,
    val severity: HemophiliaInjurySeverity? = null,
    val treatment: MedicationV2? = null,
    val factorUnits: Int? = null,
) : Parcelable {
    val dateFormatted: String
        get() = dateFormatter.format(accidentDate)

    val dateFormatted2: String
        get() = dateFormatter2.format(accidentDate)

    fun getBodyPartName(bodyInjuryList: List<HemophiliaWidgetConfig.PointItem>): String {
        if (bodyPartInjury == HemophiliaInjuryType.CUSTOM) {
            return customBodyPart.orEmpty()
        } else {
            return bodyInjuryList.firstOrNull {
                it.value == bodyPartInjury
            }?.name.orEmpty()
        }
    }

    companion object {
        private val dateFormatter by lazy { DateTimeFormatter.ofPattern("dd/MM/yyyy") }
        private val dateFormatter2 by lazy { DateTimeFormatter.ofPattern("d MMM yyyy") }

        fun dummy() = HemophiliaModuleResult(
            bodyLocation = HemophiliaBodyLocationType.LEFT_HIP,
            bodyPartInjury = HemophiliaInjuryType.GUMS_BLEED,
            customBodyPart = null,
            accidentDate = LocalDate.now(),
            reason = HemophiliaInjuryReason.SURGERY,
            note = "Testing note",
            photos = null,
            scale = 5,
            severity = HemophiliaInjurySeverity.SEVERE,
        )
    }
}
