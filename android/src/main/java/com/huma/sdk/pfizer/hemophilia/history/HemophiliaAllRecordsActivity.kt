package com.huma.sdk.pfizer.hemophilia.history

import android.content.Context
import android.content.Intent
import androidx.annotation.Keep
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.huma.sdk.pfizer.hemophilia.history.composable.HemophiliaAllRecordsScreen
import com.huma.sdk.pfizer.hemophilia.history.viewmodel.HemophiliaAllRecordsViewModel
import com.huma.sdk.pfizer.hemophilia.widget.config.HemophiliaWidgetConfig
import com.huma.sdk.ui.activity.compose.AbsComposableActivity
import org.koin.androidx.viewmodel.ext.android.viewModel

@Keep
class HemophiliaAllRecordsActivity : AbsComposableActivity() {
    private val viewModel: HemophiliaAllRecordsViewModel by viewModel()
    private val bodyInjuryList: List<HemophiliaWidgetConfig.PointItem> by lazy {
        intent.getParcelableArrayListExtra(EXTRA_BODY_INJURY_LIST)!!
    }

    override fun onResume() {
        super.onResume()
        viewModel.refresh()
    }

    @Composable
    override fun Content() {
        val records by viewModel.historyRecord.collectAsStateWithLifecycle()

        HemophiliaAllRecordsScreen(
            bodyInjuryList = bodyInjuryList,
            records = records.orEmpty(),
            onItemClick = { record ->
                HemophiliaRecordDetailActivity.launch(
                    this@HemophiliaAllRecordsActivity,
                    record,
                    record.getBodyPartName(bodyInjuryList),
                )
            },
            onBackClick = this@HemophiliaAllRecordsActivity::finish,
        )
    }

    companion object {
        private const val EXTRA_BODY_INJURY_LIST = "EXTRA_BODY_INJURY_LIST"

        fun launch(
            context: Context,
            bodyInjuryList: List<HemophiliaWidgetConfig.PointItem>,
        ) {
            Intent(context, HemophiliaAllRecordsActivity::class.java).apply {
                putParcelableArrayListExtra(EXTRA_BODY_INJURY_LIST, ArrayList(bodyInjuryList))
                context.startActivity(this)
            }
        }
    }
}
