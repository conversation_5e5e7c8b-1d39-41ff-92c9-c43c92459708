package com.huma.sdk.pfizer.hemophilia.history

import android.content.Context
import android.content.Intent
import androidx.annotation.Keep
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.huma.sdk.pfizer.hemophilia.history.composable.HemophiliaHistoryScreen
import com.huma.sdk.pfizer.hemophilia.history.viewmodel.HemophiliaHistoryViewModel
import com.huma.sdk.pfizer.hemophilia.widget.config.HemophiliaWidgetConfig
import com.huma.sdk.ui.activity.compose.AbsComposableActivity
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.parameter.parametersOf

@Keep
class HemophiliaHistoryActivity : AbsComposableActivity() {
    private val widgetId by lazy {
        intent.getStringExtra(EXTRA_WIDGET_ID)!!
    }
    private val widgetType by lazy {
        intent.getStringExtra(EXTRA_WIDGET_TYPE)!!
    }
    private val description by lazy {
        intent.getStringExtra(EXTRA_WIDGET_DESCRIPTION)!!
    }
    private val bodyInjuryList: List<HemophiliaWidgetConfig.PointItem> by lazy {
        intent.getParcelableArrayListExtra(EXTRA_BODY_INJURY_LIST)!!
    }

    private val viewModel: HemophiliaHistoryViewModel by viewModel {
        parametersOf(widgetId, widgetType)
    }

    override fun onResume() {
        super.onResume()
        viewModel.refresh()
    }

    @Composable
    override fun Content() {
        val bodyMap by viewModel.bodyMap.collectAsStateWithLifecycle()
        val historyRecords by viewModel.historyRecord.collectAsStateWithLifecycle()
        HemophiliaHistoryScreen(
            description = description,
            bodyInjuryList = bodyInjuryList,
            bodyMapState = bodyMap,
            historyRecords = historyRecords.orEmpty(),
            onShowAllRecordsClick = {
                HemophiliaAllRecordsActivity.launch(this, bodyInjuryList)
            },
            onItemClick = { selected ->
                HemophiliaRecordDetailActivity.launch(
                    this,
                    selected,
                    selected.getBodyPartName(bodyInjuryList),
                )
            },
            onBackClick = this::finish,
        )
    }

    companion object {
        private const val EXTRA_WIDGET_ID = "EXTRA_WIDGET_ID"
        private const val EXTRA_WIDGET_TYPE = "EXTRA_WIDGET_TYPE"
        private const val EXTRA_WIDGET_DESCRIPTION = "EXTRA_WIDGET_DESCRIPTION"
        private const val EXTRA_BODY_INJURY_LIST = "EXTRA_BODY_INJURY_LIST"

        fun launch(
            context: Context,
            widgetId: String,
            widgetType: String,
            description: String,
            bodyInjuryList: List<HemophiliaWidgetConfig.PointItem>,
        ) {
            Intent(context, HemophiliaHistoryActivity::class.java).apply {
                putExtra(EXTRA_WIDGET_ID, widgetId)
                putExtra(EXTRA_WIDGET_TYPE, widgetType)
                putExtra(EXTRA_WIDGET_DESCRIPTION, description)
                putParcelableArrayListExtra(EXTRA_BODY_INJURY_LIST, ArrayList(bodyInjuryList))
                context.startActivity(this)
            }
        }
    }
}
