package com.huma.sdk.pfizer.hemophilia.questionnaire.viewmodel

import android.app.Application
import com.github.michaelbull.result.onFailure
import com.github.michaelbull.result.onSuccess
import com.huma.sdk.module.medicationv2.R
import com.huma.sdk.module.medicationv2.core.domain.converters.MedicationV2ToMedicationV2RemoteConverter
import com.huma.sdk.module.medicationv2.core.domain.converters.QuestionnaireViewDataToMedicationV2Converter.Companion.DATE_PATTERN
import com.huma.sdk.module.medicationv2.core.domain.entities.MedicationSchedule
import com.huma.sdk.module.medicationv2.core.domain.usecase.RefreshMedicationsUseCase
import com.huma.sdk.module.medicationv2.core.domain.usecase.UpdateMedicationEntryUseCase
import com.huma.sdk.module.medicationv2.hemophilia.converter.MedicationFormPageToQuestionnaireStepConverter
import com.huma.sdk.module.medicationv2.hemophilia.medication_dosage.MedicationDosageAnswer
import com.huma.sdk.module.medicationv2.hemophilia.select_medication.composable.SelectMedicationAnswer
import com.huma.sdk.module.medicationv2.hemophilia.util.HemophiliaMedicationV2Utils
import com.huma.sdk.module.medicationv2.hemophilia.util.HemophiliaMedicationV2Utils.extractMedicationV2DataFromSteps
import com.huma.sdk.module.medicationv2.medicationlogs.core.domain.usecase.abstraction.FetchAndSaveAdherenceListUseCase
import com.huma.sdk.module.medicationv2.questionnaire.MedicationV2Form
import com.huma.sdk.module_kit.local_notification.core.LocalNotificationsScheduler
import com.huma.sdk.pfizer.hemophilia.data.HemophiliaRepository
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaProfileData
import com.huma.sdk.pfizer.hemophilia.questionnaire.HemophiliaQuestionnaireBuilder
import com.huma.sdk.questionnaire.core.domain.entities.FormPage
import com.huma.sdk.questionnaire.v2.EditQuestionnaireV2AbsViewModel
import com.huma.sdk.questionnaire.v2.model.QuestionnaireV2Step
import com.huma.sdk.questionnaire.v2.model.steps.NumericInputStep
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.threeten.bp.Instant
import org.threeten.bp.LocalDate
import org.threeten.bp.format.DateTimeFormatter

class HemophiliaEditProfileQuestionnaireViewModel(
    private val profileData: HemophiliaProfileData,
    context: Application,
    stepConverter: MedicationFormPageToQuestionnaireStepConverter,
    private val hemophiliaRepository: HemophiliaRepository,
    private val refreshMedicationsUseCase: RefreshMedicationsUseCase,
    private val updateMedicationEntryUseCase: UpdateMedicationEntryUseCase,
    private val medicationV2Converter: MedicationV2ToMedicationV2RemoteConverter,
    private val fetchAndSaveAdherenceListUseCase: FetchAndSaveAdherenceListUseCase,
) : EditQuestionnaireV2AbsViewModel(
    context,
    HemophiliaQuestionnaireBuilder.forProfile(context),
    stepConverter,
) {
    override fun createAnswersList(
        step: QuestionnaireV2Step,
        previousSteps: List<QuestionnaireV2Step>,
    ): List<String> {
        return when(step.questionId) {
            HemophiliaQuestionnaireBuilder.HEMOPHILIA_WEIGHT_ID -> {
                listOfNotNull(profileData.weight?.toString())
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_TYPE_ID -> {
                listOfNotNull(profileData.type?.toString())
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_SEVERITY_ID -> {
                listOfNotNull(profileData.severity?.toString())
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_ACTIVE_ANTIBODY_ID -> {
                listOfNotNull(profileData.antibody?.toString())
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_BLEEDS_COUNT_ID -> {
                listOfNotNull(profileData.bleedCount?.toString())
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_TREATMENT_COUNT_ID -> {
                val bleedCountAnswer = previousSteps.find {
                    it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_BLEEDS_COUNT_ID
                }?.answersList?.firstOrNull()
                val oldBleedCountAnswer = profileData.bleedCount?.toString()
                if (bleedCountAnswer == oldBleedCountAnswer) {
                    listOfNotNull(profileData.treatedBleedCount?.toString())
                } else {
                    emptyList()
                }
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_TARGET_JOINTS_CONDITION_ID -> {
                listOfNotNull(if (!profileData.targetJoints.isNullOrEmpty()) "true" else "false")
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_TARGET_JOINTS_ID -> {
                profileData.targetJoints?.map { it.name }.orEmpty()
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_CONDITION_ID -> {
                listOfNotNull(if (profileData.prophylacticMedication != null) "true" else "false")
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_ID -> {
                profileData.prophylacticMedication?.let {
                    SelectMedicationAnswer.fromMedicationV2(it).asAnswerList()
                }.orEmpty()
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_ON_DEMAND_ID -> {
                profileData.onDemandMedication?.let {
                    SelectMedicationAnswer.fromMedicationV2(it).asAnswerList()
                }.orEmpty()
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_DIAGNOSED_ID -> {
                profileData.additionalDiagnoses?.map { it.name }.orEmpty()
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_DOSAGE_ID -> {
                val medication = profileData.prophylacticMedication ?: return emptyList()
                MedicationDosageAnswer.fromDosageAndUnit(
                    dosage = medication.dosage.value,
                    unit = medication.dosage.unit,
                    unitText = medication.dosage.unitText,
                ).asAnswersList()
            }
            HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_FREQUENCY_ID -> {
                val medication = profileData.prophylacticMedication ?: return emptyList()
                when (medication.schedule) {
                    is MedicationSchedule.AsNeeded -> MedicationV2Form.FrequencyId.AS_NEEDED
                    is MedicationSchedule.Daily -> MedicationV2Form.FrequencyId.EVERY_DAY
                    is MedicationSchedule.Interval -> MedicationV2Form.FrequencyId.DAYS_INTERVAL
                    is MedicationSchedule.Weekly -> MedicationV2Form.FrequencyId.SPECIFIC_DAYS
                }.let {
                    listOf(it)
                }
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_DAYS_OF_WEEK_ID -> {
                val medication = profileData.prophylacticMedication ?: return emptyList()
                (medication.schedule as? MedicationSchedule.Weekly)?.let {
                    it.days.map { day ->
                        day.name
                    }
                }.orEmpty()
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_DAYS_INTERVAL_ID -> {
                val medication = profileData.prophylacticMedication ?: return emptyList()
                (medication.schedule as? MedicationSchedule.Interval)?.let {
                    listOf(it.interval.toString())
                }.orEmpty()
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_TIME_OF_DAY_ID -> {
                val medication = profileData.prophylacticMedication ?: return emptyList()
                when (val schedule = medication.schedule) {
                    is MedicationSchedule.AsNeeded -> emptyList()
                    is MedicationSchedule.Daily -> schedule.timeOfReading.map { it.time.toString() }
                    is MedicationSchedule.Interval -> schedule.timeOfReading.map { it.time.toString() }
                    is MedicationSchedule.Weekly -> schedule.timeOfReading.map { it.time.toString() }
                }
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_REMINDER_ID -> {
                val medication = profileData.prophylacticMedication ?: return emptyList()
                listOf(medication.notification.enabled.toString())
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_AS_NEEDED_ID -> {
                val medication = profileData.prophylacticMedication ?: return emptyList()
                listOfNotNull(medication.dosage.maxValue?.toString())
            }

            HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_NEXT_DOSAGE_ID -> {
                val medication = profileData.prophylacticMedication ?: return emptyList()
                listOfNotNull(
                    medication.startDate?.let {
                        LocalDate.parse(it, DateTimeFormatter.ofPattern(DATE_PATTERN))?.toString()
                    }
                )
            }

            else -> emptyList()
        }
    }

    override val pageTitle: CharSequence
        get() = form.name.orEmpty()

    override suspend fun doSubmit(): Unit = withContext(Dispatchers.IO) {
        val prophylacticMedication = HemophiliaMedicationV2Utils.extractSelectedMedicationV2FromSteps(
            steps.value,
            HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_ID,
        )?.let {
            extractMedicationV2DataFromSteps(
                it,
                steps.value,
                dosagePageId = HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_DOSAGE_ID,
                frequencyPageId = HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_FREQUENCY_ID,
                daysOfWeekPageId = HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_DAYS_OF_WEEK_ID,
                daysIntervalPageId = HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_DAYS_INTERVAL_ID,
                timeOfDayPageId = HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_TIME_OF_DAY_ID,
                reminderPageId = HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_REMINDER_ID,
                asNeededPageId = HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_AS_NEEDED_ID,
                nextDosePageId = HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_NEXT_DOSAGE_ID,
            ).copy(
                groupId = HemophiliaMedicationV2Utils.getMedicationProphylacticGroupId()
            )
        }

        if (
            prophylacticMedication?.dosage != null &&
            prophylacticMedication.dosage.maxValue != null &&
            prophylacticMedication.dosage.maxValue!! < prophylacticMedication.dosage.value
        ) {
            eventsChannel.send(
                Events.ShowErrorToast(
                    context.getString(R.string.medicationv2_failed_submission_dosage)
                )
            )
            return@withContext
        }

        val prophylacticMedicationId = prophylacticMedication?.id
        val oldProphylacticMedicationId = profileData.prophylacticMedication?.id
        val medicationV2ModuleConfigId = HemophiliaMedicationV2Utils.getMedicationV2ModuleConfigId()

        if (prophylacticMedicationId != null &&
            medicationV2ModuleConfigId != null &&
            oldProphylacticMedicationId == prophylacticMedicationId) {
            updateMedicationEntryUseCase.execute(
                UpdateMedicationEntryUseCase.Params.create(
                    prophylacticMedication,
                    oldProphylacticMedicationId,
                    medicationV2ModuleConfigId,
                )
            ).onFailure {
                val message = it.tryGetErrorMessage()
                if (message != null) {
                    eventsChannel.send(Events.ShowErrorToast(message))
                } else {
                    eventsChannel.send(Events.Error)
                }
                return@withContext
            }
        }

        val targetJoints = mutableSteps.value.find {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_TARGET_JOINTS_ID
        }?.answersList

        val onDemandMedication = HemophiliaMedicationV2Utils.extractSelectedMedicationV2FromSteps(
            steps.value,
            HemophiliaQuestionnaireBuilder.HEMOPHILIA_ON_DEMAND_ID,
        )?.copy(
            groupId = HemophiliaMedicationV2Utils.getMedicationOnDemandGroupId(),
        )

        mutableSteps.value.mapNotNull {
            it.getDetailedQuestionnaireAnswer()
        }.also { answers ->
            hemophiliaRepository.submitProfileQuestionnaire(
                answers,
                targetJoints.orEmpty(),
                prophylacticMedication?.let { medicationV2Converter(it) },
                onDemandMedication?.let {
                    medicationV2Converter(it)
                }?.copy(dosage = null, unit = null),
            ).onSuccess {
                refreshMedicationsUseCase.execute()
                fetchAndSaveAdherenceListUseCase.invoke(Instant.now())
                LocalNotificationsScheduler.start(context)
                eventsChannel.send(Events.ShowThankYouAndFinish())
            }.onFailure {
                val message = it.tryGetErrorMessage()
                if (message != null) {
                    eventsChannel.send(Events.ShowErrorToast(message))
                } else {
                    eventsChannel.send(Events.Error)
                }
            }
        }
    }

    override fun createStep(
        formPage: FormPage,
        previousSteps: List<QuestionnaireV2Step>
    ): QuestionnaireV2Step {
        val step = super.createStep(formPage, previousSteps)
        val type = step.type
        if (formPage.getId() == HemophiliaQuestionnaireBuilder.HEMOPHILIA_TREATMENT_COUNT_ID && type is NumericInputStep) {
            val previousStepAnswer = previousSteps.firstOrNull {
                it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_BLEEDS_COUNT_ID
            }?.answersList?.firstOrNull()?.toDoubleOrNull()

            return step.copy(type = type.copy(upperBound = previousStepAnswer ?: type.upperBound))
        }
        return step
    }
}