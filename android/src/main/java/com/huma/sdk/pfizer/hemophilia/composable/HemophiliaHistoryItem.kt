package com.huma.sdk.pfizer.hemophilia.composable

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.R
import com.huma.sdk.pfizer.hemophilia.module.HemophiliaModuleResult
import com.huma.sdk.ui.components.base.Palette
import com.huma.sdk.ui.components.base.toComposeColor
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.humastyle.HumaTypeStyler

@Composable
fun HemophiliaHistoryItem(
    bodyInjuryName: String,
    record: HemophiliaModuleResult,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .background(Color.White)
            .padding(horizontal = 24.dp)
    ) {
        HemophiliaHistoryItemContent(
            bodyPartName = bodyInjuryName,
            dateText = remember(record) { record.dateFormatted },
            extraNote = record.note.orEmpty(),
        )
        HorizontalDivider(color = Palette.Base.GRAY_GALLERY.toComposeColor())
    }
}

@Composable
private fun HemophiliaHistoryItemContent(
    bodyPartName: String,
    dateText: String,
    extraNote: String,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .padding(vertical = 14.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            BaseText(
                text = bodyPartName,
                style = HumaTypeStyler.headline2,
                modifier = Modifier.weight(1f)
            )
            BaseText(
                text = dateText,
                style = HumaTypeStyler.action3,
                textColor = Palette.Base.GRAY_NEVADA,
            )
            Icon(
                painterResource(R.drawable.hsdk_arrow_right),
                contentDescription = null,
            )
        }
        Spacer(Modifier.height(8.dp))
        BaseText(
            text = extraNote,
            style = HumaTypeStyler.caption1,
            overflow = TextOverflow.Ellipsis,
            maxLines = 2,
        )
    }
}
