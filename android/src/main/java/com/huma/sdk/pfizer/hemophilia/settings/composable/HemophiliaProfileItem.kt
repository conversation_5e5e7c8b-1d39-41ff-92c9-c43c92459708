package com.huma.sdk.pfizer.hemophilia.settings.composable

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.huma.sdk.ui.components.base.Palette
import com.huma.sdk.ui.components.base.toComposeColor
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.humastyle.HumaTypeStyler

@Composable
fun HemophiliaProfileItem(
    name: String,
    value: String,
) {
    Column(
        modifier = Modifier
            .padding(horizontal = 24.dp)
    ) {
        Spacer(Modifier.height(14.dp))
        BaseText(
            text = name,
            style = HumaTypeStyler.bodySemiBold,
        )
        Spacer(Modifier.height(4.dp))
        BaseText(
            text = value,
            style = HumaTypeStyler.body,
        )
        Spacer(Modifier.height(14.dp))
        HorizontalDivider(color = Palette.Base.GRAY_GALLERY.toComposeColor())
    }
}
