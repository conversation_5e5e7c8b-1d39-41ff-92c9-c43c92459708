package com.huma.sdk.pfizer.hemophilia.questionnaire.activity

import android.content.Context
import android.content.Intent
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaProfileData
import com.huma.sdk.pfizer.hemophilia.questionnaire.viewmodel.HemophiliaEditProfileQuestionnaireViewModel
import com.huma.sdk.questionnaire.v2.EditQuestionnaireV2AbsActivity
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.parameter.parametersOf

class HemophiliaEditProfileQuestionnaireActivity : EditQuestionnaireV2AbsActivity() {
    private val profileData: HemophiliaProfileData by lazy {
        intent.getParcelableExtra(EXTRA_PROFILE_DATA)!!
    }

    override val editViewModel: HemophiliaEditProfileQuestionnaireViewModel by viewModel {
        parametersOf(profileData)
    }

    companion object {
        private const val EXTRA_PROFILE_DATA = "EXTRA_PROFILE_DATA"
        fun launch(
            context: Context,
            profileData: HemophiliaProfileData,
        ) {
            Intent(context, HemophiliaEditProfileQuestionnaireActivity::class.java).apply {
                putExtra(EXTRA_PROFILE_DATA, profileData)
                context.startActivity(this)
            }
        }
    }
}