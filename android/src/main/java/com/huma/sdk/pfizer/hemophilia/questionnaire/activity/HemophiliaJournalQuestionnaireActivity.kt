package com.huma.sdk.pfizer.hemophilia.questionnaire.activity

import android.content.Context
import android.content.Intent
import androidx.annotation.Keep
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaBodyLocationType
import com.huma.sdk.pfizer.hemophilia.questionnaire.viewmodel.HemophiliaJournalQuestionnaireViewModel
import com.huma.sdk.pfizer.hemophilia.widget.config.HemophiliaWidgetConfig
import com.huma.sdk.questionnaire.v2.QuestionnaireV2AbsActivity
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.parameter.parametersOf

@Keep
class HemophiliaJournalQuestionnaireActivity : QuestionnaireV2AbsActivity() {
    private val bodyMapItem: HemophiliaWidgetConfig.PointItem by lazy {
        intent.getParcelableExtra(EXTRA_BODY_MAP_ITEM)!!
    }

    private val bodyLocation by lazy {
        intent.getSerializableExtra(EXTRA_BODY_LOCATION) as HemophiliaBodyLocationType
    }

    override val viewModel: HemophiliaJournalQuestionnaireViewModel by viewModel {
        parametersOf(bodyMapItem, bodyLocation)
    }

    companion object {
        private const val EXTRA_BODY_MAP_ITEM = "EXTRA_BODY_MAP_ITEM"
        private const val EXTRA_BODY_LOCATION = "EXTRA_BODY_LOCATION"
        fun launch(
            context: Context,
            bodyMapItem: HemophiliaWidgetConfig.PointItem,
            bodyLocation: HemophiliaBodyLocationType,
        ) {
            Intent(context, HemophiliaJournalQuestionnaireActivity::class.java).apply {
                putExtra(EXTRA_BODY_MAP_ITEM, bodyMapItem)
                putExtra(EXTRA_BODY_LOCATION, bodyLocation)
                context.startActivity(this)
            }
        }
    }
}
