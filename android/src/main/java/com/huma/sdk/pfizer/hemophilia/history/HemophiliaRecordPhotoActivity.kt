package com.huma.sdk.pfizer.hemophilia.history

import android.content.Context
import android.content.Intent
import android.text.format.DateFormat
import androidx.annotation.Keep
import androidx.compose.runtime.Composable
import com.huma.sdk.core.utils.ext.toLocalDateTime
import com.huma.sdk.pfizer.hemophilia.history.composable.HemophiliaRecordPhotoScreen
import com.huma.sdk.shared.fileid.FileId
import com.huma.sdk.ui.activity.compose.AbsComposableActivity
import java.io.Serializable
import org.threeten.bp.Instant
import org.threeten.bp.format.DateTimeFormatter

@Keep
class HemophiliaRecordPhotoActivity : AbsComposableActivity() {
    private val photoId by lazy {
        intent.getStringExtra(EXTRA_PHOTO_ID)!!
    }

    private val photoTimestamp by lazy {
        intent.getSerializableExtra(EXTRA_PHOTO_TIMESTAMP) as Instant?
    }

    @Composable
    override fun Content() {
        HemophiliaRecordPhotoScreen(
            photoId = FileId(photoId),
            timestamp = photoTimestamp?.formatTimestamp().orEmpty(),
            onBackClick = this@HemophiliaRecordPhotoActivity::finish,
        )
    }

    private fun Instant.formatTimestamp(): String {
        return toLocalDateTime().let {
            if (DateFormat.is24HourFormat(this@HemophiliaRecordPhotoActivity)) {
                dateFormatter24h.format(it)
            } else {
                dateFormatter.format(it)
            }
        }
    }

    companion object {
        private val dateFormatter by lazy { DateTimeFormatter.ofPattern("yyyy/MM/dd, HH:mm:ss") }
        private val dateFormatter24h by lazy { DateTimeFormatter.ofPattern("yyyy/MM/dd, hh:mm:ss a") }

        private const val EXTRA_PHOTO_ID = "EXTRA_PHOTO_ID"
        private const val EXTRA_PHOTO_TIMESTAMP = "EXTRA_PHOTO_TIMESTAMP"
        fun launch(
            context: Context,
            photoId: String,
            timestamp: Instant?,
        ) {
            Intent(context, HemophiliaRecordPhotoActivity::class.java).apply {
                putExtra(EXTRA_PHOTO_ID, photoId)
                putExtra(EXTRA_PHOTO_TIMESTAMP, timestamp as Serializable)
                context.startActivity(this)
            }
        }
    }
}
