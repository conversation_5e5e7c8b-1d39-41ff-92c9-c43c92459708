package com.huma.sdk.pfizer.hemophilia.widget.composable

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.R
import com.huma.sdk.pfizer.hemophilia.widget.config.HemophiliaWidgetConfig.PointItem
import com.huma.sdk.ui.components.base.Palette
import com.huma.sdk.ui.components.base.toComposeColor
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.humastyle.HumaTypeStyler

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HemophiliaAddNewNonJointBleedBottomSheet(
    title: String,
    items: List<PointItem>,
    onItemClick: (PointItem) -> Unit,
    onDismissRequest: () -> Unit,
) {
    ModalBottomSheet(
        onDismissRequest = onDismissRequest,
        sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true),
        shape = RoundedCornerShape(topEnd = 13.dp, topStart = 13.dp),
        containerColor = Color.White,
        dragHandle = null,
    ) {
        HemophiliaAddNewNonJointBleedBottomSheetContent(
            title,
            items,
            onItemClick,
        )
    }
}

@Composable
private fun HemophiliaAddNewNonJointBleedBottomSheetContent(
    title: String,
    items: List<PointItem>,
    onItemClick: (PointItem) -> Unit,
) {
    Column {
        Spacer(Modifier.height(12.dp))
        BaseText(
            text = title,
            style = HumaTypeStyler.title2Bold,
            modifier = Modifier
                .padding(horizontal = 24.dp)
        )
        Spacer(Modifier.height(24.dp))
        for (item in items) {
            HemophiliaAddNewNonJointBleedBottomSheetItem(
                title = item.name.orEmpty(),
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { onItemClick(item) }
                    .padding(horizontal = 24.dp)
            )
        }
    }
}

@Composable
private fun HemophiliaAddNewNonJointBleedBottomSheetItem(
    title: String,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
    ) {
        Spacer(Modifier.height(8.dp))
        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            BaseText(
                text = title,
                style = HumaTypeStyler.body,
            )
            Spacer(Modifier.weight(1f))
            Spacer(Modifier.width(20.dp))
            Icon(
                painter = painterResource(R.drawable.hsdk_arrow_right),
                contentDescription = null,
            )
        }
        Spacer(Modifier.height(14.dp))
        HorizontalDivider(color = Palette.Base.GRAY_GALLERY.toComposeColor())
    }
}
