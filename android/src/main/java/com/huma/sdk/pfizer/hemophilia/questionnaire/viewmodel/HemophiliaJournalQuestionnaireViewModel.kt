package com.huma.sdk.pfizer.hemophilia.questionnaire.viewmodel

import android.app.Application
import androidx.annotation.Keep
import com.huma.sdk.core.utils.commons.navigation.Screen
import com.huma.sdk.core.utils.ext.addNotNull
import com.huma.sdk.module.medicationv2.core.domain.converters.MedicationV2ToMedicationV2RemoteConverter
import com.huma.sdk.module.medicationv2.hemophilia.converter.MedicationFormPageToQuestionnaireStepConverter
import com.huma.sdk.module.medicationv2.hemophilia.select_medication.composable.SelectMedicationAnswer
import com.huma.sdk.module.medicationv2.hemophilia.util.HemophiliaMedicationV2Utils
import com.huma.sdk.module_kit.HumaModuleKitManager
import com.huma.sdk.module_kit.display.input.ui.data.output.ModuleInputResult
import com.huma.sdk.module_kit.display.input.ui.data.output.ModuleInputSubmitParams
import com.huma.sdk.pfizer.R
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaBodyLocationType
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjuryType
import com.huma.sdk.pfizer.hemophilia.module.HemophiliaModule
import com.huma.sdk.pfizer.hemophilia.module.HemophiliaModuleValue
import com.huma.sdk.pfizer.hemophilia.module.primitive.HemophiliaRecordExtraData
import com.huma.sdk.pfizer.hemophilia.questionnaire.HemophiliaQuestionnaireBuilder
import com.huma.sdk.pfizer.hemophilia.widget.config.HemophiliaWidgetConfig
import com.huma.sdk.questionnaire.v2.QuestionnaireV2AbsViewModel
import com.huma.sdk.questionnaire.v2.model.QuestionnaireV2Step
import com.huma.sdk.questionnaire.v2.model.QuestionnaireV2SummaryItem
import com.huma.sdk.ui.utils.ext.htmlToAnnotatedString
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import org.threeten.bp.LocalDate

@Keep
class HemophiliaJournalQuestionnaireViewModel(
    context: Application,
    private val bodyMapItem: HemophiliaWidgetConfig.PointItem,
    private val bodyLocation: HemophiliaBodyLocationType,
    private val medicationV2Converter: MedicationV2ToMedicationV2RemoteConverter,
    formPageConverter: MedicationFormPageToQuestionnaireStepConverter,
) : QuestionnaireV2AbsViewModel(
    context = context,
    form = HemophiliaQuestionnaireBuilder.forAddNewBleed(
        context,
        bodyMapItem.name.orEmpty(),
        HemophiliaMedicationV2Utils.getMedicationOnDemandGroupId(),
    ),
    formPageConverter = formPageConverter,
) {
    override val pageTitle: CharSequence
        get() = context.getString(
            R.string.plugin_hemophilia_journal_questionnaire_title,
            form.name.orEmpty(),
        ).htmlToAnnotatedString(context)

    override suspend fun doSubmit() {
        submitForHemophiliaJournal(
            bodyLocation,
            bodyMapItem.value,
            bodyMapItem.name.orEmpty(),
        )
    }

    private suspend fun submitForHemophiliaJournal(
        bodyLocation: HemophiliaBodyLocationType?,
        bodyInjuryType: HemophiliaInjuryType,
        bodyInjuryTypeName: String,
    ) = suspendCoroutine { continuation ->
        val record = mutableSteps.value.let { steps ->
            val accidentDate = steps.firstOrNull {
                HemophiliaQuestionnaireBuilder.ACCIDENT_DATE_ID == it.questionId
            }?.answersList?.firstOrNull()?.let { LocalDate.parse(it) }
            val reason = steps.firstOrNull {
                HemophiliaQuestionnaireBuilder.REASON_ID == it.questionId
            }?.answersList?.firstOrNull()
            val severity = steps.firstOrNull {
                HemophiliaQuestionnaireBuilder.SEVERITY_ID == it.questionId
            }?.answersList?.firstOrNull()
            val scale = steps.firstOrNull {
                HemophiliaQuestionnaireBuilder.SCALE_ID == it.questionId
            }?.answersList?.firstOrNull()?.toIntOrNull()
            val treatment = steps.firstOrNull {
                HemophiliaQuestionnaireBuilder.TREATMENT_ID == it.questionId
            }?.answersList?.let {
                SelectMedicationAnswer.fromAnswersList(it)?.selected?.backingObject
            }
            val factor = steps.firstOrNull {
                HemophiliaQuestionnaireBuilder.FACTOR_ID == it.questionId
            }?.answersList?.firstOrNull()?.toIntOrNull()
            val note = steps.firstOrNull {
                HemophiliaQuestionnaireBuilder.NOTE_ID == it.questionId
            }?.answersList?.firstOrNull()
            val photos = steps.firstOrNull {
                HemophiliaQuestionnaireBuilder.PHOTO_ID == it.questionId
            }?.answersList

            HemophiliaModuleValue.HemophiliaJournalRecord(
                bodyLocation = bodyLocation?.name,
                bodyPartInjury = bodyInjuryType.name,
                customBodyPart = bodyInjuryTypeName.takeIf {
                    bodyInjuryType == HemophiliaInjuryType.CUSTOM
                },
                extraData = HemophiliaRecordExtraData(
                    accidentDate,
                    reason,
                    note,
                    photos,
                    scale,
                    severity,
                    treatment?.let { medicationV2Converter(it) },
                    factor,
                )
            )
        }

        if (
            record.extraData.treatment?.dosage != null &&
            record.extraData.treatment.maxDosage != null &&
            record.extraData.treatment.maxDosage!! < record.extraData.treatment.dosage!!
        ) {
            eventsChannel.trySend(
                Events.ShowErrorToast(
                    context.getString(R.string.medicationv2_failed_submission_dosage)
                )
            )
            return@suspendCoroutine
        }

        HumaModuleKitManager
            .getInstance()
            .findModules(HemophiliaModule::class)
            .first()
            .onModuleInputSubmit(
                ModuleInputResult(HemophiliaModuleValue(record)),
                ModuleInputSubmitParams(
                    onSubmit = {
                        eventsChannel.trySend(Events.ShowThankYouAndFinish())
                        continuation.resume(Unit)
                    },
                    onFailure = {
                        eventsChannel.trySend(Events.Error)
                        continuation.resume(Unit)
                    },
                    loadingScreen = Screen.Loading,
                    showThankYou = false,
                    createFeedbackScreen = null,
                )
            )
    }

    override fun createSummaryItems(
        previousSteps: List<QuestionnaireV2Step>
    ): List<QuestionnaireV2SummaryItem> {
        val summaryItems = super.createSummaryItems(previousSteps)
        return summaryItems.addNotNull(
            position = 0,
            QuestionnaireV2SummaryItem(
                context.getString(R.string.common_record),
                bodyMapItem.name,
            )
        )
    }
}
