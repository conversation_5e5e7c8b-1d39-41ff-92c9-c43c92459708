package com.huma.sdk.pfizer.hemophilia.settings

import androidx.annotation.Keep
import com.huma.sdk.core.di.koin.HumaSdkKoinComponent
import com.huma.sdk.core.utils.commons.toTextRes
import com.huma.sdk.foundation.resources.R
import com.huma.sdk.shared.integration.profile.DefaultSettingInteractionHandler
import com.huma.sdk.shared.integration.profile.SettingInteractionHandler
import com.huma.sdk.shared.integration.profile.SettingItem
import com.huma.sdk.ui.offline.OfflineModeUI

@Keep
class HemophiliaProfileSettings : SettingItem, OfflineModeUI, HumaSdkKoinComponent {
    override val id: String = ID
    override val iconResId: Int = R.drawable.hsdk_ic_blood_glucose
    override val name = R.string.plugin_hemophilia_profile_questionnaire_title.toTextRes()
    override val interactionHandler: SettingInteractionHandler
        get() = DefaultSettingInteractionHandler.forScreen { context ->
            HemophiliaViewProfileActivity.intent(context)
        }

    override val type: String
        get() = ID

    @Keep
    companion object {
        const val ID = "hemophilia"
        const val TYPE = "HEMOPHILIA_PROFILE"
    }
}
