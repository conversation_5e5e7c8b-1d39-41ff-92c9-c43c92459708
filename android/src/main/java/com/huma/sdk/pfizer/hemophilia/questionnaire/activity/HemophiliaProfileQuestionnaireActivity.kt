package com.huma.sdk.pfizer.hemophilia.questionnaire.activity

import android.content.Context
import android.content.Intent
import androidx.annotation.Keep
import com.huma.sdk.pfizer.hemophilia.questionnaire.viewmodel.HemophiliaProfileQuestionnaireViewModel
import com.huma.sdk.questionnaire.v2.QuestionnaireV2AbsActivity
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

@Keep
class HemophiliaProfileQuestionnaireActivity : QuestionnaireV2AbsActivity() {
    override val viewModel: HemophiliaProfileQuestionnaireViewModel by viewModel()

    companion object {
        fun launch(context: Context) {
            Intent(context, HemophiliaProfileQuestionnaireActivity::class.java).apply {
                context.startActivity(this)
            }
        }
    }
}
