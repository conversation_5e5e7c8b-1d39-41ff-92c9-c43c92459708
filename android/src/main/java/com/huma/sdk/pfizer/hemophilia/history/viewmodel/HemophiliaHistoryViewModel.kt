package com.huma.sdk.pfizer.hemophilia.history.viewmodel

import androidx.annotation.Keep
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.github.michaelbull.result.Ok
import com.huma.sdk.module_kit.HumaModuleKitManager
import com.huma.sdk.pfizer.hemophilia.composable.HemophiliaBodyMapPoint
import com.huma.sdk.pfizer.hemophilia.data.HemophiliaRepository
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaLegendItem
import com.huma.sdk.pfizer.hemophilia.module.HemophiliaModule
import com.huma.sdk.pfizer.hemophilia.module.HemophiliaModuleResult
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

@Keep
class HemophiliaHistoryViewModel(
    private val widgetId: String,
    private val widgetType: String,
    private val hemophiliaRepository: HemophiliaRepository,
) : ViewModel() {
    private val _event = Channel<Event>(Channel.UNLIMITED)
    val event = _event.receiveAsFlow()

    private val _bodyMap = MutableStateFlow<State>(State.Loading)
    val bodyMap: StateFlow<State>
        get() = _bodyMap

    private val _historyRecords = MutableStateFlow<List<HemophiliaModuleResult>?>(null)
    val historyRecord: StateFlow<List<HemophiliaModuleResult>?>
        get() = _historyRecords

    @Keep
    sealed interface Event {
        data object Error : Event
    }

    @Keep
    sealed interface State {
        data object Loading : State
        data class Ready(
            val legend: List<HemophiliaLegendItem>,
            val bodyMap: List<HemophiliaBodyMapPoint>,
        ) : State
    }

    fun refresh() {
        viewModelScope.launch {
            launch {
                val result = HumaModuleKitManager
                    .getInstance()
                    .fetchModuleData<HemophiliaModuleResult>(
                        moduleId = HemophiliaModule.MODULE_ID
                    )
                    .map { it.data }
                    .sortedByDescending { it.creationDateTime }
                    .take(5)

                _historyRecords.value = result
            }

            launch {
                val result = hemophiliaRepository.getWidgetData(widgetId, widgetType)
                val previousState = _bodyMap.value
                _bodyMap.value = State.Loading
                if (result is Ok) {
                    _bodyMap.value = State.Ready(
                        result.value.legend,
                        result.value.getBodyMapPoints(),
                    )
                } else {
                    _bodyMap.value = previousState
                    _event.send(Event.Error)
                }
            }
        }
    }
}
