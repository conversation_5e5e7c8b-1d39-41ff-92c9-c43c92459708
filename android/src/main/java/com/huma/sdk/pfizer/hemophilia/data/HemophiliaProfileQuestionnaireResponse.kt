package com.huma.sdk.pfizer.hemophilia.data

import androidx.annotation.Keep
import com.huma.sdk.questionnaire.core.domain.entities.questions.metadata.DetailedQuestionnaireAnswer
import kotlinx.serialization.Serializable

@Keep
@Serializable
data class HemophiliaProfileQuestionnaireResponse(
    val id: String? = null,
    val answers: List<DetailedQuestionnaireAnswer>? = null,
    val targetJoints: List<String>? = null,
    val prophylacticMedication: MedicationId? = null,
    val asNeededMedication: MedicationId? = null,
    val updateDateTime: String? = null,
    val createDateTime: String? = null,
) {
    @Keep
    @Serializable
    data class MedicationId(
        val id: String? = null,
    )
}