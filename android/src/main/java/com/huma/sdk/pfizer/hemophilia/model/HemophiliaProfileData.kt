package com.huma.sdk.pfizer.hemophilia.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.huma.sdk.module.medicationv2.core.domain.entities.MedicationV2
import com.huma.sdk.pfizer.hemophilia.questionnaire.HemophiliaQuestionnaireBuilder
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import org.threeten.bp.LocalDate
import org.threeten.bp.format.DateTimeFormatter

@Keep
@Parcelize
data class HemophiliaProfileData(
    val weight: Double? = null,
    val weightQuestion: String? = null,
    val type: HemophiliaQuestionnaireBuilder.HemophiliaType? = null,
    val typeQuestion: String? = null,
    val severity: HemophiliaQuestionnaireBuilder.HemophiliaSeverity? = null,
    val severityQuestion: String? = null,
    val antibody: HemophiliaQuestionnaireBuilder.HemophiliaActiveAntibody? = null,
    val antibodyQuestion: String? = null,
    val bleedCount: Int? = null,
    val bleedCountQuestion: String? = null,
    val treatedBleedCount: Int? = null,
    val treatedBleedCountQuestion: String? = null,
    val targetJoints: List<HemophiliaQuestionnaireBuilder.HemophiliaTargetJoints>? = null,
    val targetJointsQuestion: String? = null,
    val targetJointsQuestion2: String? = null,
    val prophylacticMedication: MedicationV2? = null,
    val prophylacticMedicationQuestion: String? = null,
    val onDemandMedication: MedicationV2? = null,
    val additionalDiagnoses: List<HemophiliaQuestionnaireBuilder.HemophiliaDiagnoseOptions>? = null,
    val additionalDiagnosesQuestion: String? = null,
): Parcelable {
    @IgnoredOnParcel
    val prophylacticStartDateFormatted: String? by lazy {
        prophylacticMedication?.startDate?.let {
            formatter.format(LocalDate.parse(it))
        }
    }

    companion object {
        private val formatter by lazy {
            DateTimeFormatter.ofPattern("MMM dd, yyyy")
        }
    }
}