package com.huma.sdk.pfizer.hemophilia.history.composable

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.huma.sdk.core.utils.ext.Helpers.pxToDp
import com.huma.sdk.objectstorage.coil.rememberAsyncStoragePainter
import com.huma.sdk.pfizer.R
import com.huma.sdk.shared.fileid.FileId
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.components.composable.toolbar.TopNavBar
import com.huma.sdk.ui.humastyle.HumaTypeStyler

@Composable
fun HemophiliaRecordPhotoScreen(
    photoId: FileId,
    timestamp: String,
    onBackClick: () -> Unit,
) {
    TopNavBar(
        title = stringResource(R.string.file_question_type_photo),
        showBackButton = true,
        scrollable = true,
        onBackClick = onBackClick,
        modifier = Modifier
            .fillMaxSize()
            .statusBarsPadding()
    ) {
        Spacer(Modifier.height(16.dp))
        Image(
            painter = rememberAsyncStoragePainter(
                photoId,
                contentScale = ContentScale.FillWidth
            ),
            contentScale = ContentScale.FillWidth,
            contentDescription = null,
            modifier = Modifier
                .padding(horizontal = 32.dp)
                .heightIn(max = LocalWindowInfo.current
                    .containerSize
                    .height
                    .pxToDp(LocalContext.current).dp
                )
                .clip(RoundedCornerShape(8.dp))
                .fillMaxWidth()
        )
        Spacer(Modifier.height(24.dp))
        BaseText(
            text = timestamp,
            style = HumaTypeStyler.headline2,
            modifier = Modifier.padding(horizontal = 32.dp)
        )
    }
}
