package com.huma.sdk.pfizer.hemophilia.history.composable

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.hemophilia.composable.DEFAULT_BODY_MAP_ITEMS_1
import com.huma.sdk.pfizer.hemophilia.composable.HemophiliaBodyMap
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaBodyLocationType
import com.huma.sdk.ui.components.composable.toolbar.TopNavBar

@Composable
fun HemophiliaRecordLocationScreen(
    injuryName: String,
    bodyLocation: HemophiliaBodyLocationType,
    onBackClick: () -> Unit,
) {
    TopNavBar(
        title = injuryName,
        showBackButton = true,
        onBackClick = onBackClick,
        modifier = Modifier
            .fillMaxSize()
            .statusBarsPadding(),
    ) {
        Spacer(Modifier.height(24.dp))
        HemophiliaBodyMap(
            bodyPoints = DEFAULT_BODY_MAP_ITEMS_1,
            isEnabled = false,
            selectedPointId = bodyLocation.name,
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .weight(1f)
        )
        Spacer(Modifier.height(174.dp))
    }
}
