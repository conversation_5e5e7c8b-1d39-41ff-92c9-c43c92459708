package com.huma.sdk.pfizer.hemophilia.converter

import androidx.annotation.Keep
import com.huma.sdk.core.utils.commons.converters.Converter
import com.huma.sdk.pfizer.hemophilia.data.HemophiliaWidgetDataResponse
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaBodyLocationType
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaBodyMapColorItem
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaLegendItem
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaModuleResultItem
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaWidgetData

@Keep
class HemophiliaWidgetDataResponseToWidgetDataConverter :
    Converter<HemophiliaWidgetDataResponse, HemophiliaWidgetData> {
    override fun invoke(source: HemophiliaWidgetDataResponse): HemophiliaWidgetData {
        return with(source) {
            HemophiliaWidgetData(
                title = title,
                description = description,
                primaryCTAText = primaryCTAText,
                secondaryCTAText = secondaryCTAText,
                setupState = HemophiliaWidgetData.SetupState.entries.first { it.name == state },
                legend = legend?.mapNotNull { toLegendItem(it) }.orEmpty(),
                bodyMapColor = bodyMapColor?.map { toBodyMapItem(it) }.orEmpty(),
                hasSubmissions = hasSubmissions,
                results = results?.mapNotNull { toResultItem(it) }.orEmpty(),
            )
        }
    }

    private fun toResultItem(
        dto: HemophiliaWidgetDataResponse.HemophiliaModuleResultResponse
    ): HemophiliaModuleResultItem? {
        return HemophiliaModuleResultItem(
            id = dto.id ?: return null,
        )
    }

    private fun toLegendItem(
        dto: HemophiliaWidgetDataResponse.LegendItemResponse,
    ): HemophiliaLegendItem? {
        return HemophiliaLegendItem(
            color = dto.color ?: return null,
            label = dto.label ?: return null,
        )
    }

    private fun toBodyMapItem(
        dto: HemophiliaWidgetDataResponse.BodyMapItemResponse,
    ): HemophiliaBodyMapColorItem {
        return HemophiliaBodyMapColorItem(
            location = HemophiliaBodyLocationType.entries.first { it.name == dto.location },
            color = dto.color,
        )
    }
}
