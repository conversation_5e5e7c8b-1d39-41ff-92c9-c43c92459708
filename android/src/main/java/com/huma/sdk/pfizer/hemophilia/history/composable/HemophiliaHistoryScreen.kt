package com.huma.sdk.pfizer.hemophilia.history.composable

import androidx.compose.animation.AnimatedContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.R
import com.huma.sdk.pfizer.hemophilia.composable.DEFAULT_BODY_MAP_ITEMS_1
import com.huma.sdk.pfizer.hemophilia.composable.HemophiliaBodyMap
import com.huma.sdk.pfizer.hemophilia.composable.HemophiliaHistoryItem
import com.huma.sdk.pfizer.hemophilia.composable.HemophiliaLegends
import com.huma.sdk.pfizer.hemophilia.history.viewmodel.HemophiliaHistoryViewModel
import com.huma.sdk.pfizer.hemophilia.module.HemophiliaModuleResult
import com.huma.sdk.pfizer.hemophilia.widget.config.HemophiliaWidgetConfig
import com.huma.sdk.ui.components.base.Palette
import com.huma.sdk.ui.components.base.toComposeColor
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.components.composable.button.Button
import com.huma.sdk.ui.components.composable.button.style.DefaultButtonStyle
import com.huma.sdk.ui.components.composable.toolbar.TopNavBar
import com.huma.sdk.ui.humastyle.HumaTypeStyler
import com.huma.sdk.ui.utils.ext.throttledClickable

@Composable
fun HemophiliaHistoryScreen(
    description: String,
    bodyInjuryList: List<HemophiliaWidgetConfig.PointItem>,
    bodyMapState: HemophiliaHistoryViewModel.State,
    historyRecords: List<HemophiliaModuleResult>,
    onShowAllRecordsClick: () -> Unit,
    onItemClick: (HemophiliaModuleResult) -> Unit,
    onBackClick: () -> Unit,
) {
    val scrollState = rememberLazyListState()
    val title = stringResource(R.string.plugin_hemophilia_bleed_log_title)
    LazyColumn(
        state = scrollState,
        modifier = Modifier
            .fillMaxSize()
            .statusBarsPadding()
    ) {
        TopNavBar(
            title = title,
            showBackButton = true,
            onBackClick = onBackClick,
            scrollState = scrollState,
            headerBackgroundColor = Palette.Base.WHITE_ALABASTER.toComposeColor(),
        )

        item {
            AnimatedContent(
                bodyMapState,
                modifier = Modifier
                    .background(Palette.Base.WHITE_ALABASTER.toComposeColor())
            ) { state ->
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .height(450.dp),
                ) {
                    Spacer(Modifier.height(24.dp))

                    val bodyMapData = when (state) {
                        HemophiliaHistoryViewModel.State.Loading -> {
                            DEFAULT_BODY_MAP_ITEMS_1
                        }

                        is HemophiliaHistoryViewModel.State.Ready -> {
                            state.bodyMap
                        }
                    }
                    val legendData = when (state) {
                        HemophiliaHistoryViewModel.State.Loading -> emptyList()
                        is HemophiliaHistoryViewModel.State.Ready -> state.legend
                    }

                    HemophiliaBodyMap(
                        bodyPoints = bodyMapData,
                        isEnabled = false,
                        modifier = Modifier.weight(1f)
                    )
                    Spacer(Modifier.height(8.dp))
                    HemophiliaLegends(legendData)
                    Spacer(Modifier.height(12.dp))
                }
            }
        }

        item {
            Spacer(Modifier.height(24.dp))
        }

        items(historyRecords) { record ->
            HemophiliaHistoryItem(
                record.getBodyPartName(bodyInjuryList),
                record,
                modifier = Modifier.throttledClickable {
                    onItemClick(record)
                }
            )
        }

        if (historyRecords.size >= 5) {
            item {
                Box(
                    modifier = Modifier
                        .padding(horizontal = 24.dp)
                        .padding(top = 24.dp)
                ) {
                    Button(
                        text = stringResource(R.string.plugin_hemophilia_show_bleed_history),
                        buttonType = DefaultButtonStyle.secondary,
                        onClick = onShowAllRecordsClick,
                    )
                }
            }
        }

        if (description.isNotBlank()) {
            item {
                Spacer(Modifier.height(24.dp))
            }

            item {
                Column(
                    modifier = Modifier
                        .padding(vertical = 8.dp, horizontal = 24.dp)
                ) {
                    BaseText(
                        text = stringResource(R.string.detail_section_about),
                        style = HumaTypeStyler.headline,
                    )
                    Spacer(Modifier.height(8.dp))
                    BaseText(
                        text = description,
                        style = HumaTypeStyler.body,
                    )
                }
            }
        }

        item {
            Spacer(Modifier.navigationBarsPadding().height(40.dp))
        }
    }
}
