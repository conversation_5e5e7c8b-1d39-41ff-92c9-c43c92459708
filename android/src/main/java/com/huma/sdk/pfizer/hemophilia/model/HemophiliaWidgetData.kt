package com.huma.sdk.pfizer.hemophilia.model

import androidx.annotation.Keep
import com.huma.sdk.pfizer.hemophilia.composable.HemophiliaBodyMapLocation
import com.huma.sdk.pfizer.hemophilia.composable.HemophiliaBodyMapPoint

@Keep
data class HemophiliaWidgetData(
    val title: String? = null,
    val description: String? = null,
    val primaryCTAText: String? = null,
    val secondaryCTAText: String? = null,
    val results: List<HemophiliaModuleResultItem>? = null,
    val hasSubmissions: Boolean? = false,
    val setupState: SetupState,
    val legend: List<HemophiliaLegendItem>,
    val bodyMapColor: List<HemophiliaBodyMapColorItem>,
) {
    fun getBodyMapPoints() = bodyMapColor.filterNot {
        it.location.isHidden
    }.map {
        HemophiliaBodyMapPoint(
            it.location.name,
            it.colorOrDefault,
            HemophiliaBodyMapLocation(it.location.relativeX, it.location.relativeY),
        )
    }
    @Keep
    enum class SetupState {
        ACTIVE,
        SETUP_REQUIRED,
    }
}
