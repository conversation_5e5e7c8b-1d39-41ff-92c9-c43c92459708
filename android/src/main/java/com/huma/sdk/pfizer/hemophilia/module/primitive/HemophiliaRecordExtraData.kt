package com.huma.sdk.pfizer.hemophilia.module.primitive

import androidx.annotation.Keep
import com.huma.sdk.core.network.serialization.LocalDateSerializer
import com.huma.sdk.module.medicationv2.core.data.entities.MedicationV2Remote
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import org.threeten.bp.LocalDate

@Keep
@Serializable
data class HemophiliaRecordExtraData(
    @SerialName("accidentDate")
    @Serializable(LocalDateSerializer::class)
    val accidentDate: LocalDate? = null,
    @SerialName("reason")
    val reason: String? = null,
    @SerialName("note")
    val note: String? = null,
    @SerialName("photos")
    val photos: List<String>? = null,
    @SerialName("scale")
    val scale: Int? = null,
    @SerialName("severity")
    val severity: String? = null,
    @SerialName("treatment")
    val treatment: MedicationV2Remote? = null,
    @SerialName("factorUnits")
    val factorUnits: Int? = null,
)
