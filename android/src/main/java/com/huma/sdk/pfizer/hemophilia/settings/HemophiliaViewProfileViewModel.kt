package com.huma.sdk.pfizer.hemophilia.settings

import androidx.annotation.Keep
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.github.michaelbull.result.onFailure
import com.github.michaelbull.result.onSuccess
import com.huma.sdk.pfizer.hemophilia.data.HemophiliaRepository
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaProfileData
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

@Keep
class HemophiliaViewProfileViewModel(
    private val hemophiliaRepository: HemophiliaRepository,
) : ViewModel() {
    private val _state = MutableStateFlow<State>(State.Loading)
    val state: StateFlow<State>
        get() = _state

    fun refresh() {
        viewModelScope.launch {
            hemophiliaRepository.getProfileQuestionnaire().onSuccess {
                _state.value = State.Ready(it)
            }.onFailure {
                it.throwable?.printStackTrace()
            }
        }
    }

    sealed interface State {
        data object Loading : State
        data class Ready(val profile: HemophiliaProfileData) : State
    }
}