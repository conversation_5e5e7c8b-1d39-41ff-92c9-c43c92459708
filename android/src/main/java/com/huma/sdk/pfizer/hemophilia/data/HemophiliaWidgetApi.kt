package com.huma.sdk.pfizer.hemophilia.data

import com.huma.sdk.core.network.GenericResponse

interface HemophiliaWidgetApi {
    suspend fun getWidgetData(
        userId: String,
        widgetId: String,
        widgetType: String,
    ): GenericResponse<HemophiliaWidgetDataResponse>

    suspend fun postProfileQuestionnaire(
        userId: String,
        body: HemophiliaProfileQuestionnaireRequest,
    ): GenericResponse<Unit>

    suspend fun getProfileQuestionnaire(
        userId: String,
    ): GenericResponse<HemophiliaProfileQuestionnaireResponse>
}
