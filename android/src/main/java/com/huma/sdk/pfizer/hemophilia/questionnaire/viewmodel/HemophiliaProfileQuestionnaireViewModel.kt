package com.huma.sdk.pfizer.hemophilia.questionnaire.viewmodel

import android.app.Application
import androidx.annotation.Keep
import com.github.michaelbull.result.onFailure
import com.github.michaelbull.result.onSuccess
import com.huma.sdk.module.medicationv2.R
import com.huma.sdk.module.medicationv2.core.data.entities.MedicationV2Remote
import com.huma.sdk.module.medicationv2.core.data.entities.WeekDay
import com.huma.sdk.module.medicationv2.core.domain.converters.MedicationScheduleToMedicationScheduleRemoteConverter
import com.huma.sdk.module.medicationv2.core.domain.converters.QuestionnaireViewDataToMedicationV2Converter.Companion.DATE_PATTERN
import com.huma.sdk.module.medicationv2.core.domain.entities.MedicationSchedule
import com.huma.sdk.module.medicationv2.core.domain.entities.TimeInterval
import com.huma.sdk.module.medicationv2.core.domain.usecase.RefreshMedicationsUseCase
import com.huma.sdk.module.medicationv2.hemophilia.converter.MedicationFormPageToQuestionnaireStepConverter
import com.huma.sdk.module.medicationv2.hemophilia.medication_dosage.MedicationDosageAnswer
import com.huma.sdk.module.medicationv2.hemophilia.select_medication.composable.SelectMedicationAnswer
import com.huma.sdk.module.medicationv2.hemophilia.util.HemophiliaMedicationV2Utils
import com.huma.sdk.module.medicationv2.medicationlogs.core.domain.usecase.abstraction.FetchAndSaveAdherenceListUseCase
import com.huma.sdk.module.medicationv2.questionnaire.MedicationV2Form
import com.huma.sdk.module_kit.local_notification.core.LocalNotificationsScheduler
import com.huma.sdk.pfizer.hemophilia.data.HemophiliaRepository
import com.huma.sdk.pfizer.hemophilia.questionnaire.HemophiliaQuestionnaireBuilder
import com.huma.sdk.pfizer.hemophilia.questionnaire.HemophiliaQuestionnaireBuilder.HEMOPHILIA_BLEEDS_COUNT_ID
import com.huma.sdk.pfizer.hemophilia.questionnaire.HemophiliaQuestionnaireBuilder.HEMOPHILIA_TREATMENT_COUNT_ID
import com.huma.sdk.questionnaire.core.domain.entities.FormPage
import com.huma.sdk.questionnaire.v2.QuestionnaireV2AbsViewModel
import com.huma.sdk.questionnaire.v2.model.QuestionnaireV2Step
import com.huma.sdk.questionnaire.v2.model.steps.NumericInputStep
import com.huma.sdk.shared.deployment.storage.DeploymentStorage
import com.huma.sdk.shared.user.UserStorage
import org.threeten.bp.Instant
import org.threeten.bp.LocalDate
import org.threeten.bp.LocalTime
import org.threeten.bp.format.DateTimeFormatter

@Keep
class HemophiliaProfileQuestionnaireViewModel(
    context: Application,
    private val medicationConverter: MedicationScheduleToMedicationScheduleRemoteConverter,
    private val hemophiliaRepository: HemophiliaRepository,
    private val refreshMedicationsUseCase: RefreshMedicationsUseCase,
    private val fetchAndSaveAdherenceListUseCase: FetchAndSaveAdherenceListUseCase,
    formPageConverter: MedicationFormPageToQuestionnaireStepConverter,
) : QuestionnaireV2AbsViewModel(
    context = context,
    form = HemophiliaQuestionnaireBuilder.forProfile(context),
    formPageConverter = formPageConverter,
) {
    override val pageTitle: CharSequence
        get() = form.name.orEmpty()

    override suspend fun doSubmit() {
        submitForHemophiliaProfile()
    }

    private suspend fun submitForHemophiliaProfile() {
        val targetJoints = mutableSteps.value.find {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_TARGET_JOINTS_ID
        }?.answersList

        val prophylacticMedication = extractProphylacticMedication()
            ?.copy(groupId = HemophiliaMedicationV2Utils.getMedicationProphylacticGroupId())

        if (
            prophylacticMedication?.dosage != null &&
            prophylacticMedication.maxDosage != null &&
            prophylacticMedication.maxDosage!! < prophylacticMedication.dosage!!
        ) {
            eventsChannel.send(
                Events.ShowErrorToast(
                    context.getString(R.string.medicationv2_failed_submission_dosage)
                )
            )
            return
        }

        val onDemandMedication = mutableSteps.value.find {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_ON_DEMAND_ID
        }?.answersList?.let {
            SelectMedicationAnswer.fromAnswersList(it)
        }?.let {
            MedicationV2Remote(
                enabled = true,
                name = it.selected.title,
                unit = null,
                isNotificationEnabled = false,
                deploymentId = DeploymentStorage.deploymentId,
                userId = UserStorage.uid,
                groupId = HemophiliaMedicationV2Utils.getMedicationOnDemandGroupId(),
                schedule = medicationConverter(
                    MedicationSchedule.AsNeeded(0)
                ),
            )
        }

        mutableSteps.value.mapNotNull {
            it.getDetailedQuestionnaireAnswer()
        }.also { answers ->
            hemophiliaRepository.submitProfileQuestionnaire(
                answers,
                targetJoints.orEmpty(),
                prophylacticMedication,
                onDemandMedication,
            ).onSuccess {
                refreshMedicationsUseCase.execute()
                fetchAndSaveAdherenceListUseCase.invoke(Instant.now())
                LocalNotificationsScheduler.start(context)
                eventsChannel.send(Events.ShowThankYouAndFinish())
            }.onFailure {
                val message = it.tryGetErrorMessage()
                if (message != null) {
                    eventsChannel.send(Events.ShowErrorToast(message))
                } else {
                    eventsChannel.send(Events.Error)
                }
            }
        }
    }

    private fun extractProphylacticMedication(): MedicationV2Remote? {
        var medication = mutableSteps.value.find {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_ID
        }?.answersList?.let {
            SelectMedicationAnswer.fromAnswersList(it)
        }?.let {
            MedicationV2Remote(
                enabled = true,
                name = it.selected.title,
                dosage = it.selected.dosage.toDouble(),
                unit = it.selected.unit,
                unitText = it.selected.unit,
                isNotificationEnabled = true,
                deploymentId = DeploymentStorage.deploymentId,
                userId = UserStorage.uid,
            )
        } ?: return null

        val dosage = mutableSteps.value.find {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_DOSAGE_ID
        }?.answersList?.let {
            MedicationDosageAnswer.fromAnswersList(it)
        }!!

        medication = medication.copy(
            dosage = dosage.numeric.first().toDouble(),
            unit = dosage.dropdown[0],
            unitText = dosage.dropdown[1],
        )

        val frequency = mutableSteps.value.find {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_FREQUENCY_ID
        }?.answersList?.first()

        val timeOfDays = mutableSteps.value.find {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_TIME_OF_DAY_ID
        }?.answersList?.map {
            TimeInterval(LocalTime.parse(it))
        }.orEmpty()

        val weekDays = mutableSteps.value.find {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_DAYS_OF_WEEK_ID
        }?.answersList?.map {
            WeekDay.valueOf(it)
        }.orEmpty()

        val intervalDays = mutableSteps.value.find {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_DAYS_INTERVAL_ID
        }?.answersList?.firstOrNull()?.toIntOrNull()

        val maxDosage = mutableSteps.value.find {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_AS_NEEDED_ID
        }?.answersList?.firstOrNull()?.toDoubleOrNull()

        val notification = mutableSteps.value.find {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_REMINDER_ID
        }?.answersList?.firstOrNull()?.toBoolean() ?: false

        val nextDosage = mutableSteps.value.find {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_NEXT_DOSAGE_ID
        }?.answersList?.firstOrNull()?.let {
            LocalDate.parse(it)?.format(DateTimeFormatter.ofPattern(DATE_PATTERN))
        }

        when (frequency) {
            MedicationV2Form.FrequencyId.EVERY_DAY -> {
                medication = medication.copy(
                    schedule = medicationConverter(
                        MedicationSchedule.Daily(
                            timeOfReading = timeOfDays,
                        )
                    )
                )
            }

            MedicationV2Form.FrequencyId.SPECIFIC_DAYS -> {
                medication = medication.copy(
                    schedule = medicationConverter(
                        MedicationSchedule.Weekly(
                            timeOfReading = timeOfDays,
                            days = weekDays,
                        )
                    )
                )
            }

            MedicationV2Form.FrequencyId.DAYS_INTERVAL -> {
                medication = medication.copy(
                    schedule = medicationConverter(
                        MedicationSchedule.Interval(
                            timeOfReading = timeOfDays,
                            interval = intervalDays!!,
                        )
                    )
                )
            }

            MedicationV2Form.FrequencyId.AS_NEEDED -> {
                medication = medication.copy(
                    schedule = medicationConverter(
                        MedicationSchedule.AsNeeded(0)
                    ),
                    maxDosage = maxDosage!!,
                )
            }
        }

        medication = medication.copy(
            isNotificationEnabled = notification,
            startDate = nextDosage,
        )

        return medication
    }

    override fun createStep(
        formPage: FormPage,
        previousSteps: List<QuestionnaireV2Step>
    ): QuestionnaireV2Step {
        val step = super.createStep(formPage, previousSteps)
        val type = step.type
        if (formPage.getId() == HEMOPHILIA_TREATMENT_COUNT_ID && type is NumericInputStep) {
            val previousStepAnswer = previousSteps.firstOrNull {
                it.questionId == HEMOPHILIA_BLEEDS_COUNT_ID
            }?.answersList?.firstOrNull()?.toDoubleOrNull()

            return step.copy(type = type.copy(upperBound = previousStepAnswer ?: type.upperBound))
        }
        return step
    }
}
