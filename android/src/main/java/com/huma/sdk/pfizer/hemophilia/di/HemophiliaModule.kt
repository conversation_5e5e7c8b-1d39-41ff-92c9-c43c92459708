package com.huma.sdk.pfizer.hemophilia.di

import com.huma.sdk.pfizer.hemophilia.converter.HemophiliaProfileResponseToHemophiliaProfileDataConverter
import com.huma.sdk.pfizer.hemophilia.data.HemophiliaRepository
import com.huma.sdk.pfizer.hemophilia.data.HemophiliaWidgetApi
import com.huma.sdk.pfizer.hemophilia.data.HemophiliaWidgetApiImpl
import com.huma.sdk.pfizer.hemophilia.converter.HemophiliaWidgetDataResponseToWidgetDataConverter
import com.huma.sdk.pfizer.hemophilia.history.viewmodel.HemophiliaAllRecordsViewModel
import com.huma.sdk.pfizer.hemophilia.history.viewmodel.HemophiliaHistoryViewModel
import com.huma.sdk.pfizer.hemophilia.module.HemophiliaModuleProcessor
import com.huma.sdk.pfizer.hemophilia.questionnaire.viewmodel.HemophiliaEditProfileQuestionnaireViewModel
import com.huma.sdk.pfizer.hemophilia.questionnaire.viewmodel.HemophiliaJournalQuestionnaireViewModel
import com.huma.sdk.pfizer.hemophilia.questionnaire.viewmodel.HemophiliaProfileQuestionnaireViewModel
import com.huma.sdk.pfizer.hemophilia.settings.HemophiliaViewProfileViewModel
import com.huma.sdk.pfizer.hemophilia.widget.viewmodel.HemophiliaWidgetViewModel
import org.koin.android.ext.koin.androidApplication
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.dsl.module

val hemophiliaModule = module {
    single { HemophiliaRepository(get(), get(), get(), get()) }
    single<HemophiliaWidgetApi> { HemophiliaWidgetApiImpl(get()) }
    single { HemophiliaWidgetDataResponseToWidgetDataConverter() }
    single { HemophiliaProfileResponseToHemophiliaProfileDataConverter() }
    single { HemophiliaModuleProcessor() }

    viewModel { parameters ->
        HemophiliaJournalQuestionnaireViewModel(
            androidApplication(),
            parameters[0],
            parameters[1],
            get(),
            get(),
        )
    }
    viewModel {
        HemophiliaProfileQuestionnaireViewModel(
            androidApplication(),
            get(),
            get(),
            get(),
            get(),
            get(),
        )
    }
    viewModel { parameters -> HemophiliaHistoryViewModel(parameters[0], parameters[1], get()) }
    viewModel { HemophiliaAllRecordsViewModel() }
    viewModel { HemophiliaWidgetViewModel(get()) }
    viewModel { HemophiliaViewProfileViewModel(get()) }
    viewModel { parameters ->
        HemophiliaEditProfileQuestionnaireViewModel(
            parameters[0],
            androidApplication(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
        )
    }
}
