package com.huma.sdk.pfizer.hemophilia.converter

import com.huma.sdk.core.utils.commons.converters.Converter
import com.huma.sdk.module.medicationv2.core.domain.entities.MedicationV2
import com.huma.sdk.module.medicationv2.hemophilia.util.HemophiliaMedicationV2Utils
import com.huma.sdk.pfizer.hemophilia.data.HemophiliaProfileQuestionnaireResponse
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaProfileData
import com.huma.sdk.pfizer.hemophilia.questionnaire.HemophiliaQuestionnaireBuilder

class HemophiliaProfileResponseToHemophiliaProfileDataConverter :
    Converter<HemophiliaProfileQuestionnaireResponse, HemophiliaProfileData> {
    override fun invoke(source: HemophiliaProfileQuestionnaireResponse): HemophiliaProfileData {
        return HemophiliaProfileData(
            weight = source.getWeight(),
            weightQuestion = source.getQuestion(HemophiliaQuestionnaireBuilder.HEMOPHILIA_WEIGHT_ID),
            type = source.getType(),
            typeQuestion = source.getQuestion(HemophiliaQuestionnaireBuilder.HEMOPHILIA_TYPE_ID),
            severity = source.getSeverity(),
            severityQuestion = source.getQuestion(HemophiliaQuestionnaireBuilder.HEMOPHILIA_SEVERITY_ID),
            antibody = source.getAntibody(),
            antibodyQuestion = source.getQuestion(HemophiliaQuestionnaireBuilder.HEMOPHILIA_ACTIVE_ANTIBODY_ID),
            bleedCount = source.getBleedCount(),
            bleedCountQuestion = source.getQuestion(HemophiliaQuestionnaireBuilder.HEMOPHILIA_BLEEDS_COUNT_ID),
            treatedBleedCount = source.getTreatedBleedCount(),
            treatedBleedCountQuestion = source.getQuestion(HemophiliaQuestionnaireBuilder.HEMOPHILIA_TREATMENT_COUNT_ID),
            targetJoints = source.getTargetJoints(),
            targetJointsQuestion = source.getQuestion(HemophiliaQuestionnaireBuilder.HEMOPHILIA_TARGET_JOINTS_CONDITION_ID),
            targetJointsQuestion2 = source.getQuestion(HemophiliaQuestionnaireBuilder.HEMOPHILIA_TARGET_JOINTS_ID),
            prophylacticMedication = source.getProphylacticMedication(),
            prophylacticMedicationQuestion = source.getQuestion(HemophiliaQuestionnaireBuilder.HEMOPHILIA_PROPHYLACTIC_CONDITION_ID),
            onDemandMedication = source.getOnDemandMedication(),
            additionalDiagnoses = source.getAdditionalDiagnoses(),
            additionalDiagnosesQuestion = source.getQuestion(HemophiliaQuestionnaireBuilder.HEMOPHILIA_DIAGNOSED_ID)
        )
    }

    private fun HemophiliaProfileQuestionnaireResponse.getQuestion(id: String): String? {
        return answers?.firstOrNull { it.questionId == id }?.question
    }

    private fun HemophiliaProfileQuestionnaireResponse.getWeight(): Double? {
        return answers?.firstOrNull {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_WEIGHT_ID
        }?.answerList?.firstOrNull()?.toDoubleOrNull()
    }

    private fun HemophiliaProfileQuestionnaireResponse.getType(): HemophiliaQuestionnaireBuilder.HemophiliaType? {
        return answers?.firstOrNull {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_TYPE_ID
        }?.selectedChoices?.firstOrNull()?.let { choice ->
            HemophiliaQuestionnaireBuilder.HemophiliaType.entries.firstOrNull { it.name == choice }
        }
    }

    private fun HemophiliaProfileQuestionnaireResponse.getSeverity(): HemophiliaQuestionnaireBuilder.HemophiliaSeverity? {
        return answers?.firstOrNull {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_SEVERITY_ID
        }?.selectedChoices?.firstOrNull()?.let { choice ->
            HemophiliaQuestionnaireBuilder.HemophiliaSeverity.entries.firstOrNull { it.name == choice }
        }
    }

    private fun HemophiliaProfileQuestionnaireResponse.getAntibody(): HemophiliaQuestionnaireBuilder.HemophiliaActiveAntibody? {
        return answers?.firstOrNull {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_ACTIVE_ANTIBODY_ID
        }?.selectedChoices?.firstOrNull()?.let { choice ->
            HemophiliaQuestionnaireBuilder.HemophiliaActiveAntibody.entries.firstOrNull { it.name == choice }
        }
    }

    private fun HemophiliaProfileQuestionnaireResponse.getBleedCount(): Int? {
        return answers?.firstOrNull {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_BLEEDS_COUNT_ID
        }?.answerList?.firstOrNull()?.toIntOrNull()
    }

    private fun HemophiliaProfileQuestionnaireResponse.getTreatedBleedCount(): Int? {
        return answers?.firstOrNull {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_TREATMENT_COUNT_ID
        }?.answerList?.firstOrNull()?.toIntOrNull()
    }

    private fun HemophiliaProfileQuestionnaireResponse.getTargetJoints(): List<HemophiliaQuestionnaireBuilder.HemophiliaTargetJoints>? {
        return answers?.firstOrNull {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_TARGET_JOINTS_ID
        }?.selectedChoices?.let { choices ->
            choices.mapNotNull { choice ->
                HemophiliaQuestionnaireBuilder.HemophiliaTargetJoints.entries.firstOrNull { it.name == choice }
            }
        }
    }

    private fun HemophiliaProfileQuestionnaireResponse.getProphylacticMedication(): MedicationV2? {
        return prophylacticMedication?.id?.let {
            HemophiliaMedicationV2Utils.getMedicationById(it)
        }
    }

    private fun HemophiliaProfileQuestionnaireResponse.getOnDemandMedication(): MedicationV2? {
        return asNeededMedication?.id?.let {
            HemophiliaMedicationV2Utils.getMedicationById(it)
        }
    }

    private fun HemophiliaProfileQuestionnaireResponse.getAdditionalDiagnoses(): List<HemophiliaQuestionnaireBuilder.HemophiliaDiagnoseOptions>? {
        return answers?.firstOrNull {
            it.questionId == HemophiliaQuestionnaireBuilder.HEMOPHILIA_DIAGNOSED_ID
        }?.selectedChoices?.let { choices ->
            choices.mapNotNull { choice ->
                HemophiliaQuestionnaireBuilder.HemophiliaDiagnoseOptions.entries.firstOrNull { it.name == choice }
            }
        }
    }
}