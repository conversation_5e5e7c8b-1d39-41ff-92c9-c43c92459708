package com.huma.sdk.pfizer.hemophilia.addrecord.composable

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.R
import com.huma.sdk.pfizer.hemophilia.composable.DEFAULT_BODY_MAP_ITEMS_2
import com.huma.sdk.pfizer.hemophilia.composable.HemophiliaBodyMap
import com.huma.sdk.pfizer.hemophilia.questionnaire.activity.HemophiliaJournalQuestionnaireActivity
import com.huma.sdk.pfizer.hemophilia.widget.config.HemophiliaWidgetConfig
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.components.composable.toolbar.TopNavBar
import com.huma.sdk.ui.humastyle.HumaTypeStyler

@Composable
@Preview(showBackground = true)
private fun Preview() {
    HemophiliaAddRecordScreen(
        jointPoints = HemophiliaWidgetConfig.BodyMapItem(
            HemophiliaWidgetConfig.BleedType.JOINTS
        ),
        onBackClick = {},
    )
}

@Composable
fun HemophiliaAddRecordScreen(
    jointPoints: HemophiliaWidgetConfig.BodyMapItem,
    onBackClick: () -> Unit = {},
) {
    val context = LocalContext.current
    var showAddNewBleedBottomSheet by remember {
        mutableStateOf<HemophiliaWidgetConfig.LocationItem?>(null)
    }
    TopNavBar(
        title = stringResource(R.string.plugin_hemophilia_add_new_bleed),
        showBackButton = true,
        onBackClick = onBackClick,
        modifier = Modifier
            .fillMaxSize()
            .statusBarsPadding()
    ) {
        Spacer(Modifier.height(24.dp))
        HemophiliaBodyMap(
            bodyPoints = DEFAULT_BODY_MAP_ITEMS_2,
            onTouchPointClick = { selected ->
                jointPoints.locations?.firstOrNull {
                    it.location.name == selected.id
                }?.let {
                    showAddNewBleedBottomSheet = it
                }
            },
            selectedPointId = showAddNewBleedBottomSheet?.location?.name,
            modifier = Modifier.height(424.dp),
        )
        Spacer(Modifier.height(16.dp))
        BaseText(
            text = "Tap on a body part to add a bleed",
            style = HumaTypeStyler.body,
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
        )
        Spacer(Modifier.weight(1f))
    }

    showAddNewBleedBottomSheet?.let { locationItem ->
        HemophiliaAddNewBleedBottomSheet(
            items = locationItem.points,
            onItemClick = { item ->
                HemophiliaJournalQuestionnaireActivity.launch(
                    context = context,
                    bodyMapItem = item,
                    bodyLocation = locationItem.location,
                )
                onBackClick()
                showAddNewBleedBottomSheet = null
            },
            onDismissRequest = {
                showAddNewBleedBottomSheet = null
            }
        )
    }
}
