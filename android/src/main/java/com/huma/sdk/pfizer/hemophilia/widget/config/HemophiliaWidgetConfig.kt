package com.huma.sdk.pfizer.hemophilia.widget.config

import android.os.Parcelable
import androidx.annotation.Keep
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaBodyLocationType
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjuryType
import com.huma.sdk.widget.header.source.HeaderWidgetConfig
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Keep
@Parcelize
@Serializable
data class HemophiliaWidgetConfig(
    @SerialName("header")
    val header: HeaderWidgetConfig? = null,
    @SerialName("bodyMap")
    val bodyMap: List<BodyMapItem>? = null,
    @SerialName("description")
    val description: String? = null,
) : Parcelable {
    @Keep
    @Parcelize
    @Serializable
    data class BodyMapItem(
        @SerialName("bleedType")
        val bleedType: BleedType,
        @SerialName("title")
        val title: String? = null,
        @SerialName("description")
        val description: String? = null,
        @SerialName("locations")
        val locations: List<LocationItem>? = null,
    ) : Parcelable

    @Keep
    enum class BleedType {
        JOINTS, NON_JOINTS
    }

    @Keep
    @Parcelize
    @Serializable
    data class LocationItem(
        val location: HemophiliaBodyLocationType,
        val points: List<PointItem>
    ) : Parcelable

    @Keep
    @Parcelize
    @Serializable
    data class PointItem(
        val name: String? = null,
        val value: HemophiliaInjuryType,
    ) : Parcelable {
        companion object {
            fun forCustom(
                name: String
            ) = PointItem(
                name = name,
                value = HemophiliaInjuryType.CUSTOM,
            )
        }
    }
}
