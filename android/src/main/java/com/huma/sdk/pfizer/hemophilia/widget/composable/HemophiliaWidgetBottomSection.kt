package com.huma.sdk.pfizer.hemophilia.widget.composable

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.R
import com.huma.sdk.pfizer.hemophilia.composable.HemophiliaLegends
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaLegendItem
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaModuleResultItem
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaWidgetData
import com.huma.sdk.pfizer.hemophilia.widget.viewmodel.HemophiliaWidgetViewModel
import com.huma.sdk.ui.components.base.Palette
import com.huma.sdk.ui.components.base.toComposeColor
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.components.composable.button.Button
import com.huma.sdk.ui.components.composable.button.style.DefaultButtonStyle
import com.huma.sdk.ui.components.composable.shimmer.shimmerBrush
import com.huma.sdk.ui.humastyle.HumaTypeStyler

@Composable
fun HemophiliaWidgetBottomSection(
    state: HemophiliaWidgetViewModel.State,
    onSetupClick: () -> Unit,
    onRetryClick: () -> Unit,
    onAddRecordClick: () -> Unit,
    onViewHistoryClick: () -> Unit,
) {
    Box(
        modifier = Modifier
            .heightIn(max = 230.dp)
            .fillMaxWidth()
            .padding(horizontal = 24.dp)
    ) {
        when (state) {
            HemophiliaWidgetViewModel.State.Loading -> {
                HemophiliaWidgetBottomSectionLoading()
            }

            is HemophiliaWidgetViewModel.State.Ready -> {
                with(state.data) {
                    HemophiliaWidgetBottomSectionContent(
                        title,
                        description,
                        setupState,
                        legend,
                        primaryCTAText,
                        secondaryCTAText,
                        results.orEmpty(),
                        hasSubmissions ?: false,
                        onSetupClick,
                        onAddRecordClick,
                        onViewHistoryClick
                    )
                }
            }

            HemophiliaWidgetViewModel.State.Failed -> {
                HemophiliaWidgetBottomSectionFailed(onRetryClick)
            }
        }
    }
}

@Composable
private fun HemophiliaWidgetBottomSectionFailed(
    onRetryClick: () -> Unit,
) {
    Column {
        Spacer(Modifier.weight(1f))
        Button(
            text = stringResource(R.string.common_action_retry),
            buttonType = DefaultButtonStyle.primary,
            onClick = onRetryClick,
        )
        Spacer(Modifier.height(8.dp))
    }
}

@Composable
private fun HemophiliaWidgetBottomSectionContent(
    title: String?,
    description: String?,
    setupState: HemophiliaWidgetData.SetupState,
    legends: List<HemophiliaLegendItem>,
    primaryCTAText: String?,
    secondaryCTAText: String?,
    results: List<HemophiliaModuleResultItem>,
    hasSubmissions: Boolean,
    onSetupClick: () -> Unit,
    onAddRecordClick: () -> Unit,
    onViewHistoryClick: () -> Unit,
) {
    if (hasSubmissions) {
        Column {
            HemophiliaLegends(legends)
            Spacer(Modifier.height(16.dp))
            Button(
                text = secondaryCTAText,
                isEnable = results.isNotEmpty(),
                buttonType = DefaultButtonStyle.secondary,
                onClick = onViewHistoryClick,
            )
            Spacer(Modifier.height(8.dp))
            Button(
                text = primaryCTAText,
                buttonType = DefaultButtonStyle.primary,
                onClick = onAddRecordClick,
            )
        }
    } else {
      Column {
          HemophiliaLegends(legends)
          Spacer(Modifier.height(8.dp))
          Column(
              modifier = Modifier
                  .border(
                      1.dp,
                      Palette.Base.GRAY_GALLERY.toComposeColor(),
                      RoundedCornerShape(18.dp)
                  )
                  .padding(16.dp)
          ) {
              BaseText(
                  text = title,
                  style = HumaTypeStyler.bodySemiBold,
              )
              Spacer(Modifier.height(12.dp))
              HorizontalDivider(color = Palette.Base.GRAY_GALLERY.toComposeColor())
              Spacer(Modifier.height(12.dp))
              BaseText(
                  text = description,
                  style = HumaTypeStyler.caption1,
              )
              Spacer(Modifier.height(12.dp))
              Button(
                  text = primaryCTAText,
                  buttonType = DefaultButtonStyle.primary,
                  onClick = {
                      when (setupState) {
                          HemophiliaWidgetData.SetupState.ACTIVE -> onAddRecordClick()
                          HemophiliaWidgetData.SetupState.SETUP_REQUIRED -> onSetupClick()
                      }
                  },
              )
          }
      }
    }
}

@Composable
private fun HemophiliaWidgetBottomSectionLoading() {
    Column(
        modifier = Modifier
            .border(1.dp, Palette.Base.GRAY_GALLERY.toComposeColor(), RoundedCornerShape(18.dp))
            .padding(16.dp)
    ) {
        val shimmerBrush = shimmerBrush()
        ShimmerBoxHeight16(
            widthDp = 200,
            brush = shimmerBrush,
        )
        Spacer(Modifier.height(16.dp))
        HorizontalDivider(color = Palette.Base.GRAY_GALLERY.toComposeColor())
        Spacer(Modifier.height(12.dp))
        ShimmerBoxHeight16(
            widthDp = 300,
            brush = shimmerBrush,
        )
        Spacer(Modifier.height(4.dp))
        ShimmerBoxHeight16(
            widthDp = 150,
            brush = shimmerBrush,
        )
        Spacer(Modifier.height(12.dp))
        ShimmerBoxHeight48(
            brush = shimmerBrush,
            modifier = Modifier
                .fillMaxWidth()
        )
    }
}

@Composable
private fun ShimmerBoxHeight16(
    widthDp: Int,
    brush: Brush,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier
            .height(16.dp)
            .width(widthDp.dp)
            .background(brush, RoundedCornerShape(6.dp))
    )
}

@Composable
private fun ShimmerBoxHeight48(
    brush: Brush,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier
            .height(48.dp)
            .background(brush, CircleShape)
    )
}
