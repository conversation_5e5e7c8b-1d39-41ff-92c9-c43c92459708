package com.huma.sdk.pfizer.hemophilia.widget.composable

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.R
import com.huma.sdk.ui.components.base.Palette
import com.huma.sdk.ui.components.base.toComposeColor
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.humastyle.HumaTypeStyler

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HemophiliaAddNewBleedBottomSheet(
    jointTitle: String,
    jointDescription: String,
    nonJointTitle: String,
    nonJointDescription: String,
    onAddJointBleedClick: () -> Unit,
    onAddNonJointBleedClick: () -> Unit,
    onDismissRequest: () -> Unit,
) {
    ModalBottomSheet(
        onDismissRequest = onDismissRequest,
        sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true),
        shape = RoundedCornerShape(topEnd = 13.dp, topStart = 13.dp),
        containerColor = Color.White,
        dragHandle = null,
    ) {
        HemophiliaAddNewBleedBottomSheetContent(
            jointTitle,
            jointDescription,
            nonJointTitle,
            nonJointDescription,
            onAddJointBleedClick,
            onAddNonJointBleedClick,
        )
    }
}

@Composable
private fun HemophiliaAddNewBleedBottomSheetContent(
    jointTitle: String,
    jointDescription: String,
    nonJointTitle: String,
    nonJointDescription: String,
    onAddJointBleedClick: () -> Unit,
    onAddNonJointBleedClick: () -> Unit,
) {
    Column {
        Spacer(Modifier.height(12.dp))
        BaseText(
            text = "Add new bleed",
            style = HumaTypeStyler.title2Bold,
            modifier = Modifier
                .padding(horizontal = 24.dp)
        )
        Spacer(Modifier.height(24.dp))
        HemophiliaAddNewBleedBottomSheetItem(
            title = jointTitle,
            description = jointDescription,
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onAddJointBleedClick() }
                .padding(horizontal = 24.dp)
        )
        Spacer(Modifier.height(8.dp))
        HemophiliaAddNewBleedBottomSheetItem(
            title = nonJointTitle,
            description = nonJointDescription,
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onAddNonJointBleedClick() }
                .padding(horizontal = 24.dp)
        )
        Spacer(Modifier.height(8.dp))
    }
}

@Composable
private fun HemophiliaAddNewBleedBottomSheetItem(
    title: String,
    description: String,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
    ) {
        Spacer(Modifier.height(8.dp))
        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            BaseText(
                text = title,
                style = HumaTypeStyler.body,
            )
            Spacer(Modifier.weight(1f))
            Spacer(Modifier.width(20.dp))
            Icon(
                painter = painterResource(R.drawable.hsdk_arrow_right),
                contentDescription = null,
            )
        }
        Spacer(Modifier.height(4.dp))
        BaseText(
            text = description,
            style = HumaTypeStyler.caption1,
        )
        Spacer(Modifier.height(14.dp))
        HorizontalDivider(color = Palette.Base.GRAY_GALLERY.toComposeColor())
    }
}
