package com.huma.sdk.pfizer.hemophilia.widget.composable

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.hemophilia.addrecord.composable.HemophiliaAddCustomLocationBottomSheet
import com.huma.sdk.pfizer.hemophilia.composable.HemophiliaBodyMap
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjuryType
import com.huma.sdk.pfizer.hemophilia.widget.config.HemophiliaWidgetConfig
import com.huma.sdk.pfizer.hemophilia.widget.viewmodel.HemophiliaWidgetViewModel
import com.huma.sdk.widget.header.presentation.HeaderWidgetView

@Composable
fun HemophiliaWidgetScreen(
    hemophiliaConfig: HemophiliaWidgetConfig,
    state: HemophiliaWidgetViewModel.State,
    onSetupClick: () -> Unit = {},
    onRetryClick: () -> Unit = {},
    onAddJointBleedClick: () -> Unit = {},
    onAddNonJoinBleedClick: (HemophiliaWidgetConfig.PointItem) -> Unit,
    onViewHistoryClick: () -> Unit = {},
) {
    var showAddNewBleedBottomSheet by remember { mutableStateOf(false) }
    var showAddNewNonJointBleedBottomSheet by remember { mutableStateOf(false) }
    var showAddCustomBleedBottomSheet by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .statusBarsPadding()
            .fillMaxSize()
    ) {
        HeaderWidgetView(hemophiliaConfig.header?.copy(showLogo = true))
        Spacer(Modifier.height(16.dp))
        val bodyPoints = remember(state) {
            state.getBodyMapPoints()
        }
        HemophiliaBodyMap(
            bodyPoints = bodyPoints,
            isEnabled = false,
            modifier = Modifier.weight(1f),
        )
        Spacer(Modifier.height(8.dp))
        HemophiliaWidgetBottomSection(
            state,
            onSetupClick,
            onRetryClick,
            onAddRecordClick = {
                showAddNewBleedBottomSheet = true
            },
            onViewHistoryClick,
        )
        Spacer(Modifier.height(8.dp))
    }

    val joint = hemophiliaConfig.bodyMap?.firstOrNull {
        it.bleedType == HemophiliaWidgetConfig.BleedType.JOINTS
    }
    val nonJoint = hemophiliaConfig.bodyMap?.firstOrNull {
        it.bleedType == HemophiliaWidgetConfig.BleedType.NON_JOINTS
    }
    if (showAddNewBleedBottomSheet) {
        HemophiliaAddNewBleedBottomSheet(
            jointTitle = joint?.title.orEmpty(),
            jointDescription = joint?.description.orEmpty(),
            nonJointTitle = nonJoint?.title.orEmpty(),
            nonJointDescription = nonJoint?.description.orEmpty(),
            onAddJointBleedClick = {
                onAddJointBleedClick()
                showAddNewBleedBottomSheet = false
            },
            onAddNonJointBleedClick = {
                showAddNewNonJointBleedBottomSheet = true
                showAddNewBleedBottomSheet = false
            },
            onDismissRequest = {
                showAddNewBleedBottomSheet = false
            }
        )
    }

    if (showAddNewNonJointBleedBottomSheet) {
        HemophiliaAddNewNonJointBleedBottomSheet(
            title = nonJoint?.title.orEmpty(),
            items = nonJoint?.locations?.firstOrNull()?.points.orEmpty(),
            onItemClick = {
                if (it.value == HemophiliaInjuryType.CUSTOM) {
                    showAddCustomBleedBottomSheet = true
                } else {
                    onAddNonJoinBleedClick(it)
                }
                showAddNewNonJointBleedBottomSheet = false
            },
            onDismissRequest = {
                showAddNewNonJointBleedBottomSheet = false
            }
        )
    }

    if (showAddCustomBleedBottomSheet) {
        HemophiliaAddCustomLocationBottomSheet(
            onConfirmClick = { name ->
                onAddNonJoinBleedClick(HemophiliaWidgetConfig.PointItem.forCustom(name))
                showAddCustomBleedBottomSheet = false
            },
            onDismissRequest = {
                showAddCustomBleedBottomSheet = false
            }
        )
    }
}
