package com.huma.sdk.pfizer.hemophilia.settings

import android.content.Context
import android.content.Intent
import androidx.annotation.Keep
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.huma.sdk.pfizer.R
import com.huma.sdk.pfizer.hemophilia.questionnaire.activity.HemophiliaEditProfileQuestionnaireActivity
import com.huma.sdk.pfizer.hemophilia.settings.composable.HemophiliaViewProfileContent
import com.huma.sdk.ui.activity.compose.AbsComposableActivity
import com.huma.sdk.ui.components.composable.toolbar.ActionItem
import com.huma.sdk.ui.components.composable.toolbar.TopNavBar
import org.koin.androidx.viewmodel.ext.android.viewModel

@Keep
class HemophiliaViewProfileActivity : AbsComposableActivity() {
    private val viewModel by viewModel<HemophiliaViewProfileViewModel>()

    override fun onStart() {
        super.onStart()
        viewModel.refresh()
    }

    @Composable
    override fun Content() {
        val context = LocalContext.current
        val state by viewModel.state.collectAsStateWithLifecycle()

        TopNavBar(
            title = stringResource(R.string.plugin_hemophilia_profile_questionnaire_title),
            showBackButton = true,
            onBackClick = this@HemophiliaViewProfileActivity::finish,
            actionItems = (state as? HemophiliaViewProfileViewModel.State.Ready)?.profile?.let {
                listOf(ActionItem.DrawableRes(R.drawable.hsdk_libui_ic_edit_24dp) {
                    HemophiliaEditProfileQuestionnaireActivity.launch(context, it)
                })
            }.orEmpty(),
            scrollable = true,
            modifier = Modifier.statusBarsPadding(),
        ) {
            when(val captured = state) {
                HemophiliaViewProfileViewModel.State.Loading -> {

                }
                is HemophiliaViewProfileViewModel.State.Ready -> {
                    HemophiliaViewProfileContent(captured.profile)
                }
            }
        }
    }

    @Keep
    companion object {
        fun intent(context: Context) = Intent(
            context,
            HemophiliaViewProfileActivity::class.java,
        )
    }
}
