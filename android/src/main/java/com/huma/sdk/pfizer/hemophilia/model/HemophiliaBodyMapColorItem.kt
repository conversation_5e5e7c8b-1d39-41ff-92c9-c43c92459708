package com.huma.sdk.pfizer.hemophilia.model

import androidx.annotation.Keep
import androidx.compose.ui.graphics.Color
import com.huma.sdk.ui.components.base.Palette
import com.huma.sdk.ui.components.base.toComposeColor
import com.huma.sdk.ui.utils.ext.parseColorIfNotBlank

@Keep
data class HemophiliaBodyMapColorItem(
    val location: HemophiliaBodyLocationType,
    val color: String? = null,
    val defaultColor: Int = Palette.Base.GRAY_SILVER_SAND,
) {
    val colorOrDefault: Color
        get() = color.parseColorIfNotBlank(defaultColor.toComposeColor())
}
