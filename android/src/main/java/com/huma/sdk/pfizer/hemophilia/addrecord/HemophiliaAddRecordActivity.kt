package com.huma.sdk.pfizer.hemophilia.addrecord

import android.content.Context
import android.content.Intent
import androidx.annotation.Keep
import androidx.compose.runtime.Composable
import com.huma.sdk.pfizer.hemophilia.addrecord.composable.HemophiliaAddRecordScreen
import com.huma.sdk.pfizer.hemophilia.widget.config.HemophiliaWidgetConfig
import com.huma.sdk.ui.activity.compose.AbsComposableActivity

@Keep
class HemophiliaAddRecordActivity : AbsComposableActivity() {
    private val jointPoints: HemophiliaWidgetConfig.BodyMapItem by lazy {
        intent.getParcelableExtra(EXTRA_BODY_JOINT_POINTS)!!
    }

    @Composable
    override fun Content() {
        HemophiliaAddRecordScreen(
            jointPoints,
            onBackClick = this::finish,
        )
    }

    companion object {
        private const val EXTRA_BODY_JOINT_POINTS = "EXTRA_BODY_JOINT_POINTS"
        fun launch(
            context: Context,
            jointPoints: HemophiliaWidgetConfig.BodyMapItem,
        ) {
            Intent(context, HemophiliaAddRecordActivity::class.java).apply {
                putExtra(EXTRA_BODY_JOINT_POINTS, jointPoints)
                context.startActivity(this)
            }
        }
    }
}
