package com.huma.sdk.pfizer.hemophilia.history.composable

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.R
import com.huma.sdk.pfizer.hemophilia.composable.HemophiliaHistoryItem
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjuryType
import com.huma.sdk.pfizer.hemophilia.module.HemophiliaModuleResult
import com.huma.sdk.pfizer.hemophilia.widget.config.HemophiliaWidgetConfig
import com.huma.sdk.ui.components.composable.toolbar.TopNavBar
import com.huma.sdk.ui.utils.ext.throttledClickable

@Composable
@Preview(showBackground = true)
private fun Preview() {
    HemophiliaAllRecordsScreen(
        bodyInjuryList = listOf(
            HemophiliaWidgetConfig.PointItem(
                name = "Gums bleed",
                value = HemophiliaInjuryType.GUMS_BLEED,
            )
        ),
        records = listOf(
            HemophiliaModuleResult.dummy(),
            HemophiliaModuleResult.dummy(),
            HemophiliaModuleResult.dummy(),
            HemophiliaModuleResult.dummy(),
        ),
        onBackClick = {},
        onItemClick = {},
    )
}

@Composable
fun HemophiliaAllRecordsScreen(
    bodyInjuryList: List<HemophiliaWidgetConfig.PointItem>,
    records: List<HemophiliaModuleResult>,
    onItemClick: (HemophiliaModuleResult) -> Unit,
    onBackClick: () -> Unit,
) {
    val scrollState = rememberLazyListState()
    val title = stringResource(R.string.plugin_hemophilia_bleed_history_title)

    LazyColumn(
        state = scrollState,
        modifier = Modifier
            .fillMaxSize()
            .statusBarsPadding()
    ) {
        TopNavBar(
            title = title,
            showBackButton = true,
            onBackClick = onBackClick,
            scrollState = scrollState,
        )
        item {
            Spacer(Modifier.height(24.dp))
        }
        items(records) { record ->
            HemophiliaHistoryItem(
                record.getBodyPartName(bodyInjuryList),
                record,
                modifier = Modifier.throttledClickable {
                    onItemClick(record)
                }
            )
        }
    }
}
