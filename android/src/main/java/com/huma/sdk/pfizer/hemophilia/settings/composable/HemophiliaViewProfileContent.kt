package com.huma.sdk.pfizer.hemophilia.settings.composable

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.R
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaProfileData


@Composable
fun HemophiliaViewProfileContent(
    profile: HemophiliaProfileData,
) {
    Spacer(Modifier.height(24.dp))

    if (profile.weight != null) {
        HemophiliaProfileItem(
            name = profile.weightQuestion ?: stringResource(R.string.plugin_hemophilia_weight),
            value = stringResource(R.string.plugin_hemophilia_weight_lb, profile.weight.toString()),
        )
    }

    if (profile.type != null) {
        HemophiliaProfileItem(
            name = profile.typeQuestion
                ?: stringResource(R.string.plugin_hemophilia_type_of_hemophilia),
            value = stringResource(profile.type.titleRes),
        )
    }

    if (profile.severity != null) {
        HemophiliaProfileItem(
            name = profile.severityQuestion
                ?: stringResource(R.string.plugin_hemophilia_severity_of_hemophilia),
            value = stringResource(profile.severity.titleRes),
        )
    }

    if (profile.antibody != null) {
        HemophiliaProfileItem(
            name = profile.antibodyQuestion
                ?: stringResource(R.string.plugin_hemophilia_inhibitors_history),
            value = stringResource(profile.antibody.titleRes),
        )
    }

    if (profile.bleedCount != null) {
        HemophiliaProfileItem(
            name = profile.bleedCountQuestion
                ?: stringResource(R.string.plugin_hemophilia_bleed_count),
            value = profile.bleedCount.toString(),
        )
    }

    if (profile.treatedBleedCount != null) {
        HemophiliaProfileItem(
            name = profile.treatedBleedCountQuestion
                ?: stringResource(R.string.plugin_hemophilia_treated_bleed_count),
            value = profile.treatedBleedCount.toString(),
        )
    }

    HemophiliaProfileItem(
        name = profile.targetJointsQuestion
            ?: stringResource(R.string.plugin_hemophilia_existence_of_target_joints),
        value = if (!profile.targetJoints.isNullOrEmpty())
            stringResource(R.string.common_action_yes)
        else
            stringResource(R.string.no),
    )

    if (!profile.targetJoints.isNullOrEmpty()) {
        HemophiliaProfileItem(
            name = profile.targetJointsQuestion2
                ?: stringResource(R.string.plugin_hemophilia_target_joints),
            value = profile.targetJoints.map { stringResource(it.titleRes) }.joinToString(),
        )
    }

    HemophiliaProfileItem(
        name = profile.prophylacticMedicationQuestion
            ?: stringResource(R.string.plugin_hemophilia_prophylactic_treatment),
        value = if (profile.prophylacticMedication != null)
            stringResource(R.string.common_action_yes)
        else
            stringResource(R.string.no),
    )

    if (profile.prophylacticMedication != null) {
        profile.prophylacticMedication.codingName.name?.let {
            HemophiliaProfileItem(
                name = stringResource(R.string.plugin_hemophilia_current_prophylactic_treatment_for_hemophilia),
                value = it,
            )
        }

        profile.prophylacticStartDateFormatted?.let {
            HemophiliaProfileItem(
                name = stringResource(R.string.plugin_hemophilia_prophylactic_treatment_next_dose_date),
                value = it,
            )
        }
    }

    profile.onDemandMedication?.codingName?.name?.let {
        HemophiliaProfileItem(
            name = stringResource(R.string.plugin_hemophilia_select_on_demand_title),
            value = it,
        )
    }

    if (!profile.additionalDiagnoses.isNullOrEmpty()) {
        HemophiliaProfileItem(
            name = profile.additionalDiagnosesQuestion
                ?: stringResource(R.string.plugin_hemophilia_additional_diagnose),
            value = profile.additionalDiagnoses.map { stringResource(it.titleRes) }.joinToString(),
        )
    }

    Spacer(Modifier.height(40.dp))
}
