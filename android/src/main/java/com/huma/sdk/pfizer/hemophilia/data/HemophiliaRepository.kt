package com.huma.sdk.pfizer.hemophilia.data

import androidx.annotation.Keep
import com.github.michaelbull.result.Result
import com.github.michaelbull.result.andThen
import com.huma.sdk.core.network.commons.Error
import com.huma.sdk.core.network.converter.mapResponseToResult
import com.huma.sdk.core.utils.ext.ok
import com.huma.sdk.module.medicationv2.core.data.entities.MedicationV2Remote
import com.huma.sdk.pfizer.hemophilia.converter.HemophiliaProfileResponseToHemophiliaProfileDataConverter
import com.huma.sdk.pfizer.hemophilia.converter.HemophiliaWidgetDataResponseToWidgetDataConverter
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaProfileData
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaWidgetData
import com.huma.sdk.questionnaire.core.domain.entities.questions.metadata.DetailedQuestionnaireAnswer
import com.huma.sdk.shared.user.domain.repo.UserRepo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.JsonElement

@Keep
class HemophiliaRepository(
    private val userRepository: UserRepo,
    private val hemophiliaWidgetApi: HemophiliaWidgetApi,
    private val hemophiliaWidgetDataResponseToWidgetDataConverter: HemophiliaWidgetDataResponseToWidgetDataConverter,
    private val hemophiliaProfileResponseToHemophiliaProfileDataConverter: HemophiliaProfileResponseToHemophiliaProfileDataConverter,
) {
    suspend fun getWidgetData(
        widgetId: String,
        widgetType: String,
    ): Result<HemophiliaWidgetData, Error> {
        return withContext(Dispatchers.IO) {
            userRepository.getUser(false).andThen {
                mapResponseToResult(hemophiliaWidgetApi.getWidgetData(it.id, widgetId, widgetType)) {
                    hemophiliaWidgetDataResponseToWidgetDataConverter(requireNotNull(it)).ok()
                }
            }
        }
    }

    suspend fun getProfileQuestionnaire(): Result<HemophiliaProfileData, Error> {
        return withContext(Dispatchers.IO) {
            userRepository.getUser(false).andThen {
                mapResponseToResult(hemophiliaWidgetApi.getProfileQuestionnaire(it.id)) {
                    hemophiliaProfileResponseToHemophiliaProfileDataConverter(requireNotNull(it)).ok()
                }
            }
        }
    }

    suspend fun submitProfileQuestionnaire(
        answers: List<DetailedQuestionnaireAnswer>,
        targetJoints: List<String>,
        prophylacticMedication: MedicationV2Remote?,
        asNeededMedication: MedicationV2Remote?,
    ): Result<Unit, Error> {
        return withContext(Dispatchers.IO) {
            userRepository.getUser(false).andThen {
                hemophiliaWidgetApi.postProfileQuestionnaire(
                    it.id,
                    HemophiliaProfileQuestionnaireRequest(
                        answers = answers,
                        targetJoints = targetJoints,
                        prophylacticMedication = prophylacticMedication,
                        asNeededMedication = asNeededMedication,
                    )
                ).let {
                    mapResponseToResult(it) {
                        Unit.ok()
                    }
                }
            }
        }
    }
}
