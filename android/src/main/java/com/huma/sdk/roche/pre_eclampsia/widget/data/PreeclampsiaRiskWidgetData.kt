package com.huma.sdk.roche.pre_eclampsia.widget.data

import androidx.annotation.Keep
import com.huma.sdk.core.network.serialization.LocalDateSerializer
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import org.threeten.bp.LocalDate

@Keep
@Serializable
data class PreeclampsiaRiskWidgetData(
    val state: SetupState,
    val title: String? = null,
    val subtitle: String? = null,
    val description: String? = null,
    @SerialName("primaryCTAtext")
    val primaryCTAText: String? = null,
    @SerialName("secondaryCTAtext")
    val secondaryCTAText: String? = null,
    val progress: Float? = null,
    val progressText: String? = null,
    val message: Message? = null,
    val imageUrl: String? = null,
    @Serializable(with = LocalDateSerializer::class)
    val dueDate: LocalDate? = null,
) {
    @Keep
    @Serializable
    data class Message(
        val type: MessageType,
        val text: String? = null,
    )

    @Keep
    enum class MessageType {
        ERROR, INFO
    }

    @Keep
    enum class SetupState {
        SETUP_REQUIRED,
        ACTIVE,
    }
}
