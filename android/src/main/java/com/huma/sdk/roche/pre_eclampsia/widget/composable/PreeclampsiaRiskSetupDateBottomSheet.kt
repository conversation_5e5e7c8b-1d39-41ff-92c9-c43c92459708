package com.huma.sdk.roche.pre_eclampsia.widget.composable

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.huma.sdk.roche.R
import com.huma.sdk.ui.basiccomponent.widget.datapicker_spinner.compose.WheelDatePicker
import com.huma.sdk.ui.basiccomponent.widget.datapicker_spinner.model.Date
import com.huma.sdk.ui.basiccomponent.widget.datapicker_spinner.utils.DateUtils
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.components.composable.button.Button
import com.huma.sdk.ui.components.composable.button.style.DefaultButtonStyle
import com.huma.sdk.ui.humastyle.HumaTypeStyler
import org.threeten.bp.LocalDate
import java.util.Calendar

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PreeclampsiaRiskSetupDateBottomSheet(
    initialDate: LocalDate = LocalDate.now(),
    onDateSelected: (LocalDate) -> Unit,
    onDismissRequest: () -> Unit,
) {
    var currentSelectedDate by remember { mutableStateOf<LocalDate?>(null) }
    ModalBottomSheet(
        onDismissRequest = onDismissRequest,
        sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true),
        shape = RoundedCornerShape(topEnd = 24.dp, topStart = 24.dp),
        containerColor = Color.White,
        dragHandle = null,
    ) {
        Column(
            modifier = Modifier
                .padding(vertical = 16.dp, horizontal = 24.dp)
        ) {
            BaseText(
                text = stringResource(R.string.plugin_pre_eclampsia_risk_enter_estimated_delivery_date),
                style = HumaTypeStyler.headline,
            )
            Spacer(Modifier.height(8.dp))
            WheelDatePicker(
                startDate = Date(initialDate.year, initialDate.monthValue - 1, initialDate.dayOfMonth),
                maxDate = Date(
                    Calendar.getInstance().apply {
                        add(Calendar.WEEK_OF_MONTH, +40)
                    }.timeInMillis
                ),
                minDate = Date(DateUtils.getCurrentTime()),
                onDateChanged = { dayOfMonth, month, year, _ ->
                    currentSelectedDate = LocalDate.of(year, month + 1, dayOfMonth)
                }
            )
            Button(
                text = stringResource(R.string.common_action_save),
                buttonType = DefaultButtonStyle.primary,
                onClick = {
                    currentSelectedDate?.let(onDateSelected)
                }
            )
            Spacer(Modifier.height(8.dp))
            Button(
                text = stringResource(R.string.common_action_cancel),
                buttonType = DefaultButtonStyle.secondary,
                onClick = onDismissRequest,
            )
        }
    }
}
