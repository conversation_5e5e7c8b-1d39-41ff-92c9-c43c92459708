setup:
	@source uv sync

billing/tests/run:
	@echo "+ Checking makemigrations has ran"
	@python manage.py makemigrations --check --dry-run || (echo "models changed, run makemigrations" && exit 2)
	@echo "+ Running tests in docker-compose"
	@mkdir -p test_results
	@echo "+ Running unit tests"
	@pytest --disable-pytest-warnings -c .pytest.ini ./billing --ignore-glob='*IntegrationTests*' --cov=billing \
		--cov-report=xml:test_results/coverage-unit-tests.xml --cov-branch \
		--junitxml=test_results/unit-junit_result.xml 2>&1 | tee test_results/unit-pytest.log

	@if grep -q -e "ERROR" -e "FAILED" test_results/unit-pytest.log; then \
  		echo "Unit tests failed. Aborting..."; exit 1; \
	fi
	@echo "+ Running integration tests"
	@pytest --disable-pytest-warnings -c .pytest.ini ./billing --ignore-glob='*UnitTests*' --cov=billing \
		--cov-report=xml:test_results/coverage-integration-tests.xml --cov-branch \
		--junitxml=test_results/integration-junit_result.xml 2>&1 | tee test_results/integration-pytest.log

	@if grep -q -e "ERROR" -e "FAILED" test_results/integration-pytest.log; then \
  		echo "Integration tests failed. Aborting..."; exit 1; \
	fi

##### run full test suit in docker #####
billing/tests/run/docker-compose:
	@echo "+ Running rpmserver full test suit"
	@mkdir -p test_results
	@docker compose -f docker-compose.yaml up --build tests --exit-code-from tests
	@echo "DONE"

common/git-full-tests-code-cov-pr-comment: export COV_FILE_NAME="pr-tests-coverage.txt"
common/git-full-tests-code-cov-pr-comment:
	@echo "+ unit PR comment creation"
	@echo -n "**Unit Test** Coverage: " > $(COV_FILE_NAME) && \
		grep -oEm1 'line-rate="([0-9]+(.[0-9]+)?)"' test_results/coverage-unit-tests.xml \
			| grep -oE '[0-9]+(.[0-9]+)?' \
			| awk '{printf "%.0f", $$1 * 100}' >> $(COV_FILE_NAME) && \
			echo "%" >> $(COV_FILE_NAME)
	@echo "+ integration PR comment creation"
	@echo -n "**Integration Test** Coverage: " >> $(COV_FILE_NAME) && \
		grep -oEm1 'line-rate="([0-9]+.[0-9]+)"' test_results/coverage-integration-tests.xml \
			| grep -oE '[0-9]+.[0-9]+' \
			| awk '{printf "%.0f", $$1 * 100}' >> $(COV_FILE_NAME) && \
			echo "%" >> $(COV_FILE_NAME)

common/git-tests-code-cov-pr-comment:
	@echo "+ PR comment creation"
	@echo "#### Links" >> pr-tests-coverage.txt && \
		echo "- [Github Actions](https://github.com/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID})" >> pr-tests-coverage.txt
