name: CI

on:
  push:
    branches:
      - master
      - "release-**"
  pull_request:

jobs:
  linter:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code Repository
        uses: actions/checkout@v4
      - name: Clone Submodule
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.HSP_READER_TOKEN }}
          repository: huma-engineering/huma-server-sdk
          path: libs/huma-server-sdk
          ref: master
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
      - name: re-reroute to cloned
        run: |
          sed -i 's|../huma-server-sdk/pyproject.toml|libs/huma-server-sdk/pyproject.toml|g' pyproject.toml

      - name: Run pre-commit
        uses: pre-commit/action@v3.0.1
  tests:
    name: Tests
    runs-on: ubuntu-latest
    steps:
      - name: Clone repo
        uses: actions/checkout@v4
      - name: Clone Submodule
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.HSP_READER_TOKEN }}
          repository: huma-engineering/huma-server-sdk
          path: libs/huma-server-sdk
          ref: master
      - name: Run Test Suit
        id: run_tests
        env:
          DOCKER_BUILDKIT: "1"
        run: make billing/tests/run/docker-compose
      - name: Store unit pytest results
        uses: actions/upload-artifact@v4
        with:
          name: unit-pytest.log
          path: ./test_results/unit-pytest.log
      - name: Store integration pytest results
        uses: actions/upload-artifact@v4
        with:
          name: integration-pytest.log
          path: ./test_results/integration-pytest.log
      - name: Store unit junit report
        uses: actions/upload-artifact@v4
        with:
          name: unit-junit_result.xml
          path: ./test_results/unit-junit_result.xml
      - name: Store integration junit report
        uses: actions/upload-artifact@v4
        with:
          name: integration-junit_result.xml
          path: ./test_results/integration-junit_result.xml
      - name: Publish code coverage results
        env:
          prNumber: ${{ github.event.pull_request.number }}
        run: |
          make common/git-full-tests-code-cov-pr-comment
          echo ${{ secrets.GITHUB_TOKEN }} | gh auth login --with-token
      - name: Store Test Coverage
        uses: actions/upload-artifact@v4
        with:
          name: pr-tests-coverage
          path: pr-tests-coverage.txt
      - name: Store Tests Coverage XML Report
        if: steps.run_tests.outcome == 'success'
        uses: actions/upload-artifact@v4
        with:
          name: coverage-xml-report
          path: |
            ./test_results/coverage-unit-tests.xml
            ./test_results/coverage-integration-tests.xml

  publish_coverage_results:
    name: Publish Tests Coverage Results
    needs: tests
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    permissions:
      actions: write
      checks: write
      pull-requests: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - name: Download tests
        uses: actions/download-artifact@v4
        with:
          name: pr-tests-coverage
      - name: Merge Tests Coverage Files
        run: make common/git-tests-code-cov-pr-comment
      - name: Find Comment
        if: github.event_name == 'pull_request'
        uses: peter-evans/find-comment@v3
        id: fc
        with:
          issue-number: ${{ github.event.pull_request.number }}
          comment-author: "github-actions[bot]"
          body-includes: Coverage
      - id: get-comment-body
        if: github.event_name == 'pull_request'
        run: |
          echo 'comment_body<<EOV' >> $GITHUB_ENV
          cat pr-tests-coverage.txt >> $GITHUB_ENV
          echo 'EOV' >> $GITHUB_ENV
      - name: Create or update comment
        if: github.event_name == 'pull_request'
        uses: peter-evans/create-or-update-comment@v4
        with:
          comment-id: ${{ steps.fc.outputs.comment-id }}
          issue-number: ${{ github.event.pull_request.number }}
          body: ${{ env.comment_body }}
          edit-mode: replace

  publish_test_results:
    name: Publish Tests Results
    needs: tests
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    permissions:
      actions: write
      checks: write
      pull-requests: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - name: Download unit tests
        uses: actions/download-artifact@v4
        with:
          name: unit-junit_result.xml
          path: ./test_results/
      - name: Download integration tests
        uses: actions/download-artifact@v4
        with:
          name: integration-junit_result.xml
          path: ./test_results/
      - name: Test Results
        uses: EnricoMi/publish-unit-test-result-action@v2
        with:
          check_name: "PR Test Results"
          files: test_results/**/*.xml

  sonarcloud:
    name: SonarCloud
    needs: tests
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Shallow clones should be disabled for a better relevancy of analysis
      - name: Download Test Coverage result
        uses: actions/download-artifact@v4
        with:
          name: coverage-xml-report
      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}  # Needed to get PR information, if any
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
