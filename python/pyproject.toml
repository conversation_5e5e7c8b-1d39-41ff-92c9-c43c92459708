[project]
name = "mg-plugin"
version = "1.2.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "framework",
    "huma_plugins",
    "sdk",
]

[tool.uv]
dev-dependencies = [
    "black>=24.10.0",
    "coverage>=7.6.4",
    "freezegun>=1.5.1",
    "packaging>=24.1",
    "pipfile-requirements>=0.3.0",
    "pre-commit>=4.0.1",
    "pytest-cov>=6.0.0",
    "pytest-django>=4.9.0",
    "yamllint>=1.35.1",
]

[tool.uv.sources]
framework = { git = "https://github.com/huma-engineering/huma-server-platform.git", rev = "v2.4.4" }
huma_plugins = { path = "../../huma-server-plugins" }
sdk = { path = "../../huma-server-sdk" }
