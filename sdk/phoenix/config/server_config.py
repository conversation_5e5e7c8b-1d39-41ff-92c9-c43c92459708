from dataclasses import field
from enum import Enum

from sdk.appointment.config.config import AppointmentConfig
from sdk.auth.config.auth_config import AuthConfig
from sdk.authorization.config import AuthorizationConfig, InvitationConfig
from sdk.builder.config import BuilderConfig
from sdk.common.utils.convertible import (
    convertibleclass,
    default_field,
    enum_list_field,
    meta,
    read_yaml_file,
    required_field,
)
from sdk.common.utils.validators import validate_email
from sdk.deployment.config.config import DeploymentConfig
from sdk.key_action.config.config import KeyActionConfig
from sdk.limiter.config.limiter import ServerLimiterConfig, StorageLimiterConfig
from sdk.module_result.config.config import ModuleResultConfig
from sdk.phoenix.config.adapters_config import Adapters
from sdk.phoenix.config.base import BasePhoenixConfig, ConfigPlaces, SubscriptableConfig, UI, ui_field
from sdk.phoenix.config.project_config import Project
from sdk.rule_engine.config.rule_engine_config import RuleEngineConfig


class EmailAdapterType(Enum):
    SMTP = "smtp"
    MAILGUN = "mailgun"
    AWSEMAIL = "awsEmail"


class APISpecConfigTags(Enum):
    INTERNAL = "Internal"
    EXTERNAL = "External"


class VideoAdapterType(Enum):
    TWILIO = "twilio"
    WEBEX = "webex"


def api_doc_level(*args: APISpecConfigTags):
    from apiflask.scaffold import _annotate

    def decorator(f):
        _annotate(f, api_doc_level=set(args))
        return f

    return decorator


@convertibleclass
class SwaggerConfig(BasePhoenixConfig):
    template: dict = default_field()
    specs_route: str = field(default="/apidocs/")
    includeTags: list[APISpecConfigTags] = enum_list_field(APISpecConfigTags, default=(APISpecConfigTags.EXTERNAL,))

    def post_init(self):
        if not self.specs_route.startswith("/"):
            self.specs_route = "/" + self.specs_route
        if not self.specs_route.endswith("/"):
            self.specs_route += "/"


@convertibleclass
class IntercomConfig(BasePhoenixConfig):
    webSecretKey: str = default_field()
    androidSecretKey: str = default_field()
    iosSecretKey: str = default_field()


@convertibleclass
class MetricsConfig:
    enabled: bool = field(default=True)
    protected: bool = field(default=True)


@convertibleclass
class CeleryConfig(BasePhoenixConfig):
    brokerUrl: str = default_field()


@convertibleclass
class NotificationConfig(BasePhoenixConfig):
    pass


@convertibleclass
class VideoConfig(BasePhoenixConfig):
    pass


@convertibleclass
class AuditLogger(BasePhoenixConfig):
    pass


@convertibleclass
class CalendarConfig(BasePhoenixConfig):
    prefetchDays: int = field(default=7, metadata=meta(lambda n: n >= 0))


@convertibleclass
class FluentdConfig:
    tag: str = field(default="huma")
    host: str = field(default="localhost")
    port: int = field(default=8887)


@convertibleclass
class InboxConfig(BasePhoenixConfig):
    pass


class StorageAdapter(Enum):
    MINIO = "MINIO"
    OSS = "OSS"
    GCS = "GCS"
    AZURE = "AZURE"


@convertibleclass
class StorageConfig(BasePhoenixConfig):
    authEnabled: bool = default_field()
    storageAdapter: StorageAdapter = field(default=StorageAdapter.MINIO)
    sharedStorageAdapter: StorageAdapter = default_field()
    allowedBuckets: list[str] = default_field()
    defaultBucket: str = default_field()
    sharedBucket: str = default_field()
    defaultRegion: str = default_field()
    rateLimit: StorageLimiterConfig = default_field()

    def post_init(self):
        if not self.rateLimit:
            self.rateLimit = StorageLimiterConfig(write="10/minute", read="60 per minute")


@convertibleclass
class AssetsConfig(BasePhoenixConfig):
    CONFIG_NAME = "AssetsConfig"
    UI_TITLE = "Assets Config"

    ConfigType: ConfigPlaces = ConfigPlaces.EXTERNAL

    logoUrl: str = ui_field(
        default="https://static.cdn.huma.com/fill_8.png",
        ui_name="Logo URL",
        ui_type=UI.STRING,
    )
    boyImageUrl: str = ui_field(
        default="https://static.cdn.huma.com/boy.png",
        ui_name="Boy Image URL",
        ui_type=UI.STRING,
    )


@convertibleclass
class Server(SubscriptableConfig):
    HOST = "host"
    PORT = "port"
    HOST_URL = "hostUrl"
    HCP_URL = "hcpUrl"
    WEB_APP_URL = "webAppUrl"
    DEBUG = "debug"
    DEBUG_LOG = "debugLog"
    DEBUG_ROUTER = "debugRouter"
    TEST_ENVIRONMENT = "testEnvironment"
    VERSION = "version"
    MAX_CONTENT_SIZE = "maxContentSize"
    AUDIT_LOGGER = "auditLogger"
    SWAGGER = "swagger"
    CELERY = "celery"
    PROJECT = "project"
    RATE_LIMIT = "rateLimit"
    ADAPTERS = "adapters"
    CALENDAR = "calendar"
    STORAGE = "storage"
    AUTH = "auth"
    INBOX = "inbox"
    COUNTRY_CODE = "countryCode"
    RELEASE_DATE = "releaseDate"
    EMAIL_ADAPTER = "emailAdapter"
    SUPPORT_EMAIL = "supportEmail"
    IMPROVED_ONBOARDING = "improvedOnboarding"

    host: str = field(default="0.0.0.0")
    port: int = field(default=5000)
    hcpUrl: str = field(default="")
    hostUrl: str = default_field()
    webAppUrl: str = default_field()
    debug: bool = field(default=False)
    debugLog: bool = field(default=False)
    debugRouter: bool = field(default=False)
    testEnvironment: bool = field(default=False)
    version: str = default_field()
    maxContentSize: int = field(default=100 * 1024 * 1024)
    iosAppUrl: str = default_field()
    androidAppUrl: str = default_field()
    countryName: str = default_field()
    countryCode: str = default_field()
    improvedOnboarding: bool = field(default=False)

    adapters: Adapters = default_field()
    auditLogger: AuditLogger = default_field()
    auth: AuthConfig = default_field()
    authorization: AuthorizationConfig = field(default_factory=AuthorizationConfig)
    appointment: AppointmentConfig = field(default_factory=AppointmentConfig)
    builder: BuilderConfig = field(default_factory=BuilderConfig)
    calendar: CalendarConfig = field(default_factory=CalendarConfig)
    metrics: MetricsConfig = field(default_factory=MetricsConfig)
    celery: CeleryConfig = default_field()
    deployment: DeploymentConfig = field(default_factory=DeploymentConfig)
    emailAdapter: EmailAdapterType = field(default=EmailAdapterType.MAILGUN)
    inbox: InboxConfig = field(default_factory=InboxConfig)
    invitation: InvitationConfig = field(default_factory=InvitationConfig)
    keyAction: KeyActionConfig = field(default_factory=KeyActionConfig)
    moduleResult: ModuleResultConfig = field(default_factory=ModuleResultConfig)
    notification: NotificationConfig = field(default_factory=NotificationConfig)
    project: Project = required_field()
    rateLimit: ServerLimiterConfig = default_field()
    releaseDate: str = required_field()
    storage: StorageConfig = default_field()
    supportEmail: str = required_field(metadata=meta(validate_email))
    swagger: SwaggerConfig = default_field()
    intercom: IntercomConfig = default_field()
    assets: AssetsConfig = field(default_factory=AssetsConfig)
    ruleEngine: RuleEngineConfig = field(default_factory=RuleEngineConfig)
    videoAdapter: VideoAdapterType = field(default=VideoAdapterType.TWILIO)


@convertibleclass
class PhoenixServerConfig(SubscriptableConfig):
    server: Server = required_field()

    @classmethod
    def get(cls, config_file_path, override_config: dict = None) -> "PhoenixServerConfig":
        config_dict = read_yaml_file(config_file_path)
        if override_config:
            override_config_fields(config_dict, override_config)

        # if found_unexpected_values := validate_config_dict(cls, config_dict):
        #     raise ServerConfigsValidationFailed(found_unexpected_values)

        return cls.from_dict(config_dict)


def apply_argument(config_dict, path, value):
    key = path[0]
    if len(path) == 1:
        set_value = value
        tpe = type(config_dict.get(key))
        if value in (None, "None"):
            set_value = None
        elif tpe == bool or value in ("true", "True", "false", "False"):
            set_value = value in ("true", "True")
        elif tpe == int:
            set_value = int(value)
        elif tpe == float:
            set_value = float(value)

        config_dict[key] = set_value
    else:
        if key in config_dict:
            apply_argument(config_dict[key], path[1:], value)


def override_config_fields(config_dict, args: dict[str, str]):
    if args:
        for key, value in args.items():
            path = key.split(".")
            if path:
                apply_argument(config_dict, path, value)
