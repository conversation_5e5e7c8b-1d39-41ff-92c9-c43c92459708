from sdk.common.common_models.sort import SortField
from sdk.common.utils.inject import autoparams
from sdk.common.utils.json_utils import replace_values
from sdk.module_result.dtos.primitives import PrimitiveDTOs
from sdk.module_result.modules.cvd_risk_score._primitive import CVDRiskScoreDTO
from sdk.module_result.modules.visualizer import HTMLVisualizer
from sdk.module_result.repository.module_result_repository import (
    ModuleResultRepository,
)


class CardiovascularHTMLVisualizer(HTMLVisualizer):
    TITLE = "Cardiovascular Risk"
    template_name = "cardiovascular.html"
    t_prefix = "PDFReport.General.CardiovascularRisk"

    def get_context(self) -> dict:
        config = self.module.find_config(self.deployment.moduleConfigs, None)
        if not config:
            return {}

        with self.module.configure(config):
            primitives: list[CVDRiskScoreDTO] = self._fetch_primitives()  # noqa
            if not len(primitives):
                return {}

        data, factors = self._build_data(primitives[0])

        module_name = replace_values(config.moduleName, self.localizations) if config.moduleName else ""
        title = module_name or self._t(self.TITLE, "PDFReport.General")
        return {
            "title": title,
            "description": self._t("description"),
            "cardiovascular_data": data,
            "cardiovascular_factors": factors,
        }

    def _build_data(self, primitive: CVDRiskScoreDTO):
        risk_factors_list = [
            {
                "name": "Age",
                "imgSrc": "https://static.cdn.huma.com/pdf-template-assest/heart.png",
                "title": self._t("Age"),
            },
            {
                "name": "Medical History",
                "imgSrc": "https://static.cdn.huma.com/pdf-template-assest/medication.png",
                "title": self._t("Medical history"),
            },
            {
                "name": "Sex",
                "imgSrc": "https://static.cdn.huma.com/pdf-template-assest/gender.png",
                "title": self._t("Biological sex"),
            },
            {
                "name": "Health Rating",
                "imgSrc": "https://static.cdn.huma.com/pdf-template-assest/track.png",
                "title": self._t("Health rating"),
            },
            {
                "name": "Body Measurements",
                "imgSrc": "https://static.cdn.huma.com/pdf-template-assest/measure.png",
                "title": self._t("Body measurements"),
            },
            {
                "name": "Physical Activity",
                "imgSrc": "https://static.cdn.huma.com/pdf-template-assest/running.png",
                "title": self._t("Physical activity"),
            },
            {
                "name": "Heart Rate",
                "imgSrc": "https://static.cdn.huma.com/pdf-template-assest/heart rate.png",
                "title": self._t("Heart rate"),
            },
        ]
        risk_factors_dict = {}
        for risk_factor in primitive.riskFactors:
            risk_factors_dict[risk_factor.name.value] = {
                "status": self._t(risk_factor.status),
                "statusClass": "cardiovascular-risk",
                "type": risk_factor.status,
            }
        factors = []
        for factor in risk_factors_list:
            factors.append(
                {
                    **risk_factors_dict.get(factor["name"]),
                    "imgSrc": factor["imgSrc"],
                    "title": factor["title"],
                }
            )

        data = []
        for ind, trajectory in enumerate(primitive.riskTrajectory):
            data.append({"date": ind, "value": trajectory.riskPercentage})

        if len(data):
            data[0]["value"] = 0

        return data, factors

    @autoparams("repo")
    def _fetch_primitives(self, repo: ModuleResultRepository) -> PrimitiveDTOs:
        primitives = repo.retrieve_primitives(
            user_id=self.user.id,
            module_id=self.module.moduleId,
            primitive_name=CVDRiskScoreDTO.get_primitive_name(),
            skip=0,
            limit=1,
            direction=SortField.Direction.DESC,
            module_config_id=self.module.config.id,
        )
        self._apply_timezone_to_primitives(primitives)
        return primitives
