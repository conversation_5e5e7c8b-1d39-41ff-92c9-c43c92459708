<!-- Generated on https://beefree.io/ -->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
  <head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <meta content="width=device-width" name="viewport" />
    <meta name="color-scheme" content="light dark" />
    <meta name="supported-color-schemes" content="light dark" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <title></title>

    <style type="text/css">
      @font-face {
        font-family: "Noto Serif TC";
        src: url("https://static.cdn.huma.com/notoseriftc-light.otf");
      }

      @font-face {
        font-family: "Noto Sans-Regular";
        src: url("https://static.cdn.huma.com/notosans-regular.ttf");
      }

      @font-face {
        font-family: "Noto Sans-Medium";
        src: url("https://static.cdn.huma.com/notosans-medium.ttf");
      }

      :root {
        color-scheme: light dark;
        supported-color-schemes: light dark;
      }

      body {
        margin: 0;
        padding: 0;
      }

      table,
      td,
      tr {
        vertical-align: top;
        border-collapse: collapse;
      }

      * {
        line-height: inherit;
      }

      a[x-apple-data-detectors="true"] {
        color: inherit !important;
        text-decoration: none !important;
      }

      ul {
        padding-top: 24px;
        padding-bottom: 24px;
        padding-left: 24px;
        line-height: 24px;
      }

      .card-radius {
        overflow: hidden;
        border-radius: 32px;
      }
    </style>
    <style id="media-query" type="text/css">
      @media (prefers-color-scheme: dark) {
        table,
        tr,
        td,
        .dark-theme-text {
          background-color: #2f3033 !important;
          color: #ffffff !important;
        }

        .dark-button {
          background-color: #ebebeb !important;
          color: #2f3033 !important;
        }

        .dark-mode-container,
        .divider table {
          border-color: #6a6d72 !important;
        }
      }

      @media (max-width: 705px) {
        .block-grid,
        .col {
          min-width: 320px !important;
          max-width: 100% !important;
          display: block !important;
        }

        .block-grid {
          width: 100% !important;
        }

        .col {
          width: 100% !important;
        }

        .col > div {
          margin: 0 auto;
        }

        img.fullwidth,
        img.fullwidthOnMobile {
          max-width: 100% !important;
        }

        .no-stack .col {
          min-width: 0 !important;
          display: table-cell !important;
        }

        .no-stack.two-up .col {
          width: 50% !important;
        }

        .no-stack .col.num2 {
          width: 16.6% !important;
        }

        .no-stack .col.num3 {
          width: 25% !important;
        }

        .no-stack .col.num4 {
          width: 33% !important;
        }

        .no-stack .col.num5 {
          width: 41.6% !important;
        }

        .no-stack .col.num6 {
          width: 50% !important;
        }

        .no-stack .col.num7 {
          width: 58.3% !important;
        }

        .no-stack .col.num8 {
          width: 66.6% !important;
        }

        .no-stack .col.num9 {
          width: 75% !important;
        }

        .no-stack .col.num10 {
          width: 83.3% !important;
        }

        .video-block {
          max-width: none !important;
        }

        .mobile_hide {
          min-height: 0px;
          max-height: 0px;
          max-width: 0px;
          display: none;
          overflow: hidden;
          font-size: 0px;
        }

        .desktop_hide {
          display: block !important;
          max-height: none !important;
        }

        .card-radius {
          border-radius: 16px;
        }
      }
    </style>
    <style id="icon-media-query" type="text/css">
      @media (max-width: 705px) {
        .icons-inner {
          text-align: center;
        }

        .icons-inner td {
          margin: 0 auto;
        }
      }
    </style>
  </head>
  <body dir="%textDirection">
    <table
      width="100%"
      cellpadding="0"
      cellspacing="0"
      border="0"
      style="background-color: #f8f8f8"
    >
      <tr>
        <td align="center" style="padding: 15px">
          <table
            cellpadding="0"
            cellspacing="0"
            border="0"
            style="
              max-width: 685px;
              width: 100%;
              background-color: #ffffff;
              border-radius: 32px;
            "
          >
            <tr>
              <td style="padding: 32px">
                <!-- Logo -->
                <img
                  class="icon"
                  height="38.57"
                  src="https://static.cdn.huma.com/fill_8_dark.png"
                  style="border: 0; margin-bottom: 24px"
                />

                <!-- Content -->
                <div
                  style="
                    color: #2f3033;
                    font-family: 'Noto Sans-Regular', sans-serif;
                    line-height: 1.2;
                    text-align: left;
                    margin-bottom: 24px;
                  "
                >
                  <div
                    class="dark-theme-text"
                    style="
                      line-height: 1.2;
                      font-size: 16px;
                      font-family: 'Noto Sans-Regular', sans-serif;
                      color: #2f3033;
                      mso-line-height-alt: 14px;
                    "
                  >
                    <p
                      style="
                        font-size: 16px;
                        line-height: 24px;
                        font-family: 'Noto Sans-Regular', sans-serif;
                        word-break: break-word;
                        mso-line-height-alt: 19px;
                        margin: 0;
                      "
                    >
                      <span style="font-size: 16px">%body</span>
                    </p>
                  </div>
                </div>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>
