from pathlib import Path

from boltons.iterutils import first

from sdk import convertibleclass, meta
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.common.utils.validators import validate_entity_name
from sdk.deployment.component import DeploymentComponent
from sdk.organization.component import OrganizationComponent
from sdk.organization.dtos import OrganizationDTO
from sdk.organization.models import Organization
from sdk.organization.tests.IntegrationTests._utils import ACCOUNT_MANAGER, VALID_ORGANIZATION_ID, simple_organization
from sdk.phoenix.config.base import BasePhoenixConfig, BasePhoenixConfigMeta, UI, ui_field
from sdk.plugin_configuration.component import PluginConfigurationComponent
from sdk.plugin_configuration.models import PluginConfigContainer
from sdk.tests.test_case import SdkTestCase
from .mixin import PluginConfigTestMixing


class PluginConfigCRUDTestCase(SdkTestCase, PluginConfigTestMixing):
    components = [
        AuthComponent(),
        AuthorizationComponent(),
        DeploymentComponent(),
        OrganizationComponent(),
        PluginConfigurationComponent(),
    ]

    fixtures = [Path(__file__).parent.joinpath("fixtures/plugin_config_dump.json")]

    @classmethod
    def setUpClass(cls) -> None:
        super().setUpClass()
        BasePhoenixConfigMeta.config_classes["mySDK"] = MySDKConfig

    @classmethod
    def tearDownClass(cls):
        super().tearDownClass()
        BasePhoenixConfigMeta.config_classes.pop("mySDK")

    def test_success_create_plugin_config_container_with_organization(self):
        response = self._create_organization(simple_organization(), ACCOUNT_MANAGER)
        self.assertIsNotNone(response[OrganizationDTO.ID])
        organization = Organization.objects.get(mongoId=response[OrganizationDTO.ID])
        self.assertIsNotNone(organization.pluginConfigContainerId)
        plugin_config_container = PluginConfigContainer.objects.filter(
            mongoId=organization.pluginConfigContainerId
        ).first()
        self.assertEqual(plugin_config_container.configOwnerId, response[OrganizationDTO.ID])
        self.assertEqual(plugin_config_container.pluginConfigs, {})

    def test_success_retrieve_plugin_config(self):
        config_name = "TwilioSmsVerificationConfig"

        expected_output = {
            "title": "Twilio SMS Verification",
            "fields": [
                {
                    "title": "Enable",
                    "name": "enable",
                    "type": "TOGGLE",
                    "value": True,
                    "isRequired": False,
                },
                {
                    "title": "Enable Authentication",
                    "name": "enableAuth",
                    "type": "TOGGLE",
                    "value": True,
                    "isRequired": False,
                },
                {
                    "title": "Enable Authorization",
                    "name": "enableAuthz",
                    "type": "TOGGLE",
                    "value": True,
                    "isRequired": False,
                },
                {
                    "title": "Template Key",
                    "name": "templateKey",
                    "type": "STRING",
                    "value": "string",
                    "isRequired": False,
                },
                {
                    "title": "Service Name",
                    "name": "serviceName",
                    "type": "STRING",
                    "value": "string",
                    "isRequired": False,
                },
                {
                    "title": "Use Twilio Verify",
                    "name": "useTwilioVerify",
                    "type": "TOGGLE",
                    "value": False,
                    "isRequired": False,
                },
                {
                    "title": "Twilio Verify Service SID",
                    "name": "twilioVerifyServiceSid",
                    "type": "STRING",
                    "value": "string",
                    "isRequired": False,
                },
                {
                    "title": "Template Android Key",
                    "name": "templateAndroidKey",
                    "type": "STRING",
                    "value": "string",
                    "isRequired": False,
                },
            ],
        }

        response = self._retrieve_org_plugin_config(config_name, VALID_ORGANIZATION_ID)
        self.assertEqual(
            expected_output["title"],
            response["title"],
        )
        self.assertEqual(expected_output["fields"], response["fields"])

    def test_success_retrieve_default_plugin_config(self):
        config_name = "AssetsConfig"

        expected_output = {
            "title": "Assets Config",
            "fields": [
                {
                    "title": "Enable",
                    "name": "enable",
                    "type": "TOGGLE",
                    "value": True,
                    "isRequired": False,
                },
                {
                    "title": "Enable Authentication",
                    "name": "enableAuth",
                    "type": "TOGGLE",
                    "value": True,
                    "isRequired": False,
                },
                {
                    "title": "Enable Authorization",
                    "name": "enableAuthz",
                    "type": "TOGGLE",
                    "value": True,
                    "isRequired": False,
                },
                {
                    "title": "Logo URL",
                    "name": "logoUrl",
                    "type": "STRING",
                    "value": "https://static.cdn.huma.com/fill_8.png",
                    "isRequired": False,
                },
                {
                    "title": "Boy Image URL",
                    "name": "boyImageUrl",
                    "type": "STRING",
                    "value": "https://static.cdn.huma.com/boy.png",
                    "isRequired": False,
                },
            ],
        }
        response = self._retrieve_org_plugin_config(config_name, VALID_ORGANIZATION_ID)
        self.assertEqual(expected_output["title"], response["title"])
        self.assertEqual(expected_output["fields"], response["fields"])

    def test_success_update_existing_plugin_config(self):
        organization_id = "5fde855f12db509a2785da06"
        config_name = "TwilioSmsVerificationConfig"

        update_payload = {
            "configContents": {
                "enable": True,
                "enableAuth": True,
                "enableAuthz": True,
                "templateKey": "new-template-key",
                "serviceName": "example-service",
                "useTwilioVerify": True,
                "twilioVerifyServiceSid": "example-sid",
                "templateAndroidKey": "example-android-key",
            },
        }

        self._update_plugin_config(config_name, organization_id, update_payload)
        response = self._retrieve_org_plugin_config(config_name, organization_id)
        use_twilio_verify_field = first(
            response["fields"],
            key=lambda field: field["name"] == "useTwilioVerify",
        )
        self.assertIsNotNone(use_twilio_verify_field, "useTwilioVerify field is missing in response.")
        self.assertTrue(
            use_twilio_verify_field["value"],
            "useTwilioVerify value was not updated to True.",
        )

    def test_success_update_non_existing_plugin_config(self):
        organization_id = "5fde855f12db509a2785da06"
        config_name = "AssetsConfig"

        update_payload = {
            "configContents": {
                "enable": True,
                "enableAuth": True,
                "enableAuthz": True,
                "logoUrl": "new-template-key",
                "boyImageUrl": "example-service",
            },
        }

        self._update_plugin_config(config_name, organization_id, update_payload)
        response = self._retrieve_org_plugin_config(config_name, organization_id)
        data_values = {field["name"]: field["value"] for field in response["fields"]}
        self.assertEqual(data_values, update_payload["configContents"])

    def test_success_update_dynamic_plugin_config(self):
        """Make sure configs are created in runtime if they are not defined in the code base."""
        organization_id = "5fde855f12db509a2785da06"
        config_name = "mySDK"

        update_payload = {
            "configContents": {
                "enable": True,
                "enableAuth": True,
                "enableAuthz": True,
                "clientId": "ey-pkenet-tmweal",
                "clientSecret": "ipexvee-rcmelas",
            },
        }

        self._update_plugin_config(config_name, organization_id, update_payload)
        response = self._retrieve_org_plugin_config(config_name, organization_id)
        data_values = {field["name"]: field["value"] for field in response["fields"]}

        self.assertEqual("MySDK Integration", response["title"])
        self.assertEqual(update_payload["configContents"], data_values)

        response = self._retrieve_organization(organization_id)
        self.assertIn("plugins", response)
        self.assertIn("mySDK", response["plugins"])
        my_sdk_plugin = response["plugins"]["mySDK"]
        self.assertEqual(update_payload["configContents"], my_sdk_plugin)

    def test_success_plugin_config_created_on_update(self):
        """Make sure plugin config container is created if it does not exist during the update configs operation."""
        organization_id = "5fde855f12db509a2785da06"
        config_name = "mySDK"

        # Remove the plugin config container from the database
        PluginConfigContainer.objects.filter(configOwnerId=organization_id).delete()

        update_payload = {
            "configContents": {
                "enable": True,
                "enableAuth": True,
                "enableAuthz": True,
                "clientId": "ey-pkenet-tmweal",
                "clientSecret": "ipexvee-rcmelas",
            },
        }

        # Update the configs for mySDK plugin
        self._update_plugin_config(config_name, organization_id, update_payload)

        # Check configs are present in the get organization response
        response = self._retrieve_organization(organization_id)
        self.assertIn("plugins", response)
        self.assertIn("mySDK", response["plugins"])
        my_sdk_plugin = response["plugins"]["mySDK"]
        self.assertEqual(update_payload["configContents"], my_sdk_plugin)


@convertibleclass
class MySDKConfig(BasePhoenixConfig):
    CONFIG_NAME = "mySDK"
    UI_TITLE = "MySDK Integration"

    clientId: str = ui_field(
        default=None,
        ui_name="Client ID",
        ui_type=UI.STRING,
        metadata=meta(validate_entity_name),
    )
    clientSecret: str = ui_field(
        default=None,
        ui_name="Client secret",
        ui_type=UI.STRING,
        metadata=meta(validate_entity_name),
    )
