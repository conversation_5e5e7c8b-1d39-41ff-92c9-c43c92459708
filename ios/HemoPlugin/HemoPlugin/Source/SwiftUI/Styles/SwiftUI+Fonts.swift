//
//  SwiftUI+Fonts.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI
import HumaFoundation

extension SwiftUI.Font {
    private static let common = CommonTypeStyle()

    /// VictorSerifSmooth-Regular 30
    static var header: Font { common.fontUI(styled: .header, sized: 30) }

    /// VictorSerifSmooth-Regular 30
    static var viewTitle: Font { common.fontUI(styled: .header, sized: 20) }

    // MARK: - Regular

    /// NotoSans with `sized` parameter
    static func regular(sized: CGFloat) -> Font { common.fontUI(styled: .regular, sized: sized) }

    /// NotoSans 16
    static var `default`: Font { common.fontUI(styled: .regular, sized: .default) }

    /// NotoSans 20
    static var large: Font { common.fontUI(styled: .regular, sized: .large) }

    /// NotoSans 14
    static var small: Font { common.fontUI(styled: .regular, sized: .small) }

    /// NotoSans 12
    static var xSmall: Font { common.fontUI(styled: .regular, sized: .xSmall) }

    /// NotoSans 10
    static var xxSmall: Font { common.fontUI(styled: .regular, sized: .xxSmall) }

    /// NotoSans-Medium 16
    static var medium: Font { common.fontUI(styled: .medium, sized: .default) }

    /// NotoSans-Medium 12
    static var xSmallMedium: Font { common.fontUI(styled: .medium, sized: .xSmall) }

    /// NotoSans-Semibold 16
    static var semibold: Font { common.fontUI(styled: .semibold, sized: .default) }

    /// NotoSans-Bold 24
    static var xLargeBold: Font { common.fontUI(styled: .bold, sized: .xLarge) }

    /// NotoSans-Bold 16
    static var bold: Font { common.fontUI(styled: .bold, sized: .default) }

    /// NotoSans-Bold 16
    static var xSmallBold: Font { common.fontUI(styled: .bold, sized: .xSmall) }

    /// NotoSans-Semibold 16
    static var xsSmallSemibold: Font { common.fontUI(styled: .semibold, sized: .xxSmall) }

    /// NotoSans-Semibold 14
    static var smallSemibold: Font { common.fontUI(styled: .semibold, sized: .small) }

    static func styledFont(styled style: CommonTypeStyle.FontStyle = .regular, sized size: CGFloat) -> Font {
        common.fontUI(styled: style, sized: size)
    }

}
