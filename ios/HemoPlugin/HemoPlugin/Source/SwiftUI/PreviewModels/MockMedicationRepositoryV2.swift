//
//  MockMedicationRepositoryV2.swift
//  Pods
//
//  Created by <PERSON> on 21/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation
import HumaModules

final class MockMedicationRepositoryV2: AnyMedicationRepositoryV2 {
    var extendedPayload: Bool = false

    /// Observe medications.
    /// - returns: An opaque listener token object that should be retained to keep the observation alive.
    func observeMedications(changeListener: @escaping RepositoryCompletion<[MedicationV2]>) -> Disposable {
        AnyDisposable()
    }
    func updateMedication(_ medication: MedicationV2, completion: @escaping RepositoryCompletion<Void>) {

    }
    func addMedication(_ medication: MedicationV2, completion: @escaping RepositoryCompletion<MedicationV2>) {

    }
    func deleteMedication(_ medication: MedicationV2, completion: @escaping RepositoryCompletion<Void>) {

    }
    func refreshMedications(completion: @escaping RepositoryCompletion<Void>) {

    }
    func acknowledgeUpdates(completion: @escaping RequestCompletion<Void>) {

    }

    /// Retrieve the oldest medication from cache
    func getEarliestReception(completion: @escaping RepositoryCompletion<MedicationV2>) {

    }

    /// Allows getting all medications from cache or api.
    func getMedications(completion: @escaping RepositoryCompletion<[MedicationV2]>) {
        
    }
}
