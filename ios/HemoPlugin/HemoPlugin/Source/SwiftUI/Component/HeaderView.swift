//
//  HeaderView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 10/04/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//
import SwiftUI

struct HeaderView: View {

    enum Style {
        case `default`
        case compact
        case stacked
    }

    @Environment(\.presentationMode) var presentationMode
    let title: String
    var style: Style = .default
    var onBack: (() -> Void)?

    var rightButtonIcon: String? = nil
    var onRightButtonTap: (() -> Void)? = nil

    private var btnBack : some View {
        Button(action: {
            presentationMode.wrappedValue.dismiss()
            onBack?()
        }) {
            Image(HumaAssets.icBackArrow.name, bundle: HumaFoundationBundle.bundle)
                .frame(square: 24)
        }
    }

    private var rightAccessoryButton: some View {
        Button(action: {
            onRightButtonTap?()
        }) {
            if let icon = rightButtonIcon {
                Image(icon, bundle: HumaFoundationBundle.bundle)
                    .frame(square: 24)
            }
        }
    }

    var body: some View {
        switch style {
        case .default:
            defaultBody
                .padding(.top, Dimensions.horizontalPadding)
        case .compact:
            compactBody
                .padding(.top, Dimensions.horizontalPadding)
        case .stacked:
            stackedBody
                .padding(.top, Dimensions.horizontalPadding)
        }

    }
}

private extension HeaderView {
    var defaultBody: some View {
        HStack {
            btnBack

            Text(title)
                .font(.header)
                .foregroundColor(.charcoalGrey)
                .padding(.leading, 8)

            Spacer()
            if rightButtonIcon != nil{
                rightAccessoryButton
            }
        }
        .frame(minHeight: 60)
        .background(.white)
    }

    var stackedBody: some View {
        VStack(alignment: .leading) {
            HStack {
                btnBack
                Spacer()
                if rightButtonIcon != nil{
                    rightAccessoryButton
                }
            }
            Text(title)
                .font(.header)
                .foregroundColor(.charcoalGrey)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .frame(minHeight: 60)
        .background(.white)
    }

    var compactBody: some View {
        HStack {
            btnBack

            HTMLText(text: title)
                .font(.large)
                .foregroundColor(.charcoalGrey)
                .padding(.leading, 8)

            Spacer()
            if rightButtonIcon != nil{
                rightAccessoryButton
            }
        }
        .frame(minHeight: 60)
        .background(.white)
    }
}
