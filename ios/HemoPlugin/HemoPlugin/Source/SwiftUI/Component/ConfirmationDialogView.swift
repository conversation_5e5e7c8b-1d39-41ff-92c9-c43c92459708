//
//  ConfirmationDialogView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 20/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct ConfirmationDialogView: View {
    @ObservedObject var viewModel: ConfirmationDialogViewModel

    var body: some View {
        VStack(spacing: Dimensions.spacingMedium) {
            Spacer(minLength: Dimensions.horizontalPadding)

            Text(viewModel.title)
                .fixedSize(horizontal: false, vertical: true)
                .multilineTextAlignment(.center)
                .font(.large)
                .foregroundColor(.charcoalGrey)
                .padding(.horizontal, Dimensions.horizontalPadding)

            if let message = viewModel.message {
                Text(message)
                    .lineLimit(nil)
                    .font(.default)
                    .foregroundColor(.charcoalGrey)
                    .fixedSize(horizontal: false, vertical: true)
            }
            VStack(spacing: Dimensions.spacing) {
                Button(viewModel.cancelTitle) {
                    viewModel.onCancel.trigger()
                    viewModel.dismiss()
                }
                .pillStyle(.large, fullWidth: true)

                Button(viewModel.confirmTitle) {
                    viewModel.onConfirm.trigger()
                    viewModel.dismiss()
                }
                .pillStyle(.largeFillDestructive, fullWidth: true)
            }
            .padding(.vertical, Dimensions.verticalPadding)
        }
        .padding(.horizontal, Dimensions.horizontalPadding)
        .padding(.vertical, Dimensions.verticalPadding)
    }
}
