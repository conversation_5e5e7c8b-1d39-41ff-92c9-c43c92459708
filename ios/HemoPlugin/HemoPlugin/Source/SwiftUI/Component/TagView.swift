//
//  TagView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 17/03/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct TagViewItem: Hashable, Identifiable {
    var id = UUID() // Unique identifier for each tag
    var title: String
    var isSelected: Bool
}

enum TagStyle {
    case defaultStyle
    case circular
}

struct TagView: View {
    @Binding var tags: [TagViewItem]
    @State private var totalHeight = CGFloat.zero
    var style: TagStyle // Enum to determine tag style

    var body: some View {
        VStack {
            GeometryReader { geometry in
                self.generateContent(in: geometry)
            }
        }
        .frame(height: totalHeight)
    }

    private func generateContent(in g: GeometryProxy) -> some View {
        var width = CGFloat.zero
        var height = CGFloat.zero
        return ZStack(alignment: .topLeading) {
            ForEach(tags.indices, id: \.self) { index in
                getTagView(for: tags[index])
                    .padding([.horizontal, .vertical], 4)
                    .alignmentGuide(.leading, computeValue: { d in
                        if (abs(width - d.width) > g.size.width) {
                            width = 0
                            height -= d.height
                        }
                        let result = width
                        if index == self.tags.count - 1 {
                            width = 0 // last item
                        } else {
                            width -= d.width
                        }
                        return result
                    })
                    .alignmentGuide(.top, computeValue: {d in
                        let result = height
                        if index == self.tags.count - 1 {
                            height = 0 // last item
                        }
                        return result
                    })
                    .onTapGesture {
                        tags[index].isSelected.toggle()
                    }
            }
        }
        .background(viewHeightReader($totalHeight))
    }

    private func defaultTag(for tag: TagViewItem) -> some View {
        Text(tag.title)
            .foregroundColor(tag.isSelected ? Color.white : Color.charcoalGrey)
            .font(.xSmallBold)
            .padding(.vertical, 8)
            .padding(.horizontal, 16)
            .lineLimit(1)
            .fixedSize(horizontal: true, vertical: false)
            .background(tag.isSelected ? Color.charcoalGrey : Color.veryLightGrey)
            .cornerRadius(Dimensions.tagCornerRadius)
    }

    private func circularTag(for tag: TagViewItem) -> some View {
        let initials = tag.title.split(separator: " ").compactMap { $0.first }.prefix(1).map { String($0) }.joined()
        return Text(initials)
            .foregroundColor(Color.charcoalGrey)
            .font(.xSmallBold)
            .frame(width: 32, height: 32)
            .background(tag.isSelected ? Color.veryLightGrey : Color.white)
            .overlay(
                Circle()
                    .stroke(tag.isSelected ? Color.clear : Color.veryLightGrey, lineWidth: 1)
            )
            .clipShape(Circle())
    }

    private func viewHeightReader(_ binding: Binding<CGFloat>) -> some View {
        return GeometryReader { geometry -> Color in
            let rect = geometry.frame(in: .local)
            DispatchQueue.main.async {
                binding.wrappedValue = rect.size.height
            }
            return .clear
        }
    }

    @ViewBuilder
    private func getTagView(for tag: TagViewItem) -> some View {
        switch style {
        case .defaultStyle:
            defaultTag(for: tag)
        case .circular:
            circularTag(for: tag)
        default:
            EmptyView()
        }
    }
}

struct TagView_Preview: View {

    @State var tags: [TagViewItem] = [
        TagViewItem(title: "Tag 1", isSelected: false),
        TagViewItem(title: "Tag 2", isSelected: true),
        TagViewItem(title: "Tag 3", isSelected: false),
        TagViewItem(title: "Mon", isSelected: false),
        TagViewItem(title: "Tue", isSelected: true),
        TagViewItem(title: "Wed", isSelected: false),
    ]

    var body: some View {
        VStack {
            TagView(tags: $tags, style: .defaultStyle) // Default tags
            TagView(tags: $tags, style: .circular)    // Circular tags
        }
    }
}

#Preview {
    TagView_Preview()
}
