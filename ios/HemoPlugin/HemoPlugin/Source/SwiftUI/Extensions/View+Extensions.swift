//
//  View+Extensions.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

// MARK: - Size & Aligment

extension View {
    /// Clips self to shape `RoundedRectangle` or `Circle` according to given enum case.
    @ViewBuilder
    func cornerRadius(_ radius: CornerRadiusStyle) -> some View {
        switch radius {
        case .value(let value):
            clipShape(RoundedRectangle(cornerRadius: value, style: .continuous))
        case .circle:
            clipShape(Circle())
        case .capsule:
            clipShape(Capsule())
        }
    }

    /// Clips self to `RoundedCorner` with given radius with indipendent value for each corner
    func cornerRadius(_ radius: CornerRadiusIndependent) -> some View {
        cornerRadius(.topLeft, radius.topLeft)
            .cornerRadius(.topRight, radius.topRight)
            .cornerRadius(.bottomLeft, radius.bottomLeft)
            .cornerRadius(.bottomRight, radius.bottomRight)
    }

    /// Clips self to shape `RoundedRectangle` with given radius.
    func cornerRadius(_ radius: CGFloat) -> some View {
        clipShape(RoundedRectangle(cornerRadius: radius, style: .continuous))
    }

    /// Clips self to shape `RoundedCorner` with given radius.
    func cornerRadius(_ corners: UIRectCorner, _ radius: CGFloat) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners) )
    }

    func clipAndStroke(cornerRadius: CGFloat, color: Color) -> some View {
        clipShape(RoundedRectangle(cornerRadius: cornerRadius))
        .overlay {
            RoundedRectangle(cornerRadius: cornerRadius, style: .continuous)
                .stroke(color)
        }
    }

    /// Helper to set a frame from `CGSize`
    func frame(size: CGSize, alignment: Alignment = .center) -> some View {
        frame(width: size.width, height: size.height, alignment: alignment)
    }

    /// Helper to set a square frame from `CGFloat`
    func frame(square: CGFloat) -> some View {
        frame(width: square, height: square)
    }

    /// Does not return view if isHidden is `true`
    @ViewBuilder
    func hidden(_ isHidden: Bool) -> some View {
        if !isHidden { self }
    }

    /// Does not return view if isVisible is `false`
    @ViewBuilder
    func visible(_ isVisible: Bool) -> some View {
        if isVisible { self }
    }

    /// Changes opacity based on bool flag.
    func opacity(_ isVisible: Bool) -> some View {
        opacity(isVisible ? 1 : 0)
    }

    /// Align view to specific edge by using `Spacer()`
    @ViewBuilder
    func aligned(to edge: Edge) -> some View {
        switch edge {
        case .top:
            VStack(spacing: .zero) {
                self
                Spacer(minLength: .zero)
            }
        case .bottom:
            VStack(spacing: .zero)  {
                Spacer(minLength: .zero)
                self
            }
        case .leading:
            HStack(spacing: .zero) {
                self
                Spacer(minLength: .zero)
            }
        case .trailing:
            HStack(spacing: .zero) {
                Spacer(minLength: .zero)
                self
            }
        }
    }

    /// Sets self as label for button
    func button(_ action: @escaping VoidCompletion) -> Button<Self> {
        Button(action: action, label: { self })
    }

    /// Sets content shape as rectangle to expand tap area for rounded views
    func expandTouchArea() -> some View {
        contentShape(Rectangle())
    }
}

extension Text {
    @ViewBuilder
    func numericTextTransition() -> some View {
        if #available(iOS 16.0, *) {
            self.contentTransition(.numericText())
        } else {
            self
        }
    }
}


extension NSAttributedString {
    var converted: AttributedString? { AttributedString(self) }
}

// MARK: - Conditions

extension View {
    /// Applies the given transform if the given condition evaluates to `true`.
    /// - Parameters:
    ///   - condition: The condition to evaluate.
    ///   - transform: The transform to apply to the source `View`.
    /// - Returns: Either the original `View` or the modified `View` if the condition is `true`.
    @ViewBuilder
    func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }

    /// Applies the given transform if the given condition evaluates to `true`.
    /// - Parameters:
    ///   - condition: The condition to evaluate.
    ///   - transform: The transform to apply to the source `View` if condition is `true`
    ///   - else: The transform to apply to the source `View` if condition is `false`
    /// - Returns: The modified `View` with one of provided closures.
    func `if`<Content: View>(
        _ condition: Bool,
        transform: (Self) -> Content,
        else elseTransform: (Self) -> Content
    ) -> some View {
        if condition {
            transform(self)
        } else {
            elseTransform(self)
        }
    }

    /// Applies the given transform if the given optional has a value.
    /// - Parameters:
    ///   - optional: The optional value to evaluate.
    ///   - transform: The transform to apply to the source `View` with the unwrapped value.
    /// - Returns: Either the original `View` or the modified `View` if the optional is not `nil`.
    @ViewBuilder
    func ifLet<Content: View, Wrapped>(_ optional: Wrapped?, transform: (Self, Wrapped) -> Content) -> some View {
        if let value = optional {
            transform(self, value)
        } else {
            self
        }
    }

}

extension View {
  func disableBounces() -> some View {
    modifier(DisableBouncesModifier())
  }
}

struct DisableBouncesModifier: ViewModifier {
  func body(content: Content) -> some View {
    content
      .onAppear {
        UIScrollView.appearance().bounces = false
      }
      .onDisappear {
        UIScrollView.appearance().bounces = true
      }
  }
}

enum CornerRadiusStyle {
    case value(CGFloat)
    case circle
    case capsule
}

struct CornerRadiusIndependent {
    let topLeft: CGFloat
    let topRight: CGFloat
    let bottomLeft: CGFloat
    let bottomRight: CGFloat

    func offset(by value: CGFloat) -> Self {
        .init(
            topLeft: topLeft + value,
            topRight: topRight + value,
            bottomLeft: bottomLeft + value,
            bottomRight: bottomRight + value
        )
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

extension UIApplication {
    func hideKeyboard() {
        sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}
