//
//  HumaAnalytics.swift
//  Pods
//
//  Created by <PERSON> on 24/06/2025.
//
import HumaFoundation

final class HemoPluginEvent: HumaAnalytics.KeyedEvent<HemoPluginEvent.Keys> {

   enum Keys: String, CustomStringConvertible {
        case moduleID = "module_id"
        case moduleName = "module_name"
    }

    enum Event: String, CaseIterable {
        // Hemophilia Journal
        case hemoProfileCompleted = "Hemo Profile Completed"
        case viewHistory = "View History"
        case addNewBleedCompleted = "Add New Bleed Completed"
        case viewLogDetails = "View Log Details"
        case viewBodyMapDetails = "View Body Map Details"
        case viewPhotosDetails = "View Photos Details"
        case editHemoProfileCompleted = "Edit Hemo Profile Completed"
        case scanHemoProfileMedication = "Scan Hemo Profile Medication"
        case searchHemoProfileMedication = "Search Hemo Profile Medication"

        // Medication Module
        case moduleOpened = "Module Opened"
        case editMedication = "Edit Medication"
        case addMedication = "Add Medication"
        case deleteMedication = "Delete Medication"
        case scanMedication = "Scan Medication"
        case searchMedication = "Search Medication"

    }

    // MARK: - Life Cycle

    init(
        name: Event,
        moduleID: String,
        moduleName: String
    ) {
        super.init(
            name: name.rawValue,
            properties: [
                .moduleID: moduleID,
                .moduleName: moduleName
            ]
        )
    }
}
