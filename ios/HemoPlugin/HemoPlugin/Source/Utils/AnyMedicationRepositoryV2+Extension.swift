//
//  AnyMedicationRepositoryV2+Extension.swift
//  HemoPlugin
//
//  Created by <PERSON> on 17/03/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaModules

extension AnyMedicationRepositoryV2 {
    func getMedicationAsync() async throws -> [MedicationV2] {
        try await withCheckedThrowingContinuation { continuation in
            self.getMedications() { result in
                switch result {
                case .success(let results):
                    let newValue = results.filter(\.enabled)
                    continuation.resume(returning: newValue)
                case .failure(let error):
                    continuation.resume(throwing: error)
                }
            }
        }
    }
}
