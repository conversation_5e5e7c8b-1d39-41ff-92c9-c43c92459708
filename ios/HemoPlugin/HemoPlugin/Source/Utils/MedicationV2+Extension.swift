//
//  MedicationV2+Extension.swift
//  HemoPlugin
//
//  Created by <PERSON> on 20/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaModules

extension MedicationModuleV2.ConfigBody {
    func getMedicationGroup(for tag: MedicationTag) -> MedicationModuleV2.MedicationGroup? {
        self.groups?.first(where: { group in
            group.tags.contains(tag.rawValue)
        })
    }

    func getMedicationGroup(by id: String) -> MedicationModuleV2.MedicationGroup? {
        self.groups?.first(where: { group in
            group.id == id
        })
    }
}
