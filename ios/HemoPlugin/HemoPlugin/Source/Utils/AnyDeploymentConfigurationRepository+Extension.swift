//
//  DeploymentConfiguration+Extension.swift
//  HemoPlugin
//
//  Created by <PERSON> on 17/03/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation
import HumaModules

extension AnyDeploymentConfigurationRepository {
    func getMedicationV2Config() -> MedicationModuleV2.ConfigBody? {
        guard let deploymentConfiguration = configuration,
              let medicationV2Config = deploymentConfiguration.moduleConfig(
                for: HumaModuleID.medicationsv2
              )?.config.decodeBody(MedicationModuleV2.ConfigBody.self) else { return nil }
        return medicationV2Config
    }
}
