//
//  AnyFileRepository+Extenstion.swift
//  HemoPlugin
//
//  Created by <PERSON> on 25/06/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.

import HumaFoundation

extension AnyFileRepository {
    func uploadAsync(file: Data) async throws -> String {
        try await withCheckedThrowingContinuation { continuation in
            uploadFile(with: file, filename: UUID().uuidString + ".jpeg") { result in
                switch result {
                case .success(let fileID):
                    continuation.resume(returning: fileID)
                case .failure(let error):
                    continuation.resume(throwing: error)
                }
            }
        }
    }
}
