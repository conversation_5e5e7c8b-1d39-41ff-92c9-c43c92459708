//
//  MedicationQuestionFactory.swift
//  Pods
//
//  Created by <PERSON> on 24/06/2025.
//
import HumaFoundation
import HumaModules

final class MedicationQuestionFactory {

    private var medicationV2Config: MedicationModuleV2.ConfigBody?
    private var source: AnyQuestionSource

    init(medicationV2Config: MedicationModuleV2.ConfigBody?, source: AnyQuestionSource) {
        self.medicationV2Config = medicationV2Config
        self.source = source
    }

    func makeMedicationQuestions(editing medication: MedicationV2? = nil) -> [BaseQuestion] {
        var questions: [BaseQuestion] = []
        let isEditing = medication != nil
        let dosage: MedicationDosageV2? = source.getAnswer(for: MedicationQuestionnaireSource.QuestionID.dosage.rawValue)
        let unit = dosage?.customUnit ?? dosage?.unit.rawValue

        if !isEditing {
            if let medicationV2Config = medicationV2Config, let groups = medicationV2Config.groups {
                questions.append(makeIntroQuest<PERSON>(nextQuestionID: MedicationQuestionnaireSource.QuestionID.medicationType.rawValue))
                questions.append(BaseQuestion(
                    id: MedicationQuestionnaireSource.QuestionID.medicationType.rawValue,
                    title: HumaStrings.pluginHemophiliaPleaseSelectTypeOfYourMedication,
                    type: .singleChoice,
                    validationRules: [.required()],
                    nextQuestionID: MedicationQuestionnaireSource.QuestionID.medicationName.rawValue
                ))
            } else {
                questions.append(makeIntroQuestion(nextQuestionID: MedicationQuestionnaireSource.QuestionID.medicationName.rawValue))
            }
            questions.append(BaseQuestion(
                id: MedicationQuestionnaireSource.QuestionID.medicationName.rawValue,
                title: HumaStrings.medicationv2InputNameTitle,
                type: .autocompleteSearch(.init(placeholder: HumaStrings.pluginHemophiliaStartTypingToSearch, allowScanning: true)),
                validationRules: [.required()],
                nextQuestionID: MedicationQuestionnaireSource.QuestionID.dosage.rawValue
            ))
        }
        // Dosage question
        questions.append(BaseQuestion(
            id: MedicationQuestionnaireSource.QuestionID.dosage.rawValue,
            title: HumaStrings.medicationv2InputDosageTitle,
            type: .valueUnit(.init(placeholder: Strings.medicationv2InputDosagePlaceholder)),
            validationRules: [.required(), .numeric()],
            nextQuestionID: MedicationQuestionnaireSource.QuestionID.medicationFrequency.rawValue,
            initialValue: medication?.dosage
        ))
        // Frequency question
        questions.append(BaseQuestion(
            id: MedicationQuestionnaireSource.QuestionID.medicationFrequency.rawValue,
            title: HumaStrings.medicationInputFrequencyTitle,
            type: .singleChoice,
            validationRules: [.required()],
            branchingLogic: [
                MedicationFrequency.everyDay.rawValue: MedicationQuestionnaireSource.QuestionID.timeOfDay.rawValue,
                MedicationFrequency.specificDays.rawValue: MedicationQuestionnaireSource.QuestionID.specificDays.rawValue,
                MedicationFrequency.daysInterval.rawValue: MedicationQuestionnaireSource.QuestionID.dayInterval.rawValue,
                MedicationFrequency.asNeeded.rawValue: MedicationQuestionnaireSource.QuestionID.maxDosage.rawValue
            ],
            initialValue: medication?.schedule?.identifier
        ))
        // Time of day question
        questions.append(BaseQuestion(
            id: MedicationQuestionnaireSource.QuestionID.timeOfDay.rawValue,
            title: HumaStrings.medicationv2InputTimeTitle,
            type: .time,
            validationRules: [.required()],
            nextQuestionID: MedicationQuestionnaireSource.QuestionID.setReminder.rawValue,
            initialValue: medication?.schedule?.timeRanges?.first?.startTime
        ))
        // Set reminder question
        questions.append(BaseQuestion(
            id: MedicationQuestionnaireSource.QuestionID.setReminder.rawValue,
            title: HumaStrings.medicationv2EnableReminderTitle,
            type: .booleanChoice,
            validationRules: [.required()],
            initialValue: medication?.isNotificationEnabled?.yesNoValue
        ))
        // Specific days question
        questions.append(BaseQuestion(
            id: MedicationQuestionnaireSource.QuestionID.specificDays.rawValue,
            title: HumaStrings.medicationv2InputDaysTitle,
            subtitle: isEditing ? HumaStrings.pluginHemophiliaPleaseSelectAllThatApply : nil,
            type: .multipleChoice(.init(type: .circle)),
            validationRules: [.required()],
            nextQuestionID: MedicationQuestionnaireSource.QuestionID.timeOfDay.rawValue,
            initialValue: medication?.schedule?.specificWeekDays?.map(\ .rawValue)
        ))
        // Day interval question
        questions.append(BaseQuestion(
            id: MedicationQuestionnaireSource.QuestionID.dayInterval.rawValue,
            title: HumaStrings.medicationv2InputIntervalTitle,
            type: .picker(.init(placeholder: HumaStrings.medicationv2InputIntervalPlaceholder)),
            validationRules: [.required()],
            nextQuestionID: MedicationQuestionnaireSource.QuestionID.timeOfDay.rawValue,
            initialValue: medication != nil ? getMedciationScheduleIntervals(medication: medication!) : nil
        ))
        // Max dosage question
        questions.append(BaseQuestion(
            id: MedicationQuestionnaireSource.QuestionID.maxDosage.rawValue,
            title: HumaStrings.medicationv2InputDailyDoseTitle,
            type: .numeric(.init(placeholder: HumaStrings.medicationv2InputDailyDosePlaceholder)),
            validationRules: [.required()],
            initialValue: medication?.maxDosage?.string(maximumDecimalPlaces: 0)
        ))
        return questions
    }
}

private extension MedicationQuestionFactory {
    private func getMedciationScheduleIntervals(medication: MedicationV2) -> OptionItem? {
        if case let .interval(duration, _) = medication.schedule {
            let first = duration.index(duration.startIndex, offsetBy: 1)
            let last = duration.index(duration.endIndex, offsetBy: -1)
            guard let days = String(duration[first ..< last]).intValue else { return nil }
            return .init(value: "\(days)_DAYS", title: Strings.moduleMedicationDays(days))
        }
        return nil
    }

    func makeIntroQuestion(nextQuestionID: String) -> BaseQuestion {
           BaseQuestion(
               id: MedicationQuestionnaireSource.QuestionID.intro.rawValue,
               title: HumaStrings.moduleMedicationv2Title,
               subtitle: HumaStrings.medicationv2DescriptionV2,
               type: .intro(.init(introImage: HumaAssets.medicationIntroPage.name)),
               validationRules: [.required()],
               nextQuestionID: nextQuestionID
           )
       }
}
