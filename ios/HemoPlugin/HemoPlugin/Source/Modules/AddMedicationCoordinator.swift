//
//  AddMedicationCoordinator.swift
//  HemoPlugin
//
//  Created by <PERSON> on 15/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation
import SwiftUI

final class AddMedicationCoordinator<QuestionSource: AnyQuestionSource>: BaseCoordinator, AnyModuleInputCoordinator {

    // MARK: - Properties
    
    // Dependencies
    var resolver: Resolver
    var questionnireSource: QuestionSource
    
    // Events
    @Triggered var onDiscard: Event<ModuleInputContext>
    @Triggered var onSubmit: Event<Void>
    @Triggered var onSuccessSubmit: Event<String?>
    @Triggered var onStepCompleted: Event<ModuleInputContext>
    @Triggered var onSuccess: Event<Void>
    
    // MARK: - Initialization
    
    init(
        navigator: AnyNavigator,
        resolver: Resolver,
        questionnireSource: QuestionSource
    ) {
        self.resolver = resolver
        self.questionnireSource = questionnireSource
        super.init(navigator: navigator)
    }
    
    // MARK: - Coordinator Lifecycle
    
    override func start(animated: Bool) -> Completable? {
        guard let controller = makeController() else { return nil }
        push(controller)
        return controller
    }
    
    func finishWithSuccess() {
        $onSuccess.trigger()
    }
    
    // MARK: - Controller Creation
    
    func makeController() -> UIViewController? {
        let view = QuestionnaireView(source: questionnireSource)

        let container = UIHostingController(rootView: view)
        let controller = ViewController()
        controller.navigationBarPreference = .hidden
        controller.addFullscreenChild(container)
        return controller
    }
}
