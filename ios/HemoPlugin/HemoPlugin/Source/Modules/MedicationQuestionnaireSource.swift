//
//  MedicationQuestionnaireSource.swift
//  Pods
//
//  Created by <PERSON> on 15/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Foundation
import UIKit
import Combine
import HumaFoundation
import HumaModules

class MedicationQuestionnaireSource: AnyQuestionSource {
    // Common properties
    let didSubmitQuestionnaire: TriggeredEvent<Void> = .init()
    let showConfirmationDialog: TriggeredEvent<Bool> = .init()
    let exitFlow: TriggeredEvent<Void> = .init()

    var confirmationDialogViewModel: ConfirmationDialogViewModel? = .init(title: HumaStrings.commonAlertDeleteTitle)
    var answers: [String : Answer] = [:]
    var headerConfiguration: HeaderConfiguration { makeHeaderConfiguration()}

    private let medicationV2Repository: AnyMedicationRepositoryV2
    private let deploymentConfigurationRepository: AnyDeploymentConfigurationRepository
    private var cmsMedications: [CMSM<PERSON>ationResponse.MedicationItem] = []
    private let headerTitle: String
    private var medicationV2Config: MedicationModuleV2.ConfigBody? {
        deploymentConfigurationRepository.getMedicationV2Config()
    }
    private let pfizerPluginRepository: AnyPfizerPluginRepository?
    private let medication: MedicationV2?
    private let isEdit: Bool

    init(
        pfizerPluginRepository: AnyPfizerPluginRepository? = nil,
        medication: MedicationV2? = nil,
        medicationV2Repository: AnyMedicationRepositoryV2,
        deploymentConfigurationRepository: AnyDeploymentConfigurationRepository,
        headerTitle: String
    ) {
        self.pfizerPluginRepository = pfizerPluginRepository
        self.medication = medication
        self.medicationV2Repository = medicationV2Repository
        self.deploymentConfigurationRepository = deploymentConfigurationRepository
        self.headerTitle = headerTitle
        self.isEdit = medication != nil
        if !isEdit { getMedications() }
        if isEdit { subscribeToEvents() }
    }

    enum QuestionID: String {
        case intro = "INTRO_ID"
        case medicationType = "MEDICATION_TYPE_ID"
        case medicationName = "MEDICATION_NAME_ID"
        case dosage = "DOSAGE_ID"
        case medicationFrequency = "FREQUENCY_ID"
        case timeOfDay = "TIME_OF_DAY_ID"
        case setReminder = "REMINDER_ID"
        case specificDays = "ON_DEMAND_ID"
        case dayInterval = "DAYS_INTERVAL_ID"
        case maxDosage = "MAX_DOSAGE_ID"
    }

    func loadQuestions() -> [BaseQuestion] {
        let factory = MedicationQuestionFactory(medicationV2Config: medicationV2Config, source: self)
        return factory.makeMedicationQuestions(editing: medication)
    }

    func onSubmitQuestionnaire(answers: [String : Answer]) async throws -> CreateObjectResponse {
        guard let medicationObj = getMedication(for: answers), !validateDosage() else {
            throw RepositoryError.addFailed(reason: nil)
        }
        if isEdit {
            try await withCheckedThrowingContinuation { continuation in
                medicationV2Repository.updateMedication(medicationObj) { result in
                    self.didSubmitQuestionnaire.trigger()
                    continuation.resume(with: result)
                }
            }
        } else {
            try await withCheckedThrowingContinuation { continuation in
                medicationV2Repository.addMedication(medicationObj) { result in
                    self.didSubmitQuestionnaire.trigger()
                    continuation.resume(with: result)
                }
            }
        }
        return PreviewModels.createObjectResponse
    }

    func getAnswer<T>(for questionID: String) -> T? {
        answers.first(where: { $0.key == questionID })?.value.value as? T
    }

    func getOptions(for questionID: String) -> [OptionItem] {
        switch questionID {
        case QuestionID.medicationType.rawValue:
            guard let medicationV2Config = medicationV2Config,
                  let groups = medicationV2Config.groups else {
                return []
            }
            return groups.map(\ .optionItem)
        case QuestionID.medicationFrequency.rawValue:
            return MedicationFrequency.allCases.map(\ .optionItem)
        case QuestionID.specificDays.rawValue:
            return WeekDay.allCases.map(\ .optionItem)
        case QuestionID.dayInterval.rawValue:
            return MedicationDay.all.map(\ .optionItem)
        case QuestionID.dosage.rawValue:
            guard let medicationV2Config = deploymentConfigurationRepository.getMedicationV2Config() else { return [] }
            return MedicationDosageOptionsProvider.options(from: medicationV2Config)
        default: return []
        }
    }

    func searchAutocompleteOptions(for searchTerm: String, questionID: String) -> [CMSMedicationResponse.MedicationItem] {
        guard !isEdit, questionID == QuestionID.medicationName.rawValue else { return [] }
        let search = searchTerm.lowercased()
        let filtered: [CMSMedicationResponse.MedicationItem]
        if let medicationType: OptionItem = getAnswer(for: QuestionID.medicationType.rawValue),
           let medicationV2Config = medicationV2Config,
           let medicationGroup = medicationV2Config.getMedicationGroup(by: medicationType.value),
           let medicationTag = medicationGroup.medicationTag {
            filtered = cmsMedications.filter {
                switch medicationTag {
                case .other:
                    return !($0.tags?.contains(.prophylactic) ?? false)
                    && !($0.tags?.contains(.onDemand) ?? false)
                    && (search.isEmpty || $0.title.lowercased().contains(search))
                case .prophylactic, .onDemand:
                    return ($0.tags?.contains(medicationTag) ?? false)
                    && (search.isEmpty || $0.title.lowercased().contains(search))
                }
            }
        } else {
            filtered = cmsMedications.filter {
                search.isEmpty || $0.title.lowercased().contains(search)
            }
        }
        return filtered.sorted { $0.title < $1.title }
    }
}

// MARK: - Private Methods

private extension MedicationQuestionnaireSource {

    func getMedication(for answers: [String : Answer]) -> MedicationV2? {
        guard let deploymentConfiguration = deploymentConfigurationRepository.configuration,
              let moduleConfig = deploymentConfiguration.moduleConfig(for: HumaModuleID.medicationsv2)?.config,
              let medicationV2Config = medicationV2Config,
              let dosage: MedicationDosageV2 = getAnswer(for: QuestionID.dosage.rawValue),
              let frequency: OptionItem = getAnswer(for: QuestionID.medicationFrequency.rawValue) else {
            return nil
        }
        let reminder: OptionItem? = getAnswer(for: QuestionID.setReminder.rawValue)
        let timeOfDay: [Date]? = getAnswer(for: QuestionID.timeOfDay.rawValue)
        let daysOfWeek: [OptionItem]? = getAnswer(for: QuestionID.specificDays.rawValue)
        let daysIntervalOptionItem: OptionItem? = getAnswer(for: QuestionID.dayInterval.rawValue)
        let daysInterval = daysIntervalOptionItem?.value ?? "1_DAY"
        let daysIntervalValue = daysInterval.components(separatedBy: "_").first ?? "1"
        let maxDosage: String? = getAnswer(for: QuestionID.maxDosage.rawValue)
        let maxDoasgeDouble = maxDosage.flatMap(Double.init)
        let medicationSchedule = buildMedicationSchedule(
            value: frequency.value,
            timeOfDay: timeOfDay ?? [],
            daysOfWeek: daysOfWeek?.compactMap { WeekDay(rawValue: $0.value) } ?? [],
            daysInterval: Int(daysIntervalValue) ?? 1
        )
        if isEdit, let med = medication {
            return MedicationV2(
                id: med.id,
                configId: moduleConfig.inputConfig.moduleConfigID,
                name: med.name,
                dosage: dosage,
                maxDosage: maxDoasgeDouble,
                schedule: medicationSchedule,
                isNotificationEnabled: reminder?.value == BoolQuestion.yes.rawValue,
                customUnit: nil,
                groupID: med.groupID
            )
        } else {
            guard let cmsMedication: CMSMedicationResponse.MedicationItem = getAnswer(for: QuestionID.medicationName.rawValue) else { return nil }
            let medicationType: OptionItem? = getAnswer(for: QuestionID.medicationType.rawValue)
            let medicationGroup = medicationV2Config.getMedicationGroup(by: medicationType?.value ?? "")
            let medicationTag = medicationGroup?.medicationTag
            let groupID = (medicationGroup?.medicationTag == .other || medicationTag.isNil) ? nil : medicationGroup?.id
            return MedicationV2(
                configId: moduleConfig.inputConfig.moduleConfigID,
                name: cmsMedication.title,
                dosage: dosage,
                maxDosage: maxDoasgeDouble,
                schedule: medicationSchedule,
                isNotificationEnabled: reminder?.value == BoolQuestion.yes.rawValue,
                customUnit: nil,
                groupID: groupID
            )
        }
    }

    func getMedications() {
        guard let pfizerPluginRepository = pfizerPluginRepository else { return }
        Task {
            do {
                self.cmsMedications = try await pfizerPluginRepository.getCMSMedication().items
            } catch {
                self.cmsMedications = []
            }
        }
    }

    func buildMedicationSchedule(value: String, timeOfDay: [Date], daysOfWeek: [WeekDay], daysInterval: Int) -> MedicationScheduleV2 {
        var times: [ISODuration] { timeOfDay.map(ISODuration.timeDuration(from:)) }
        switch value {
        case "EVERY_DAY":
            return .everyDay(times: times)
        case "SPECIFIC_DAYS":
            return .specific(days: daysOfWeek, times: times)
        case "DAYS_INTERVAL":
            return .interval(duration: "P\(daysInterval)D", times: times)
        case "AS_NEEDED":
            return .asNeeded
        default:
            return .asNeeded
        }
    }

    func subscribeToEvents() {
        confirmationDialogViewModel?.onCancel.addObserver(self) { observer in
            observer.showConfirmationDialog.trigger(false)
        }
        confirmationDialogViewModel?.onConfirm.addObserver(self) { observer in
            self.medicationV2Repository.deleteMedication(self.medication!) { [weak observer] result in
                guard case .success = result else { return }
                observer?.exitFlow.trigger()
                observer?.showConfirmationDialog.trigger(false)
            }
        }
    }

    func validateDosage() -> Bool {
        guard let dosageObject: MedicationDosageV2 = getAnswer(for: QuestionID.dosage.rawValue),
              let maxDoasgeString: String = getAnswer(for: QuestionID.maxDosage.rawValue),
              let maxDosage = maxDoasgeString.doubleValue else {
            return false
        }
        if maxDosage <= dosageObject.value {
            executeOnMainThread {
                HemoJournalWidget.showSnackBar(message: HumaStrings.medicationv2FailedSubmissionDosage)
            }
        }
        return maxDosage <= dosageObject.value
    }

    func makeHeaderConfiguration() -> HeaderConfiguration {
        if isEdit {
            return HeaderConfiguration(
                title: headerTitle,
                rightButtonIcon: HumaAssets.icTrashBin.name,
                rightButtonAction: { [weak self] in
                    guard let self = self else { return }
                    self.showConfirmationDialog.trigger(true)
                }
            )
        } else {
            return HeaderConfiguration(
                title: headerTitle,
                rightButtonIcon: nil,
                rightButtonAction: nil
            )
        }
    }
}

extension MedicationScheduleV2 {
    var identifier: String {
        switch self {
        case .everyDay:
            return "EVERY_DAY"
        case .specific:
            return "SPECIFIC_DAYS"
        case .interval:
            return "DAYS_INTERVAL"
        case .timeRanges(let asNeeded, let timeRanges):
            return "AS_NEEDED"
        case .asNeeded:
            return "AS_NEEDED"
        @unknown default:
            return ""
        }
    }
}

extension Bool {
    var yesNoValue: String {
        self ? "YES" : "NO"
    }
}

