//
//  PfizerMedicationModule.swift
//  HemoPlugin
//
//  Created by <PERSON> on 15/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation
import HumaModules

final class PfizerMedicationModule: MedicationModuleV2 {

    private var medicationQuestionnireSource: MedicationQuestionnaireSource {
        .init(
            pfizerPluginRepository: PfizerPluginRepository(networking: resolver.resolve()),
            medicationV2Repository: resolver.resolve(),
            deploymentConfigurationRepository: resolver.resolve(),
            headerTitle: HumaStrings.commonActionAddMedication
        )
    }

    override func makeInputCoordinator(for configID: String?) -> (any AnyModuleInputCoordinator)? {
        AddMedicationCoordinator(
            navigator: navigator,
            resolver: resolver,
            questionnireSource: medicationQuestionnireSource
        )
    }

    override func onEditMedicaiton(medication: MedicationV2) {

        var medicationQuestionnireSource: MedicationQuestionnaireSource {
            .init(
                medication: medication,
                medicationV2Repository: resolver.resolve(),
                deploymentConfigurationRepository: resolver.resolve(),
                headerTitle: medication.name
            )
        }

        let coordinator = AddMedicationCoordinator(
            navigator: navigator,
            resolver: resolver,
            questionnireSource: medicationQuestionnireSource
        )
        
        startCoordinator(coordinator)
    }
}
