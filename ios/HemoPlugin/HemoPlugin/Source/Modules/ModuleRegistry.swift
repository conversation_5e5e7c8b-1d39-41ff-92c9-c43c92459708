//
//  ModuleRegistry.swift
//  HumaAirQuality
//
//  Created by <PERSON><PERSON><PERSON> on 19.09.2024.
//  Copyright © 2024 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation
import HumaModuleKit
import HumaModules

private enum Const {
    static let moduleID = HumaModuleID.medicationsv2
}

public final class ModuleRegistry: AnyModuleRegistry {
    public static var entries: [ModuleID : ModuleProvider] {
        [
            Const.moduleID: .module
        ]
    }
}

extension ModuleProvider {
    static var module: Self {
        .dynamicModule { config in
            @FeatureFlag(flagName: "hemophilia-medicationV2", defaultValue: false)
            var enableMedicationModule: Bool
            if enableMedicationModule {
                return PfizerMedicationModule.self
            }
            return MedicationModuleV2.self
        }
    }
}
