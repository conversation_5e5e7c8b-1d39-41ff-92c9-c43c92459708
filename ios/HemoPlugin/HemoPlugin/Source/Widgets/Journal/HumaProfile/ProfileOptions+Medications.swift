//
//  ProfileOptions+Units.swift
//  HumaProfileSDK
//
//  Created by <PERSON><PERSON><PERSON> on 08/11/22.
//  Copyright © 2022 Huma Therapeutics Ltd. All rights reserved.
//

import UIKit
import HumaFoundation
import HumaModuleKit
import HumaProfileKit

extension ProfileOptions {

    /// Profile option for viewing units.
    public class Medication: AnyProfileOption {

        typealias ModuleWithConfig = (module: AnyModule, config: ModuleConfig)

        // MARK: - Public Properties

        /// The text of title. The default value is `Units`.
        public var title: String { Strings.moduleMedicationTitle }
        /// An option image.
        public var icon: UIImage { Asset.medication.image }

        private var moduleID: String
        private(set) var moduleWithDetails: AnyModuleWithDetail?

        private var moduleTypeRepository: AnyModuleTypeRepository { resolver.resolve() }
        private var deploymentConfigurationRepository: AnyDeploymentConfigurationRepository { resolver.resolve() }
        private var deploymentConfiguration: DeploymentConfiguration? {
            deploymentConfigurationRepository.configuration
        }
        private var navigator: AnyNavigator

        // MARK: - Private Properties
        private let resolver: Resolver

        /// Initialization.
        /// - Parameters:
        ///    - manager: Is used for managing units.
        public init(navigator: AnyNavigator, resolver: Resolver, moduleID: String) {
            self.moduleID = moduleID
            self.resolver = resolver
            self.navigator = navigator
        }

        /// Add `UnitsCoordinator` as child.
        public func handle(in coordinator: AnyCoordinator) {
            openDetail(coordinator: coordinator)
        }
    }
}

private extension ProfileOptions.Medication {

    func openDetail(coordinator: AnyCoordinator) {
        guard let moduleConfig = deploymentConfiguration?.moduleConfig(for: moduleID)?.config else {
            return
        }
        moduleTypeRepository.getModuleTypes(
            moduleID: moduleID,
            moduleConfigID: moduleConfig.inputConfig.moduleConfigID,
            forceRefresh: false
        ) { result in
            _ = result.map { [weak self] in
                guard let self = self,
                      let moduleWithConfig = self.instantiateModule(with: $0),
                      let moduleWithDetail = moduleWithConfig.module as? AnyModuleWithDetail else {
                    return
                }
                moduleWithDetail.onOpenFlow.addObserver(self) { observer, moduleCoordinator in
                    coordinator.addChild(moduleCoordinator)
                }
                moduleWithDetail.openDetail(context: ModuleAnalyticsContext.profile)
                self.moduleWithDetails = moduleWithDetail
            }
        }
    }

    func instantiateModule(with typeWithConfig: ModuleTypeWithConfig) -> ModuleWithConfig? {
        do {
            let module = try typeWithConfig.moduleType.init(
                navigator: navigator,
                resolver: resolver,
                config: typeWithConfig.config
            )
            return (module, typeWithConfig.config.config)
        } catch {
            Logger.e("Couldn't initialize \(typeWithConfig.moduleType) module with \(typeWithConfig.config)")
            return nil
        }
    }
}
