//
//  ProfileOptions+Units.swift
//  HumaProfileSDK
//
//  Created by <PERSON><PERSON><PERSON> on 08/11/22.
//  Copyright © 2022 Huma Therapeutics Ltd. All rights reserved.
//

import UIKit
import SwiftUI
import HumaFoundation
import HumaProfileKit

extension ProfileOptions {

    /// Profile option for viewing units.
    public class HemoProfile: AnyProfileOption {

        // MARK: - Public Properties

        /// The text of title. The default value is `Units`.
        public var title: String { HumaStrings.pluginHemophiliaProfileQuestionnaireTitle }
        /// An option image.
        public var icon: UIImage = .init(
            named: "blood_drop",
            in: HemoPluginBundle.bundle,
            with: nil
        ) ?? HumaAssets.icProfile.image

        private var deploymentConfigurationRepository: AnyDeploymentConfigurationRepository { resolver.resolve() }
        private var deploymentConfiguration: DeploymentConfiguration? {
            deploymentConfigurationRepository.configuration
        }
        private var navigator: AnyNavigator

        // MARK: - Private Properties
        private let resolver: Resolver

        /// Initialization.
        /// - Parameters:
        ///    - manager: Is used for managing units.
        public init(navigator: AnyNavigator, resolver: Resolver) {
            self.resolver = resolver
            self.navigator = navigator
        }

        /// Add `UnitsCoordinator` as child.
        public func handle(in coordinator: AnyCoordinator) {
            let child = HemoProfileDetailsCoordinator(navigator: navigator, resolver: resolver)
            coordinator.addChild(child)
        }
    }
}

private class HemoProfileDetailsCoordinator: BaseCoordinator {

    private let resolver: Resolver

    public init(navigator: AnyNavigator, resolver: Resolver) {
        self.resolver = resolver
        super.init(navigator: navigator)
    }

    final override func start(animated: Bool) -> Completable? {
        guard let controller = makeController() else { return nil }
        push(controller)
        return controller
    }
}

private extension HemoProfileDetailsCoordinator {
    func makeController() -> UIViewController? {

        let model = HemoProfileDetailsViewModel(
            pfizerPluginRepository: PfizerPluginRepository(networking: resolver.resolve()),
            deploymentConfigurationRepository: resolver.resolve(),
            medicationRepositoryV2: resolver.resolve()
        )
        let view = HemoProfileDetailsView(viewModel: model)
        let container = UIHostingController(rootView: view)
        let controller = ViewController()
        controller.navigationBarPreference = .hidden
        controller.hidesBottomBarWhenPushed = true
        controller.addFullscreenChild(container)
        return controller
    }
}
