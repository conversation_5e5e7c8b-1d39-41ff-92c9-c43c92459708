//
//  HemoJournalWidgetCoordinator.swift
//  HemoPlugin
//
//  Created by <PERSON> on 17/03/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation
import SwiftUI

final class HemoJournalWidgetCoordinator: BaseCoordinator {

    private let resolver: Resolver
    private let widgetConfig: WidgetConfig
    private var config: HemoJournalWidgetConfig?
    private let pfizerPluginRepository: AnyPfizerPluginRepository
    private var viewModel: HemoJournalCardViewModel?

    public init(
        navigator: AnyNavigator,
        resolver: Resolver,
        widgetConfig: WidgetConfig
    ) {
        self.resolver = resolver
        self.widgetConfig = widgetConfig
        self.pfizerPluginRepository = PfizerPluginRepository(networking: resolver.resolve())
        self.config = try? widgetConfig.decodeBody()
        super.init(navigator: navigator)
    }

    final override func start(animated: Bool) -> Completable? {
        guard let controller = makeController() else { return nil }
        push(controller)
        return controller
    }

    func updateConfiguration(config: HemoJournalWidgetConfig) {
        self.config = config
        viewModel?.updateConfiguration(config: config)
    }
}

private extension HemoJournalWidgetCoordinator {
    func makeController() -> UIViewController? {
        guard let config = self.config else { return nil }

        let model = HemoJournalCardViewModel(
            widgetConfigInfo: widgetConfig.info,
            config: config,
            repository: HemoJournalRepository(networking: resolver.resolve()),
            fileRepository: resolver.resolve(),
            pfizerPluginRepository: pfizerPluginRepository,
            moduleResultSubmitRepository: resolver.resolve(),
            moduleResultRepository: resolver.resolve(),
            medicationRepositoryV2: resolver.resolve(),
            userRepository: resolver.resolve(),
            deploymentConfigurationRepository: resolver.resolve()
        )

        model.onHemoProfile.addObserver(self) { observer, source in
            observer.addChild(observer.makeQuestionnaireCoordinator(source: source))
        }

        model.onAddNewBleed.addObserver(self) { observer, viewModel in
            observer.addChild(observer.makeAddNewBleedCoordinator(viewModel: viewModel))
        }

        model.onAddNewNonJoinBleed.addObserver(self) { observer, source in
            observer.addChild(observer.makeQuestionnaireCoordinator(source: source))
        }

        model.onViewHistory.addObserver(self) { observer, viewModel in
            observer.addChild(observer.makeViewHistroyCoordinator(viewModel: viewModel))
        }

        self.viewModel = model

        let view = HemoJournalCardView(viewModel: model)
        let container = UIHostingController(rootView: view)
        let controller = ViewController()
        controller.navigationBarPreference = .hidden
        controller.addFullscreenChild(container)
        return controller
    }

    func makeQuestionnaireCoordinator<Source: AnyQuestionSource>(source: Source) -> AnyCoordinator {
        let view = QuestionnaireView(source: source)
        return HemoQuestionnaireCoordinator(navigator: navigator, content: view)
    }

    func makeAddNewBleedCoordinator<ViewModel: AnyAddBleedViewModel>(viewModel: ViewModel) -> AnyCoordinator {
        let view = AddBleedView(viewModel: viewModel)
        let coordinator = HemoQuestionnaireCoordinator(navigator: navigator, content: view)

        viewModel.onSubmitQuestionnaire.addObserver(self) { [weak coordinator] observer in
            executeOnMainThread(after: 0.2) {
                observer.pop()
            }
        }

        return coordinator
    }

    func makeViewHistroyCoordinator<ViewModel: AnyDetailsViewModel>(viewModel: ViewModel) -> AnyCoordinator {
        let view = DetailsView(viewModel: viewModel)
        return HemoQuestionnaireCoordinator(navigator: navigator, content: view)
    }
}

extension ViewController {
    func addFullscreenChild(_ child: UIViewController) {
        addChild(child)
        view.addSubview(child.view)
        child.view.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activateFullLayoutConstraint(view: child.view)
        child.didMove(toParent: self)
    }
}
