//
//  HemoProfileQuestionnaireCoordinator.swift
//  Pods
//
//  Created by <PERSON> on 20/06/2025.
//

import HumaFoundation
import SwiftUI

final class HemoQuestionnaireCoordinator<Content: SwiftUI.View>: BaseCoordinator {

    private let content: Content

    public init(
        navigator: AnyNavigator,
        content: Content
    ) {
        self.content = content
        super.init(navigator: navigator)
    }

    final override func start(animated: Bo<PERSON>) -> Completable? {
        guard let controller = makeController() else { return nil }
        push(controller)
        return controller
    }
}

private extension HemoQuestionnaireCoordinator {
    func makeController() -> UIViewController? {
        let container = UIHostingController(rootView: content)
        let controller = ViewController()
        controller.navigationBarPreference = .hidden
        controller.hidesBottomBarWhenPushed = true
        controller.addFullscreenChild(container)
        return controller
    }
}
