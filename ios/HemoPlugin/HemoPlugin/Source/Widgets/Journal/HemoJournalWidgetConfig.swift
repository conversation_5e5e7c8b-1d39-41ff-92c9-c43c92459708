//
//  HemoJournalWidgetConfig.swift
//  HemoPlugin
//
//  Created by <PERSON> on 17/03/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation

struct HemoJournalWidgetConfig: Codable, Hashable, Equatable {
    let header: Header
    let bodyMap: [BleedData]
    let jointData: BleedData
    let nonJointData: BleedData
    let description: String?

    enum CodingKeys: CodingKey {
        case header
        case bodyMap
        case description
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        header = try container.decode(Header.self, forKey: .header)
        bodyMap = try container.decode([BleedData].self, forKey: .bodyMap)
        description = try container.decodeIfPresent(String.self, forKey: .description)

        guard let jointData = bodyMap.first(where: { $0.bleedType == .joints }) else {
            throw DecodingError.dataCorruptedError(
                forKey: .bodyMap,
                in: container,
                debugDescription: "No BleedData with bleedType '.joints' found in bodyMap."
            )
        }
        self.jointData = jointData

        guard let nonJointData = bodyMap.first(where: { $0.bleedType == .nonJoints }) else {
            throw DecodingError.dataCorruptedError(
                forKey: .bodyMap,
                in: container,
                debugDescription: "No BleedData with bleedType '.nonJoints' found in bodyMap."
            )
        }
        self.nonJointData = nonJointData
    }

}

extension HemoJournalWidgetConfig {
    struct Header: Codable, Hashable, Equatable {
        let title: String
        let icon: String?
    }

    struct BleedData: Codable, Equatable, Hashable, Identifiable {
        var id = UUID()
        let bleedType: BleedType
        let title: String
        let description: String
        let locations: [BodyLocation]

        enum CodingKeys: CodingKey {
            case bleedType
            case title
            case description
            case locations
        }
    }

    struct BodyLocation: Codable, Hashable, Equatable, Identifiable {
        var id = UUID()
        let location: BodyLocationType
        let points: [BodyPartData]

        enum CodingKeys: CodingKey {
            case location
            case points
        }
    }

    struct BodyPartData: Codable, Hashable, Equatable, Identifiable {
        var id = UUID()
        let name: String
        let value: BodyPartType

        enum CodingKeys: CodingKey {
            case name
            case value
        }
    }
}

extension HemoJournalWidgetConfig {
    func getBodyPartData(for bodyPart: BodyPartType) -> BodyPartData? {
        bodyMap
            .flatMap { $0.locations }
            .flatMap { $0.points }
            .first { $0.value == bodyPart }
    }
}
