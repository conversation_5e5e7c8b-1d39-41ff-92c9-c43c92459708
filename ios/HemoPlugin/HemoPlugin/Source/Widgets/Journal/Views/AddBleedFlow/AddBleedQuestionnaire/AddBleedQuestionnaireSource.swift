//
//  AddBleedQuestionnaireSource.swift
//  HemoPlugin
//
//  Created by <PERSON> on 01/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Foundation
import UIKit
import Combine
import HumaFoundation
import HumaModules

class AddBleedQuestionnaireSource: AnyQuestionSource {
    let didSubmitQuestionnaire: TriggeredEvent<Void> = .init()
    let showConfirmationDialog: TriggeredEvent<Bool> = .init()
    let exitFlow: TriggeredEvent<Void> = .init()
    let customBodyPart: String?
    let bodyPartInjury: String
    
    var confirmationDialogViewModel: ConfirmationDialogViewModel? = nil
    var answers: [String : Answer] = [:]

    private let pfizerPluginRepository: AnyPfizerPluginRepository
    private var cmsMedications: [CMSMedicationResponse.MedicationItem] = []
    private let moduleResultSubmitRepository: AnyModuleResultSubmitRepository
    private let medicationRepository: AnyMedicationRepositoryV2
    private let userRepository: AnyUserObserveRepository
    private let fileRepository: AnyFileRepository
    private var deploymentConfiguration: AnyConfiguration? { deploymentConfigurationRepository.configuration }
    private let bodyLocation: BodyLocationType
    private let bodyPart: BodyPartType
    private var medicationModuleResult: [MedicationV2] = []

    private let deploymentConfigurationRepository: AnyDeploymentConfigurationRepository

    init(
        bodyLocation: BodyLocationType,
        bodyPart: BodyPartType,
        pfizerPluginRepository: AnyPfizerPluginRepository,
        moduleResultSubmitRepository: AnyModuleResultSubmitRepository,
        medicationRepository: AnyMedicationRepositoryV2,
        userRepository: AnyUserObserveRepository,
        fileRepository: AnyFileRepository,
        deploymentConfigurationRepository: AnyDeploymentConfigurationRepository,
        customBodyPart: String?,
        bodyPartInjury: String
    ) {
        self.bodyLocation = bodyLocation
        self.bodyPart = bodyPart
        self.pfizerPluginRepository = pfizerPluginRepository
        self.moduleResultSubmitRepository = moduleResultSubmitRepository
        self.userRepository = userRepository
        self.fileRepository = fileRepository
        self.customBodyPart = customBodyPart
        self.medicationRepository = medicationRepository
        self.bodyPartInjury = bodyPartInjury
        self.deploymentConfigurationRepository = deploymentConfigurationRepository
        getMedications()
    }

    // Add enum for Question IDs
    enum QuestionID: String {
        case bleedSeverity = "BLEED_SEVERITY"
        case bleedReason = "BLEED_REASON"
        case bleedTreated = "BLEED_TREATED"
        case bleedTreatment = "BLEED_TREATMENT"
        case bleedFactorCount = "BLEED_FACTOR_COUNT"
        case bleedDate = "BLEED_DATE"
        case painScale = "PAIN_SCALE"
        case bleedNotes = "BLEED_NOTES"
        case bleedPhotos = "BLEED_PHOTOS"
    }

    func loadQuestions() -> [BaseQuestion] {
        return [
            BaseQuestion(
                id: QuestionID.bleedSeverity.rawValue,
                title: HumaStrings.pluginHemophiliaSeverityOfTheBleed,
                type: .singleChoice,
                validationRules: [.required()],
                nextQuestionID: QuestionID.bleedReason.rawValue
            ),
            BaseQuestion(
                id: QuestionID.bleedReason.rawValue,
                title: HumaStrings.pluginHemophiliaReasonForTheBleed,
                type: .singleChoice,
                validationRules: [.required()],
                nextQuestionID: QuestionID.bleedTreated.rawValue
            ),
            BaseQuestion(
                id: QuestionID.bleedTreated.rawValue,
                title: HumaStrings.pluginHemophiliaDidYouTreatTheBleed,
                type: .booleanChoice,
                validationRules: [.required()],
                branchingLogic: [
                    "YES": QuestionID.bleedTreatment.rawValue,
                    "NO": QuestionID.bleedDate.rawValue
                ]

            ),
            BaseQuestion(
                id: QuestionID.bleedTreatment.rawValue,
                title: HumaStrings.pluginHemophiliaWhatTreatmentWasUsedToTreatTheBleed,
                type: .autocompleteSearch(.init(placeholder: HumaStrings.pluginHemophiliaStartTypingToSearch, allowScanning: false)),
                validationRules: [.required()],
                nextQuestionID: QuestionID.bleedFactorCount.rawValue
            ),
            BaseQuestion(
                id: QuestionID.bleedFactorCount.rawValue,
                title: HumaStrings.pluginHemophiliaFactorUnitsTitle,
                type: .numeric(.init(placeholder: HumaStrings.pluginHemophiliaNumberOfFactorUnits)),
                validationRules: [
                    .required(message: HumaStrings.pluginHemophiliaNumberOfFactorUnits),
                    .numeric(message: HumaStrings.pluginHemophiliaFactorUnitValidation)
                ],
                nextQuestionID: QuestionID.bleedDate.rawValue
            ),
            BaseQuestion(
                id: QuestionID.bleedDate.rawValue,
                title: HumaStrings.pluginHemophiliaPleaseConfirmDateOfTheBleedPain,
                type: .date(.init(range: Date.distantPast...Date())),
                validationRules: [.required()],
                nextQuestionID: QuestionID.painScale.rawValue
            ),
            BaseQuestion(
                id: QuestionID.painScale.rawValue,
                title: HumaStrings.pluginHemophiliaPainScale,
                subtitle: HumaStrings.pluginHemophiliaPainScaleDescription,
                type: .slider,
                validationRules: [.required()],
                nextQuestionID: QuestionID.bleedNotes.rawValue
            ),
            BaseQuestion(
                id: QuestionID.bleedNotes.rawValue,
                title: HumaStrings.pluginHemophiliaDoYouHaveAnyNotesToAdd,
                subtitle: HumaStrings.pluginHemophiliaAddNotesDescription,
                type: .multilineText(.init(placeholder: HumaStrings.pluginHemophiliaEnterYourNotesHere)),
                isMandatory: false,
                nextQuestionID: QuestionID.bleedPhotos.rawValue
            ),
            BaseQuestion(
                id: QuestionID.bleedPhotos.rawValue,
                title: HumaStrings.pluginHemophiliaAddPhotosTitle,
                subtitle: HumaStrings.pluginHemophiliaAddPhotosDescription,
                type: .photo,
                isMandatory: false
            )
        ]
    }

    func getOptions(for questionID: String) -> [OptionItem] {
        switch questionID {
        case QuestionID.bleedSeverity.rawValue:
            return BleedSeverity.allCases.map(\.optionItem)
        case QuestionID.bleedReason.rawValue:
            return BleedReason.allCases.map(\.optionItem)
        default: return []
        }
    }

    func searchAutocompleteOptions(for searchTerm: String, questionID: String) -> [CMSMedicationResponse.MedicationItem] {
        switch questionID {
        case QuestionID.bleedTreatment.rawValue:
            if searchTerm.isEmpty {
                let meds = cmsMedications
                    .filter { $0.tags?.contains(.onDemand) ?? false }
                    .sorted(by: \.title)
                return meds
            } else {
                return cmsMedications.filter {
                    $0.title.lowercased().contains(searchTerm.lowercased()) && ($0.tags?.contains(.onDemand) ?? false)
                }
                .sorted(by: \.title)
            }
        default:
            return []
        }
    }

    func onSubmitQuestionnaire(answers: [String : Answer]) async throws -> CreateObjectResponse {
        guard let inputConfig = deploymentConfiguration?.moduleConfig(
            for: HumaModuleID.hemophiliaJournal
        )?.config.inputConfig,
              let userID = userRepository.currentUser?.userID,
              let bleedSeverityOption: OptionItem = getAnswer(for: QuestionID.bleedSeverity.rawValue),
              let bleedSeverity = BleedSeverity(rawValue: bleedSeverityOption.value),
              let bleedReasonOption: OptionItem = getAnswer(for: QuestionID.bleedReason.rawValue),
              let bleedReason = BleedReason(rawValue: bleedReasonOption.value),
              let bleedDate: Date = getAnswer(for: QuestionID.bleedDate.rawValue),
              let bleedPainScale: Double = getAnswer(for: QuestionID.painScale.rawValue) else {
            throw RepositoryError.addFailed(reason: nil)
        }
        let bleedTreatment: CMSMedicationResponse.MedicationItem? = getAnswer(for: QuestionID.bleedTreatment.rawValue)
        let bleedNotes: String? = getAnswer(for: QuestionID.bleedNotes.rawValue)
        let bleedPhotos: [Data]? = getAnswer(for: QuestionID.bleedPhotos.rawValue)
        let bleedFactor: String? = getAnswer(for: QuestionID.bleedFactorCount.rawValue)

        // Upload photos and collect file IDs
        var photoIDs: [String] = []
        if let photos = bleedPhotos, !photos.isEmpty {
            for photo in photos {
                do {
                    let fileID = try await fileRepository.uploadAsync(file: photo)
                    photoIDs.append(fileID)
                } catch {
                    print("Failed to upload photo: \(error.localizedDescription)")
                    // Continue with remaining photos even if one fails
                }
            }
        }

        var treatment: HemoJournalPrimitive.Treatment?
        if let bleedTreatment = bleedTreatment {
            treatment = .init(
                id: bleedTreatment.id,
                enabled: true,
                name: bleedTreatment.title,
                codingList: [],
                userId: userID,
                deploymentId: deploymentConfiguration?.deploymentID ?? "",
                dosage: bleedTreatment.dosage,
                unit: bleedTreatment.unit,
                isNotificationEnabled: false,
                schedule: .init(asNeeded: true),
                submitterUserType: "USER",
                tags: bleedTreatment.tags
            )
        }

        let primitive = HemoJournalPrimitive(
            bodyLocation: bodyLocation,
            bodyPartInjury: bodyPart,
            customBodyPart: customBodyPart,
            extraData: .init(
                accidentDate: bleedDate.iso8601ShortDate,
                reason: bleedReason,
                note: bleedNotes,
                photos: photoIDs.isEmpty ? nil : photoIDs,
                scale: bleedPainScale.integer,
                severity: bleedSeverity,
                treatment: treatment,
                factorUnits: bleedFactor?.intValue ?? 0
            )
        )
        let moduleResult = ModuleResult(moduleConfigID: inputConfig.moduleID, value: primitive)
        try await moduleResultSubmitRepository.submitResults([moduleResult], inputConfig: inputConfig)
        return PreviewModels.createObjectResponse
    }

    func getAnswer<T>(for questionID: String) -> T? {
        answers.first(where: { $0.key == questionID })?.value.value as? T
    }

    private func getMedications() {
        Task {
            do {
                guard let medicationV2Config = deploymentConfigurationRepository.getMedicationV2Config(),
                let onDemandGroupID = medicationV2Config.getMedicationGroup(for: .onDemand) else { return }
                self.medicationModuleResult = try await medicationRepository.getMedicationAsync()
                    .filter({ $0.groupID == onDemandGroupID.id })
                let fetchedCMSMedications = try await pfizerPluginRepository.getCMSMedication().items
                let medicationNames = Set(medicationModuleResult.map { $0.name })
                self.cmsMedications = fetchedCMSMedications
                    .filter { medicationNames.contains($0.title) && $0.tags.contains(.onDemand) }
            } catch {
                self.cmsMedications = []
            }
        }
    }

    var headerConfiguration: HeaderConfiguration {
        HeaderConfiguration(
            title: HumaStrings.pluginHemophiliaJournalQuestionnaireTitle(bodyPartInjury),
            rightButtonIcon: nil,
            rightButtonAction: nil
        )
    }

}
