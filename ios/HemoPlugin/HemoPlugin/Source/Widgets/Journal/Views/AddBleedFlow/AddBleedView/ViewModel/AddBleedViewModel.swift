//
//  AddBleedViewModel.swift
//  HemoPlugin
//
//  Created by <PERSON> on 08/04/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI
import HumaFoundation
import HumaModules

protocol AnyAddBleedViewModel: ObservableObject {
    associatedtype BodyMapVM: AnyBodyMapViewModel

    var bodyMapViewModel: BodyMapVM { get }
    var reusableListViewModel: ReusableListViewModel { get }
    var showSheet: Bool? { get set }
    var navigateToQuestionnaire: Bool { get set }
    var selectedJointBleed: HemoJournalWidgetConfig.BodyPartData? { get set}
    var addBleedQuestionnaireSource: AddBleedQuestionnaireSource? { get }
    var onSubmitQuestionnaire: TriggeredEvent<Void> { get }

}

final class AddBleedViewModel: AnyAddBleedViewModel {

    typealias BodyMapVM = BodyMapViewModel

    @Published var bodyMapViewModel: BodyMapVM
    @Published var showSheet: Bool? = nil
    var reusableListViewModel: ReusableListViewModel { makeJointBleedViewModel() }
    var selectedBodyLocation: HemoJournalWidgetConfig.BodyLocation?
    @Published var navigateToQuestionnaire: Bool = false
    var selectedJointBleed: HemoJournalWidgetConfig.BodyPartData?
    @Published var addBleedQuestionnaireSource: AddBleedQuestionnaireSource? = nil

    var onSubmitQuestionnaire: TriggeredEvent<Void> = .init()

    var pfizerPluginRepository: AnyPfizerPluginRepository
    var moduleResultSubmitRepository: AnyModuleResultSubmitRepository
    var userRepository: AnyUserObserveRepository
    var fileRepository: AnyFileRepository
    var medicationRepositoryV2: AnyMedicationRepositoryV2
    private let deploymentConfigurationRepository: AnyDeploymentConfigurationRepository

    init(
        bodyMapViewModel: BodyMapVM,
        pfizerPluginRepository: AnyPfizerPluginRepository,
        moduleResultSubmitRepository: AnyModuleResultSubmitRepository,
        userRepository: AnyUserObserveRepository,
        fileRepository: AnyFileRepository,
        medicationRepositoryV2: AnyMedicationRepositoryV2,
        deploymentConfigurationRepository: AnyDeploymentConfigurationRepository
    ) {
        self.pfizerPluginRepository = pfizerPluginRepository
        self.moduleResultSubmitRepository = moduleResultSubmitRepository
        self.userRepository = userRepository
        self.fileRepository = fileRepository
        self.medicationRepositoryV2 = medicationRepositoryV2
        self.deploymentConfigurationRepository = deploymentConfigurationRepository
        self.bodyMapViewModel = BodyMapViewModel(
            bodyMapColor: [],
            legend: nil,
            jointData: bodyMapViewModel.jointData,
            allowInteraction: bodyMapViewModel.allowInteraction,
            legendText: bodyMapViewModel.legendText,
            selectedBodyPoint: nil
        )
        self.bodyMapViewModel.onBodyPointSelectd.addObserver(self) { observer, bodyLocation in
            observer.selectedBodyLocation = bodyLocation
            observer.showSheet = true
        }
    }

    func makeJointBleedViewModel() -> ReusableListViewModel {
        guard let selectedBodyLocation else {
            return .init(title: "", items: [], onItemSelected: { _ in })
        }
        let jointPoints = bodyMapViewModel.jointData.locations.first { $0.location == selectedBodyLocation.location }?.points ?? []
        let items: [ReusableListViewModel.Item] = jointPoints.map ({
            .init(id: $0.id, title: $0.name, description: nil)
        })
        return .init(
            title: HumaStrings.pluginHemophiliaAddNewBleed,
            items: items,
            onItemSelected: { [weak self] item in
                self?.showSheet = false
                if let selectedItem = jointPoints.first(where: { $0.id == item.id }) {
                    guard let self = self, let bodyLocation = self.selectedBodyLocation else { return }
                    self.selectedJointBleed = selectedItem

                    self.addBleedQuestionnaireSource = AddBleedQuestionnaireSource(
                        bodyLocation: bodyLocation.location,
                        bodyPart: selectedItem.value,
                        pfizerPluginRepository: self.pfizerPluginRepository,
                        moduleResultSubmitRepository: self.moduleResultSubmitRepository,
                        medicationRepository: self.medicationRepositoryV2,
                        userRepository: self.userRepository,
                        fileRepository: self.fileRepository,
                        deploymentConfigurationRepository: deploymentConfigurationRepository,
                        customBodyPart: nil,
                        bodyPartInjury: selectedJointBleed?.name ?? ""
                    )

                    self.addBleedQuestionnaireSource?.didSubmitQuestionnaire.addObserver(self) { observer in
                        observer.onSubmitQuestionnaire.trigger()
                    }

                    if selectedItem.value != .custom {
                        self.navigateToQuestionnaire = true
                    }
                }
            }
        )
    }
}
