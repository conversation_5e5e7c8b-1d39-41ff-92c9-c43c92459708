//
//  AddBleedSubmissionView.swift
//  Pods
//
//  Created by <PERSON> on 19/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//
import SwiftUI

struct AddBleedSubmissionView: View {
    // Required parameters
    let answers: [String: Answer]
    let customBodyPart: String?
    let bodyPartInjury: String

    // UI control
    @Binding var isLoading: Bool
    var onSubmit: () -> Void
    
    // Question IDs for reference
    typealias QuestionIDs = AddBleedQuestionnaireSource.QuestionID

    var body: some View {
        VStack(alignment: .leading, spacing: Dimensions.spacing) {
            Text(HumaStrings.pluginHemophiliaConfirmTheInformationTitle)
                .font(.xLargeBold)
                .foregroundColor(Color.charcoalGrey)

            Text(HumaStrings.pluginHemophiliaConfirmTheInformationDescription)
                .font(.default)
                .foregroundColor(Color.charcoalGrey)

            VStack(alignment: .leading, spacing: Dimensions.spacingMedium) {

                // Location information
                if let customPart = customBodyPart {
                    makeSummaryRow(title: HumaStrings.commonRecord, value: customPart)
                } else {
                    makeSummaryRow(title: HumaStrings.commonRecord, value: bodyPartInjury)
                }

                // Severity
                if let answer = answers[QuestionIDs.bleedSeverity.rawValue], let value = answer.value as? OptionItem {
                    makeSummaryRow(title: HumaStrings.pluginHemophiliaSeverityOfTheBleed, value: value.title)
                }

                // Reason
                if let answer = answers[QuestionIDs.bleedReason.rawValue], let value = answer.value as? OptionItem {
                    makeSummaryRow(title: HumaStrings.pluginHemophiliaReasonForTheBleed, value: value.title)
                }

                // Treatment
                if let answer = answers[QuestionIDs.bleedTreatment.rawValue],
                    let value = answer.value as? CMSMedicationResponse.MedicationItem {
                    makeSummaryRow(title: HumaStrings.pluginHemophiliaTreatment, value: value.title)
                }

                // Factor count
                if let answer = answers[QuestionIDs.bleedFactorCount.rawValue], let value = answer.value as? String {
                    makeSummaryRow(title: HumaStrings.pluginHemophiliaFactorUnitsSummaryTitle, value: value)
                }

                // Date
                if let answer = answers[QuestionIDs.bleedDate.rawValue], let value = answer.value as? Date {
                    makeSummaryRow(title: HumaStrings.pluginHemophiliaBleedDate, value: value.formattedDate)
                }

                // Pain scale
                if let answer = answers[QuestionIDs.painScale.rawValue], let value = answer.value as? Double {
                    makeSummaryRow(title: HumaStrings.pluginHemophiliaPainScaleOfTheBleed, value: "\(Int(value))")
                }

                // Notes
                if let answer = answers[QuestionIDs.bleedNotes.rawValue], let value = answer.value as? String, !value.isEmpty {
                    makeSummaryRow(title: "Notes", value: value)
                }

                // Photos
                if let answer = answers[QuestionIDs.bleedPhotos.rawValue], let value = answer.value as? String, !value.isEmpty {
                    makeSummaryRow(title: HumaStrings.modulePhotosTitle, value: "Photos Attached")
                }
            }
            .padding(.bottom, 16)

            Button(HumaStrings.commonActionSubmit) {
                onSubmit()
            }
            .pillStyle(isLoading: $isLoading, style: .largeFill, fullWidth: true)
        }
    }
    
    // Helper function to create summary rows
    private func makeSummaryRow(title: String, value: String) -> some View {
        HStack(spacing: Dimensions.spacingMedium) {
            Image("tick", bundle: HemoPluginBundle.bundle)

            VStack(alignment: .leading, spacing: Dimensions.spacing) {
                Text(title)
                    .font(.default)
                    .foregroundColor(.charcoalGrey)

                Text(value)
                    .font(.bold)
                    .foregroundColor(.charcoalGrey)

            }
        }
    }
}
