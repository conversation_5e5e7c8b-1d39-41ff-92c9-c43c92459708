//
//  AddBleedView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct AddBleedView<ViewModel: AnyAddBleedViewModel>: View {
    // MARK: - Properties
    @StateObject var viewModel: ViewModel

    // MARK: - Body
    var body: some View {
        ZStack(alignment: .center) {
            // Navigation links
            navigationLinks

            // Main content
            mainContent
        }
        .halfSheet(
            showSheet: $viewModel.showSheet,
            content: {
                ReusableListView(viewModel: viewModel.reusableListViewModel)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(.white)
            }
        )
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true)
    }

    // MARK: - Main Content
    private var mainContent: some View {
        VStack(alignment: .center, spacing: Dimensions.spacing) {
            // Header
            HeaderView(title: HumaStrings.pluginHemophiliaAddNewBleed, style: .stacked)

            // Body map
            BodyMapView(viewModel: viewModel.bodyMapViewModel)
        }
        .padding(.horizontal, Dimensions.horizontalPadding)
        .padding(.bottom, Dimensions.spacing)
    }
}

// MARK: - Navigation
private extension AddBleedView {
    var navigationLinks: some View {
        VStack {
            if let addBleedQuestionnaireSource = viewModel.addBleedQuestionnaireSource {
                NavigationLink("", isActive: $viewModel.navigateToQuestionnaire) {
                    QuestionnaireView(source: addBleedQuestionnaireSource)
                }
            }
        }
    }
}

#Preview {
    AddBleedView(viewModel: PreviewModels.addBleedVM)
}
