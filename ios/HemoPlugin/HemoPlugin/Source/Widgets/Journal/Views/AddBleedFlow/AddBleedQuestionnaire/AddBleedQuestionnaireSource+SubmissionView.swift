//
//  AddBleedQuestionnaireSource+SubmissionView.swift
//  Pods
//
//  Created by <PERSON> on 19/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//
import SwiftUI

extension AddBleedQuestionnaireSource {

    // Implement AnyQuestionSource protocol method for custom submission view
    @ViewBuilder
    func makeSubmissionView(viewModel: QuestionnaireViewModel) -> AnyView? {
        AnyView(
            AddBleedSubmissionView(
                answers: viewModel.answers,
                customBodyPart: customBodyPart,
                bodyPartInjury: bodyPartInjury,
                isLoading: Binding(
                    get: { viewModel.isSubmitting },
                    set: { viewModel.isSubmitting = $0 }
                )
            ) {
                viewModel.submitQuestionnaire()
            }
        )
    }

    // Helper function to create summary rows
    private func makeSummaryRow(title: String, value: String) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.footnote)
                .foregroundColor(.gray)

            Text(value)
                .font(.body)
                .foregroundColor(.charcoalGrey)

            Divider()
                .padding(.top, 4)
        }
    }
}
