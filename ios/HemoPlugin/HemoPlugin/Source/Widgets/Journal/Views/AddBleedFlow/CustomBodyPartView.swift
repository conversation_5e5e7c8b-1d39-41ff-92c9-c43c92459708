//
// CustomBodyPartView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct CustomBodyPartView: View {
    
    @State var customBodyPart: String?
    @State private var validationError: String?
    let onSubmit: (String) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: Dimensions.spacingMedium) {
            Text(HumaStrings.pluginHemophiliaAddCustomLocation)
                .font(.bold)
                .dynamicTypeSize(.medium)
                .padding(.top, Dimensions.spacingMedium)

            Text(HumaStrings.pluginHemophiliaAddCustomLocationDescription)
                .font(.default)
                .dynamicTypeSize(.medium)
                .fixedSize(horizontal: false, vertical: true)

            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    TextEditor(text: $customBodyPart.toUnwrapped(defaultValue: ""))
                        .font(.default)
                        .padding()
                        .foregroundColor(Color.charcoalGrey)
                        .frame(minHeight: 150)
                        .onChange(of: customBodyPart) { _ in
                            validateInput()
                        }
                }
                .overlay(
                    RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                        .strokeBorder(validationError != nil ? Color.red : Color.veryLightGrey, style: StrokeStyle(lineWidth: Dimensions.lineWidth))
                )
                if let error = validationError {
                    Text(error)
                        .font(.default)
                        .foregroundColor(.red)
                        .padding(.horizontal, 4)
                }
            }

            Button(HumaStrings.commonActionConfirm) {
                guard isInputValid() else { return }
                onSubmit(customBodyPart ?? "")
            }
            .pillStyle(.largeFill, fullWidth: true)
            .disabled(!isInputValid())
        }
        .padding(.horizontal, 24)
    }

    private func validateInput() {
         guard let inputValue = customBodyPart, !inputValue.isEmpty else {
             validationError = HumaStrings.validationCommonMandatory
             return
         }
         if inputValue.count > 42 {
             validationError = HumaStrings.pluginHemophiliaAddCustomPartTextLengthError
             return
         }
         validationError = nil
     }

    private func isInputValid() -> Bool {
          guard let inputValue = customBodyPart, !inputValue.isEmpty else { return false }
          if inputValue.count > 42 { return false }
          return true
      }
}
