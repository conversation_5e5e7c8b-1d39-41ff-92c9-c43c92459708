//
// PhotoLogView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

// MARK: - View
struct PhotoLogView: View {
    @ObservedObject var viewModel: PhotoLogViewModel

    // MARK: - Body
    var body: some View {
        VStack(alignment: .center, spacing: Dimensions.spacing) {
            // Header
            HeaderView(title: HumaStrings.personalDocumentsFileTypePhoto, style: .stacked)

            // Photos scroll view
            photosView
        }
        .onAppear(perform: {
            Task {
                await viewModel.loadPhotos()
            }
        })
        .padding(.horizontal, Dimensions.horizontalPadding)
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true)
    }
}

private extension PhotoLogView {
    var photosView: some View {
        ScrollView(showsIndicators: false) {
            ForEach(viewModel.photosUrls, id: \.fileID ) { photosUrl in
                VStack(alignment: .leading, spacing: Dimensions.spacingMedium) {
                    HumaSUIImageView(
                        content: .link(photosUrl.url),
                        radius: .value(Dimensions.cornerRadius),
                        placeholder: .none
                    )
                    .frame(maxWidth: .infinity)

                    Text(photosUrl.timestamp.stringWithTemplate("yyyy/MM/dd, HH:mm:ss"))
                        .font(.default)
                        .foregroundColor(.charcoalGrey)
                }
            }
        }
    }
}
