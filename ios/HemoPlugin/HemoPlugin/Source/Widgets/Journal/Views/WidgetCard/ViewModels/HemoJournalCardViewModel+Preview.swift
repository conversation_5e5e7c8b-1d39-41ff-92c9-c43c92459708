//
//  HemoJournalCardViewModel+Preview.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Combine
import HumaFoundation

extension HemoJournalCardViewModel {
    static var previewSetupRequired: HemoJournalCardViewModel {
        .init(
            widgetConfigInfo: WidgetConfig.preview.info,
            config: PreviewModels.journalWidgetConfig,
            repository: MockHemoJournalRepository(response: PreviewModels.widgetDataResponseSetupRequire),
            fileRepository: MockFileRepository(),
            pfizerPluginRepository: MockMedicationRepository(),
            moduleResultSubmitRepository: MockModuleResultSubmitRepository(),
            moduleResultRepository: MockModuleResultRepository(),
            medicationRepositoryV2: MockMedicationRepositoryV2(),
            userRepository: MockUserObserveRepository(),
            deploymentConfigurationRepository: MockConfigurationRepository()
        )
    }

    static var previewActive: HemoJournalCardViewModel {
        .init(
            widgetConfigInfo: WidgetConfig.preview.info,
            config: PreviewModels.journalWidgetConfig,
            repository: MockHemoJournalRepository(response: PreviewModels.widgetDataResponseActive),
            fileRepository: MockFileRepository(),
            pfizerPluginRepository: MockMedicationRepository(),
            moduleResultSubmitRepository: MockModuleResultSubmitRepository(),
            moduleResultRepository: MockModuleResultRepository(),
            medicationRepositoryV2: MockMedicationRepositoryV2(),
            userRepository: MockUserObserveRepository(),
            deploymentConfigurationRepository: MockConfigurationRepository()
        )
    }
}

fileprivate final class MockHemoJournalRepository: AnyHemoJournalRepository {

    var response: WidgetDataResponse

    init(response: WidgetDataResponse) {
        self.response = response
    }

    func getWidgetData(
        widgetType: String,
        widgetID: String,
        completion: @escaping RepositoryCompletion<WidgetDataResponse>
    ) {
        completion(.success(response))
    }
}

fileprivate extension WidgetConfig {
    static var preview: WidgetConfig {
        .init(type: "", id: "", order: 1, body: nil)
    }
}
