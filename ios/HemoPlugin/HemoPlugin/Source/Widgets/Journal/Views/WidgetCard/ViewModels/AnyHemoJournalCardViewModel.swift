//
//  AnyHemoJournalCardViewModel.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI
import HumaFoundation

// MARK: - Interface

protocol AnyHemoJournalCardViewModel: ObservableObject {
    associatedtype HeaderVM: AnyHeaderViewModel
    associatedtype BodyMapVM: AnyBodyMapViewModel
    associatedtype SetupProfileVM: AnySetupProfileCardViewModel
    associatedtype ActiveProfileVM: AnyActiveProfileCardViewModel
    associatedtype DetailsVM: AnyDetailsViewModel
    associatedtype AddBleedVM: AnyAddBleedViewModel

    // Navigation properties
    var showBleedTypeSheet: Bool { get set }
    var showNonJointBleedTypeSheet: Bool { get set }
    var showCustomBodyPartSheet: Bool { get set }
    var showSheet: Bool? { get set }
    
    // Configuration and content
    var config: HemoJournalWidgetConfig { get }
    var content: ContentState<Void> { get }

    // Events
    var onTap: TriggeredEvent<Void> { get }
    var onHemoProfile: TriggeredEvent<AnyQuestionSource> { get }
    var onViewHistory: TriggeredEvent<DetailsVM> { get }
    var onAddNewNonJoinBleed: TriggeredEvent<AnyQuestionSource> { get }
    var onAddNewBleed: TriggeredEvent<AddBleedVM> { get }

    // View Models
    var headerViewModel: HeaderVM? { get }
    var bodyMapViewModel: BodyMapVM? { get }
    var bodyMapViewModelEditable: BodyMapVM? { get }
    var setupProfileViewModel: SetupProfileVM? { get }
    var activeProfileViewModel: ActiveProfileVM? { get }
    var bleedTypeViewModel: ReusableListViewModel { get }
    var nonJointBleedViewModel: ReusableListViewModel { get }
    var addBleedVM: AddBleedVM? { get }
    var detailsVM: DetailsVM? { get }

    // Question source
    var profileQuestionnaireSource: HemoProfileQuestionnaireSource { get }
    var addBleedQuestionnaireSource: AddBleedQuestionnaireSource? { get }

    // Properties
    var selectedBleedType: HemoJournalWidgetConfig.BleedData? { get set }
    var selectedNonJointBleed: HemoJournalWidgetConfig.BodyPartData? { get set }
    var selectedJointBleed: HemoJournalWidgetConfig.BodyPartData? { get set }
    var customBodyPart: String? { get set }

    // Methods
    func loadData() async
    func showAddNewNonJoinBleed()
    func updateConfiguration(config: HemoJournalWidgetConfig)
}

// MARK: - Bundle

class HemoPluginBundle: NSObject {}

extension HemoPluginBundle {
    static var bundle: Bundle {
        Bundle(for: self)
    }
}
