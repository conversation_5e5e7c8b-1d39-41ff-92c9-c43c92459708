//
//  HemoJournalCardView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct HemoJournalCardView<ViewModel: AnyHemoJournalCardViewModel>: View {
    @StateObject var viewModel: ViewModel
    
    // MARK: - Body

    var body: some View {
        NavigationView {
            ZStack(alignment: .center) {
                loadingView
                    .transition(.opacity)
                    .animation(.default, value: viewModel.content.isLoading)
                    .visible(viewModel.content.isLoading)

                if let result = viewModel.content.result {
                    contentView(response: result)
                        .transition(.opacity)
                        .animation(.default, value: viewModel.content.shouldShowResults)
                        .visible(viewModel.content.shouldShowResults)
                        .padding(.horizontal, Dimensions.horizontalPadding)
                        .padding(.bottom, Dimensions.verticalPadding)
                }
            }
            .navigationBarBackButtonHidden(true)
            .navigationBarHidden(true)
            .onAppear() {
                Task { await viewModel.loadData() }
            }
            .halfSheet(
                showSheet: $viewModel.showSheet,
                content: {
                    if viewModel.showBleedTypeSheet {
                        ReusableListView(viewModel: viewModel.bleedTypeViewModel)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .background(.white)
                    } else if viewModel.showNonJointBleedTypeSheet {
                        ReusableListView(viewModel: viewModel.nonJointBleedViewModel)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .background(.white)
                    } else if viewModel.showCustomBodyPartSheet {
                        CustomBodyPartView {
                            viewModel.customBodyPart = $0
                            viewModel.showAddNewNonJoinBleed()
                            viewModel.showCustomBodyPartSheet = false
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(.white)
                    }
                },
                onDismiss: {
                    DispatchQueue.main.async {
                        viewModel.showBleedTypeSheet = false
                        viewModel.showNonJointBleedTypeSheet = false
                        viewModel.showCustomBodyPartSheet = false
                    }
                })
            }
        }

}

// MARK: - Private Helpers

private extension HemoJournalCardView {

    func contentView(response: Void) -> some View {
        VStack(alignment: .center, spacing: 8) {

            if let viewModel = viewModel.headerViewModel {
                JournalHeaderView(viewModel: viewModel)
                    .padding(.top, 40)
            }

            if let viewModel = viewModel.bodyMapViewModel {
                BodyMapView(viewModel: viewModel)
            }

            if let viewModel = viewModel.setupProfileViewModel {
                SetupProfileCardView(viewModel: viewModel)
            }

            if let viewModel = viewModel.activeProfileViewModel {
                ActiveProfileCardView(viewModel: viewModel)
            }
        }
    }

    var loadingView: some View {
        LoadingView()
    }
}

// MARK: - Preview
#Preview {
    HemoJournalCardView(
        viewModel: HemoJournalCardViewModel.previewActive
    )
}
