//
//  HemoJournalCardViewModel.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Combine
import HumaFoundation
import HumaModules

final class HemoJournalCardViewModel: AnyHemoJournalCardViewModel {

    // MARK: - Type Aliases
    typealias HeaderVM = HeaderViewModel
    typealias BodyMapVM = BodyMapViewModel
    typealias SetupProfileVM = SetupProfileCardViewModel
    typealias ActiveProfileVM = ActiveProfileCardViewModel
    typealias AddBleedVM = AddBleedViewModel
    typealias DetailsVM = DetailsViewModel

    // MARK: - Published Properties
    @Published private(set) var config: HemoJournalWidgetConfig
    @Published private(set) var content: ContentState<Void> = .loading
    @Published var showBleedTypeSheet: Bool = false {
        didSet { updateShowSheet() }
    }
    @Published var showNonJointBleedTypeSheet: Bool = false {
        didSet { updateShowSheet() }
    }
    @Published var showCustomBodyPartSheet: Bool = false {
        didSet { updateShowSheet() }
    }
    @Published var showSheet: Bool? = false

    // MARK: - View Models
    var headerViewModel: HeaderVM?
    var bodyMapViewModel: BodyMapVM?
    var bodyMapViewModelEditable: BodyMapViewModel?
    var setupProfileViewModel: SetupProfileVM?
    var activeProfileViewModel: ActiveProfileCardViewModel?
    var addBleedVM: AddBleedViewModel?
    var detailsVM: DetailsViewModel?

    lazy var bleedTypeViewModel: ReusableListViewModel = makeBleedTypeViewModel()
    lazy var nonJointBleedViewModel: ReusableListViewModel = makeNonJointBleedViewModel()
    var profileQuestionnaireSource: HemoProfileQuestionnaireSource {
        HemoProfileQuestionnaireSource(
            pfizerPluginRepository: pfizerPluginRepository,
            deploymentConfigurationRepository: deploymentConfigurationRepository,
            medicationRepositoryV2: medicationRepositoryV2,
            headerTitle: HumaStrings.pluginHemophiliaProfileQuestionnaireTitle
        )
    }
    var addBleedQuestionnaireSource: AddBleedQuestionnaireSource? { makeAddBleedQuestionnaireSource() }

    // MARK: - Properties
    var selectedBleedType: HemoJournalWidgetConfig.BleedData?
    var selectedNonJointBleed: HemoJournalWidgetConfig.BodyPartData?
    var selectedJointBleed: HemoJournalWidgetConfig.BodyPartData?
    var customBodyPart: String?

    // MARK: - Private Properties
    private var isLoadingData = false
    private let widgetConfigInfo: WidgetConfig.Info
    private let repository: AnyHemoJournalRepository
    private let fileRepository: AnyFileRepository
    private let pfizerPluginRepository: AnyPfizerPluginRepository
    private let moduleResultSubmitRepository: AnyModuleResultSubmitRepository
    private let moduleResultRepository: AnyModuleResultObserveRepository
    private let medicationRepositoryV2: AnyMedicationRepositoryV2
    private let userRepository: AnyUserObserveRepository
    private let deploymentConfigurationRepository: AnyDeploymentConfigurationRepository
    private var hasBleedData: Bool = false

    // MARK: - Events
    var onTap: TriggeredEvent<Void> = .init()
    var onHemoProfile: TriggeredEvent<AnyQuestionSource> = .init()
    var onViewHistory: TriggeredEvent<DetailsViewModel> = .init()
    var onAddNewBleed: TriggeredEvent<AddBleedViewModel> = .init()
    var onAddNewNonJoinBleed: TriggeredEvent<AnyQuestionSource> = .init()

    // MARK: - Initialization
    init(
        widgetConfigInfo: WidgetConfig.Info,
        config: HemoJournalWidgetConfig,
        repository: AnyHemoJournalRepository,
        fileRepository: AnyFileRepository,
        pfizerPluginRepository: AnyPfizerPluginRepository,
        moduleResultSubmitRepository: AnyModuleResultSubmitRepository,
        moduleResultRepository: AnyModuleResultObserveRepository,
        medicationRepositoryV2: AnyMedicationRepositoryV2,
        userRepository: AnyUserObserveRepository,
        deploymentConfigurationRepository: AnyDeploymentConfigurationRepository,
        content: ContentState<Void> = .loading
    ) {
        self.widgetConfigInfo = widgetConfigInfo
        self.config = config
        self.content = content
        self.repository = repository
        self.fileRepository = fileRepository
        self.pfizerPluginRepository = pfizerPluginRepository
        self.moduleResultSubmitRepository = moduleResultSubmitRepository
        self.moduleResultRepository = moduleResultRepository
        self.medicationRepositoryV2 = medicationRepositoryV2
        self.userRepository = userRepository
        self.deploymentConfigurationRepository = deploymentConfigurationRepository
    }

    // MARK: - Data Loading
    func loadData() async {
        await loadBleedLogData()
        await loadWidgetData()
    }

    func loadWidgetData() async {
        guard !isLoadingData else { return }
        isLoadingData = true
        do {
            let newResponse = try await repository.getWidgetData(
                widgetType: widgetConfigInfo.type,
                widgetID: widgetConfigInfo.id
            )
            setupHeaderViewModel(newResponse: newResponse)
            setupProfileViewModels(newResponse: newResponse)
            setupBodyMapViewModels(newResponse: newResponse)
            setupAddBleedAndDetailsVM(newResponse: newResponse)
            Task { @MainActor in
                content = .results(())
                isLoadingData = false
            }
        } catch {
            Task { @MainActor in
                isLoadingData = false
                content = .empty
            }
        }
    }

    func showAddNewNonJoinBleed() {
        guard let source = addBleedQuestionnaireSource else { return }
        onAddNewNonJoinBleed.trigger(source)
    }

    func updateConfiguration(config: HemoJournalWidgetConfig) {
        self.config = config
        headerViewModel?.title = config.header.title
        headerViewModel?.logoImageName = config.header.icon
    }
}

private extension HemoJournalCardViewModel {
    func setupHeaderViewModel(newResponse: WidgetDataResponse) {
        headerViewModel = HeaderViewModel(
            title: config.header.title,
            logoImageName: config.header.icon,
            fileRepository: fileRepository
        )
    }

    func setupProfileViewModels(newResponse: WidgetDataResponse) {
        switch newResponse.state {
        case .active:
            if hasBleedData {
                activeProfileViewModel = ActiveProfileCardViewModel(
                    primaryButtonTitle: newResponse.primaryCTAtext,
                    secondaryButtonTitle: newResponse.secondaryCTAtext
                )
                activeProfileViewModel?.onPrimaryButtonTapped.addObserver(self) { observer in
                    observer.showBleedTypeSheet = true
                }
                activeProfileViewModel?.onSecondaryButtonTapped.addObserver(self, using: { observer in
                    observer.showDetails()
                })
                setupProfileViewModel = nil
            } else {
                setupProfileViewModel = SetupProfileCardViewModel(
                    headline: newResponse.title,
                    bodyText: newResponse.description,
                    primaryButtonTitle: newResponse.primaryCTAtext
                )
                setupProfileViewModel?.onPrimaryButtonTapped.addObserver(self) { observer in
                    observer.showBleedTypeSheet = true
                }
                activeProfileViewModel = nil
            }
        case .setup:
            setupProfileViewModel = SetupProfileCardViewModel(
                headline: newResponse.title,
                bodyText: newResponse.description,
                primaryButtonTitle: newResponse.primaryCTAtext
            )
            setupProfileViewModel?.onPrimaryButtonTapped.addObserver(self) {
                if newResponse.state == .setup {
                    $0.onHemoProfile.trigger($0.profileQuestionnaireSource)
                } else {
                    $0.showAddNewBleed()
                }
            }
            activeProfileViewModel = nil
        }
    }

    func setupBodyMapViewModels(newResponse: WidgetDataResponse) {
        let bodyMapViewModel = BodyMapViewModel(
            bodyMapColor: newResponse.bodyMapColor ?? [],
            legend: newResponse.legend,
            jointData: config.jointData,
            legendText: newResponse.tooltip,
            selectedBodyPoint: nil
        )
        self.bodyMapViewModel = bodyMapViewModel
        let bodyMapViewModelEditable = BodyMapViewModel(
            bodyMapColor: newResponse.bodyMapColor ?? [],
            legend: nil,
            jointData: config.jointData,
            allowInteraction: true,
            legendText: HumaStrings.pluginHemophiliaAddBleedLegend,
            selectedBodyPoint: nil
        )
        self.bodyMapViewModelEditable = bodyMapViewModelEditable
    }

    func setupAddBleedAndDetailsVM(newResponse: WidgetDataResponse) {
        addBleedVM = .init(
            bodyMapViewModel: bodyMapViewModelEditable!,
            pfizerPluginRepository: pfizerPluginRepository,
            moduleResultSubmitRepository: moduleResultSubmitRepository,
            userRepository: userRepository,
            fileRepository: fileRepository,
            medicationRepositoryV2: medicationRepositoryV2,
            deploymentConfigurationRepository: deploymentConfigurationRepository
        )
        detailsVM = .init(
            bodyMapViewModel: bodyMapViewModel!,
            pfizerPluginRepository: pfizerPluginRepository,
            moduleResultRepository: moduleResultRepository,
            deploymentConfigurationRepository: deploymentConfigurationRepository,
            userRepository: userRepository,
            fileRepository: fileRepository,
            widgetConfig: config
        )
    }

    func updateShowSheet() {
        showSheet = showBleedTypeSheet || showNonJointBleedTypeSheet || showCustomBodyPartSheet
    }

    func makeBleedTypeViewModel() -> ReusableListViewModel {
        let items: [ReusableListViewModel.Item] = config.bodyMap.map {
            .init(id: $0.id, title: $0.title, description: $0.description)
        }
        return .init(
            title: HumaStrings.pluginHemophiliaAddNewBleed,
            items: items,
            onItemSelected: { [weak self] item in
                guard let self = self else { return }
                if let selectedItem = self.config.bodyMap.first(where: { $0.id == item.id }) {
                    self.selectedBleedType = selectedItem
                    switch selectedItem.bleedType {
                    case .joints:
                        self.showBleedTypeSheet = false
                        self.showAddNewBleed()
                    case .nonJoints:
                        executeOnMainThread(after: 0.2) { [weak self] in
                            self?.showNonJointBleedTypeSheet = true
                        }
                    }
                }
                self.showBleedTypeSheet = false
            }
        )
    }

    func makeNonJointBleedViewModel() -> ReusableListViewModel {
        let nonJointPoints = config.nonJointData.locations.first { $0.location == .other }?.points ?? []
        let items: [ReusableListViewModel.Item] = nonJointPoints.map ({
            .init(id: $0.id, title: $0.name, description: nil)
        })
        return .init(
            title: config.nonJointData.title,
            items: items,
            onItemSelected: { [weak self] item in
                if let selectedItem = nonJointPoints.first(where: { $0.id == item.id }) {
                    self?.selectedNonJointBleed = selectedItem
                    if selectedItem.value != .custom {
                        self?.showNonJointBleedTypeSheet = false
                        self?.showAddNewNonJoinBleed()
                    } else {
                        executeOnMainThread(after: 0.2) { [weak self] in
                            self?.showCustomBodyPartSheet = true
                        }
                    }
                }
                self?.showNonJointBleedTypeSheet = false
            }
        )
    }

    func makeAddBleedQuestionnaireSource() -> AddBleedQuestionnaireSource? {
        guard let selectedNonJointBleed = self.selectedNonJointBleed else { return nil }
        let source = AddBleedQuestionnaireSource(
            bodyLocation: .other,
            bodyPart: selectedNonJointBleed.value,
            pfizerPluginRepository: pfizerPluginRepository,
            moduleResultSubmitRepository: moduleResultSubmitRepository,
            medicationRepository: medicationRepositoryV2,
            userRepository: userRepository,
            fileRepository: fileRepository,
            deploymentConfigurationRepository: deploymentConfigurationRepository,
            customBodyPart: customBodyPart,
            bodyPartInjury: customBodyPart ?? selectedNonJointBleed.name
        )

        source.didSubmitQuestionnaire.addObserver(self) { observer in
            observer.selectedNonJointBleed = nil
        }

        return source
    }

    func loadBleedLogData() async {
        guard let moduleConfig = deploymentConfigurationRepository.configuration?.moduleConfig(for: HumaModuleID.hemophiliaJournal)?.config else {
            return
        }
        guard !isLoadingData else { return }
        isLoadingData = true
        do {
            let moduleResult = try await moduleResultRepository.getModuleResultAsync(
                moduleID: moduleConfig.inputConfig.moduleID,
                moduleConfigID: moduleConfig.inputConfig.moduleConfigID,
                primitiveType: HemoJournalPrimitive.self
            )
            self.hasBleedData = !moduleResult.isEmpty
        } catch {
            self.hasBleedData = false
        }
        isLoadingData = false
    }

    func showAddNewBleed() {
        guard let viewModel = addBleedVM else { return }
        onAddNewBleed.trigger(viewModel)
    }

    func showDetails() {
        guard let viewModel = detailsVM else { return }
        onViewHistory.trigger(viewModel)
    }

}

extension AnyModuleResultObserveRepository {
    // Example async function for fetching module results
    func getModuleResultAsync<Primitive: AnyCodablePrimitive>(moduleID: String, moduleConfigID: String, primitiveType: Primitive.Type) async throws -> [ModuleResult<Primitive>] {
        try await withCheckedThrowingContinuation { continuation in
            self.getModuleResults(moduleID: moduleID, moduleConfigID: moduleConfigID, primitiveType: primitiveType) { result in
                switch result {
                case .success(let results):
                    continuation.resume(returning: results)
                case .failure(let error):
                    continuation.resume(throwing: error)
                }
            }
        }
    }
}
