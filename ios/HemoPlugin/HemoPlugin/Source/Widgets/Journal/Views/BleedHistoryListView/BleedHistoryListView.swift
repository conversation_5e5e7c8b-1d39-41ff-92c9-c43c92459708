//
//  BleedHistoryListView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 12/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct BleedItem: Identifiable {
    let id: String
    let title: String
    let date: Date
    let description: String?
    let dateFormat: String

    init(id: String, title: String, date: Date, description: String?, dateFormat: String) {
        self.id = id
        self.title = title
        self.date = date
        self.description = description
        self.dateFormat = dateFormat
    }
}

// MARK: - View
struct BleedHistoryListView: View {
    @ObservedObject var viewModel: BleedHistoryViewModel
    var onTap: ((ModuleResult<HemoJournalPrimitive>) -> Void)?
    
    init(viewModel: BleedHistoryViewModel, onTap: ((ModuleResult<HemoJournalPrimitive>) -> Void)? = nil) {
        self.onTap = onTap
        self.viewModel = viewModel
    }

    // MARK: - Body
    var body: some View {
        ZStack(alignment: .center) {
            // Navigation links
            navigationLinks

            // Main content
            mainContent
        }
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true)
    }

    // MARK: - Main Content
    private var mainContent: some View {
        VStack(alignment: .center, spacing: Dimensions.spacing) {
            // Header
            HeaderView(title: HumaStrings.pluginHemophiliaBleedHistoryTitle, style: .stacked)

            // List view
            bleedListView
        }
        .padding(.horizontal, Dimensions.horizontalPadding)
    }

    private var bleedListView: some View {
        ScrollView(showsIndicators: false) {
            ForEach(viewModel.bleedItems) { item in
                BleedHistoryRow(item: item) { bleedItem in
                    viewModel.selectBleedItem(id: bleedItem.id)
                }
            }
        }
    }
}

struct BleedHistoryRow: View {
    let item: BleedItem
    var onTap: ((BleedItem) -> Void)?

    var body: some View {
        VStack {
            HStack {
                VStack(alignment: .leading, spacing: Dimensions.spacingSmall) {
                    HStack {
                        Text(item.title)
                            .font(.bold)
                            .dynamicTypeSize(.medium)
                            .foregroundColor(Color.charcoalGrey)

                        Spacer()

                        Text(item.date.stringWithFormat(item.dateFormat))
                            .font(.default)
                            .dynamicTypeSize(.medium)
                            .foregroundColor(Color.lightGrey1)

                        Image(HumaAssets.commonChevronRightRoundedEdge.name, bundle: HumaFoundationBundle.bundle)
                    }

                    if let description = item.description {
                        Text(description)
                            .lineLimit(2)
                            .font(.xSmall)
                            .dynamicTypeSize(.medium)
                            .foregroundColor(Color.charcoalGrey)
                    }
                }
            }
            .padding(.top, 14)
            .contentShape(Rectangle())
            .onTapGesture {
                onTap?(item)
            }

            Divider()
                .background(Color.veryLightGrey)
        }
    }
}

// MARK: - Navigation
private extension BleedHistoryListView {
    var navigationLinks: some View {
        VStack {
            if let selectedBleedLog = viewModel.selectedBleedLog {
                NavigationLink("", isActive: $viewModel.navigateToBleedLog) {
                    BleedDetailsView(
                        viewModel: .init(
                            result: selectedBleedLog,
                            fileRepository: viewModel.fileRepository,
                            widgetConfig: viewModel.widgetConfig
                        )
                    )
                }
            }
        }
    }
}
