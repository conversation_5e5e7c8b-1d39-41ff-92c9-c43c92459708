//
// BleedHistoryViewModel.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation

// MARK: - ViewModel
class BleedHistoryViewModel: ObservableObject {
    @Published var items: [ModuleResult<HemoJournalPrimitive>] = []
    @Published var selectedBleedLog: ModuleResult<HemoJournalPrimitive>?
    @Published var navigateToBleedLog = false
    let fileRepository: AnyFileRepository
    let widgetConfig: HemoJournalWidgetConfig

    var bleedItems: [BleedItem] {
        items.compactMap {
            guard let date = $0.primitive.extraData.accidentDate.toDate() else { return nil }
            let customBodyPart = $0.primitive.customBodyPart
            let bodyPartData = widgetConfig.getBodyPartData(for: $0.primitive.bodyPartInjury)
            return BleedItem(
                id: $0.id.rawValue,
                title: customBodyPart ?? bodyPartData?.name ?? $0.primitive.bodyPartInjury.rawValue,
                date: date,
                description: $0.primitive.extraData.note,
                dateFormat: "dd MMM yyyy"
            )
        }
    }
    
    init(
        items: [ModuleResult<HemoJournalPrimitive>] = [],
        fileRepository: AnyFileRepository,
        widgetConfig: HemoJournalWidgetConfig
    ) {
        self.items = items
        self.fileRepository = fileRepository
        self.widgetConfig = widgetConfig
    }
    
    func selectBleedItem(id: String) {
        if let selectedItem = items.first(where: { $0.id.rawValue == id }) {
            selectedBleedLog = selectedItem
            navigateToBleedLog = true
        }
    }
}
