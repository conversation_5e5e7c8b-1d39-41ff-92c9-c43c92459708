//
// DetailsViewModel.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI
import HumaFoundation
import HumaModules

protocol AnyDetailsViewModel: ObservableObject {
    associatedtype BodyMapVM: AnyBodyMapViewModel

    var moduleConfig: ModuleConfig? { get }
    var bodyMapViewModel: BodyMapVM { get }
    var navigateToBleedLog: Bool { get set }
    var navigateToBleedHistory: Bool { get set }
    var moduleResults: [ModuleResult<HemoJournalPrimitive>] { get set }
    var fileRepository: AnyFileRepository { get }
    var widgetConfig: HemoJournalWidgetConfig { get }

}

final class DetailsViewModel: AnyDetailsViewModel {

    typealias BodyMapVM = BodyMapViewModel

    @Published var moduleConfig: ModuleConfig?
    @Published var bodyMapViewModel: Body<PERSON><PERSON>VM
    @Published var navigateToBleedLog: Bool = false
    @Published var navigateToBleedHistory: Bool = false
    @Published var moduleResults: [ModuleResult<HemoJournalPrimitive>] = []

    var pfizerPluginRepository: AnyPfizerPluginRepository
    var moduleResultRepository: AnyModuleResultObserveRepository
    var deploymentConfigurationRepository: AnyDeploymentConfigurationRepository
    var userRepository: AnyUserObserveRepository
    var fileRepository: AnyFileRepository
    var widgetConfig: HemoJournalWidgetConfig

    init(
        bodyMapViewModel: BodyMapVM,
        pfizerPluginRepository: AnyPfizerPluginRepository,
        moduleResultRepository: AnyModuleResultObserveRepository,
        deploymentConfigurationRepository: AnyDeploymentConfigurationRepository,
        userRepository: AnyUserObserveRepository,
        fileRepository: AnyFileRepository,
        widgetConfig: HemoJournalWidgetConfig
    ) {
        self.pfizerPluginRepository = pfizerPluginRepository
        self.moduleResultRepository = moduleResultRepository
        self.userRepository = userRepository
        self.fileRepository = fileRepository
        self.deploymentConfigurationRepository = deploymentConfigurationRepository
        self.bodyMapViewModel = bodyMapViewModel
        self.moduleConfig = deploymentConfigurationRepository.configuration?
            .moduleConfig(for: HumaModuleID.hemophiliaJournal)?.config
        self.widgetConfig = widgetConfig

        loadData()
    }
}

private extension DetailsViewModel {
    func loadData() {
        guard let inputConfig = self.moduleConfig?.inputConfig else { return }
        moduleResultRepository
            .getModuleResults(
                moduleID: inputConfig.moduleID,
                moduleConfigID: inputConfig.moduleConfigID,
                primitiveType: HemoJournalPrimitive.self
            ) { result in
            switch result {
            case .success(let moduleResult):
                self.moduleResults = moduleResult
            case .failure(let error):
                print(error)
            }
        }
    }
}
