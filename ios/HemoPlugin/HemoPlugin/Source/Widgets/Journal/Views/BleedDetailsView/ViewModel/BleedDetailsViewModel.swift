//
// BleedDetailsViewModel.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation

// MARK: - ViewModel
class BleedDetailsViewModel: ObservableObject {

    let result: ModuleResult<HemoJournalPrimitive>
    let fileRepository: AnyFileRepository
    let widgetConfig: HemoJournalWidgetConfig

    // Navigation states
    @Published var navigateToBodyMap: Bool = false
    @Published var navigateToPhotos: Bool = false

    init(
        result: ModuleResult<HemoJournalPrimitive>,
        fileRepository: AnyFileRepository,
        widgetConfig: HemoJournalWidgetConfig
    ) {
        self.result = result
        self.fileRepository = fileRepository
        self.widgetConfig = widgetConfig
    }
    
    // User actions
    func showBodyMapDetails() {
        navigateToBodyMap = true
    }
    
    func showPhotoDetails() {
        navigateToPhotos = true
    }
}
