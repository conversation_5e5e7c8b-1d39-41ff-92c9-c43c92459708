//
// BleedDetailsView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI
import Combine

// MARK: - View
struct BleedDetailsView: View {
    @ObservedObject var viewModel: BleedDetailsViewModel

    var body: some View {
        ZStack {
            // Navigation links
            navigationLinks

            // Main content
            mainContent
        }
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true)
    }
}

private extension BleedDetailsView {

    private var mainContent: some View {
        VStack(alignment: .leading, spacing: Dimensions.spacing) {
            // Header - directly accessing result
            HeaderView(title: title, style: .stacked)

            // Details section
            detailsSection(locationTitle: title)
        }
        .padding(.horizontal, Dimensions.horizontalPadding)
    }

    private func detailsSection(locationTitle: String) -> some View {
        ScrollView(showsIndicators: false) {
            VStack(alignment: .leading, spacing: Dimensions.spacing) {
                if let date = viewModel.result.primitive.extraData.accidentDate.toDate() {
                    makeRow(
                        title: HumaStrings.pluginHemophiliaDateOfBleed,
                        value: date.formattedDate
                    )
                }
                if let name = viewModel.result.primitive.extraData.treatment?.name {
                    makeRow(title: HumaStrings.pluginHemophiliaTreatment, value: name)
                }

                makeRow(
                    title: HumaStrings.pluginHemophiliaReasonForTheBleed,
                    value: viewModel.result.primitive.extraData.reason.title
                )

                makeRow(
                    title: HumaStrings.pluginHemophiliaSeverityOfTheBleed,
                    value: viewModel.result.primitive.extraData.severity.title
                )

                makeRow(
                    title: HumaStrings.pluginHemophiliaPainScaleOfTheBleed,
                    value: viewModel.result.primitive.extraData.scale.string
                )

                if let note = viewModel.result.primitive.extraData.note {
                    makeRow(title: HumaStrings.pluginHemophiliaNote, value: note)
                }


                let shouldShowDetailsSection = viewModel.result.primitive.bodyLocation != .other ||
                !(viewModel.result.primitive.extraData.photos?.isEmpty ?? true)
                if shouldShowDetailsSection {
                    Text(HumaStrings.commonDetails)
                        .font(.bold)
                        .foregroundColor(.charcoalGrey)
                        .padding(.vertical, Dimensions.spacingMedium)

                    if viewModel.result.primitive.bodyLocation != .other {
                        makeClickableRow(
                            title: HumaStrings.pluginHemophiliaBodyLocation,
                            value: locationTitle,
                            action: {
                                viewModel.showBodyMapDetails()
                            })
                    }

                    if let photos = viewModel.result.primitive.extraData.photos, !photos.isEmpty {
                        makeClickableRow(
                            title: HumaStrings.personalDocumentsFileTypePhoto,
                            value: HumaStrings.pluginHemophiliaPhotoAttached,
                            action: {
                                viewModel.showPhotoDetails()
                            })
                    }
                }

                Spacer()
            }
        }
    }

    func makeRow(title: String, value: String) -> some View {
        VStack(alignment: .leading, spacing: Dimensions.spacingMedium) {
            Text(title)
                .font(.bold)
                .foregroundColor(.charcoalGrey)
            Text(value)
                .font(.default)
                .foregroundColor(.charcoalGrey)
            Divider()
                .background(Color.veryLightGrey)
        }
    }

    func makeClickableRow(title: String, value: String, action: (() -> Void)?) -> some View {
        VStack(alignment: .leading, spacing: Dimensions.spacingMedium) {
            HStack {
                VStack(alignment: .leading, spacing: Dimensions.spacing) {
                    Text(title)
                        .font(.default)
                        .foregroundColor(.charcoalGrey)
                    Text(value)
                        .font(.xSmall)
                        .foregroundColor(.charcoalGrey)
                }
                Spacer()
                Image(HumaAssets.commonChevronRightRoundedEdge.name, bundle: HumaFoundationBundle.bundle)

            }
            .contentShape(Rectangle())
            .onTapGesture {
                action?()
            }
            Divider()
                .background(Color.veryLightGrey)
        }
    }

    var title: String {
        let customBodyPart = viewModel.result.primitive.customBodyPart
        let bodyPartData = viewModel.widgetConfig.getBodyPartData(for: viewModel.result.primitive.bodyPartInjury)
        let title = customBodyPart ?? bodyPartData?.name ?? viewModel.result.primitive.bodyPartInjury.rawValue
        return title
    }
}

// MARK: - Navigation
private extension BleedDetailsView {
    var navigationLinks: some View {
        VStack {
            NavigationLink("", isActive: $viewModel.navigateToBodyMap) {
                BodyMapLogView(bodyLocation: viewModel.result.primitive.bodyLocation, title: title)
            }

            if let photos = viewModel.result.primitive.extraData.photos {
                NavigationLink("", isActive: $viewModel.navigateToPhotos) {

                    PhotoLogView(
                        viewModel: .init(
                            photos: photos
                                .map({ PhotoItem(fileID: $0, timestamp: viewModel.result.startDateTime, url: nil) }),
                            fileRepository: viewModel.fileRepository
                        )
                    )
                }
            }
        }
    }
}
