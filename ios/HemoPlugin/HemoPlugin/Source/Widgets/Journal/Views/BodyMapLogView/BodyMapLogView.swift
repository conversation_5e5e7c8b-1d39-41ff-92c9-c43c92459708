//
// BodyMapLogView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

// MARK: - ViewModel
class BodyMapLogViewModel: ObservableObject {
    let bodyLocation: BodyLocationType

    @Published private(set) var jointData: HemoJournalWidgetConfig.BleedData
    @Published private(set) var selectedBodyPoint: HemoJournalWidgetConfig.BodyLocation?
    
    init(bodyLocation: BodyLocationType) {
        self.bodyLocation = bodyLocation
        
        let locations: [HemoJournalWidgetConfig.BodyLocation] = BodyLocationType
            .allCases
            .filter( { $0 != .other })
            .map({ bodyLocationType in
                HemoJournalWidgetConfig.BodyLocation(location: bodyLocationType, points: [])
            })
        
        self.jointData = .init(bleedType: .joints, title: "", description: "", locations: locations)
        self.selectedBodyPoint = jointData.locations.first(where: { $0.location == bodyLocation })
    }
}

// MARK: - View
struct BodyMapLogView: View {
    @StateObject private var viewModel: BodyMapLogViewModel
    let title: String

    init(bodyLocation: BodyLocationType, title: String) {
        _viewModel = StateObject(wrappedValue: BodyMapLogViewModel(bodyLocation: bodyLocation))
        self.title = title
    }

    // MARK: - Body
    var body: some View {
        VStack(alignment: .center, spacing: Dimensions.spacing) {
            if let selectedBodyPoint = viewModel.selectedBodyPoint {
                // Header
                HeaderView(title: title, style: .stacked)

                // List view
                BodyMapView(
                    viewModel: BodyMapViewModel(
                        bodyMapColor: [],
                        legend: nil,
                        jointData: viewModel.jointData,
                        allowInteraction: false,
                        legendText: "",
                        selectedBodyPoint: selectedBodyPoint
                    )
                )
            }
        }
        .padding(.horizontal, Dimensions.horizontalPadding)
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true)
    }
}
