//
//  LoadingView.swift
//  HumaLifeLightPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Lottie
import SwiftUI

struct LoadingView: View {
    var viewModel: BodyMapViewModel = {
        let bodyPoints: [BodyMapColor] = BodyLocationType.allCases.map { .init(location: $0, color: "#EBEBEB") }
        return .init(
            bodyMapColor: bodyPoints,
            legend: nil,
            jointData: .init(bleedType: .joints, title: "", description: "", locations: []),
            legendText: ""
        )
    }()

    var body: some View {
        return VStack(alignment: .center, spacing: 8) {
            BodyMapView(viewModel: viewModel)
                .padding(.top, 75)

            LoadingContentCardView()
        }
    }
}

struct LoadingContentCardView: View {

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            RoundedRectangle(cornerRadius: 8)
                .frame(width: 200, height: 14)

            Divider()
                .background(Color.veryLightGrey)

            RoundedRectangle(cornerRadius: 8)
                .frame(width: .infinity, height: 14)

            RoundedRectangle(cornerRadius: 4)
                .frame(width: 140, height: 16)
                .padding(.top, 8)
        }
        .shimmer()
        .padding(16)
        .background(Color.white)
        .cornerRadius(10)
        .overlay(
            RoundedRectangle(cornerRadius: 10)
                .stroke(Color.veryLightGrey, lineWidth: 1)
        )
        .padding()
    }
}

#Preview {
    LoadingView(viewModel: PreviewModels.bodyMapVM)
}
