//
// ReusableListView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct ReusableListView: View {
    @ObservedObject var viewModel: ReusableListViewModel

    var body: some View {
        VStack(alignment: .leading) {
            // Header Title
            Text(viewModel.title)
                .font(.bold)
                .dynamicTypeSize(.medium)
                .padding(.vertical, 14)

            // List of Items
            if !viewModel.items.isEmpty {
                ForEach(viewModel.items) { item in
                    VStack {
                        HStack {
                            VStack(alignment: .leading, spacing: Dimensions.spacingSmall) {
                                Text(item.title) // Replace this with the necessary property
                                    .font(.default)
                                    .dynamicTypeSize(.medium)
                                    .foregroundColor(Color.charcoalGrey)

                                // Description Text
                                if let description = item.description {
                                    Text(description)
                                        .font(.xSmall)
                                        .dynamicTypeSize(.medium)
                                        .foregroundColor(Color.charcoalGrey)
                                }
                            }
                            Spacer()
                            Image(systemName: "chevron.right")
                        }
                        .padding(.vertical, 8)
                        .contentShape(Rectangle())
                        .onTapGesture {
                            viewModel.onItemSelected(item)
                        }

                        Divider()
                            .background(Color.veryLightGrey)
                    }
                }
                Spacer()
            }
        }
        .background(Color.white)
        .padding(.horizontal, 24)
    }
}

#Preview {
    ReusableListView(
        viewModel: .init(
            title: HumaStrings.pluginHemophiliaAddNewBleed,
            items: [
                .init(
                    id: UUID(),
                    title: "Add joint bleed",
                    description: "Record a bleed in joints like knees, elbows, or ankles."
                ),
                .init(
                    id: UUID(),
                    title: "Add non-joint bleed",
                    description: "Record a bleed in areas like nose, gum or menstrual."
                )
            ],
 onItemSelected: { item in

            }
        )
    )
}
