//
//  HemoProfileQuestionnaireSource.swift
//  HemoPlugin
//
//  Created by <PERSON> on 17/03/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Foundation
import UIKit
import Combine
import HumaFoundation
import HumaModules
import HumaWidgetKit

class HemoProfileQuestionnaireSource: AnyQuestionSource {

    private enum Const {
        static let profileWidgetIdentifier: String = "com.huma.widget.profile"
        static let widgetKitNotificaiton: String = "WidgetKitNotification"
    }

    let didSubmitQuestionnaire: TriggeredEvent<Void> = .init()
    let showConfirmationDialog: TriggeredEvent<Bool> = .init()
    let exitFlow: TriggeredEvent<Void> = .init()
    let hemoProfileQuestionnaireResponse: HemoProfileQuestionnaireResponse?

    var confirmationDialogViewModel: ConfirmationDialogViewModel? = nil
    var answers: [String : Answer] = [:]
    var userMedications: [MedicationV2] = []
    var prophylacticMedicatonID: CMSMedicationUserMedicationIdentifier?
    var onDemandMedicationID: CMSMedicationUserMedicationIdentifier?
    var cmsMedications: [CMSMedicationResponse.MedicationItem] = []

    private let pfizerPluginRepository: AnyPfizerPluginRepository
    private var deploymentConfigurationRepository: AnyDeploymentConfigurationRepository?
    private let headerTitle: String
    private let medicationRepositoryV2: AnyMedicationRepositoryV2

    init(
        pfizerPluginRepository: AnyPfizerPluginRepository,
        deploymentConfigurationRepository: AnyDeploymentConfigurationRepository?,
        medicationRepositoryV2: AnyMedicationRepositoryV2,
        headerTitle: String,
        hemoProfileQuestionnaireResponse: HemoProfileQuestionnaireResponse? = nil
    ) {
        self.headerTitle = headerTitle
        self.pfizerPluginRepository = pfizerPluginRepository
        self.deploymentConfigurationRepository = deploymentConfigurationRepository
        self.hemoProfileQuestionnaireResponse = hemoProfileQuestionnaireResponse
        self.medicationRepositoryV2 = medicationRepositoryV2
        getCMSMedications()
    }

    // Add enum for Question IDs
    enum QuestionID: String {
        case weight = "HEMOPHILIA_WEIGHT_ID"
        case hemophiliaType = "HEMOPHILIA_TYPE_ID"
        case hemophiliaSeverity = "HEMOPHILIA_SEVERITY_ID"
        case inhibitorHistory = "HEMOPHILIA_ACTIVE_ANTIBODY_ID"
        case bleedCount = "HEMOPHILIA_BLEEDS_COUNT_ID"
        case treatedBleedCount = "HEMOPHILIA_TREATMENT_COUNT_ID"
        case targetJoints = "HEMOPHILIA_TARGET_JOINTS_CONDITION_ID"
        case targetJointLocations = "HEMOPHILIA_TARGET_JOINTS_ID"
        case onProphylacticTreatment = "HEMOPHILIA_PROPHYLACTIC_CONDITION_ID"
        case prophylacticTreatment = "HEMOPHILIA_PROPHYLACTIC_ID"
        case dosage = "HEMOPHILIA_PROPHYLACTIC_DOSAGE_ID"
        case medicationFrequency = "HEMOPHILIA_PROPHYLACTIC_FREQUENCY_ID"
        case timeOfDay = "HEMOPHILIA_PROPHYLACTIC_TIME_OF_DAY_ID"
        case setReminder = "HEMOPHILIA_PROPHYLACTIC_REMINDER_ID"
        case nextDose = "HEMOPHILIA_PROPHYLACTIC_NEXT_DOSAGE_ID"
        case factorTreatment = "HEMOPHILIA_PROPHYLACTIC_DAYS_OF_WEEK_ID"
        case diagnoses = "HEMOPHILIA_DIAGNOSED_ID"
        case specificDays = "HEMOPHILIA_ON_DEMAND_ID"
        case dayInterval = "HEMOPHILIA_PROPHYLACTIC_DAYS_INTERVAL_ID"
        case maxDosage = "MAX_DOSAGE_ID"
    }

    func loadQuestions() -> [BaseQuestion] {
        let questionFactory = HemoProfileQuestionnaireFactory(source: self)
        return questionFactory.makeQuestions()
    }

    func getOptions(for questionID: String) -> [OptionItem] {
        switch questionID {
        case QuestionID.hemophiliaType.rawValue:
            return HemophiliaType.allCases.map(\.optionItem)
        case QuestionID.hemophiliaSeverity.rawValue:
            return HemophiliaSeverity.allCases.map(\.optionItem)
        case QuestionID.inhibitorHistory.rawValue:
            return InhibitorHistory.allCases.map(\.optionItem)
        case QuestionID.targetJointLocations.rawValue:
            return BodyLocationType.allCases.compactMap { locationType in
                guard locationType != .other else { return nil }
                return locationType.optionItem
            }
        case QuestionID.medicationFrequency.rawValue:
            return MedicationFrequency.allCases.map(\.optionItem)
        case QuestionID.diagnoses.rawValue:
            return DiagnosisType.allCases.map(\.optionItem)
        case QuestionID.specificDays.rawValue:
            return WeekDay.allCases.map(\.optionItem)
        case QuestionID.dayInterval.rawValue:
            return MedicationDay.all.map(\.optionItem)
        case QuestionID.dosage.rawValue:
            guard let medicationV2Config = deploymentConfigurationRepository?.getMedicationV2Config() else { return [] }
            return MedicationDosageOptionsProvider.options(from: medicationV2Config)
        default: return []
        }
    }

    func searchAutocompleteOptions(for searchTerm: String, questionID: String) -> [CMSMedicationResponse.MedicationItem] {
        let tag: MedicationTag?
        switch questionID {
        case QuestionID.prophylacticTreatment.rawValue:
            tag = .prophylactic
        case QuestionID.factorTreatment.rawValue:
            tag = .onDemand
        default:
            return []
        }

        return cmsMedications
            .filter {
                $0.tags.contains(tag!)
                && (searchTerm.isEmpty || $0.title.localizedCaseInsensitiveContains(searchTerm))
            }
            .sorted(by: \.title)
    }

    func onSubmitQuestionnaire(answers: [String : Answer]) async throws -> CreateObjectResponse {
        guard !validateDosage() else {
            throw RepositoryError.addFailed(reason: nil)
        }
        let targetJoints: [OptionItem]? = getAnswer(for: QuestionID.targetJointLocations.rawValue)
        let answerBodies = answers.compactMapValues { $0.answerBody }.map({ $0.value })
        let prophylacticMedication = getProphylacticMedication(for: answers)
        let onDemandMedication = getOnDemandMedication(for: answers)
        let requestBody = HemoProfileQuestionnaireRequest(
            answers: answerBodies,
            targetJoints: targetJoints?.map(\.value) ?? [],
            prophylacticMedication: prophylacticMedication,
            asNeededMedication: onDemandMedication,
            addProphylacticMedication: prophylacticMedication?.id.rawValue == hemoProfileQuestionnaireResponse?.prophylacticMedication?.id,
            addAsNeededMedication: onDemandMedication?.id.rawValue == hemoProfileQuestionnaireResponse?.asNeededMedication?.id
        )

        let response = try await pfizerPluginRepository.submitHemoProfileQuestionnaire(requestBody: requestBody)
        medicationRepositoryV2.refreshMedications()
        refreshProfileWidget()
        return response
    }

    var headerConfiguration: HeaderConfiguration {
        HeaderConfiguration(
            title: headerTitle,
            rightButtonIcon: nil,
            rightButtonAction: nil
        )
    }

    func getAnswer<T>(for questionID: String) -> T? {
        answers.first(where: { $0.key == questionID })?.value.value as? T
    }

    func buildMedicationFrequency(from schedule: MedicationScheduleV2) -> MedicationFrequency {
        switch schedule {
        case .everyDay(let times):
            return .everyDay
        case .specific(let days, let times):
            return .specificDays
        case .interval(let duration, let times):
            return .daysInterval
        case .timeRanges(let asNeeded, let timeRanges):
            return .asNeeded
        case .asNeeded:
            return .asNeeded
        }
    }
}

// MARK: - Private Methods
private extension HemoProfileQuestionnaireSource {
    func getProphylacticMedication(for answers: [String : Answer]) -> MedicationV2? {
        guard let medicationV2Config = deploymentConfigurationRepository?.getMedicationV2Config(),
              let cmsMedication: CMSMedicationResponse.MedicationItem = getAnswer(for: QuestionID.prophylacticTreatment.rawValue),
              let dosage: MedicationDosageV2 = getAnswer(for: QuestionID.dosage.rawValue),
              let frequencyOption: OptionItem = getAnswer(for: QuestionID.medicationFrequency.rawValue),
              let frequency = MedicationFrequency(rawValue: frequencyOption.value),
              let nextDose: Date = getAnswer(for: QuestionID.nextDose.rawValue) else {
            return nil
        }
        let reminder: OptionItem? = getAnswer(for: QuestionID.setReminder.rawValue)
        let timeOfDay: [Date]? = getAnswer(for: QuestionID.timeOfDay.rawValue)
        let daysOfWeek: [OptionItem]? = getAnswer(for: QuestionID.specificDays.rawValue)
        let daysIntervalOptionItem: OptionItem? = getAnswer(for: QuestionID.dayInterval.rawValue)
        let daysInterval = daysIntervalOptionItem?.value ?? "1_DAY"
        let daysIntervalValue = daysInterval.components(separatedBy: "_").first ?? "1"

        let medicationSchedule = buildMedicationSchedule(
            frequency: frequency,
            timeOfDay: timeOfDay ?? [],
            daysOfWeek: daysOfWeek?.compactMap({ WeekDay(rawValue: $0.value)}) ?? [],
            daysInterval: Int(daysIntervalValue) ?? 1
        )
        var medicationID: Identifier<MedicationV2> = .uuid
        if let medicationIDMap = prophylacticMedicatonID, cmsMedication.id == medicationIDMap.cmsMedicationId {
            medicationID = .init(medicationIDMap.userMedicationId)
        }
        return MedicationV2(
            id: medicationID,
            name: cmsMedication.title,
            dosage: dosage,
            schedule: medicationSchedule,
            isNotificationEnabled: reminder?.value == BoolQuestion.yes.rawValue,
            startDate: nextDose,
            customUnit: nil,
            groupID: medicationV2Config.getMedicationGroup(for: .prophylactic)?.id
        )
    }

    func getOnDemandMedication(for answers: [String : Answer]) -> MedicationV2? {
        guard let medicationV2Config = deploymentConfigurationRepository?.getMedicationV2Config(),
              let cmsMedication: CMSMedicationResponse.MedicationItem = getAnswer(
                for: QuestionID.factorTreatment.rawValue
              ) else {
            return nil
        }
        
        var medicationID: Identifier<MedicationV2> = .uuid
        if let medicationIDMap = onDemandMedicationID, cmsMedication.id == medicationIDMap.cmsMedicationId {
            medicationID = .init(medicationIDMap.userMedicationId)
        }

        return MedicationV2(
            id: medicationID,
            name: cmsMedication.title,
            dosage: nil,
            schedule: .asNeeded,
            isNotificationEnabled: false,
            customUnit: nil,
            groupID: medicationV2Config.getMedicationGroup(for: .onDemand)?.id
        )
    }

    func getCMSMedications() {
        Task {
            do {
                self.cmsMedications = try await pfizerPluginRepository.getCMSMedication().items
            } catch {
                self.cmsMedications = []
            }

            do {
                self.userMedications = try await medicationRepositoryV2.getMedicationAsync()
            } catch {
                self.userMedications = []
            }
        }
    }

    func buildMedicationSchedule(frequency: MedicationFrequency, timeOfDay: [Date], daysOfWeek: [WeekDay], daysInterval: Int) -> MedicationScheduleV2 {
        var times: [ISODuration] { timeOfDay.map(ISODuration.timeDuration(from:)) }
        
        switch frequency {
        case .everyDay:
            return .everyDay(times: times)
        case .specificDays:
            return .specific(days: daysOfWeek, times: times)
        case .daysInterval:
            return .interval(duration: "P\(daysInterval)D", times: times)
        case .asNeeded:
            return .asNeeded
        }
    }

    private func validateDosage() -> Bool {
        guard let dosageObject: MedicationDosageV2 = getAnswer(for: QuestionID.dosage.rawValue),
              let maxDoasgeString: String = getAnswer(for: QuestionID.maxDosage.rawValue),
              let maxDosage = maxDoasgeString.doubleValue else {
            return false
        }
        if maxDosage <= dosageObject.value {
            executeOnMainThread {
                HemoJournalWidget.showSnackBar(message: HumaStrings.medicationv2FailedSubmissionDosage)
            }
        }
        return maxDosage <= dosageObject.value
    }

    func refreshProfileWidget() {
        guard let widget = deploymentConfigurationRepository?
            .configuration?
            .builder?
            .tabs
            .compactMap ({ tab in
                tab.widgets?.compactMap ({ widgetConfig in
                    widgetConfig.info.type == Const.profileWidgetIdentifier ? widgetConfig : nil
                })
            })
            .flatMap({ $0 })
            .first else {
                return
            }
        let userInfo = WidgetKitNotification(type: .refresh, identifier: widget.id)
        NotificationCenter.default
            .post(name: .widgetKitNotification, object: nil, userInfo: [Const.widgetKitNotificaiton: userInfo])
    }
}
