//
//  HemoProfileQuestionnaireSource+Success.swift
//  HemoPlugin
//
//  Created by <PERSON> on 17/03/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

extension HemoProfileQuestionnaireSource {
    func makeSuccessScreen(viewModel: QuestionnaireViewModel) -> AnyView? {
        AnyView(
            SuccessScreen(
                title: HumaStrings.submissionTitleThankYou,
                subtitle: HumaStrings.submissionDescriptionResultsSubmitted,
                imageName: HumaAssets.thumbsUpBig.name,
                onDone: { [weak self] in
                    viewModel.showSuccessScreen = false
                    viewModel.pendingDismiss = false
                    viewModel.shouldDismiss = true
                    self?.didSubmitQuestionnaire.trigger()
                })
        )
    }
}
