// HemoProfileQuestionnaireFactory.swift
// Factory for creating HemoProfileQuestionnaire questions

import HumaFoundation

class HemoProfileQuestionnaireFactory {
    private let source: HemoProfileQuestionnaireSource

    init(source: HemoProfileQuestionnaireSource) {
        self.source = source
    }

    func makeQuestions() -> [BaseQuestion] {
        let preFilledAnswers = getPrefilledAnswers()
        let weightRanges = makeWeightRanges()
        let dosage: MedicationDosageV2? = source.getAnswer(for: HemoProfileQuestionnaireSource.QuestionID.dosage.rawValue)
        let unit = dosage?.customUnit ?? dosage?.unit.rawValue
        return [
            makeWeightQuestion(preFilledAnswers, weightRanges),
            makeSingleChoiceQuestion(
                id: .hemophiliaType,
                title: HumaStrings.pluginHemophiliaPleaseIndicateYourTypeOfHemophilia,
                next: .hemophiliaSeverity,
                initial: preFilledAnswers?[.hemophiliaType]
            ),
            makeSingleChoiceQuestion(
                id: .hemophiliaSeverity,
                title: HumaStrings.pluginHemophiliaPleaseIndicateTheSeverityOfYourHemophilia,
                subtitle: HumaStrings.pluginHemophiliaSeverityDescription,
                next: .inhibitorHistory,
                initial: preFilledAnswers?[.hemophiliaSeverity]
            ),
            makeSingleChoiceQuestion(
                id: .inhibitorHistory,
                title: HumaStrings.pluginHemophiliaActiveAntibodyTitle,
                subtitle: HumaStrings.pluginHemophiliaActiveAntibodyDescription,
                next: .bleedCount,
                initial: preFilledAnswers?[.inhibitorHistory]
            ),
            makeBleedCountQuestion(preFilledAnswers),
            makeTreatedBleedCountQuestion(preFilledAnswers),
            makeBooleanChoiceQuestion(
                id: .targetJoints,
                title: HumaStrings.pluginHemophiliaDoYouHaveTargetJoints,
                subtitle: HumaStrings.pluginHemophiliaTargetJointsConditionDescription,
                branching: ["YES": .targetJointLocations, "NO": .onProphylacticTreatment],
                initial: preFilledAnswers?[.targetJoints]
            ),
            makeMultipleChoiceQuestion(
                id: .targetJointLocations,
                title: HumaStrings.pluginHemophiliaWhichAreYourTargetJoints,
                subtitle: HumaStrings.pluginHemophiliaPleaseSelectAllThatApply,
                next: .onProphylacticTreatment,
                initial: preFilledAnswers?[.targetJointLocations]
            ),
            makeBooleanChoiceQuestion(
                id: .onProphylacticTreatment,
                title: HumaStrings.pluginHemophiliaAreYouOnProphylacticTreatment,
                branching: ["YES": .prophylacticTreatment, "NO": .factorTreatment],
                initial: preFilledAnswers?[.onProphylacticTreatment]
            ),
            makeAutocompleteSearchQuestion(
                id: .prophylacticTreatment,
                title: HumaStrings.pluginHemophiliaSelectProphylacticTitle,
                placeholder: HumaStrings.pluginHemophiliaStartTypingToSearch,
                allowScanning: true,
                next: .dosage,
                initial: preFilledAnswers?[.prophylacticTreatment]
            ),
            makeValueUnitQuestion(
                id: .dosage,
                title: Strings.medicationv2InputDosageTitle,
                placeholder: Strings.medicationv2InputDosagePlaceholder,
                unit: unit,
                next: .medicationFrequency,
                initial: preFilledAnswers?[.dosage]
            ),
            makeSingleChoiceQuestion(
                id: .medicationFrequency,
                title: HumaStrings.medicationInputFrequencyTitle,
                branching: [
                    MedicationFrequency.everyDay.rawValue: .timeOfDay,
                    MedicationFrequency.specificDays.rawValue: .specificDays,
                    MedicationFrequency.daysInterval.rawValue: .dayInterval,
                    MedicationFrequency.asNeeded.rawValue: .maxDosage
                ],
                initial: preFilledAnswers?[.medicationFrequency]
            ),
            makeTimeQuestion(preFilledAnswers),
            makeBooleanChoiceQuestion(
                id: .setReminder,
                title: HumaStrings.medicationv2EnableReminderTitle,
                next: .nextDose,
                initial: preFilledAnswers?[.setReminder]
            ),
            makeDateQuestion(preFilledAnswers),
            makeAutocompleteSearchQuestion(
                id: .factorTreatment,
                title: HumaStrings.pluginHemophiliaSelectOnDemandTitle,
                placeholder: HumaStrings.pluginHemophiliaStartTypingToSearch,
                allowScanning: true,
                next: .diagnoses,
                initial: preFilledAnswers?[.factorTreatment]
            ),
            makeMultipleChoiceQuestion(
                id: .diagnoses,
                title: HumaStrings.pluginHemophiliaDiagnosedQuestionTitle,
                subtitle: HumaStrings.pluginHemophiliaPleaseSelectAllThatApply,
                initial: preFilledAnswers?[.diagnoses]
            ),
            makeMultipleChoiceQuestion(
                id: .specificDays,
                title: HumaStrings.medicationv2InputDaysTitle,
                type: .circle,
                next: .timeOfDay,
                initial: preFilledAnswers?[.specificDays]
            ),
            makePickerQuestion(
                id: .dayInterval,
                title: HumaStrings.medicationv2InputIntervalTitle,
                placeholder: HumaStrings.medicationv2InputIntervalPlaceholder,
                next: .timeOfDay,
                initial: preFilledAnswers?[.dayInterval]
            ),
            makeMaxDosageQuestion(preFilledAnswers, unit)
        ]
    }

    // MARK: - Question Builders

    private func makeWeightRanges() -> HumaUnits.Ranges<HumaUnits.Mass.Option> {
        [.kg: 20...300, .lb: 45...660, .st: 3.1428...47.3]
    }

    private func makeWeightQuestion(
        _ preFilledAnswers: [HemoProfileQuestionnaireSource.QuestionID: Any]?,
        _ weightRanges: HumaUnits.Ranges<HumaUnits.Mass.Option>
    ) -> BaseQuestion {
        BaseQuestion(
            id: HemoProfileQuestionnaireSource.QuestionID.weight.rawValue,
            title: HumaStrings.moduleWeightInputSubtitle,
            type: .numeric(
                .init(
                    placeholder: HumaStrings.pluginHemophiliaEnterYourWeight,
                    allowDecimals: false,
                    accessoryText: HumaStrings.commonUnitPoundSingular
                )
            ),
            validationRules: [
                .required(message: HumaStrings.pluginHemophiliaEnterYourWeight),
                .numeric(message: HumaStrings.pluginHemophiliaWeightValidation),
                .custom({ value in
                    guard let weightString = value as? String,
                          let weight = Double(weightString) else { return false }
                    return weightRanges[.lb].contains(weight) ?? false
                },
                        message: HumaStrings.commonNumberRangeError(
                            Float(weightRanges[.lb].lowerBound).string(decimalPlacesRange: .zero),
                            Float(weightRanges[.lb].upperBound).string(decimalPlacesRange: .zero)
                        ))
            ],
            nextQuestionID: HemoProfileQuestionnaireSource.QuestionID.hemophiliaType.rawValue,
            initialValue: preFilledAnswers?[.weight]
        )
    }

    private func makeSingleChoiceQuestion(
        id: HemoProfileQuestionnaireSource.QuestionID,
        title: String,
        subtitle: String? = nil,
        next: HemoProfileQuestionnaireSource.QuestionID? = nil,
        isMandatory: Bool = true,
        branching: [String: HemoProfileQuestionnaireSource.QuestionID]? = nil,
        initial: Any? = nil
    ) -> BaseQuestion {
        BaseQuestion(
            id: id.rawValue,
            title: title,
            subtitle: subtitle,
            type: .singleChoice,
            isMandatory: isMandatory,
            validationRules: [.required()],
            nextQuestionID: next?.rawValue,
            branchingLogic: branching?.mapValues { $0.rawValue },
            initialValue: initial
        )
    }

    private func makeBooleanChoiceQuestion(
        id: HemoProfileQuestionnaireSource.QuestionID,
        title: String,
        subtitle: String? = nil,
        next: HemoProfileQuestionnaireSource.QuestionID? = nil,
        branching: [String: HemoProfileQuestionnaireSource.QuestionID]? = nil,
        initial: Any? = nil
    ) -> BaseQuestion {
        BaseQuestion(
            id: id.rawValue,
            title: title,
            subtitle: subtitle,
            type: .booleanChoice,
            validationRules: [.required()],
            nextQuestionID: next?.rawValue,
            branchingLogic: branching?.mapValues { $0.rawValue },
            initialValue: initial
        )
    }

    private func makeMultipleChoiceQuestion(
        id: HemoProfileQuestionnaireSource.QuestionID,
        title: String,
        subtitle: String? = nil,
        type: MultipleChoiceType = .default,
        next: HemoProfileQuestionnaireSource.QuestionID? = nil,
        initial: Any? = nil
    ) -> BaseQuestion {
        return BaseQuestion(
            id: id.rawValue,
            title: title,
            subtitle: subtitle,
            type: .multipleChoice(.init(type: type)),
            validationRules: [.required()],
            nextQuestionID: next?.rawValue,
            initialValue: initial
        )
    }

    private func makeBleedCountQuestion(_ preFilledAnswers: [HemoProfileQuestionnaireSource.QuestionID: Any]?) -> BaseQuestion {
        BaseQuestion(
            id: HemoProfileQuestionnaireSource.QuestionID.bleedCount.rawValue,
            title: HumaStrings.pluginHemophiliaBleedCountTitle,
            type: .numeric(.init(placeholder: HumaStrings.pluginHemophiliaEnterNumberOfBleeds)),
            validationRules: [
                .required(message: HumaStrings.pluginHemophiliaEnterNumberOfBleeds),
                .numeric(message: HumaStrings.pluginHemophiliaBleedValidation),
                .custom({ value in
                    guard let bleedCountString = value as? String, let bleedCount = Int(bleedCountString) else { return false }
                    return bleedCount < 15
                }, message: HumaStrings.pluginHemophiliaMakeSureThisNumberReflectsYourActualBleedCount)
            ],
            nextQuestionID: HemoProfileQuestionnaireSource.QuestionID.treatedBleedCount.rawValue,
            initialValue: preFilledAnswers?[.bleedCount]
        )
    }

    private func makeTreatedBleedCountQuestion(_ preFilledAnswers: [HemoProfileQuestionnaireSource.QuestionID: Any]?) -> BaseQuestion {
        BaseQuestion(
            id: HemoProfileQuestionnaireSource.QuestionID.treatedBleedCount.rawValue,
            title: HumaStrings.pluginHemophiliaHowManyOfThoseBleedsWereTreated,
            type: .numeric(.init(placeholder: HumaStrings.pluginHemophiliaEnterNumberOfTreatedBleeds)),
            validationRules: [
                .required(message: HumaStrings.pluginHemophiliaEnterNumberOfTreatedBleeds),
                .numeric(message: HumaStrings.pluginHemophiliaTreatedBleedValidation),
                .custom({ [weak source] value in
                    guard let source = source else { return false }
                    if let bleedCountString: String = source.getAnswer(for: HemoProfileQuestionnaireSource.QuestionID.bleedCount.rawValue),
                       let bleedCount = bleedCountString.intValue,
                       let treadedBleedCountString = value as? String,
                       let treadedBleedCount = treadedBleedCountString.intValue {
                        return treadedBleedCount <= bleedCount
                    }
                    return true
                }, message: HumaStrings.pluginHemophiliaBleedCountValidation)
            ],
            nextQuestionID: HemoProfileQuestionnaireSource.QuestionID.targetJoints.rawValue,
            initialValue: preFilledAnswers?[.treatedBleedCount]
        )
    }

    private func makeAutocompleteSearchQuestion(
        id: HemoProfileQuestionnaireSource.QuestionID,
        title: String,
        placeholder: String,
        allowScanning: Bool = false,
        next: HemoProfileQuestionnaireSource.QuestionID? = nil,
        initial: Any? = nil
    ) -> BaseQuestion {
        BaseQuestion(
            id: id.rawValue,
            title: title,
            type: .autocompleteSearch(.init(placeholder: placeholder, allowScanning: allowScanning)),
            validationRules: [.required()],
            nextQuestionID: next?.rawValue,
            initialValue: initial
        )
    }

    private func makeValueUnitQuestion(
        id: HemoProfileQuestionnaireSource.QuestionID,
        title: String,
        placeholder: String,
        unit: String?,
        next: HemoProfileQuestionnaireSource.QuestionID? = nil,
        initial: Any? = nil
    ) -> BaseQuestion {
        BaseQuestion(
            id: id.rawValue,
            title: title,
            type: .valueUnit(.init(placeholder: placeholder)),
            validationRules: [
                .required(message: HumaStrings.medicationv2InputErrorInvalidDosage),
                .numeric(message: HumaStrings.medicationv2InputErrorInvalidDosage)
            ],
            nextQuestionID: next?.rawValue,
            initialValue: initial
        )
    }

    private func makeTimeQuestion(_ preFilledAnswers: [HemoProfileQuestionnaireSource.QuestionID: Any]?) -> BaseQuestion {
        BaseQuestion(
            id: HemoProfileQuestionnaireSource.QuestionID.timeOfDay.rawValue,
            title: HumaStrings.medicationv2InputTimeTitle,
            type: .time,
            validationRules: [.required()],
            nextQuestionID: HemoProfileQuestionnaireSource.QuestionID.setReminder.rawValue,
            initialValue: preFilledAnswers?[.timeOfDay]
        )
    }

    private func makeDateQuestion(_ preFilledAnswers: [HemoProfileQuestionnaireSource.QuestionID: Any]?) -> BaseQuestion {
        BaseQuestion(
            id: HemoProfileQuestionnaireSource.QuestionID.nextDose.rawValue,
            title: HumaStrings.pluginHemophiliaWhenIsYourNextDose,
            type: .date(.init(range: Date()...Date.distantFuture)),
            validationRules: [.required()],
            nextQuestionID: HemoProfileQuestionnaireSource.QuestionID.factorTreatment.rawValue,
            initialValue: preFilledAnswers?[.nextDose]
        )
    }

    private func makePickerQuestion(
        id: HemoProfileQuestionnaireSource.QuestionID,
        title: String,
        placeholder: String,
        next: HemoProfileQuestionnaireSource.QuestionID? = nil,
        initial: Any? = nil
    ) -> BaseQuestion {
        BaseQuestion(
            id: id.rawValue,
            title: title,
            type: .picker(.init(placeholder: placeholder)),
            validationRules: [.required()],
            nextQuestionID: next?.rawValue,
            initialValue: initial
        )
    }

    private func makeMaxDosageQuestion(
        _ preFilledAnswers: [HemoProfileQuestionnaireSource.QuestionID: Any]?,
        _ unit: String?
    ) -> BaseQuestion {
        BaseQuestion(
            id: HemoProfileQuestionnaireSource.QuestionID.maxDosage.rawValue,
            title: HumaStrings.medicationv2InputDailyDoseTitle,
            type: .numeric(
                .init(
                    placeholder: HumaStrings.medicationv2InputDailyDosePlaceholder,
                    allowDecimals: true,
                    accessoryText: unit
                )
            ),
            validationRules: [.required()],
            nextQuestionID: HemoProfileQuestionnaireSource.QuestionID.nextDose.rawValue,
            initialValue: preFilledAnswers?[.maxDosage]
        )
    }

    func getPrefilledAnswers() -> [HemoProfileQuestionnaireSource.QuestionID: Any]? {
        guard let hemoProfileQuestionnaireResponse = source.hemoProfileQuestionnaireResponse else { return nil }
        var preFilledAnswers = hemoProfileQuestionnaireResponse.mapAnswers()
        preFilledAnswers[.targetJointLocations] = hemoProfileQuestionnaireResponse.targetJoints

        if let medicationID = hemoProfileQuestionnaireResponse.prophylacticMedication?.id,
           let prophylacticMedication = source.userMedications.first(where: { $0.id.rawValue == medicationID }),
           let cmsMedicaiton = source.cmsMedications.first(
            where: { $0.title == prophylacticMedication.name && $0.tags.contains(.prophylactic)
            }) {
            preFilledAnswers[.prophylacticTreatment] = cmsMedicaiton
            preFilledAnswers[.dosage] = prophylacticMedication.dosage

            source.prophylacticMedicatonID = (cmsMedicaiton.id, prophylacticMedication.id.rawValue)

            if let schedule = prophylacticMedication.schedule {
                preFilledAnswers[.medicationFrequency] = source.buildMedicationFrequency(from: schedule).rawValue
            }

            preFilledAnswers[.timeOfDay] = prophylacticMedication.schedule?.timesOfReadings?.map { $0.date() } ?? []

            if case let .specific(days, _) = prophylacticMedication.schedule {
                preFilledAnswers[.specificDays] = days
                    .compactMap { WeekDay(weekDay: $0.weekDay) }
                    .map({ $0.rawValue })
            }

            if case let .interval(duration, _) = prophylacticMedication.schedule {
                let first = duration.index(duration.startIndex, offsetBy: 1)
                let last = duration.index(duration.endIndex, offsetBy: -1)
                let days = String(duration[first ..< last]).intValue
                guard let days = String(duration[first ..< last]).intValue else { return nil }
                let optionItem: OptionItem = .init(value: "\(days)_DAYS", title: Strings.moduleMedicationDays(days))

                preFilledAnswers[.dayInterval] = optionItem
            }

            preFilledAnswers[.setReminder] = (prophylacticMedication.isNotificationEnabled ?? false) ? "YES": "NO"

            preFilledAnswers[.nextDose] = prophylacticMedication.startDate
            preFilledAnswers[.maxDosage] = prophylacticMedication.maxDosage?.string(maximumDecimalPlaces: 0)
        }

        if let medicationID = hemoProfileQuestionnaireResponse.asNeededMedication?.id {
            if let onDemenadMedication = source.userMedications.first(where: { $0.id.rawValue == medicationID }),
               let cmsMedicaiton = source.cmsMedications.first(
                where: { $0.title == onDemenadMedication.name && $0.tags.contains(.onDemand)
                }) {
                preFilledAnswers[.factorTreatment] = cmsMedicaiton

                source.onDemandMedicationID = (cmsMedicaiton.id, onDemenadMedication.id.rawValue)
            }
        }
        return preFilledAnswers
    }
}
