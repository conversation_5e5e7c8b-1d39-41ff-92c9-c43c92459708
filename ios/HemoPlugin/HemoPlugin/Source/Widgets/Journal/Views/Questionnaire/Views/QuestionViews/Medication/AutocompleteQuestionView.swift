//
//  AutocompleteQuestionView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI
import Combine

struct AutocompleteQuestionView: View {
    private let question: BaseQuestion
    @ObservedObject var viewModel: QuestionnaireViewModel
    @State private var selectedOption: CMSMedicationResponse.MedicationItem?
    @State private var isPresentingSearch = false
    @State private var hasSubmitted = false
    @State private var showScanner: Bool = false
    @State private var hasPressedNext: Bool = false

    private var questionProperties: AutoCompleteQuestionProperties? {
        guard case .autocompleteSearch(let questionProperties) = question.type else { return nil }
        return questionProperties
    }

    init(question: BaseQuestion, viewModel: QuestionnaireViewModel) {
        self.question = question
        self.viewModel = viewModel
        let initialValue = question.initialValue as? CMSMedicationResponse.MedicationItem
        self._selectedOption = State(initialValue: initialValue)
    }

    private var placeholder: String { questionProperties?.placeholder ?? HumaStrings.pluginHemophiliaStartTypingToSearch }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Button(action: {
                    isPresentingSearch = true
                }) {
                    Text(placeholder)
                        .font(.default)
                        .foregroundColor(selectedOption == nil ? .gray : .charcoalGrey)
                        .aligned(to: .leading)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .foregroundColor(.charcoalGrey)

                if let questionProperties = questionProperties, questionProperties.allowScanning {
                    Button(action: {
                        showScanner = true
                    }) {
                        Image(HumaAssets.icQrCode.name, bundle: HumaFoundationBundle.bundle)
                    }
                    .padding(.trailing, Dimensions.spacing)
                }
            }
            .overlay(
                RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                    .strokeBorder(Color.veryLightGrey, style: StrokeStyle(lineWidth: Dimensions.lineWidth))
            )

            if let selectedOption = self.selectedOption {
                MedicationRowView(option: selectedOption, isSelected: false, selectable: false)
            }

            if !question.isMandatory {
                Button(HumaStrings.commonSkipTheQuestion) {
                    viewModel.skipQuestion(for: question.id)
                }
                .padding(.top, 8)
                .foregroundColor(.charcoalGrey)
            }

            if !hasPressedNext {
                // Submit button
                Button(HumaStrings.commonActionConfirm) {
                    submitAnswer()
                }
                .pillStyle(.largeFill, fullWidth: true)
                .disabled(question.isMandatory && (selectedOption == nil))
                .opacity(question.isMandatory && (selectedOption == nil) ? 0.6 : 1.0)
                .padding(.top, 16)
            }
        }
        .fullScreenCover(isPresented: $isPresentingSearch) {
            AutocompleteSearchView(
                question: question,
                viewModel: viewModel,
                selectedOption: $selectedOption,
                isPresentingSearch: $isPresentingSearch
            )
        }
        .fullScreenCover(isPresented: $showScanner) {
            MedicationScannerView(
                isPresented: $showScanner,
                medicationResolver: { code in
                    // Use the same search logic as before
                    let searchResults = viewModel.searchAutocompleteOptions(for: "", questionID: question.id)
                    return searchResults.first(where: {
                        guard let identifier = $0.identifier else { return false }
                        return identifier == code
                    })
                }) { medication in
                    selectMedication(medication: medication)
                }
        }
    }

    private func selectMedication(medication: CMSMedicationResponse.MedicationItem) {
        selectedOption = medication
        isPresentingSearch = false
    }

    private func submitAnswer() {
        if let option = selectedOption, !hasSubmitted {
            hasSubmitted = true  // Set flag to prevent infinite loop
            hasPressedNext = true
            viewModel.submitAnswer(answer: .init(question: question, value: option))
        }
    }
}

struct AutocompleteSearchView: View {
    let question: BaseQuestion
    @ObservedObject var viewModel: QuestionnaireViewModel
    @Binding var selectedOption: CMSMedicationResponse.MedicationItem?
    @Binding var isPresentingSearch: Bool
    @State private var searchText: String = ""
    @State private var searchResults: [CMSMedicationResponse.MedicationItem] = []
    @State private var temporarySelection: CMSMedicationResponse.MedicationItem?
    @State private var showScanner: Bool = false

    private var placeholder: String { questionProperties?.placeholder ?? "" }
    private var questionProperties: AutoCompleteQuestionProperties? {
        guard case .autocompleteSearch(let questionProperties) = question.type else { return nil }
        return questionProperties
    }

    var body: some View {
        NavigationView {
            VStack {
                HeaderView(title: HumaStrings.pluginHemophiliaSelectMedicationTitle, style: .stacked, onBack: {
                    isPresentingSearch = false
                })
                // Search field
                TextField(placeholder, text: $searchText)
                    .font(.default)
                    .keyboardType(.default)
                    .padding()
                    .foregroundColor(.charcoalGrey)
                    .overlay(
                        RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                            .strokeBorder(Color.veryLightGrey, style: StrokeStyle(lineWidth: Dimensions.lineWidth))
                    )

                ScrollView(showsIndicators: false) {
                    if searchResults.isEmpty {
                        VStack(spacing: 20) {
                            // Only show Add button if user has typed something
                            if !searchText.isEmpty {
                                HStack {
                                    Image(HumaAssets.plus.name, bundle: HumaFoundationBundle.bundle)

                                    Button(HumaStrings.autocompleteInputAddNewValue(searchText), action: {
                                        // Create a custom medication item with the search text
                                        let customOption = CMSMedicationResponse.MedicationItem(
                                            id: UUID().uuidString,
                                            title: searchText
                                        )
                                        selectMedication(medication: customOption)
                                    })
                                    .underlineStyle(style: .large)
                                }
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .padding(.horizontal)
                            }
                        }
                    } else {
                        ForEach(searchResults, id: \.id) { option in
                            Button(action: {
                                selectMedication(medication: option)
                            }) {
                                MedicationRowView(
                                    option: option,
                                    isSelected: temporarySelection == option
                                )
                            }
                        }
                    }
                }
                .background(.clear)

                if let allowScanning = questionProperties?.allowScanning, allowScanning {
                    // Scan button
                    Button(HumaStrings.pluginHemophiliaScanMedication, action: {
                        showScanner = true
                    })
                    .pillStyle(.large, fullWidth: true)
                }
            }
            .onChange(of: searchText) { _ in
                performSearch(query: searchText)
            }
            .padding(.horizontal, Dimensions.horizontalPadding)
            .padding(.vertical, Dimensions.verticalPadding)
            .navigationBarBackButtonHidden(true)
            .navigationBarHidden(true)
            .onAppear {
                executeOnMainThread(after: 0.2) {
                    performSearch(query: "")
                }
            }
            .fullScreenCover(isPresented: $showScanner) {
                MedicationScannerView(
                    isPresented: $showScanner,
                    medicationResolver: { code in
                        // Use the same search logic as before
                        let searchResults = viewModel.searchAutocompleteOptions(for: "", questionID: question.id)
                        return searchResults.first(where: { $0.identifier?.range(of: code) != nil })
                    }) { medication in
                        selectMedication(medication: medication)
                    }
            }
        }
    }


    private func performSearch(query: String) {
        self.searchResults = viewModel.searchAutocompleteOptions(for: query, questionID: question.id)
    }

    private func selectMedication(medication: CMSMedicationResponse.MedicationItem) {
        temporarySelection = medication
        selectedOption = medication
        isPresentingSearch = false
    }
}

private struct MedicationRowView: View {
    var option: CMSMedicationResponse.MedicationItem
    var isSelected: Bool
    var selectable: Bool = true

    var body: some View {
        VStack(alignment: .leading, spacing: Dimensions.spacing) {
            Text(option.title)
                .font(.bold)
                .foregroundColor(Color.charcoalGrey)
                .multilineTextAlignment(.leading)
                .lineLimit(nil) // Allow multiline
                .fixedSize(horizontal: false, vertical: true)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .enableCardStyling()
        .padding(.horizontal, 2)
    }
}

private struct AutocompleteSearchView_Preview : View {

    @State var selectedOption: CMSMedicationResponse.MedicationItem? = nil
    @State var isPresentingSearch: Bool = false

    var body: some View {
        AutocompleteSearchView(
            question: BaseQuestion(
                id: HemoProfileQuestionnaireSource.QuestionID.prophylacticTreatment.rawValue,
                title: HumaStrings.pluginHemophiliaSelectProphylacticTitle,
                type: .autocompleteSearch(.init(placeholder: HumaStrings.pluginHemophiliaStartTypingToSearch, allowScanning: true)),
                validationRules: [.required()],
                nextQuestionID: HemoProfileQuestionnaireSource.QuestionID.dosage.rawValue
            ),
            viewModel: .init(questionSource: PreviewModels.hemoProfileQuestionSource),
            selectedOption: $selectedOption,
            isPresentingSearch: $isPresentingSearch
        )
    }
}

#Preview {
    Group {
        AutocompleteQuestionView(
            question: BaseQuestion(
                id: HemoProfileQuestionnaireSource.QuestionID.prophylacticTreatment.rawValue,
                title: HumaStrings.pluginHemophiliaSelectProphylacticTitle,
                type: .autocompleteSearch(.init(placeholder: HumaStrings.pluginHemophiliaStartTypingToSearch, allowScanning: true)),
                validationRules: [.required()],
                nextQuestionID: HemoProfileQuestionnaireSource.QuestionID.dosage.rawValue
            ),
            viewModel: .init(questionSource: PreviewModels.hemoProfileQuestionSource)
        )
        .padding()

        AutocompleteSearchView_Preview()
    }
}

