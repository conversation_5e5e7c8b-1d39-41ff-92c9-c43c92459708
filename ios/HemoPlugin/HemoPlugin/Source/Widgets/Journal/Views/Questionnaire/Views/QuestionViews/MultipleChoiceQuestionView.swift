//
// MultipleChoiceQuestionView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.

import SwiftUI

struct MultipleChoiceQuestionView: View {
    private let question: BaseQuestion
    private var options: [OptionItem]
    @ObservedObject private var viewModel: QuestionnaireViewModel
    private let choiceType: MultipleChoiceType
    @State private var selectedOptions: [OptionItem]?
    @State private var tagItems: [TagViewItem] = []
    // Track if user has pressed Next
    @State private var hasPressedNext: Bool = false

    init(
        question: BaseQuestion,
        viewModel: QuestionnaireViewModel,
        choiceType: MultipleChoiceType
    ) {
        self.question = question
        self.viewModel = viewModel
        self.choiceType = choiceType

        self.options = viewModel.getOptions(for: question.id)
        let initialStrings = question.initialValue as? [String]
        let filteredOptions = initialStrings?.compactMap { initialValue in options.first { $0.value == initialValue } }
        self._selectedOptions = State(initialValue: filteredOptions)
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Use the provided TagView with tagType
            TagView(tags: $tagItems, style: choiceType.tagType)
                .onChange(of: tagItems) { newTags in
                    // Update selectedOptions when tags change
                    let selectedIds = newTags.filter { $0.isSelected }.map { $0.id }
                    selectedOptions = options.filter { option in selectedIds.contains(option.id) }
                    hasPressedNext = false
                }

            // Submit button
            Button(HumaStrings.commonActionNext) {
                submitAnswer()
            }
            .pillStyle(.largeFill, fullWidth: true)
            .disabled(disableNextButton)
            .opacity(disableNextButton ? 0.6 : 1.0)
            .padding(.top, 16)
        }
        .onAppear {
            initializeTagItems()
        }
    }

    private func initializeTagItems() {
        // Initialize tag items with selected status based on selectedOptions
        tagItems = options.map { option in
            TagViewItem(
                id: option.id,
                title: option.title,
                isSelected: selectedOptions?.contains(where: {
                    $0.id == option.id
                }) ?? false
            )
        }
    }

    private func submitAnswer() {
        if let options = selectedOptions, !options.isEmpty {
            hasPressedNext = true
            UIApplication.shared.hideKeyboard()
            let answer = Answer(question: question, value: options)
            viewModel.submitAnswer(answer: answer)
        }
    }

    private var disableNextButton: Bool {
        guard let selectedOptions = selectedOptions else { return true }
        let hasSelectedOptions = selectedOptions.isEmpty
        return question.isMandatory && hasSelectedOptions || hasPressedNext
    }
}

private extension MultipleChoiceType {
    var tagType: TagStyle {
        switch self {
        case .circle:
            return .circular
        case .default:
            return .defaultStyle
        }
    }
}

private struct MultipleChoiceQuestionView_Preview: View {
    var body: some View {
        MultipleChoiceQuestionView(
            question: BaseQuestion(
                id: HemoProfileQuestionnaireSource.QuestionID.targetJointLocations.rawValue,
                title: HumaStrings.pluginHemophiliaWhichAreYourTargetJoints,
                type: .multipleChoice(.init(type: .default)),
                validationRules: [.required()],
                nextQuestionID: HemoProfileQuestionnaireSource.QuestionID.onProphylacticTreatment.rawValue
            ),
            viewModel: .init(questionSource: PreviewModels.hemoProfileQuestionSource),
            choiceType: .default
        )
    }
}

#Preview {
    MultipleChoiceQuestionView_Preview()
}
