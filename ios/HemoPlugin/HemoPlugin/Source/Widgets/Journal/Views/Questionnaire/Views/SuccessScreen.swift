//
//  SuccessScreen.swift
//  HemoPlugin
//
//  Created by <PERSON> on 20/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct SuccessScreen: View {
    let title: String
    let subtitle: String
    let imageName: String
    let onDone: () -> Void

    var body: some View {
        VStack (alignment: .leading, spacing: Dimensions.spacing) {
            Text(title)
                .font(.header)
                .foregroundColor(.charcoalGrey)

            Text(subtitle)
                .font(.default)
                .foregroundColor(.charcoalGrey)

            Spacer()
            HStack (spacing: Dimensions.spacing) {
                Spacer()
                Image(imageName, bundle: HumaFoundationBundle.bundle)
                Spacer()
            }
            Spacer()
            Button(HumaStrings.commonActionDone, action: onDone)
                .pillStyle(.largeFill, fullWidth: true)
        }
        .padding(.horizontal, Dimensions.horizontalPadding)
        .padding(.vertical, Dimensions.verticalPadding)
    }
}

// Preview for SwiftUI canvas
struct SuccessScreen_Previews: PreviewProvider {
    static var previews: some View {
        SuccessScreen(
            title: "Success!",
            subtitle: "Your medication has been added successfully.",
            imageName: "checkmark.seal.fill",
            onDone: {}
        )
    }
}
