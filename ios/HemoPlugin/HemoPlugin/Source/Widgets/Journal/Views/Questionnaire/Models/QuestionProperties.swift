//
//  QuestionProperties.swift
//  Pods
//
//  Created by <PERSON> on 25/06/2025.
//

enum MultipleChoiceType: Equatable {
    case `default`
    case circle
}

protocol AnyQuestionProperties: Equatable {}

struct NumericQuestionProperties: AnyQuestionProperties {
    let placeholder: String
    let allowDecimals: Bool
    let accessoryText: String?

    init(placeholder: String, allowDecimals: Bool = false, accessoryText: String? = nil) {
        self.placeholder = placeholder
        self.allowDecimals = allowDecimals
        self.accessoryText = accessoryText
    }
}

struct DateQuestionProperties: AnyQuestionProperties {
    let range: ClosedRange<Date>?
}

struct MultipleChoiceQuestionProperties: AnyQuestionProperties {
    let type: MultipleChoiceType
}

struct IntoQuestionProperties: AnyQuestionProperties {
    let introImage: String?
}

struct AutoCompleteQuestionProperties: AnyQuestionProperties {
    let placeholder: String
    let allowScanning: Bool
}

struct PickerQuestionProperties: AnyQuestionProperties {
    let placeholder: String
}

struct MultiLineTextQuestionProperties: AnyQuestionProperties {
    let placeholder: String
}

struct ValueUnitQuestionProperties: AnyQuestionProperties {
    let placeholder: String
}
