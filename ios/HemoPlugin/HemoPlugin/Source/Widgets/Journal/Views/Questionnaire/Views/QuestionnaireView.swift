//
//  QuestionnaireView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct QuestionnaireView<Source: AnyQuestionSource>: View {
    let submissionViewID = "submissionView"
    let submitButtonID = "submitButton"

    @Environment(\.presentationMode) var presentationMode
    @StateObject private var viewModel: QuestionnaireViewModel
    @State private var questionHeight: CGFloat = 0
    private let source: Source
    @FocusState private var focusedField: String?
    @State private var keyboardDoneID: String? = nil

    init(source: Source) {
        _viewModel = StateObject(wrappedValue: QuestionnaireViewModel(questionSource: source))
        self.source = source
    }

    var body: some View {
        VStack {
            headerSection
            questionsSection
            footerSection
        }
        .padding(.horizontal, Dimensions.horizontalPadding)
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true)
        .onChange(of: viewModel.shouldDismiss) { shouldDismiss in
            handleDismiss(shouldDismiss)
        }
        .fullScreenCover(isPresented: $viewModel.showSuccessScreen, onDismiss: {
            presentationMode.wrappedValue.dismiss()
        }) {
            if let successScreen = source.makeSuccessScreen(viewModel: viewModel) {
                successScreen
            }
        }
        .toolbar { keyboardToolbar }
        .halfSheet(showSheet: $viewModel.showSheet) {
            confirmationDialogSection
        }
    }

    // MARK: - Sections

    private var headerSection: some View {
        HeaderView(
            title: source.headerConfiguration.title,
            style: .compact,
            rightButtonIcon: source.headerConfiguration.rightButtonIcon,
            onRightButtonTap: source.headerConfiguration.rightButtonAction
        )
    }

    private var questionsSection: some View {
        ScrollViewReader { proxy in
            ScrollView(showsIndicators: false) {
                VStack(alignment: .leading, spacing: 20) {
                    let orderedVisibleQuestions = viewModel.getOrderedVisibleQuestions()
                    ForEach(orderedVisibleQuestions, id: \.id) { question in
                        QuestionView(
                            question: question,
                            viewModel: viewModel,
                            isLastVisible: question.id == orderedVisibleQuestions.last?.id,
                            focusedField: $focusedField,
                            keyboardDoneID: $keyboardDoneID
                        )
                        .id("\(question.id)-\(viewModel.resetToken[question.id]?.uuidString ?? "0")")
                        .padding(.bottom, 20)
                        .background(
                            GeometryReader { geometry in
                                Color.clear
                                    .onAppear {
                                        questionHeight = geometry.size.height
                                    }
                            }
                        )
                    }
                    submissionSection(proxy: proxy)
                    Spacer(minLength: calculateSpacerHeight())
                }
                .id(submitButtonID)
            }
            .onChange(of: viewModel.scrollTrigger) { _ in
                scrollToLastQuestion(proxy: proxy)
            }
        }
    }

    private func submissionSection(proxy: ScrollViewProxy) -> some View {
        Group {
            if viewModel.showSubmitButton {
                if let customView = source.makeSubmissionView(viewModel: viewModel) {
                    customView
                        .id(submissionViewID)
                        .onAppear {
                            withAnimation { proxy.scrollTo(submissionViewID, anchor: .top) }
                        }
                } else {
                    defaultSubmissionView(proxy: proxy)
                }
            }
        }
    }

    private func defaultSubmissionView(proxy: ScrollViewProxy) -> some View {
        VStack(alignment: .leading, spacing: Dimensions.spacingMedium) {
            Text(HumaStrings.pluginHemophiliaConfirmTheInformationTitle)
                .font(.bold)
                .foregroundColor(Color.charcoalGrey)
            Text(HumaStrings.pluginHemophiliaConfirmTheInformationDescription)
                .font(.xSmall)
                .foregroundColor(Color.charcoalGrey)
            Button(HumaStrings.commonActionSubmit) {
                viewModel.submitQuestionnaire()
            }
            .pillStyle(isLoading: $viewModel.isSubmitting, style: .largeFill, fullWidth: true)
        }
        .onAppear {
            withAnimation { proxy.scrollTo(submitButtonID, anchor: .bottom) }
        }
    }

    private var footerSection: some View {
        Group {
            if let footerView = source.makeFooterView(viewModel: viewModel) {
                footerView
                    .padding(.vertical, Dimensions.spacingMedium)
            }
        }
    }

    private var keyboardToolbar: some ToolbarContent {
        ToolbarItemGroup(placement: .keyboard) {
            HStack {
                Spacer()
                Button(HumaStrings.commonActionDone) {
                    if let id = focusedField { keyboardDoneID = id }
                    focusedField = nil
                }
                .font(.semibold)
                .foregroundColor(.charcoalGrey)
            }
        }
    }

    private var confirmationDialogSection: some View {
        Group {
            if let viewModel = source.confirmationDialogViewModel {
                ConfirmationDialogView(viewModel: viewModel)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(.white)
            }
        }
    }

    // MARK: - Helpers

    private func handleDismiss(_ shouldDismiss: Bool) {
        if shouldDismiss {
            if let successScreen = source.makeSuccessScreen(viewModel: viewModel) {
                viewModel.showSuccessScreen = true
            } else {
                presentationMode.wrappedValue.dismiss()
            }
        }
    }

    private func scrollToLastQuestion(proxy: ScrollViewProxy) {
        let orderedVisibleQuestions = viewModel.getOrderedVisibleQuestions()
        if let lastQuestion = orderedVisibleQuestions.last {
            withAnimation { proxy.scrollTo(lastQuestion.id, anchor: .top) }
        }
    }

    private func calculateSpacerHeight() -> CGFloat {
        let screenHeight = UIScreen.main.bounds.height
        let spacerHeight = (screenHeight - questionHeight) / 2
        return max(spacerHeight, 0)
    }
}

private extension Notification.Name {
    static let numericFieldDone = Notification.Name("NumericFieldDone")
}

struct DefaultQuestionnaireView: View {
    var body: some View {
        QuestionnaireView(source: PreviewModels.hemoProfileQuestionSource)
    }
}

struct QuestionnaireView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            DefaultQuestionnaireView()
        }
    }
}
