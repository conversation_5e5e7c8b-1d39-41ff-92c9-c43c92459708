//
//  AnyQuestionSource.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

extension AnyQuestionSource {
    @ViewBuilder func makeSubmissionView(viewModel: QuestionnaireViewModel) -> AnyView? {
        return nil
    }

    @ViewBuilder func makeFooterView(viewModel: QuestionnaireViewModel) -> AnyView? {
        return nil
    }

    @ViewBuilder func makeSuccessScreen(viewModel: QuestionnaireViewModel) -> AnyView? {
        return nil
    }
}
