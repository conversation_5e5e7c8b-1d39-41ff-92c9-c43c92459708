//
// SliderQuestionView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.

import SwiftUI

struct SliderQuestionView: View {
    private let question: BaseQuestion
    @State private var value: Double
    @ObservedObject private var viewModel: QuestionnaireViewModel
    private let range: ClosedRange<Double>
    private let startLabel: String
    private let endLabel: String
    @State private var validationError: String?

    init(
        question: BaseQuestion,
        viewModel: QuestionnaireViewModel,
        range: ClosedRange<Double>,
        startLabel: String,
        endLabel: String
    ) {
        self.question = question
        self.viewModel = viewModel
        self.range = range
        self.startLabel = startLabel
        self.endLabel = endLabel

        let intialValue = question.initialValue as? Double
        self._value = State(initialValue: intialValue ?? (range.upperBound + range.lowerBound) / 2)
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HumaSliderView(
                value: $value,
                range: range,
                startLabel: startLabel,
                endLabel: endLabel,
                onDragEnded: {
                    submitAnswer()
                }
            )
            .onChange(of: value) { _ in
                // Clear validation errors when user interacts
                validationError = nil
                // Answer will be submitted when user lifts thumb, not on every change
            }

            if let error = validationError {
                Text(error)
                    .font(.default)
                    .foregroundColor(.red)
                    .padding(.horizontal, 4)
            }
        }
    }

    private func validateInput() -> Bool {
        // Clear previous errors
        validationError = nil

        // For slider, we mainly check if the value is within the range
        // which should always be true due to the slider constraints
        if value < range.lowerBound || value > range.upperBound {
            validationError = HumaStrings
                .commonNumberRangeError(
                    range.lowerBound.string(maximumDecimalPlaces: 0),
                    range.upperBound.string(maximumDecimalPlaces: 0)
                )
            return false
        }

        return true
    }

    private func submitAnswer() {
        if validateInput() {
            viewModel.submitAnswer(answer: .init(question: question, value: value))
        }
    }
}
