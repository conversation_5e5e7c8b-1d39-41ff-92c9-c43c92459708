//
// ValidationRule.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.

import Foundation

enum ValidationRule {
    case required(message: String? = nil)
    case numeric(message: String? = nil)
    case custom((Any?) -> Bool, message: String? = nil)
    
    func validate(_ value: Any?) -> Bool {
        switch self {
        case .required:
            if let str = value as? String {
                return !str.isEmpty
            }
            return value != nil
            
        case .numeric:
            guard let str = value as? String else { return false }
            return Double(str) != nil
            
        case .custom(let validator, _):
            return validator(value)
        }
    }
    
    func errorMessage() -> String {
        switch self {
        case .required(let message):
            return message ?? HumaStrings.validationCommonMandatory

        case .numeric(let message):
            return message ?? "Please enter a valid number"
            
        case .custom(_, let message):
            return message ?? "Validation failed"
        }
    }
}
