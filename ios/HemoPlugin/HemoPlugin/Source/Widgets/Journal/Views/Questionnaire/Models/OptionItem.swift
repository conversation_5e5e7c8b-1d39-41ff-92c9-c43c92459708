//
// OptionItem.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Foundation

struct OptionItem: Codable, Equatable, Identifiable, Hashable {
    let id = UUID()
    let value: String
    let title: String

    enum CodingKeys: CodingKey {
        case value
        case title
    }

    static func == (lhs: OptionItem, rhs: OptionItem) -> Bool {
        lhs.value == rhs.value
    }
}
