//
// Question.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Foundation

protocol Question {
    var id: String { get }
    var title: String { get }
    var subtitle: String? { get }
    var type: QuestionType { get }
    var isMandatory: Bool { get }
    var validationRules: [ValidationRule] { get }
    var nextQuestionID: String? { get }
    var branchingLogic: [String: String]? { get }
    var initialValue: Any? { get }
}

// Base implementation for questions
struct BaseQuestion: Question, Identifiable {
    let id: String
    let title: String
    let subtitle: String?
    let type: QuestionType
    let isMandatory: Bool
    let validationRules: [ValidationRule]
    let nextQuestionID: String?
    let branchingLogic: [String: String]?
    let initialValue: Any?

    // Add an initializer with a default nil value for initialValue
    init(id: String, 
         title: String, 
         subtitle: String? = nil,
         type: QuestionType,
         isMandatory: Bool = true,
         validationRules: [ValidationRule] = [],
         nextQuestionID: String? = nil,
         branchingLogic: [String: String]? = nil,
         initialValue: Any? = nil
    ) {
        self.id = id
        self.title = title
        self.subtitle = subtitle
        self.type = type
        self.isMandatory = isMandatory
        self.validationRules = validationRules
        self.nextQuestionID = nextQuestionID
        self.branchingLogic = branchingLogic
        self.initialValue = initialValue
    }
}
