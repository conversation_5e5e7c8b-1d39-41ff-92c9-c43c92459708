//
// QuestionView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.

import SwiftUI

struct QuestionView: View {
    let question: BaseQuestion
    @ObservedObject var viewModel: QuestionnaireViewModel
    let isLastVisible: Bool
    var focusedField: FocusState<String?>.Binding
    var keyboardDoneID: Binding<String?>

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            if case .intro = question.type {
                // Do not show title/subtitle for intro
            } else {
                Text(question.title)
                    .font(.large)
                    .foregroundColor(Color.charcoalGrey)
                
                if let subtitle = question.subtitle, !subtitle.isEmpty {
                    HTMLText(text: subtitle)
                        .font(.default)
                        .padding(.bottom, 4)
                }
            }

            switch question.type {
            case .intro:
                IntroQuestionView(question: question, viewModel: viewModel)
            case .numeric:
                NumericQuestionView(
                    question: question,
                    viewModel: viewModel,
                    focusedField: focusedField,
                    keyboardDoneID: keyboardDoneID
                )
            case .booleanChoice:
                BooleanChoiceQuestionView(question: question, viewModel: viewModel)
            case .singleChoice:
                SingleChoiceQuestionView(question: question, viewModel: viewModel)
            case .multipleChoice(let properties):
                MultipleChoiceQuestionView(question: question, viewModel: viewModel, choiceType: properties.type)
            case .date(let properties):
                DateQuestionView(question: question, viewModel: viewModel, range: properties.range)
            case .time:
                TimeQuestionView(question: question, viewModel: viewModel)
            case .autocompleteSearch:
                AutocompleteQuestionView(question: question, viewModel: viewModel)
            case .picker:
                PickerQuestionView(question: question, viewModel: viewModel)
            case .valueUnit:
                ValueUnitQuestionView(
                    question: question,
                    viewModel: viewModel,
                    focusedField: focusedField,
                    keyboardDoneID: keyboardDoneID
                )
            case .multilineText:
                MultilineTextQuestionView(
                    question: question,
                    viewModel: viewModel,
                    focusedField: focusedField,
                    keyboardDoneID: keyboardDoneID
                )
            case .slider:
                SliderQuestionView(
                    question: question,
                    viewModel: viewModel,
                    range: 0...10,
                    startLabel: HumaStrings.pluginHemophiliaNoPain,
                    endLabel: HumaStrings.ohsAnswersPainSevere
                )
            case .photo:
                PhotoPickerQuestionView(question: question, viewModel: viewModel)
            }
            
            if !question.isMandatory && isLastVisible {
                Button(HumaStrings.commonSkipTheQuestion) {
                    viewModel.skipQuestion(for: question.id)
                }
                .underlineStyle()
                .padding(.top, 8)
            }
        }
    }
}
