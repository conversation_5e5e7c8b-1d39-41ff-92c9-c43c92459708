//
// Answer.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Foundation
import HumaFoundation

struct Answer {
    let question: Question
    let value: Any

    // Initialize with optional secondaryValue
    init(question: Question, value: Any) {
        self.question = question
        self.value = value
    }

    // Helper to handle different types of values
    var stringValue: String? {
        if let str = value as? String {
            return str
        } else if let optionItem = value as? OptionItem {
            return optionItem.value
        } else if let date = value as? Date {
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            return formatter.string(from: date)
        } else if let array = value as? [OptionItem] {
            return array.map(\.value).joined(separator: ", ")
        } else {
            return String(describing: value)
        }
    }
}

extension Answer {
    var answerBody: AnswerBody? {
        switch question.type {
        case .numeric:
            guard let primaryValue = self.stringValue?.doubleValue else { return nil }
            return .init(
                answersList:[primaryValue.string(maximumDecimalPlaces: 1)],
                question: question.title,
                questionId: question.id,
                format: QuestionFormat.text.rawValue,
                extraFields: nil
            )
        case .booleanChoice:
            guard let primaryValue = self.stringValue else { return nil }
            return .init(
                answersList:[primaryValue == "YES" ? "true" : "false"],
                question: question.title,
                questionId: question.id,
                format: QuestionFormat.boolean.rawValue,
                extraFields: nil
            )
        case .singleChoice:
            guard let primaryValue = self.stringValue else { return nil }
            return .init(
                answersList:[primaryValue],
                question: question.title,
                questionId: question.id,
                format: QuestionFormat.textChoice.rawValue,
                extraFields: [
                    "selectionCriteria": "SINGLE",
                    "selectedChoices": [primaryValue]
                ]
            )
        case .multipleChoice:
            guard let primaryValue = self.value as? [OptionItem] else { return nil }
            return .init(
                answersList: primaryValue.map({ $0.value }),
                question: question.title,
                questionId: question.id,
                format: QuestionFormat.textChoice.rawValue,
                extraFields: [
                    "selectionCriteria": "MULTIPLE",
                    "selectedChoices": primaryValue.map({ $0.value })
                ]
            )
        case .date:
            guard let primaryValue = self.stringValue else { return nil }
            return .init(
                answersList: [primaryValue],
                question: question.title,
                questionId: question.id,
                format: QuestionFormat.date.rawValue,
                extraFields: nil
            )
        case .time:
            guard let primaryValue = self.stringValue else { return nil }
            return .init(
                answersList: [primaryValue],
                question: question.title,
                questionId: question.id,
                format: QuestionFormat.timeSeconds.rawValue,
                extraFields: nil
            )
        case .autocompleteSearch:
            guard let primaryValue = self.value as? CMSMedicationResponse.MedicationItem else { return nil }
            return .init(
                answersList:[primaryValue.title],
                question: question.title,
                questionId: question.id,
                format: QuestionFormat.autocompleteText.rawValue,
                extraFields: [
                    "selectionCriteria": "SINGLE",
                    "selectedChoices": [primaryValue.title]
                ]
            )
        case .picker:
            guard let primaryValue = self.value as? [String] else { return nil }
            return .init(
                answersList: primaryValue,
                question: question.title,
                questionId: question.id,
                format: QuestionFormat.textChoice.rawValue,
                extraFields: [
                    "selectionCriteria": "SINGLE",
                    "selectedChoices": primaryValue
                ]
            )
        default:
            return nil
        }
    }
}

