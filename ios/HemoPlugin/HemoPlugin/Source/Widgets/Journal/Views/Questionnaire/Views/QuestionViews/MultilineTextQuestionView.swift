//
// MultilineTextQuestionView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct MultilineTextQuestionView: View {
    private let question: BaseQuestion
    @ObservedObject private var viewModel: QuestionnaireViewModel
    var focusedField: FocusState<String?>.Binding
    var keyboardDoneID: Binding<String?>
    @State private var value: String?
    @State private var validationError: String?

    private var questionProperties: MultiLineTextQuestionProperties? {
        guard case .multilineText(let questionProperties) = question.type else { return nil }
        return questionProperties
    }

    init(question: BaseQuestion, viewModel: QuestionnaireViewModel, focusedField: FocusState<String?>.Binding, keyboardDoneID: Binding<String?>) {
        self.question = question
        self.viewModel = viewModel
        self.focusedField = focusedField
        self.keyboardDoneID = keyboardDoneID
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            ZStack(alignment: .topLeading) {
                TextEditor(text: $value.toUnwrapped(defaultValue: ""))
                    .font(.default)
                    .focused(focusedField, equals: question.id)
                    .frame(minHeight: 100)
                    .padding(5)
                    .background(Color.white)
                    .foregroundColor(Color.charcoalGrey)
                    .onChange(of: value) { _ in
                        // Clear validation errors when user types
                        validationError = nil
                    }
                if (value == nil || value?.isEmpty == true) {
                    Text(questionProperties?.placeholder ?? "")
                        .foregroundColor(Color.gray.opacity(0.8))
                        .font(.default)
                        .padding(.horizontal, 10)
                        .padding(.top, 13)
                        .allowsHitTesting(false)
                }
            }
            .overlay(
                RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                    .strokeBorder(validationError != nil ? Color.red : Color.veryLightGrey, style: StrokeStyle(lineWidth: Dimensions.lineWidth))
            )
            
            if let error = validationError {
                Text(error)
                    .font(.default)
                    .foregroundColor(.red)
                    .padding(.horizontal, 4)
            }
        }
        .onChange(of: keyboardDoneID.wrappedValue) { doneID in
            if doneID == question.id {
                if validateInput() {
                    submitAnswer()
                }
                keyboardDoneID.wrappedValue = nil
            }
        }
    }

    private func validateInput() -> Bool {
        // Clear previous errors
        validationError = nil
        
        guard let value = value, !value.isEmpty else {
            if question.isMandatory {
                return false
            }
            return true
        }
        
        // Check against all validation rules
        for rule in question.validationRules {
            if !rule.validate(value) {
                validationError = rule.errorMessage()
                return false
            }
        }
        
        return true
    }
    
    private func submitAnswer() {
        viewModel.submitAnswer(answer: .init(question: question, value: value))
    }
}
