//
// NumericQuestionView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct NumericQuestionView: View {
    private let question: BaseQuestion
    @ObservedObject private var viewModel: QuestionnaireViewModel
    var focusedField: FocusState<String?>.Binding
    var keyboardDoneID: Binding<String?>
    @State private var value: String? = nil
    @State private var validationError: String?
    private var initialValue: String? { question.initialValue as? String }
    // Track if user has pressed Next
    @State private var hasPressedNext: Bool = false

    private var questionProperties: NumericQuestionProperties? {
        guard case .numeric(let questionProperties) = question.type else { return nil }
        return questionProperties
    }

    init(
        question: BaseQuestion,
        viewModel: QuestionnaireViewModel,
        focusedField: FocusState<String?>.Binding,
        keyboardDoneID: Binding<String?>
    ) {
        self.question = question
        self.viewModel = viewModel
        self.focusedField = focusedField
        self.keyboardDoneID = keyboardDoneID
        self._value = State(initialValue: question.initialValue as? String)
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                TextField(questionProperties?.placeholder ?? "", text: $value.toUnwrapped(defaultValue: ""))
                    .font(.default)
                    .keyboardType(.decimalPad)
                    .focused(focusedField, equals: question.id)
                    .padding()
                    .foregroundColor(Color.charcoalGrey)
                    .onChange(of: value) { newValue in
                        _ = validateInput() // Set validationError live while typing
                        // If user changes input after pressing Next, show the button again
                        if hasPressedNext {
                            hasPressedNext = false
                        }
                    }
                if let accessoryString = questionProperties?.accessoryText {
                    Text(accessoryString)
                        .font(.default)
                        .foregroundColor(.charcoalGrey)
                        .padding(.trailing, Dimensions.horizontalPadding)
                }
            }
            .overlay(
                RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                    .strokeBorder(validationError != nil ? Color.red : Color.veryLightGrey, style: StrokeStyle(lineWidth: Dimensions.lineWidth))
            )
            if let error = validationError {
                Text(error)
                    .font(.default)
                    .foregroundColor(.red)
                    .padding(.horizontal, 4)
            }

            // Show Next button only if not just after pressing Next and not empty
            if !hasPressedNext && !(value?.isEmpty ?? true) {
                Button(HumaStrings.commonActionNext) {
                    if validateInput() {
                        submitAnswer()
                        focusedField.wrappedValue = nil
                    }
                }
                .pillStyle(.largeFill, fullWidth: true)
                .disabled(question.isMandatory && !isInputValid())
                .opacity(question.isMandatory && !isInputValid() ? 0.6 : 1.0)
                .padding(.top, 16)
            }
        }
        .onChange(of: keyboardDoneID.wrappedValue) { doneID in
            if doneID == question.id {
                if validateInput() {
                    submitAnswer()
                    // Removed lastSubmittedValue assignment
                    // Dismiss keyboard
                    focusedField.wrappedValue = nil
                }
                keyboardDoneID.wrappedValue = nil
            }
        }
        .onChange(of: focusedField.wrappedValue) { newValue in
            if newValue == question.id, let value = value, !value.isEmpty {
                viewModel.resetSubsequentQuestions(after: question.id)
            }
        }
    }

    // Pure function for validation, does not modify state
    private func isInputValid() -> Bool {
        guard let inputValue = value, !inputValue.isEmpty else {
            return !question.isMandatory
        }
        for rule in question.validationRules {
            if !rule.validate(inputValue) {
                return false
            }
        }
        return true
    }

    private func validateInput() -> Bool {
        // Clear previous errors
        validationError = nil
        guard let inputValue = value, !inputValue.isEmpty else {
            if question.isMandatory {
                return false
            }
            return true
        }
        for rule in question.validationRules {
            if !rule.validate(inputValue) {
                validationError = rule.errorMessage()
                return false
            }
        }
        return true
    }

    private func submitAnswer() {
        // Convert to Double before submitting to ensure it's a valid number
        if let value = value?.doubleValue {
            hasPressedNext = true
            viewModel.submitAnswer(answer: .init(question: question, value: value.string(maximumDecimalPlaces: 1)))
        }
    }
}

private struct NumericQuestionView_Previews: View {

    @State var text = ""
    @FocusState var focusedNumericField: String?

    var body: some View {
        NumericQuestionView(
            question: BaseQuestion(
                id: HemoProfileQuestionnaireSource.QuestionID.weight.rawValue,
                title: HumaStrings.moduleWeightInputSubtitle,
                type: .numeric(.init(placeholder: HumaStrings.moduleWeightInputSubtitle)),
                validationRules: [.required(), .numeric()],
                nextQuestionID: HemoProfileQuestionnaireSource.QuestionID.hemophiliaType.rawValue
            ),
            viewModel: .init(questionSource: PreviewModels.hemoProfileQuestionSource),
            focusedField: $focusedNumericField,
            keyboardDoneID: .constant(nil)
        )
    }
}

#Preview {
    NumericQuestionView_Previews()
        .padding()
}
