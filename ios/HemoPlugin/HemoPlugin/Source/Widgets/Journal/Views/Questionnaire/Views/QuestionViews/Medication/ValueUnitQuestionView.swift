//
// ValueUnitQuestionView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct ValueUnitQuestionView: View {
    // MARK: - Properties
    let question: Question
    @State var value: MedicationDosageV2?
    @ObservedObject var viewModel: QuestionnaireViewModel
    var focusedField: FocusState<String?>.Binding
    var keyboardDoneID: Binding<String?>
    private var options: [OptionItem]
    @State var selection: OptionItem?
    
    // UI state
    @FocusState private var isTextFieldFocused: Bool
    @State private var showPicker: Bool? = nil
    @State private var selectedIndex = 0
    @State private var validationError: String?
    @State private var dosageText: String = ""
    @State private var hasPressedNext: Bool = false

    init(
        question: Question,
        viewModel: QuestionnaireViewModel,
        focusedField: FocusState<String?>.Binding,
        keyboardDoneID: Binding<String?>
    ) {
        self.question = question
        self.viewModel = viewModel
        self.focusedField = focusedField
        self.keyboardDoneID = keyboardDoneID
        self.options = viewModel.getOptions(for: question.id)
        self._value = State(initialValue: question.initialValue as? MedicationDosageV2)
    }

    private var selectionText: String {
        selection?.title ?? HumaStrings.medicationInputUnitPlaceholder
    }

    // MARK: - Body
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            TextField(HumaStrings.medicationDosagePlaceholder, text: $dosageText)
                .font(.default)
                .keyboardType(.numberPad)
                .focused(focusedField, equals: question.id)
                .padding()
                .overlay(
                    RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                        .strokeBorder(validationError != nil ? Color.red : Color.veryLightGrey, style: StrokeStyle(lineWidth: Dimensions.lineWidth))
                )
                .foregroundColor(.charcoalGrey)
                .onChange(of: dosageText) { _ in
                    validationError = nil
                }
                .onChange(of: keyboardDoneID.wrappedValue) { doneID in
                    if doneID == question.id {
                        updateValueFromText()
                        keyboardDoneID.wrappedValue = nil
                    }
                }

            Button(
                action: {
                    if let selection = self.selection,
                       let index = options.firstIndex(where: { $0.value == selection.value }) {
                        selectedIndex = index
                    }
                    showPicker = true
                }) {
                    Text(selectionText)
                        .frame(minWidth: 0, maxWidth: .infinity, alignment: .leading)
                        .font(.default)
                        .foregroundColor(selection == nil ? .lightGrey3 : Color.charcoalGrey)
                        .padding()
                        .overlay(
                            RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                                .strokeBorder(validationError != nil ? Color.red : Color.veryLightGrey, style: StrokeStyle(lineWidth: Dimensions.lineWidth))
                        )
                }
                .onChange(of: selection) { newValue in
                    // Clear validation errors when user selects a unit
                    validationError = nil
                }

            if let error = validationError {
                Text(error)
                    .font(.default)
                    .foregroundColor(.red)
                    .padding(.horizontal, 4)
            }

            if !hasPressedNext {
                // Submit button
                Button(HumaStrings.commonActionNext) {
                    if validateInput() {
                        submitAnswer()
                    }
                }
                .pillStyle(.largeFill, fullWidth: true)
                .disabled(disableNextButton)
                .opacity(disableNextButton ? 0.6 : 1.0)
                .padding(.top, 16)
            }
        }
        .onAppear {
            // Initialize the UI from the initial value
            initializeFromValue()
            if value == nil {
                focusedField.wrappedValue = question.id
            }
        }
        .halfSheet(showSheet: $showPicker) {
            UnitPickerSheetView(
                options: options,
                onDone: { selectedIndex in
                    if !options.isEmpty {
                        self.selectedIndex = 0
                        self.selectedIndex = selectedIndex
                        self.selection = nil
                        self.selection = options[selectedIndex]
                        showPicker = false
                        focusedField.wrappedValue = nil
                        updateValueFromText()
                    }
                },
                onCancel: {
                    showPicker = false
                }
            )
        } onDismiss: {
            if value == nil {
                focusedField.wrappedValue = question.id
            }
        }
    }
    
    // Initialize UI elements from the initial MedicationDosageV2 value
    private func initializeFromValue() {
        if let medicationDosage = value {
            // Set the dosage text
            dosageText = "\(medicationDosage.value)"
            // Find and set the corresponding unit option
            if let customUnit = medicationDosage.customUnit {
                if let unitOption = options.first(where: { $0.title == customUnit }) {
                    selection = unitOption
                }
            } else {
                if let unitOption = options.first(where: { $0.value == medicationDosage.unit.rawValue }) {
                    selection = unitOption
                }
            }
        }
    }
    
    // Update the MedicationDosageV2 value from the text field
    private func updateValueFromText() {
        if let dosageValue = Double(dosageText) {
            if let unit = selection,
               let unitEnum = MedicationDosageV2.Unit(rawValue: unit.value) {
                value = MedicationDosageV2(value: dosageValue, unit: unitEnum)
            } else if let currentValue = value {
                // Keep the current unit if selection is nil
                value = MedicationDosageV2(value: dosageValue, unit: currentValue.unit)
            } else {
                // Default to 'other' if no unit is available
                value = MedicationDosageV2(value: dosageValue, unit: .other)
            }
            hasPressedNext = false
        }
    }

    private func validateInput() -> Bool {
        // Clear previous errors
        validationError = nil
        
        // Check if value is provided
        guard let dosageValue = value?.value else {
            if question.isMandatory {
                validationError = HumaStrings.medicationv2InputErrorInvalidDosage
                return false
            }
            return true
        }
        
        // Check if unit is selected
        if selection == nil && question.isMandatory {
            validationError = HumaStrings.medicationv2InputErrorInvalidDosage
            return false
        }
        
        // Check other validation rules
        for rule in question.validationRules {
            if !rule.validate("\(dosageValue)") {
                validationError = rule.errorMessage()
                return false
            }
        }
        
        return true
    }

    /// Submits the answer to the view model
    private func submitAnswer() {
        if let value = self.value?.value, let selection = self.selection,
            let unit = MedicationDosageV2.Unit(rawValue: selection.value) {
            let customUnit: String? = unit == .custom ? selection.title : nil
            hasPressedNext = true
            UIApplication.shared.hideKeyboard()
            viewModel
                .submitAnswer(
                    answer: .init(
                        question: question,
                        value: MedicationDosageV2(value: value, unit: unit, customUnit: customUnit)
                    )
                )
        }
    }

    private var disableNextButton: Bool {
        guard let selection = selection else { return true }
        return question.isMandatory && dosageText.isEmpty
    }
}

#Preview {
    ValueUnitQuestionView_Preview()
        .padding()
}
