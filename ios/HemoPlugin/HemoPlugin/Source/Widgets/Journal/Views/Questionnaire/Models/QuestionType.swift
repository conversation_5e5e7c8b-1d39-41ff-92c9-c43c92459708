//
// QuestionType.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Foundation

enum QuestionType: Equatable {
    case intro(IntoQuestionProperties)
    case booleanChoice
    case singleChoice
    case multipleChoice(MultipleChoiceQuestionProperties)
    case date(DateQuestionProperties)
    case time
    case autocompleteSearch(AutoCompleteQuestionProperties)
    case picker(PickerQuestionProperties)
    case numeric(NumericQuestionProperties)
    case valueUnit(ValueUnitQuestionProperties)
    case multilineText(MultiLineTextQuestionProperties)
    case slider
    case photo

    static func == (lhs: QuestionType, rhs: QuestionType) -> Bool {
        switch (lhs, rhs) {
        case (.intro, .intro),
             (.booleanChoice, .booleanChoice),
             (.singleChoice, .singleChoice),
             (.time, .time),
             (.autocompleteSearch, .autocompleteSearch),
             (.picker, .picker),
             (.numeric, .numeric),
             (.valueUnit, .valueUnit),
             (.multilineText, .multilineText),
             (.slider, .slider),
             (.photo, .photo):
            return true
        case let (.multipleChoice(a), .multipleChoice(b)):
            return a == b
        case let (.date(a), .date(b)):
            return a == b
        default:
            return false
        }
    }
}
