//
//  ValueUnitQuestionView+Preview.swift
//  HemoPlugin
//
//  Created by <PERSON> on 29/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//
import SwiftUI

struct ValueUnitQuestionView_Preview: View {
    @State var medicineDosage = MedicationDosageV2(value: 25, unit: .application)
    @FocusState private var focusedField: String?
    @State private var keyboardDoneID: String? = nil

    var body: some View {
        ValueUnitQuestionView(
            question: BaseQuestion(
                id: "dosage_id",
                title: HumaStrings.medicationDosageHeading,
                type: .valueUnit(.init(placeholder: "")),
                validationRules: [.required(), .numeric()],
                nextQuestionID: "frequency_id",
                initialValue: medicineDosage
            ),
            viewModel: .init(questionSource: PreviewModels.hemoProfileQuestionSource),
            focusedField: $focusedField,
            keyboardDoneID: $keyboardDoneID
        )
    }
}
