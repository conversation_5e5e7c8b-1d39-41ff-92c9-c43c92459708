//
//  DateQuestionView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 01/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct DateQuestionView: View {
    private let question: BaseQuestion
    @ObservedObject private var viewModel: QuestionnaireViewModel
    private let range: ClosedRange<Date>?
    @State private var date: Date?
    @State private var showDatePicker: Bool? = nil
    @State private var tempDate: Date = Date()
    @State private var validationError: String?
    // Track if user has pressed Next
    @State private var hasPressedNext: Bool = false

    init(question: BaseQuestion, viewModel: QuestionnaireViewModel, range: ClosedRange<Date>?) {
        self.question = question
        self.viewModel = viewModel
        self.range = range
        self._date = State(initialValue: question.initialValue as? Date)
    }

    var formattedDate: String {
        if let date = date {
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            return formatter.string(from: date)
        } else {
            return HumaStrings.dateQuest<PERSON>Placeholder
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Button(action: {
                tempDate = date ?? Date()
                
                // Ensure initial date is within range constraints if they exist
                if let dateRange = range {
                    if tempDate < dateRange.lowerBound {
                        tempDate = dateRange.lowerBound
                    } else if tempDate > dateRange.upperBound {
                        tempDate = dateRange.upperBound
                    }
                }
                
                showDatePicker = true
            }) {
                HStack {
                    Text(formattedDate)
                        .frame(minWidth: 0, maxWidth: .infinity, alignment: .leading)
                        .font(.default)
                        .foregroundColor(date == nil ? .lightGrey3 : Color.charcoalGrey)
                        .padding()

                    Image(HumaAssets.icCalendar.name, bundle: HumaFoundationBundle.bundle)
                        .resizable()
                        .frame(square: 16)
                        .padding(.trailing, Dimensions.horizontalPadding)
                }
                .overlay(
                    RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                        .strokeBorder(validationError != nil ? Color.red : Color.veryLightGrey, style: StrokeStyle(lineWidth: Dimensions.lineWidth))
                )
            }
            
            if let error = validationError {
                Text(error)
                    .font(.default)
                    .foregroundColor(.red)
                    .padding(.horizontal, 4)
            }

            if !question.isMandatory {
                Button(HumaStrings.commonSkipTheQuestion) {
                    viewModel.skipQuestion(for: question.id)
                }
                .padding(.top, 8)
                .foregroundColor(.charcoalGrey)
            }

            if !hasPressedNext && date != nil {
                Button(HumaStrings.commonActionNext) {
                    submitAnswers()
                }
                .pillStyle(.largeFill, fullWidth: true)
                .disabled(question.isMandatory && (date == nil))
                .opacity(question.isMandatory && (date == nil) ? 0.6 : 1.0)
                .padding(.top, 16)
            }
        }
        .halfSheet(showSheet: $showDatePicker) {
            VStack(spacing: 0) {
                // Toolbar
                HStack {
                    Button(HumaStrings.commonActionCancel) {
                        showDatePicker = false
                    }
                    .font(.default)
                    .foregroundColor(.charcoalGrey)

                    Spacer()

                    Text(HumaStrings.dateQuestionPlaceholder)
                        .font(.bold)

                    Spacer()

                    Button(HumaStrings.commonActionDone) {
                        date = tempDate
                        showDatePicker = false
                        submitAnswers()
                    }
                    .font(.default)
                    .foregroundColor(.charcoalGrey)
                }
                .padding()
                .background(Color(.systemGroupedBackground))

                
                // Date picker with correct optional range handling
                if let dateRange = range {
                    DatePicker("", selection: $tempDate, in: dateRange, displayedComponents: [.date])
                        .datePickerStyle(WheelDatePickerStyle())
                        .labelsHidden()
                        .padding()
                } else {
                    DatePicker("", selection: $tempDate, displayedComponents: [.date])
                        .datePickerStyle(WheelDatePickerStyle())
                        .labelsHidden()
                        .padding()
                }
            }
            .background(Color(.systemGroupedBackground))
        }
    }
    
    private func validateInput() -> Bool {
        // Clear previous errors
        validationError = nil
        
        guard let selectedDate = date else {
            if question.isMandatory {
                return false
            }
            return true
        }
        
        // Check against all validation rules
        for rule in question.validationRules {
            if !rule.validate(selectedDate) {
                validationError = rule.errorMessage()
                return false
            }
        }
        
        return true
    }

    private func submitAnswers() {
        if validateInput() {
            hasPressedNext = true
            UIApplication.shared.hideKeyboard()
            viewModel.submitAnswer(answer: .init(question: question, value: date))
        }
    }
}
