//
//  BooleanChoiceQuestionView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 01/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct BooleanChoiceQuestionView: View {
    private let question: BaseQuestion
    private let options: [OptionItem] = [
        .init(value: "YES", title: HumaStrings.commonActionYes),
        .init(value: "NO", title: HumaStrings.commonActionNo)
    ]
    @ObservedObject private var viewModel: QuestionnaireViewModel
    @State private var selectedOptionState: OptionItem? {
        didSet {
            submitAnswers()
        }
    }
    // Track if user has pressed Next
    @State private var hasPressedNext: Bool = false

    init(
        question: BaseQuestion,
        viewModel: QuestionnaireViewModel
    ) {
        self.question = question
        self.viewModel = viewModel
        let filteredOptions = options.first { $0.value == (question.initialValue as? String) }
        self._selectedOptionState = State(initialValue: filteredOptions)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            ForEach(options, id: \.value) { option in
                Button(option.title) {
                    selectedOptionState = option
                }
                .pillStyle(selectedOptionState?.value == option.value ? .largeFill : .large, fullWidth: true)
            }

            if !hasPressedNext && question.initialValue != nil {
                // Submit button
                Button(HumaStrings.commonActionNext) {
                    submitAnswers()
                }
                .pillStyle(.largeFill, fullWidth: true)
                .disabled(question.isMandatory && (selectedOptionState?.value.isEmpty ?? true))
                .opacity(question.isMandatory && (selectedOptionState?.value.isEmpty ?? true) ? 0.6 : 1.0)
                .padding(.top, 16)
            }
        }
    }

    private func submitAnswers() {
        if let selectedOptionState = selectedOptionState {
            hasPressedNext = true
            UIApplication.shared.hideKeyboard()
            viewModel.submitAnswer(answer: .init(question: question, value: selectedOptionState))
        }

    }
}
