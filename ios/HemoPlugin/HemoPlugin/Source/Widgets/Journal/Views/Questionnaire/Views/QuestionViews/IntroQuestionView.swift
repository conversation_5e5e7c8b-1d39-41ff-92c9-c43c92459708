//
// IntroQuestionView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.

import SwiftUI

struct IntroQuestionView: View {
    private let question: BaseQuestion
    @ObservedObject private var viewModel: QuestionnaireViewModel
    
    @State private var showImage = false
    @State private var showTitle = false
    @State private var showSubtitle = false
    @State private var showButton = false
    // Track if user has pressed Next
    @State private var hasPressedNext: Bool = false
    // Track if Next was shown after animation
    @State private var lastSubmitted: Bool = false

    private let animationDuration: Double = 0.6

    init(question: BaseQuestion, viewModel: QuestionnaireViewModel) {
        self.question = question
        self.viewModel = viewModel
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            if case .intro(let questionProperties) = question.type,
                let image = questionProperties.introImage, showImage {
                HStack {
                    Spacer()
                    Image(image, bundle: HumaFoundationBundle.bundle)
                        .transition(.opacity)
                    Spacer()
                }
            }

            if showTitle {
                Text(question.title)
                    .font(.large)
                    .foregroundColor(.charcoalGrey)
                    .fixedSize(horizontal: false, vertical: true)
                    .transition(.move(edge: .bottom).combined(with: .opacity))
            }

            if let subtitle = question.subtitle, showSubtitle {
                HTMLText(text: subtitle)
                    .font(.default)
                    .fixedSize(horizontal: false, vertical: true)
                    .transition(.move(edge: .bottom).combined(with: .opacity))
            }
            if showButton && !hasPressedNext {
                Spacer()

                Button(HumaStrings.commonActionNext) {
                    submitAnswers()
                    hasPressedNext = true
                    lastSubmitted = true
                    // Dismiss keyboard (if any)
                    UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                }
                .pillStyle(.largeFill, fullWidth: true)
                .padding(.top, 16)
                .transition(.move(edge: .bottom).combined(with: .opacity))
            }
        }
        .onChange(of: showButton) { newValue in
            if hasPressedNext && newValue && !lastSubmitted {
                hasPressedNext = false
            }
            if newValue {
                lastSubmitted = false
            }
        }
        .onAppear {
            animateSequence()
        }
    }

    private func animateSequence() {
        withAnimation(.easeInOut(duration: animationDuration)) {
            showImage = true
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + (animationDuration - 0.1)) {
            withAnimation(.easeInOut(duration: animationDuration)) {
                showTitle = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + (animationDuration - 0.1)) {
                withAnimation(.easeInOut(duration: animationDuration)) {
                    showSubtitle = true
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + (animationDuration - 0.1)) {
                    withAnimation(.easeInOut(duration: animationDuration)) {
                        showButton = true
                    }
                }
            }
        }
    }

    private func submitAnswers() {
        viewModel.submitAnswer(answer: .init(question: question, value: ""))
    }
}
