//
// PickerQuestionView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.

import SwiftUI

struct PickerQuestionView: View {
    private let question: BaseQuestion
    @ObservedObject private var viewModel: QuestionnaireViewModel
    private var options: [OptionItem]
    @State private var selection: OptionItem?
    @State private var showPicker: Bool? = nil
    @State private var selectedIndex = 0
    // Track if user has pressed Next
    @State private var hasPressedNext: Bool = false

    private var questionProperties: PickerQuestionProperties? {
        guard case .picker(let questionProperties) = question.type else { return nil }
        return questionProperties
    }

    private var text: String {
        if let selection = self.selection {
            return selection.title
        } else {
            return questionProperties?.placeholder ?? ""
        }
    }

    init(question: BaseQuestion, viewModel: QuestionnaireViewModel) {
        self.question = question
        self.viewModel = viewModel
        self._selection = State(initialValue: question.initialValue as? OptionItem)
        self.options = viewModel.getOptions(for: question.id)
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Button(
                action: {
                    if let selection = self.selection,
                       let index = options.firstIndex(where: { $0.value == selection.value}) {
                        selectedIndex = index
                    }
                    showPicker = true
                }) {
                    Text(text)
                        .frame(minWidth: 0, maxWidth: .infinity, alignment: .leading)
                        .font(.default)
                        .foregroundColor(selection == nil ? .lightGrey3 : Color.charcoalGrey)
                        .padding()
                        .overlay(
                            RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                                .strokeBorder(Color.veryLightGrey, style: StrokeStyle(lineWidth: Dimensions.lineWidth))
                        )
                }

            if !question.isMandatory {
                Button(HumaStrings.commonSkipTheQuestion) {
                    viewModel.skipQuestion(for: question.id)
                }
                .padding(.top, 8)
                .foregroundColor(.charcoalGrey)
            }

            if !hasPressedNext && question.initialValue != nil {
                // Submit button
                Button(HumaStrings.commonActionNext) {
                    submitAnswers()
                }
                .pillStyle(.largeFill, fullWidth: true)
                .padding(.top, 16)
            }
        }
        .halfSheet(showSheet: $showPicker) {
            VStack(spacing: 0) {
                // Toolbar
                HStack {
                    Button(HumaStrings.commonActionCancel) {
                        showPicker = false
                    }
                    .foregroundColor(.charcoalGrey)

                    Spacer()

                    Text(questionProperties?.placeholder ?? "")
                        .font(.headline)

                    Spacer()

                    Button(HumaStrings.commonActionDone) {
                        if !options.isEmpty {
                            selection = options[selectedIndex]
                            showPicker = false
                            submitAnswers()
                        }
                    }
                    .foregroundColor(.charcoalGrey)
                }
                .padding()
                .background(Color(.systemGroupedBackground))

                // Option picker
                Picker("", selection: $selectedIndex) {
                    ForEach(0..<options.count, id: \.self) { index in
                        Text(options[index].title)
                            .tag(index)
                    }
                }
                .pickerStyle(WheelPickerStyle())
                .frame(height: 200)
                .background(Color(.systemGroupedBackground))
                .padding()
            }
            .background(Color(.systemGroupedBackground))
        }
    }

    private func submitAnswers() {
        if let selection = self.selection {
            hasPressedNext = true
            UIApplication.shared.hideKeyboard()
            viewModel.submitAnswer(answer: .init(question: question, value: selection))
        }
    }
}
