//
// HemoProfileDetailsView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI
import Combine

// MARK: - View
struct HemoProfileDetailsView: View {
    @ObservedObject var viewModel: HemoProfileDetailsViewModel

    var body: some View {
        ZStack {
            // Navigation links
            navigationLinks

            // Main content
            mainContent
                .padding(.top, Dimensions.horizontalPadding )
        }
        .onAppear {
            Task { @MainActor in
                try await viewModel.loadData()
            }
        }
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true)
    }
}

private extension HemoProfileDetailsView {

    var mainContent: some View {
        VStack(alignment: .leading, spacing: Dimensions.spacingMedium) {
            // Header - directly accessing result
            HeaderView(
                title: HumaStrings.pluginHemophiliaProfileQuestionnaireTitle,
                style: .stacked,
                rightButtonIcon: HumaAssets.icEdit.name) {
                    viewModel.navigateToEdit = true
                }

            // Details section
            detailsSection
        }
        .padding(.horizontal, Dimensions.horizontalPadding)
    }

    var detailsSection: some View {
        ScrollView(showsIndicators: false) {
            VStack(alignment: .leading, spacing: Dimensions.spacing) {
                makeAnswerRow(for: .weight)
                makeAnswerRow(for: .hemophiliaType)
                makeAnswerRow(for: .hemophiliaSeverity)
                makeAnswerRow(for: .inhibitorHistory)
                makeAnswerRow(for: .bleedCount)
                makeAnswerRow(for: .treatedBleedCount)
                makeAnswerRow(for: .targetJoints)
                makeAnswerRow(for: .targetJointLocations)
                makeAnswerRow(for: .onProphylacticTreatment)
                makeAnswerRow(for: .prophylacticTreatment)
                makeAnswerRow(for: .nextDose)
                makeAnswerRow(for: .factorTreatment)
                makeAnswerRow(for: .diagnoses)
                Spacer()
            }
        }
    }

    func makeRow(title: String, value: String) -> some View {
        VStack(alignment: .leading, spacing: Dimensions.spacingMedium) {
            Text(title)
                .font(.bold)
                .foregroundColor(.charcoalGrey)
            Text(value)
                .font(.default)
                .foregroundColor(.charcoalGrey)
            Divider()
                .background(Color.veryLightGrey)
        }
    }

    func makeAnswerRow(for questionID: HemoProfileQuestionnaireSource.QuestionID) -> some View {
        Group {
            if let answer = getAnswer(for: questionID), let answerText = getAnswerText(answer: answer) {
                makeRow(title: answer.question, value: answerText)
            } else {
                EmptyView()
            }
        }
    }

    func getAnswer(for questionID: HemoProfileQuestionnaireSource.QuestionID) -> AnswerBody? {
        viewModel.result?.answers.first { $0.questionId == questionID.rawValue }
    }

    func getAnswerText(answer: AnswerBody) -> String? {
        if let questionIDString = answer.questionId,
           let questionID = HemoProfileQuestionnaireSource.QuestionID(rawValue: questionIDString),
           let answersList = answer.answersList,
           let answerValue = answersList.first {
            var answerText = answerValue
            switch questionID {
            case .weight:
                answerText = HumaStrings.pluginHemophiliaWeightLb(answerValue) // TODO: - Pass correct unit from user preference
            case .hemophiliaType:
                if let type = HemophiliaType(rawValue: answerValue) {
                    answerText = type.title
                }
            case .hemophiliaSeverity:
                if let type = HemophiliaSeverity(rawValue: answerValue) {
                    answerText = type.title
                }
            case .inhibitorHistory:
                if let type = InhibitorHistory(rawValue: answerValue) {
                    answerText = type.title
                }
            case .targetJointLocations:
                let titles: [String] = answersList.compactMap { BodyLocationType(rawValue: $0)?.title }
                return titles.joined(separator: ",")
            case .medicationFrequency:
                if let type = MedicationFrequency(rawValue: answerValue) {
                    answerText = type.title
                }
            case .diagnoses:
                let titles: [String] = answersList.compactMap {
                    DiagnosisType(rawValue: $0)?.title
                }
                return titles.joined(separator: ",")
            case .targetJoints, .onProphylacticTreatment:
                if answerValue == "true" {
                    answerText = HumaStrings.commonActionYes
                } else {
                    answerText = HumaStrings.commonActionNo
                }
            default:
                return answerValue
                break
            }

            return answerText
        }
        return nil
    }
}

// MARK: - Navigation
private extension HemoProfileDetailsView {
    var navigationLinks: some View {
        VStack {
            if let source = viewModel.source {
                NavigationLink("", isActive: $viewModel.navigateToEdit) {
                    QuestionnaireView(source: source)
                }
            }
        }
    }
}
