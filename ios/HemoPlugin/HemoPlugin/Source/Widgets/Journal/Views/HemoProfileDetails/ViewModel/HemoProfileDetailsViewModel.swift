//
// HemoProfileDetailsViewModel.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation
import HumaModules

// MARK: - ViewModel
class HemoProfileDetailsViewModel: ObservableObject {

    @Published var result: HemoProfileQuestionnaireResponse?
    private let pfizerPluginRepository: AnyPfizerPluginRepository
    private let deploymentConfigurationRepository: AnyDeploymentConfigurationRepository
    private let medicationRepositoryV2: AnyMedicationRepositoryV2
    var source: HemoProfileQuestionnaireSource?

    // Navigation states
    @Published var navigateToEdit: Bool = false

    init(
        pfizerPluginRepository: AnyPfizerPluginRepository,
        deploymentConfigurationRepository: AnyDeploymentConfigurationRepository,
        medicationRepositoryV2: AnyMedicationRepositoryV2
    ) {
        self.pfizerPluginRepository = pfizerPluginRepository
        self.deploymentConfigurationRepository = deploymentConfigurationRepository
        self.medicationRepositoryV2 = medicationRepositoryV2
    }

    func loadData() async throws {
        let response = try await pfizerPluginRepository.getHemoProfileQuestionnaire()
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            self.result = response
            self.source = .init(
                pfizerPluginRepository: self.pfizerPluginRepository,
                deploymentConfigurationRepository: self.deploymentConfigurationRepository,
                medicationRepositoryV2: medicationRepositoryV2,
                headerTitle: HumaStrings.pluginHemophiliaProfileQuestionnaireTitle,
                hemoProfileQuestionnaireResponse: response
            )
        }
    }

    // User actions
    func editProfile() {
        navigateToEdit = true
    }

}
