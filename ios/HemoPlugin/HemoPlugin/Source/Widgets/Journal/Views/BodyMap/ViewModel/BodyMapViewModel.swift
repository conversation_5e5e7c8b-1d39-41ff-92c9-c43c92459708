//
//  BodyMapViewModel.swift
//  HemoPlugin
//
//  Created by <PERSON> on 17/03/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation
import SwiftUI

protocol AnyBodyMapViewModel: ObservableObject {
    // Properties
    var legendText: String? { get }
    var allowInteraction: Bool { get }
    var bodyMapColor: [BodyMapColor] { get }
    var legend: [LegendItem]? { get }
    var selectedBodyPoint: HemoJournalWidgetConfig.BodyLocation? { get }
    var jointData: HemoJournalWidgetConfig.BleedData { get }

    // Events
    var onBodyPointSelectd: TriggeredEvent<HemoJournalWidgetConfig.BodyLocation> { get }

    // Methods
    func onBodyPointSelectd(bodyLocation: HemoJournalWidgetConfig.BodyLocation)
    func getColorForLocation(_ bodyLocation: HemoJournalWidgetConfig.BodyLocation) -> Color
}

class BodyMapViewModel: AnyBodyMapViewModel {
    // MARK: - Published Properties
    @Published var bodyParts: [HemoJournalWidgetConfig.BodyPartData]?
    @Published var selectedBodyPoint: HemoJournalWidgetConfig.BodyLocation?

    // MARK: - Events
    var onBodyPointSelectd: TriggeredEvent<HemoJournalWidgetConfig.BodyLocation> = .init()
    
    // MARK: - Properties
    var  bodyMapColor: [BodyMapColor]
    var legend: [LegendItem]?
    var jointData: HemoJournalWidgetConfig.BleedData
    var allowInteraction: Bool
    var legendText: String?

    // MARK: - Initialization
    init(
        bodyMapColor: [BodyMapColor],
        legend: [LegendItem]?,
        jointData: HemoJournalWidgetConfig.BleedData,
        allowInteraction: Bool = false,
        legendText: String?,
        selectedBodyPoint: HemoJournalWidgetConfig.BodyLocation? = nil
    ) {
        self.bodyMapColor = bodyMapColor
        self.legend = legend
        self.jointData = jointData
        self.allowInteraction = allowInteraction
        self.legendText = legendText
        self.selectedBodyPoint = selectedBodyPoint
    }

    // MARK: - Methods
    func onBodyPointSelectd(bodyLocation: HemoJournalWidgetConfig.BodyLocation) {
        selectedBodyPoint = bodyLocation
        onBodyPointSelectd.trigger(bodyLocation)
    }
    
    func getColorForLocation(_ bodyLocation: HemoJournalWidgetConfig.BodyLocation) -> Color {
        if let bodyMapColor = bodyMapColor.first(where: { $0.location == bodyLocation.location }) {
            return Color(hex: bodyMapColor.color)
        }
        return Color.lightGrey2
    }
}
