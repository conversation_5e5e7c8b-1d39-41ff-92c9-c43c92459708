//
//  MedicationRepository.swift
//  HemoPlugin
//
//  Created by <PERSON> on 26.11.2024.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation

/// Interface for repository which provides actions.
protocol AnyPfizerPluginRepository: AnyObject {
    func getCMSMedication(completion: @escaping RepositoryCompletion<CMSMedicationResponse>)

    func submitHemoProfileQuestionnaire(
        requestBody: HemoProfileQuestionnaireRequest,
        completion: @escaping RepositoryCompletion<CreateObjectResponse>
    )

    func getHemoProfileQuestionnaire(completion: @escaping RepositoryCompletion<HemoProfileQuestionnaireResponse>)

}

extension AnyPfizerPluginRepository {
    func getCMSMedication() async throws -> CMSMedicationResponse {
        try await withCheckedThrowingContinuation { continuation in
            getCMSMedication() { result in
                continuation.resume(with: result)
            }
        }
    }

    func submitHemoProfileQuestionnaire(requestBody: HemoProfileQuestionnaireRequest) async throws -> CreateObjectResponse {
        try await withCheckedThrowingContinuation { continuation in
            submitHemoProfileQuestionnaire(requestBody: requestBody) { result in
                continuation.resume(with: result)
            }
        }
    }

    func getHemoProfileQuestionnaire() async throws -> HemoProfileQuestionnaireResponse {
        try await withCheckedThrowingContinuation { continuation in
            getHemoProfileQuestionnaire() { result in
                continuation.resume(with: result)
            }
        }
    }
}

/// Default implementation of `AnyActionsRepository`. Uses `AnyHumaNetworking` to make calls to API.
final class PfizerPluginRepository: AnyPfizerPluginRepository {
    private let networking: AnyNetworking

    public init(networking: AnyNetworking) {
        self.networking = networking
    }
    
    public func getCMSMedication(
        completion: @escaping RepositoryCompletion<CMSMedicationResponse>
    ) {
        networking.getCMSMedication() { response in
            switch response.result {
            case .success(let result):
                completion(.success(result))
            case .failure(let error):
                completion(.failure(.fetchFailed(reason: error)))
            }
        }
    }

    public func submitHemoProfileQuestionnaire(
        requestBody: HemoProfileQuestionnaireRequest,
        completion: @escaping RepositoryCompletion<CreateObjectResponse>
    ) {
        networking.submitHemoProfileQuestionnaire(requestBody: requestBody) { response in
            switch response.result {
            case .success(let result):
                completion(.success(result))
            case .failure(let error):
                completion(.failure(.addFailed(reason: error)))
            }
        }
    }

    public func getHemoProfileQuestionnaire(completion: @escaping RepositoryCompletion<HemoProfileQuestionnaireResponse>) {
        networking.getHemoProfileQuestionnaire() { response in
            switch response.result {
            case .success(let result):
                completion(.success(result))
            case .failure(let error):
                completion(.failure(.addFailed(reason: error)))
            }
        }
    }
}
