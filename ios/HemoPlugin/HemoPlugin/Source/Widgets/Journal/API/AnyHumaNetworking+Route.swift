//
//  AnyHumaNetworking+Route.swift
//  HemoPlugin
//
//  Created by <PERSON> on 26.11.2024.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation

extension AnyNetworking {
    func getWidgetData(
        widgetType: String,
        widgetID: String,
        completion: @escaping RequestCompletion<WidgetDataResponse>
    ) {
        request(
            Endpoint.getWidgetData(widgetType: widgetType, widgetID: widgetID),
            completion: completion
        )
    }

    func getCMSMedication(completion: @escaping RequestCompletion<CMSMedicationResponse>) {
        request(
            Endpoint.getCMSMedication(),
            completion: completion
        )
    }

    func submitHemoProfileQuestionnaire(requestBody: HemoProfileQuestionnaireRequest, completion: @escaping RequestCompletion<CreateObjectResponse>) {
        request(
            Endpoint.submitHemoProfileQuestionnaire(requestBody: requestBody),
            completion: completion
        )
    }

    func getHemoProfileQuestionnaire(completion: @escaping RequestCompletion<HemoProfileQuestionnaireResponse>) {
        request(
            Endpoint.getHemoProfileQuestionnaire(),
            completion: completion
        )
    }
}

extension Endpoint {
    static func getWidgetData(widgetType: String, widgetID: String) -> Endpoint {
        .init(
            path: "/api/sdk/v1/user/\(Endpoint.variableUserIDMacro)/widget/\(widgetType)/\(widgetID)",
            method: .get
        )
    }

    static func getCMSMedication(collection: String = "medications", body: CMSMedicationRequest? = nil) -> Endpoint {
        let requestBody: CMSMedicationRequest = body ?? CMSMedicationRequest(collection: collection)
        return .init(
            path: "/api/extensions/v1/cms/\(collection)/search",
            method: .post,
            body: .json(requestBody, encoder: .huma)
        )
    }

    static func submitHemoProfileQuestionnaire(requestBody: HemoProfileQuestionnaireRequest) -> Endpoint {
        .init(
            path: "/api/hemophilia/v1/user/\(Endpoint.variableUserIDMacro)/prerequisites",
            method: .post,
            body: .json(requestBody, encoder: .huma)
        )
    }

    static func getHemoProfileQuestionnaire() -> Endpoint {
        .init(
            path: "/api/hemophilia/v1/user/\(Endpoint.variableUserIDMacro)/prerequisites",
            method: .get
        )
    }
}
