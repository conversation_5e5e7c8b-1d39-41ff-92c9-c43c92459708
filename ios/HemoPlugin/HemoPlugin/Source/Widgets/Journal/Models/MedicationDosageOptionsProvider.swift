import Foundation
import HumaModules

struct MedicationDosageOptionsProvider {
    static func options(from config: MedicationModuleV2.ConfigBody?) -> [OptionItem] {
        if let defaultUnits = config?.defaultUnits {
            return defaultUnits.map { .init(value: MedicationDosageV2.Unit.custom.rawValue, title: $0) }
        } else {
            return MedicationDosageV2.Unit.allCases.compactMap {
                guard $0 != .custom else { return nil }
                return .init(value: $0.rawValue, title: $0.localizedDescription)
            }
        }
    }
}
