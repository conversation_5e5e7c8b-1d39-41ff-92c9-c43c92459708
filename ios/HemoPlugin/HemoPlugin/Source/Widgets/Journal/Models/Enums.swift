//
//  Enums.swift
//  Pods
//
//  Created by <PERSON> on 24/06/2025.
//
// Protocol to ensure enums have optionItem

import HumaFoundation
import HumaModules

protocol OptionItemConvertible {
    var optionItem: OptionItem { get }
}

enum MedicationFrequency: String, CaseIterable, OptionItemConvertible {
    case everyDay = "EVERY_DAY"
    case specificDays = "SPECIFIC_DAYS"
    case daysInterval = "DAYS_INTERVAL"
    case asNeeded = "AS_NEEDED"

    var title: String {
        switch self {
        case .everyDay: return HumaStrings.medicationv2EveryDay
        case .specificDays: return HumaStrings.medicationv2SpecificDay
        case .daysInterval: return HumaStrings.medicationv2DaysInterval
        case .asNeeded: return HumaStrings.medicationv2AsNeeded
        }
    }

    var optionItem: OptionItem {
        .init(value: self.rawValue, title: self.title)
    }
}

enum DiagnosisType: String, CaseIterable, OptionItemConvertible {
    case arthropathy = "ARTHROPATHY"
    case chronicPain = "CHRONIC_PAIN"
    case hiv = "HIV"
    case hepatitisC = "HEPATITIS_C"
    case hepatitisB = "HEPATITIS_B"
    case jointReplacement = "PREVIOUS_JOINT_REPLACEMENT"
    case heartDisease = "HEART_DISEASE"
    case diabetes = "DIABETES"
    case other = "OTHER"
    case none = "NONE"

    var title: String {
        switch self {
        case .arthropathy: return HumaStrings.pluginHemophiliaArthropathy
        case .chronicPain: return HumaStrings.pluginHemophiliaChronicPain
        case .hiv: return HumaStrings.pluginHemophiliaHiv
        case .hepatitisC: return HumaStrings.pluginHemophiliaHepatitisC
        case .hepatitisB: return HumaStrings.pluginHemophiliaHepatitisB
        case .jointReplacement: return HumaStrings.pluginHemophiliaPreviousJointReplacement
        case .heartDisease: return HumaStrings.pluginHemophiliaHeartDisease
        case .diabetes: return HumaStrings.pluginHemophiliaDiabetes
        case .other: return HumaStrings.pluginHemophiliaOther
        case .none: return HumaStrings.pluginHemophiliaNone
        }
    }

    var optionItem: OptionItem {
        .init(value: self.rawValue, title: self.title)
    }
}

enum HemophiliaType: String, CaseIterable, OptionItemConvertible {
    case typeA = "HEMOPHILIA_A"
    case typeB = "HEMOPHILIA_B"

    var title: String {
        switch self {
        case .typeA: return HumaStrings.pluginHemophiliaTypeA
        case .typeB: return HumaStrings.pluginHemophiliaTypeB
        }
    }

    var optionItem: OptionItem {
        .init(value: self.rawValue, title: self.title)
    }
}

enum HemophiliaSeverity: String, CaseIterable, OptionItemConvertible {
    case mild = "MILD"
    case moderate = "MODERATE"
    case severe = "SEVERE"
    case notSure = "NOT_SURE"

    var title: String {
        switch self {
        case .mild: return HumaStrings.ohsAnswersPainMild
        case .moderate: return HumaStrings.ohsAnswersPainModerate
        case .severe: return HumaStrings.ohsAnswersPainSevere
        case .notSure: return HumaStrings.commonNotSure
        }
    }

    var optionItem: OptionItem {
        .init(value: self.rawValue, title: self.title)
    }
}

enum InhibitorHistory: String, CaseIterable, OptionItemConvertible {
    case never = "NEVER_HAD_AN_INHIBITOR"
    case past = "HAD_AN_INHIBITOR_IN_THE_PAST"
    case active = "HAVE_AN_ACTIVE_INHIBITOR"
    case notSure = "NOT_SURE"

    var title: String {
        switch self {
        case .never: return HumaStrings.pluginHemophiliaNeverHadAnInhibitor
        case .past: return HumaStrings.pluginHemophiliaHadAnInhibitorInThePast
        case .active: return HumaStrings.pluginHemophiliaHaveAnActiveInhibitor
        case .notSure: return HumaStrings.commonNotSure
        }
    }

    var optionItem: OptionItem {
        .init(value: self.rawValue, title: self.title)
    }
}

enum BoolQuestion: String, CaseIterable, OptionItemConvertible {
    case yes = "YES"
    case no = "NO"

    var title: String {
        switch self {
        case .yes: return HumaStrings.commonActionYes
        case .no: return HumaStrings.commonActionNo
        }
    }

    var optionItem: OptionItem {
        .init(value: self.rawValue, title: self.title)
    }
}

extension WeekDay: OptionItemConvertible {
    var optionItem: OptionItem {
        .init(value: self.rawValue, title: day)
    }

    private var day: String {
        switch self {
        case .friday:
            return Strings.commonDaysFridayShort
        case .monday:
            return Strings.commonDaysMondayShort
        case .saturday:
            return Strings.commonDaysSaturdayShort
        case .sunday:
            return Strings.commonDaysSundayShort
        case .thursday:
            return Strings.commonDaysThursdayShort
        case .tuesday:
            return Strings.commonDaysTuesdayShort
        case .wednesday:
            return Strings.commonDaysWednesdayShort
        }
    }
}

enum BleedType: String, Codable, Equatable, Hashable {
    case joints = "JOINTS"
    case nonJoints = "NON_JOINTS"
}

enum BodyPartType: String, Codable, CaseIterable {
    case jointRightWrist = "JOINT_RIGHT_WRIST"
    case muscleRightArm = "MUSCLE_RIGHT_ARM"
    case surfaceRightArm = "SURFACE_RIGHT_ARM"
    case jointLeftWrist = "JOINT_LEFT_WRIST"
    case muscleLeftArm = "MUSCLE_LEFT_ARM"
    case surfaceLeftArm = "SURFACE_LEFT_ARM"
    case jointRightElbow = "JOINT_RIGHT_ELBOW"
    case jointLeftElbow = "JOINT_LEFT_ELBOW"
    case jointRightShoulder = "JOINT_RIGHT_SHOULDER"
    case jointLeftShoulder = "JOINT_LEFT_SHOULDER"
    case jointRightHip = "JOINT_RIGHT_HIP"
    case jointLeftHip = "JOINT_LEFT_HIP"
    case muscleRightLeg = "MUSCLE_RIGHT_LEG"
    case muscleLeftLeg = "MUSCLE_LEFT_LEG"
    case surfaceRightLeg = "SURFACE_RIGHT_LEG"
    case surfaceLeftLeg = "SURFACE_LEFT_LEG"
    case jointRightKnee = "JOINT_RIGHT_KNEE"
    case jointLeftKnee = "JOINT_LEFT_KNEE"
    case jointRightAnkle = "JOINT_RIGHT_ANKLE"
    case jointLeftAnkle = "JOINT_LEFT_ANKLE"
    case gumsBleed = "GUMS_BLEED"
    case noseBleed = "NOSE_BLEED"
    case menstrual = "MENSTRUAL"
    case bloodInStool = "BLOOD_IN_STOOL"
    case bloodInUrine = "BLOOD_IN_URINE"
    case custom = "CUSTOM"
}

enum BodyLocationType: String, Codable, CaseIterable, OptionItemConvertible {
    case rightWrist = "RIGHT_WRIST"
    case leftWrist = "LEFT_WRIST"
    case rightElbow = "RIGHT_ELBOW"
    case leftElbow = "LEFT_ELBOW"
    case rightShoulder = "RIGHT_SHOULDER"
    case leftShoulder = "LEFT_SHOULDER"
    case rightHip = "RIGHT_HIP"
    case leftHip = "LEFT_HIP"
    case rightKnee = "RIGHT_KNEE"
    case leftKnee = "LEFT_KNEE"
    case rightAnkle = "RIGHT_ANKLE"
    case leftAnkle = "LEFT_ANKLE"
    case other = "OTHER"

    var title: String {
        switch self {
        case .rightWrist: return HumaStrings.pluginHemophiliaRightWrist
        case .leftWrist: return HumaStrings.pluginHemophiliaLeftWrist
        case .rightElbow: return HumaStrings.pluginHemophiliaRightElbow
        case .leftElbow: return HumaStrings.pluginHemophiliaLeftElbow
        case .rightShoulder: return HumaStrings.pluginHemophiliaRightShoulder
        case .leftShoulder: return HumaStrings.pluginHemophiliaLeftShoulder
        case .rightHip: return HumaStrings.pluginHemophiliaRightHip
        case .leftHip: return HumaStrings.pluginHemophiliaLeftHip
        case .rightKnee: return HumaStrings.pluginHemophiliaRightKnee
        case .leftKnee: return HumaStrings.pluginHemophiliaLeftKnee
        case .rightAnkle: return HumaStrings.pluginHemophiliaRightAnkle
        case .leftAnkle: return HumaStrings.pluginHemophiliaLeftAnkle
        case .other: return HumaStrings.pluginHemophiliaOther
        }
    }

    var optionItem: OptionItem {
        .init(value: self.rawValue, title: self.title)
    }
}

extension MedicationModuleV2.MedicationGroup: OptionItemConvertible {
    var optionItem: OptionItem { .init(value: id, title: name) }
}

enum BleedSeverity: String, Codable, CaseIterable, OptionItemConvertible {
    case mild = "MILD"
    case moderate = "MODERATE"
    case severe = "SEVERE"

    var title: String {
        switch self {
        case .mild: return HumaStrings.ohsAnswersPainMild
        case .moderate: return HumaStrings.ohsAnswersPainModerate
        case .severe: return HumaStrings.ohsAnswersPainSevere
        }
    }

    var optionItem: OptionItem {
        .init(value: self.rawValue, title: self.title)
    }
}

enum BleedReason: String, Codable, CaseIterable, OptionItemConvertible {
    case spontaneous = "SPONTANEOUS"
    case activity = "ACTIVITY"
    case nonActivity = "NON_ACTIVITY"
    case postActivity = "POST_ACTIVITY"
    case surgery = "SURGERY"
    case other = "OTHER"

    var title: String {
        switch self {
        case .spontaneous: return HumaStrings.pluginHemophiliaSpontaneous
        case .activity: return HumaStrings.pluginHemophiliaInjuryRelatedToActivity
        case .nonActivity: return HumaStrings.pluginHemophiliaInjuryNotRelatedToActivity
        case .postActivity: return HumaStrings.pluginHemophiliaPostActivity
        case .surgery: return HumaStrings.pluginHemophiliaSurgeryProcedure
        case .other: return HumaStrings.pluginHemophiliaOther
        }
    }

    var optionItem: OptionItem {
        .init(value: self.rawValue, title: self.title)
    }
}
