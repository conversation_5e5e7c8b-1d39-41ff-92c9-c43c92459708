//
//  CMSMedicationResponse.swift
//  HemoPlugin
//
//  Created by <PERSON> on 01/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Foundation
import HumaModules

struct CMSMedicationResponse: Codable, Equatable {

    struct MedicationItem: Codable, Equatable, Identifiable {
        var id: String
        var unit: String?
        var title: String
        var dosage: Int?
        var frequency: String?
        var maxDosage: Int?
        var isCustom: Bool = false
        var identifier: String?
        var tags: [MedicationTag]?

        enum CodingKeys: String, CodingKey {
            case id
            case data
            case tags
        }

        enum DataKeys: String, CodingKey {
            case unit
            case title
            case dosage
            case frequency
            case identifier
        }

        init(from decoder: Decoder) throws {
            let container = try decoder.container(keyedBy: CodingKeys.self)

            id = try container.decode(String.self, forKey: .id)
            tags = try container.decodeIfPresent([MedicationTag].self, forKey: .tags)

            let dataContainer = try container.nestedContainer(keyedBy: DataKeys.self, forKey: .data)
            unit = try dataContainer.decodeIfPresent(String.self, forKey: .unit)
            title = try dataContainer.decode(String.self, forKey: .title)
            dosage = try dataContainer.decodeIfPresent(Int.self, forKey: .dosage)
            frequency = try dataContainer.decodeIfPresent(String.self, forKey: .frequency)
            identifier = try dataContainer.decodeIfPresent(String.self, forKey: .identifier)
        }
        
        init(
            id: String,
            title: String,
            dosage: Int? = nil,
            unit: String? = nil,
            frequency: String? = nil,
            maxDosage: Int? = nil,
            isCustom: Bool = false,
            tags: [MedicationTag]? = nil,
            identifier: String? = nil
        ) {
            self.id = id
            self.title = title
            self.dosage = dosage
            self.unit = unit
            self.frequency = frequency
            self.maxDosage = maxDosage
            self.isCustom = isCustom
            self.tags = tags
            self.identifier = identifier
        }

        func encode(to encoder: Encoder) throws {
            var container = encoder.container(keyedBy: CodingKeys.self)
            try container.encode(id, forKey: .id)
            try container.encodeIfPresent(tags, forKey: .tags)

            var dataContainer = container.nestedContainer(keyedBy: DataKeys.self, forKey: .data)
            try dataContainer.encodeIfPresent(unit, forKey: .unit)
            try dataContainer.encode(title, forKey: .title)
            try dataContainer.encodeIfPresent(dosage, forKey: .dosage)
            try dataContainer.encodeIfPresent(frequency, forKey: .frequency)
            try dataContainer.encodeIfPresent(identifier, forKey: .identifier)
        }

    }

    var total: Int?
    var limit: Int?
    var skip: Int?
    var items: [MedicationItem]
}

enum MedicationTag: String, Codable, CaseIterable {
    case onDemand = "OnDemand"
    case prophylactic = "Prophylactic"
    case other = "Other"
}

extension MedicationModuleV2.MedicationGroup {
    var medicationTag: MedicationTag? {
        guard let tag = tags.first else { return nil }
        return .init(rawValue: tag)
    }
}
