//
//  HemoProfileQuestionnaireResponse.swift
//  Pods
//
//  Created by <PERSON> on 23/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

struct HemoProfileQuestionnaireResponse: Codable {

    struct Medication: Codable {
        let id: String
    }

    let id: String
    let userId: String
    let answers: [AnswerBody]
    let targetJoints: [String]?
    let prophylacticMedication: Medication?
    let asNeededMedication: Medication?
}

extension HemoProfileQuestionnaireResponse {
    /// Maps answers and additional properties to a dictionary keyed by QuestionID.
    func mapAnswers() -> [HemoProfileQuestionnaireSource.QuestionID: Any] {
        var result: [HemoProfileQuestionnaireSource.QuestionID: Any] = [:]
        
        if let targetJoints = self.targetJoints, !targetJoints.isEmpty {
            result[.targetJointLocations] = targetJoints
        }

        answers.forEach { answerObject in
            guard let questionIDStr = answerObject.questionId,
                  let questionID = HemoProfileQuestionnaireSource.QuestionID(rawValue: questionIDStr),
                  let answerList = answerObject.answersList else { return }
            
            switch questionID {
            case .weight, .hemophiliaType, .hemophiliaSeverity, .inhibitorHistory, .bleedCount, .treatedBleedCount:
                if let answer = answerList.first { result[questionID] = answer }
            case .targetJoints, .onProphylacticTreatment:
                if let answer = answerList.first { result[questionID] = (answer == "true" ? "YES" : "NO") }
            case .targetJointLocations:
                // Already handled above
                break
            case .prophylacticTreatment, .dosage, .medicationFrequency, .timeOfDay, .setReminder, .nextDose, .factorTreatment, .specificDays, .dayInterval, .maxDosage:
                // Not mapped here
                break
            case .diagnoses:
                result[questionID] = answerList
            }
        }
        return result
    }
}
