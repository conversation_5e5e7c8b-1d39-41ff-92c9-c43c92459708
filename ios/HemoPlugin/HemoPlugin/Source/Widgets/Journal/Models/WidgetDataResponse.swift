//
//  WidgetDataResponse.swift
//  HemoPlugin
//
//  Created by <PERSON> on 26/03/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Foundation

enum WidgetState: String, Codable, CaseIterable {
    case active = "ACTIVE"
    case setup = "SETUP_REQUIRED"
}

struct WidgetDataResponse: Codable, Equatable {
    var title: String?
    var description: String?
    var primaryCTAtext: String
    var secondaryCTAtext: String?
    var tooltip: String?
    var bodyMapColor: [BodyMapColor]?
    var legend: [LegendItem]?
    var state: WidgetState
}

struct LegendItem: Codable, Equatable, Identifiable {
    let id = UUID()
    var color: String
    var label: String

    enum CodingKeys: String, CodingKey {
        case color
        case label
    }
}

struct BodyMapColor: Codable, Equatable, Identifiable {
    let id = UUID()
    let location: BodyLocationType
    let color: String

    enum CodingKeys: String, CodingKey {
        case location
        case color
    }
}

extension BodyLocationType {
    var position: CGPoint {
        switch self {
        case .leftWrist:
            return CGPoint(x: 0.93, y: 0.49)
        case .rightWrist:
            return CGPoint(x: 0.07, y: 0.49)
        case .leftElbow:
            return CGPoint(x: 0.90, y: 0.40)
        case .rightElbow:
            return CGPoint(x: 0.10, y: 0.40)
        case .leftShoulder:
            return CGPoint(x: 0.88, y: 0.22)
        case .rightShoulder:
            return CGPoint(x: 0.12, y: 0.22)
        case .leftHip:
            return CGPoint(x: 0.73, y: 0.57)
        case .rightHip:
            return CGPoint(x: 0.27, y: 0.57)
        case .leftKnee:
            return CGPoint(x: 0.65, y: 0.75)
        case .rightKnee:
            return CGPoint(x: 0.35, y: 0.75)
        case .leftAnkle:
            return CGPoint(x: 0.60, y: 0.95)
        case .rightAnkle:
            return CGPoint(x: 0.40, y: 0.95)
        case .other:
            return CGPoint.zero
        }
    }
}
