//
//  AnyHemoJournalPrimitive.swift
//  Pods
//
//  Created by <PERSON> on 25/06/2025.
//
import HumaFoundation

protocol AnyHemoJournalPrimitive: AnyCodablePrimitive {
    var bodyLocation: BodyLocationType { get }
    var bodyPartInjury: BodyPartType { get }
    var customBodyPart: String? { get }
    var extraData: HemoJournalPrimitive.ExtraData { get }
}

struct HemoJournalPrimitive: AnyCodablePrimitive, AnyHemoJournalPrimitive {

    struct ExtraData: Codable {
        let accidentDate: String
        let reason: BleedReason
        let note: String?
        let photos: [String]?
        let scale: Int
        let severity: BleedSeverity
        let treatment: Treatment?
        let factorUnits: Int?
    }

    struct Treatment: Codable {
        let id: String
        let enabled: Bool
        let name: String
        let codingList: [String]
        let userId: String
        let deploymentId: String
        let dosage: Int?
        let unit: String?
        let isNotificationEnabled: Bool
        let schedule: Schedule
        let submitterUserType: String
        let tags: [MedicationTag]?
    }

    struct Schedule: Codable {
        let asNeeded: Bool
    }

    static var primitiveName: String { "HemophiliaJournal" }
    var bodyLocation: BodyLocationType
    var bodyPartInjury: BodyPartType
    var customBodyPart: String?
    var extraData: ExtraData

}
