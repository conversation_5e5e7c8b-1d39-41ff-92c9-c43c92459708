//
//  HemoProfileQuestionnaireRequest.swift
//  HemoPlugin
//
//  Created by <PERSON> on 02/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

struct HemoProfileQuestionnaireRequest: Codable {
    let answers: [AnswerBody]
    let targetJoints: [String]
    let prophylacticMedication: MedicationV2?
    let asNeededMedication: MedicationV2?
    var addProphylacticMedication: Bool = false
    var addAsNeededMedication: Bool = false

    // Custom encoding implementation
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encode(answers, forKey: .answers)
        try container.encode(targetJoints, forKey: .targetJoints)
        
        // Custom encoding for prophylacticMedication
        if let medication = prophylacticMedication {
            var medicationContainer = container.nestedContainer(keyedBy: MedicationCodingKeys.self, forKey: .prophylacticMedication)
            try encodeMedication(medication, to: &medicationContainer, shouldAddID: addProphylacticMedication)
        } else {
            try container.encodeNil(forKey: .prophylacticMedication)
        }
        
        // Custom encoding for asNeededMedication
        if let medication = asNeededMedication {
            var medicationContainer = container.nestedContainer(keyedBy: MedicationCodingKeys.self, forKey: .asNeededMedication)
            try encodeMedication(medication, to: &medicationContainer, shouldAddID: addAsNeededMedication)
        } else {
            try container.encodeNil(forKey: .asNeededMedication)
        }
    }
    
    // Helper method to encode MedicationV2 without id
    private func encodeMedication(
        _ medication: MedicationV2,
        to container: inout KeyedEncodingContainer<MedicationCodingKeys>,
        shouldAddID: Bool
    ) throws {
        if shouldAddID {
            try container.encode(medication.id, forKey: .id)
        }
        try container.encode(medication.configId, forKey: .configId)
        try container.encode(medication.name, forKey: .name)
        try container.encode(medication.coding, forKey: .coding)
        // Handle dosage encoding differently - dosage has custom encoding
        if let dosage = medication.dosage, dosage.value > 0 {
            try container.encode(dosage.value, forKey: .value)
            try container.encode(dosage.unit, forKey: .unit)
            try container.encodeIfPresent(dosage.customUnit, forKey: .customUnit)
        }
        try container.encodeIfPresent(medication.mealSchedule, forKey: .mealSchedule)
        if let maxDosage = medication.maxDosage, maxDosage > 0 {
            try container.encodeIfPresent(medication.maxDosage, forKey: .maxDosage)
        }
        try container.encodeIfPresent(medication.schedule, forKey: .schedule)
        try container.encodeIfPresent(medication.isNotificationEnabled, forKey: .isNotificationEnabled)
        try container.encode(medication.createDateTime, forKey: .createDateTime)
        try container.encode(medication.updateDateTime, forKey: .updateDateTime)
        try container.encode(medication.submitterUserType, forKey: .submitterUserType)
        try container.encodeIfPresent(medication.adherence, forKey: .adherence)
        try container.encodeIfPresent(medication.adherences, forKey: .adherences)
        try container.encode(medication.enabled, forKey: .enabled)
        try container.encode(medication.groupID, forKey: .groupID)
        try container.encodeIfPresent(medication.startDate?.iso8601ShortDateUTC, forKey: .startDate)
        try container.encodeIfPresent(medication.endDate?.iso8601ShortDateUTC, forKey: .endDate)
        try container.encodeIfPresent(medication.indication, forKey: .indication)
        try container.encodeIfPresent(medication.customUnit, forKey: .customUnit)
    }
    
    enum CodingKeys: String, CodingKey {
        case answers, targetJoints, prophylacticMedication, asNeededMedication
    }
    
    // Define coding keys for all properties of MedicationV2 except id
    enum MedicationCodingKeys: String, CodingKey {
        case id
        case configId = "moduleConfigId"
        case name
        case coding
        // Add dosage related keys
        case value = "dosage"
        case unit
        case customUnit
        case mealSchedule
        case maxDosage
        case schedule
        case isNotificationEnabled
        case createDateTime
        case updateDateTime
        case startDate
        case endDate
        case indication
        case submitterUserType
        case adherence
        case adherences
        case enabled
        case additionalFields
        case groupID = "groupId"
    }
}
