//
//  CMSMedicationRequest.swift
//  HemoPlugin
//
//  Created by <PERSON> on 01/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

struct CMSMedicationRequest: Codable, Equatable {
    var collection: String
    var sort: [SortOrder] = [.title]
}

struct SortOrder: Codable, Equatable {
    let field: String
    let direction: String
}

extension SortOrder {
    static var title = SortOrder(field: "data.title", direction: "ASC")
}
