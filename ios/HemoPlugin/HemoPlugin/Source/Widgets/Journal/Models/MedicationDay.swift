//
//  MedicationDay.swift
//  Pods
//
//  Created by <PERSON> on 24/06/2025.
//

struct MedicationDay: OptionItemConvertible {
    static let all: [MedicationDay] = (1...365).map { MedicationDay(day: $0) }

    let day: Int

    var displayString: String {
        HumaStrings.moduleMedicationDays(day)
    }

    var optionItem: OptionItem {
        OptionItem(value: "\(day)_DAYS", title: displayString)
    }
}
