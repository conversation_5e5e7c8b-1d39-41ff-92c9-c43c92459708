//
//  TypeAliases.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation
import HumaQuestionnaire

public typealias VoidCompletion = HumaFoundation.VoidCompletion

typealias HumaAssets = HumaFoundation.HumaAssets

typealias HumaFoundationBundle = HumaFoundation.HumaFoundationUI

typealias HumaStrings = HumaFoundation.HumaStrings

typealias QuestionnaireForm = HumaFoundation.QuestionnaireForm

typealias Questions = HumaQuestionnaire.Questions

typealias QuestionFormat = HumaQuestionnaire.QuestionFormat

typealias HTMLStringBuilder = HumaFoundation.HTMLStringBuilder

typealias AnswerBody = HumaQuestionnaire.QuestionnairePrimitive.Answer

typealias MedicationV2 = HumaFoundation.MedicationV2

typealias CreateObjectResponse = HumaFoundation.CreateObjectResponse

typealias MedicationDosageV2 = HumaFoundation.MedicationD<PERSON>V2

typealias ModuleResult<Primitive: AnyCodablePrimitive> = HumaFoundation.ModuleResult<Primitive>

public typealias TriggeredEvent<Arg> = HumaFoundation.TriggeredEvent<Arg>

typealias CMSMedicationUserMedicationIdentifier = (cmsMedicationId: String, userMedicationId: String)

extension HumaFoundationBundle {
    public static var bundle: Bundle {
        Bundle(for: self)
    }
}

extension HTMLStringBuilder {
    private static let common = CommonTypeStyle()

    static let `default`: Self = {
        return HTMLStringBuilder(
            font: common.default,
            boldFont: common.defaultSemibold
        )
    }()
}
