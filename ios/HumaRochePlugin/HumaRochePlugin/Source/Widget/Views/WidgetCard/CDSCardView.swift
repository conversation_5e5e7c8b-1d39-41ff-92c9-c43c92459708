//
//  CDSCardView.swift
//  HumaRochePlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct CDSCardView<ViewModel: AnyCDSCardViewModel>: View {
    @StateObject var viewModel: ViewModel

    // MARK: - Body

    var body: some View {
        ZStack(alignment: .center) {
            loadingView
                .transition(.opacity)
                .animation(.default, value: viewModel.content.isLoading)
                .visible(viewModel.content.isLoading)

            if let result = viewModel.content.result {
                contentView(response: result)
                    .transition(.opacity)
                    .animation(.default, value: viewModel.content.shouldShowResults)
                    .visible(viewModel.content.shouldShowResults)
            }
        }
        .padding(16)
        .enableCardStyling()
        .padding(.vertical, 8)
        .padding(.horizontal, 24)
        .task {
            await viewModel.loadData()
        }
    }
}

// MARK: - Private Helpers

private extension CDSCardView {

    func contentView(response: GetWidgetDataResponse) -> some View {
        VStack(spacing: 0) {
            switch response.state {
            case .setupRequired:
                PregnancySetupView(
                    viewModel: response,
                    isUserOnboarded: viewModel.ensureOnboardingCompletion(),
                    dateRange: dateRange,
                    onDateSelected: { date in
                    Task {
                        await viewModel.setPregnancyDate(date: date)
                    }
                })
            case .active:
                PregnancyCareView(
                    viewModel: response,
                    isUserOnboarded: viewModel.ensureOnboardingCompletion(),
                    dateRange: dateRange,
                    onDateSelected: { date in
                    Task {
                        await viewModel.setPregnancyDate(date: date)
                    }
                }) {
                    viewModel.onStartQuestionnaire.trigger()
                }
            }
        }
    }

    var loadingView: some View {
        LoadingView()
    }

    var dateRange: ClosedRange<Date> {
        // Calculate the date range
        let currentDate = Date()
        let fortyWeeksFromNow = Calendar.current.date(byAdding: .weekOfYear, value: 40, to: currentDate)!
        return currentDate...fortyWeeksFromNow
    }
}

private struct PregnancyCareView: View {

    let viewModel: GetWidgetDataResponse
    var isUserOnboarded: Bool
    var dateRange: ClosedRange<Date>
    var onDateSelected: (Date) -> Void
    var onStartQuestionnaire: () -> Void

    @State private var selectedDate = Date()
    @State private var showSheet: Bool? = nil

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(viewModel.title)
                .font(.bold)

            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    HTMLText(text: viewModel.subtitle)

                    Text(viewModel.description)
                        .font(.xSmall)
                        .foregroundColor(Color.charcoalGrey)

                    if let secondaryCTAtext = viewModel.secondaryCTAtext {
                        Button(secondaryCTAtext, action: {
                            guard isUserOnboarded else {
                                SnackBar.show(message: HumaStrings.welcomeWidgetCompleteOnboardingError)
                                return
                            }
                            showSheet = true
                        })
                        .underlineStyle()
                    }
                }
                .padding()

                if let imageUrlString = viewModel.imageUrl, let imageUrl = URL(string: imageUrlString) {
                    HumaSUIImageView(
                        content: .link(imageUrl),
                        contentMode: .fit,
                        radius: .value(0),
                        placeholder: .none
                    )
                    .padding()
                    .frame(square: 136)
                }
            }
            .frame(maxWidth: .infinity)
            .overlay(
                RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                    .stroke(Color.veryLightGrey, lineWidth: Dimensions.lineWidth)
            )
            .cornerRadius(Dimensions.cornerRadius)

            VStack(alignment: .leading) {
                // Progress bar
                ProgressView(value: CGFloat(viewModel.progress ?? 0) / 100)
                    .progressViewStyle(LinearProgressViewStyle(tint: Color.charcoalGrey))
                    .frame(height: 8)
                    .background(Color.veryLightGrey)
                    .scaleEffect(x: 1, y: 4, anchor: .center)
                    .cornerRadius(3)

                if let progressText = viewModel.progressText {
                    Text(progressText)
                        .font(.xxSmall)
                        .foregroundColor(.charcoalGrey)
                }
            }

            if let message = viewModel.message {
                MessageView(message: message)
            }

            if let primaryCTAtext = viewModel.primaryCTAtext {
                Button(primaryCTAtext, action: {
                    // Retake questionnaire action
                    onStartQuestionnaire()
                })
                .pillStyle(.medium)
            }
        }
        .halfSheet(showSheet: $showSheet) {
            DatePickerModalView(
                selectedDate: $selectedDate,
                title: HumaStrings.pluginPreEclampsiaRiskEnterEstimatedDeliveryDate,
                dateRange: dateRange,
                onSave: {
                    onDateSelected(selectedDate)
                    showSheet = false
                },
                onCancel: {
                    showSheet = false
                }
            )
            .edgesIgnoringSafeArea(.bottom)
        }
    }
}

private struct PregnancySetupView: View {
    let viewModel: GetWidgetDataResponse
    var isUserOnboarded: Bool
    var dateRange: ClosedRange<Date>
    var onDateSelected: (Date) -> Void

    @State private var selectedDate = Date()
    @State private var showSheet: Bool? = nil


    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(viewModel.title)
                .font(.bold)
                .foregroundColor(Color.charcoalGrey)

            VStack(alignment: .leading, spacing: 8) {

                HTMLText(text: viewModel.subtitle)

                Text(viewModel.description)
                    .font(.xSmall)
                    .foregroundColor(Color.charcoalGrey)
                    .frame(maxWidth: .infinity)

                if let primaryCTAtext = viewModel.primaryCTAtext {
                    Button(primaryCTAtext, action: {
                        guard isUserOnboarded else {
                            SnackBar.show(message: HumaStrings.welcomeWidgetCompleteOnboardingError)
                            return
                        }
                        showSheet = true
                    })
                    .pillStyle(.medium)
                }
            }
        }
        .halfSheet(showSheet: $showSheet) {
            DatePickerModalView(
                selectedDate: $selectedDate,
                title: HumaStrings.pluginPreEclampsiaRiskEnterEstimatedDeliveryDate,
                dateRange: dateRange,
                onSave: {
                    onDateSelected(selectedDate)
                    showSheet = false
                },
                onCancel: {
                    showSheet = false
                }
            )
            .edgesIgnoringSafeArea(.bottom)
        }
    }
}

private struct HTMLText: View {
    var text: String

    var body: some View {
        let nsAttributedString = HTMLStringBuilder.default.build(from: text)

        if let attributedString = try? AttributedString(nsAttributedString, including: \.uiKit) {
            Text(AttributedString(attributedString, including: \.uiKit))
                .foregroundColor(Color.charcoalGrey)
                .lineLimit(nil)
                .fixedSize(horizontal: false, vertical: true)
        } else {
            Text(text)
                .foregroundColor(Color.charcoalGrey)
                .lineLimit(nil)
                .fixedSize(horizontal: false, vertical: true)
        }
    }
}

private struct MessageView: View {
    var message: GetWidgetDataResponse.Message

    var body: some View {
        VStack(alignment: .leading, spacing: Dimensions.spacing) {
            if message.type == .error {
                Image("exclamation", bundle: CDSBundle.bundle)
            }

            Text(message.text)
                .font(.xSmall)
                .foregroundColor(message.type.color)
        }
    }
}

extension GetWidgetDataResponse.MessageType {

    var color: SwiftUI.Color {
        switch self {
        case .error:
            return Color.error
        case .info:
            return Color.charcoalGrey
        }
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        Color.lightGrey3
            .ignoresSafeArea()
        ScrollView {
            VStack(spacing: 8) {
                CDSCardView(viewModel: PreviewCDSCardViewModel.setupRequired)

                CDSCardView(viewModel: PreviewCDSCardViewModel.active)

                CDSCardView(viewModel: PreviewCDSCardViewModel.loading)
            }
        }
    }
}
