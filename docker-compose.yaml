services:
  postgres:
    image: postgres:16-bookworm
    volumes:
      - huma_plugin_postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=huma
      - POSTGRES_USER=huma_user
      - POSTGRES_PASSWORD=password123
    healthcheck:
      test: ["CMD-SHELL", "pg_isready", "-d", "huma"]
      interval: 30s
      timeout: 60s
      retries: 5
      start_period: 80s

    expose:
      - 5432
    ports:
      - '5432:5432'

  py-minio:
    image: minio/minio
    volumes:
      - py_miniodata:/data
    ports:
      - "9005:9000"
      - "9006:9006"
    environment:
      MINIO_ACCESS_KEY: minio
      MINIO_SECRET_KEY: minio123
    command: server /data --console-address ":9006"

  py-redis:
    image: sameersbn/redis:4.0.9-3
    ports:
      - "6389:6379"
    environment:
      REDIS_PASSWORD: redispassword
    volumes:
      - py_redisdata:/var/lib/redis
    restart: always

  start_dependencies:
    image: dadarek/wait-for-dependencies
    depends_on:
      - py-minio
      - py-redis
    command: py-minio:9000 py-redis:6379

  delay_dependencies:
    container_name: delay-for-dependencies
    image: alpine:3.6
    command: >
      /bin/sh -c "
        sleep 10;
      "
    depends_on:
      - start_dependencies

  tests:
    depends_on:
      - delay_dependencies
    build: .
    links:
      - py-minio
      - py-redis
      - postgres
    environment:
      MP_REDIS_URL: redis://default:redispassword@py-redis:6379
      MP_MINIO_URL: py-minio:9000
      MP_MINIO_BASE_URL: http://py-minio:9000
      MP_POSTGRES_HOST: postgres
    volumes:
      - ./test_results:/server/test_results

volumes:
  py_redisdata:
  py_miniodata:
  huma_plugin_postgres_data:
