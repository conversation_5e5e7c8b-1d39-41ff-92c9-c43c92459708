import datetime
from pathlib import Path

from boltons.iterutils import first
from dateutil.relativedelta import relativedelta
from flask import url_for
from freezegun import freeze_time

from huma_plugins.tests.plugin_test_case import PluginsTestCase
from huma_plugins.widgets.profile import ProfileWidget, SettingType
from pfizer.components.hemophilia.component import HemophiliaComponent
from pfizer.components.hemophilia.dtos import HemophiliaJournalDTO, body_map_tree
from pfizer.components.hemophilia.dtos.enums import BleedType, BodyLocation, BodyPartInjury
from pfizer.components.hemophilia.dtos.journal import InjuryReason, InjurySeverity
from pfizer.components.hemophilia.modules import HemophiliaJournalModule
from pfizer.widgets.hemophilia import HemophiliaJournalWidget
from pfizer.widgets.hemophilia._use_case import AMBER, GREY, LIGHT_GREY, RED
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.builder.component import BuilderComponent
from sdk.deployment.component import DeploymentComponent
from sdk.module_result.component import ModuleResultComponent
from sdk.versioning.component import VersionComponent

USER = "60d9b676b07e15e833eae4b5"
SUPER_ADMIN = "60d9b5b9b07e15e833eae4a2"
TAB_ID = "60d9b623b07e15e833eae4a6"
WIDGET_ID = "668d3827538192c6fe227592"
DEPLOYMENT_ID = "60d9b623b07e15e833eae4a5"


class HemophiliaTestCase(PluginsTestCase):
    components = [
        AuthComponent(),
        AuthorizationComponent(),
        DeploymentComponent(),
        ModuleResultComponent([HemophiliaJournalModule()]),
        BuilderComponent(widgets=[HemophiliaJournalWidget(), ProfileWidget()]),
        HemophiliaComponent(),
        VersionComponent(server_version="1.0.0", api_version="v1"),
    ]
    fixtures = [
        Path(__file__).parent / "fixtures" / "hemophilia_dump.json",
    ]

    def test_widget_configuration(self):
        configuration = self._get_configuration()
        hemo_widget = first(
            configuration["builder"]["tabs"][0]["widgets"], key=lambda w: w["type"] == HemophiliaJournalWidget.id
        )
        self.assertIsNotNone(hemo_widget)
        self.assertEqual("Hi Harry", hemo_widget["config"]["header"]["title"])
        self.assertEqual("60357d1ee0524ba963a83ebe", hemo_widget["config"]["header"]["icon"])

        # check for the presence of the random key sequence
        body_map = hemo_widget["config"]["bodyMap"]
        self.assertEqual(len(body_map_tree), len(body_map))

        joint_map = first(body_map, key=lambda t: t["bleedType"] == BleedType.JOINTS.name)

        self.assertEqual(len(joint_map["locations"]), len(body_map_tree[BleedType.JOINTS]))
        left_leg = first(joint_map["locations"], key=lambda b: b["location"] == BodyLocation.LEFT_KNEE.name)
        self.assertEqual(3, len(left_leg["points"]))
        for point in left_leg["points"]:
            self.assertIn("name", point)
            self.assertIn("value", point)

        # profile widget should include a hemophilia section
        profile_widget = first(
            configuration["builder"]["tabs"][1]["widgets"], key=lambda w: w["type"] == ProfileWidget.id
        )
        hemophilia_title = first(
            profile_widget["config"]["body"],
            key=lambda b: b["type"] == SettingType.MENU_TITLE and b["data"]["title"] == "Hemophilia",
        )
        hemophilia_menu_item = first(profile_widget["config"]["body"], key=lambda b: b["type"] == "HEMOPHILIA_PROFILE")
        self.assertIsNotNone(hemophilia_title)
        self.assertIsNotNone(hemophilia_menu_item)

    def test_widget_data__prerequisites(self):
        # get widget data before prerequisites are completed
        data = self._get_widget_data()

        self.assertEqual("Set up your hemophilia profile", data["title"])
        self.assertEqual(
            "You haven’t completed your hemophilia profile yet. Complete to start tracking bleeds.", data["description"]
        )
        self.assertEqual("Set up", data["primaryCTAtext"])
        self.assertEqual("SETUP_REQUIRED", data["state"])
        self.assertEqual(len(BodyLocation), len(data["bodyMapColor"]))
        for body_location in data["bodyMapColor"]:
            self.assertEqual(body_location["color"], LIGHT_GREY)

        self.assertNotIn("secondaryCTAtext", data)
        self.assertNotIn("legend", data)

    def test_widget_data__empty(self):
        # get widget data after prerequisites are completed
        self._complete_hemo_prerequisites({"answers": [{"question": "How old are you", "answerTest": "21"}]})

        data = self._get_widget_data()

        self.assertEqual("No bleeds", data["title"])
        self.assertEqual("Add your bleeds to guide treatment and prevent complications.", data["description"])
        self.assertEqual("Add new bleed", data["primaryCTAtext"])
        self.assertEqual("View history", data["secondaryCTAtext"])

        self.assertNotIn("legend", data)
        self.assertNotIn("tooltip", data)
        self.assertEqual(len(BodyLocation), len(data["bodyMapColor"]))
        self.assertEqual(0, len(data["results"]))
        self.assertFalse(data["hasSubmissions"])

    def test_widget_data__coloring(self):
        self._complete_hemo_prerequisites({"answers": [{"question": "How old are you", "answerTest": "21"}]})

        # get widget data after submitting a few journal entries
        for entry in self.__historical_data:
            self._create_journal_record(entry)

        with freeze_time("2025-04-01T00:00:00Z"):
            data = self._get_widget_data()

        self.assertNotIn("title", data)
        self.assertNotIn("description", data)

        self.assertEqual("Add new bleed", data["primaryCTAtext"])
        self.assertEqual("View history", data["secondaryCTAtext"])
        self.assertEqual("Data shown from the past 6 months", data["tooltip"])

        self.assertIn("bodyMapColor", data)
        self.assertEqual(len(self.__historical_data), len(data["results"]))
        self.assertTrue(data["hasSubmissions"])

        coloring = data["bodyMapColor"]

        self.assertColoring(coloring, location=BodyLocation.RIGHT_HIP, color=RED)
        self.assertColoring(coloring, location=BodyLocation.LEFT_SHOULDER, color=AMBER)
        self.assertColoring(coloring, location=BodyLocation.LEFT_WRIST, color=GREY)

        # assert color updated to red based on the latest target joints entry
        self._complete_hemo_prerequisites(
            {
                "answers": [{"question": "How old are you", "answerTest": "21"}],
                "targetJoints": [BodyLocation.LEFT_WRIST.name, BodyLocation.LEFT_SHOULDER.name],
            }
        )
        with freeze_time("2025-04-01T00:00:00Z"):
            data = self._get_widget_data()

        coloring = data["bodyMapColor"]
        self.assertColoring(coloring, location=BodyLocation.RIGHT_HIP, color=RED)
        self.assertColoring(coloring, location=BodyLocation.LEFT_SHOULDER, color=RED)
        self.assertColoring(coloring, location=BodyLocation.LEFT_WRIST, color=RED)

        # assert color is back to gray after 6 months of inactivity
        with freeze_time(datetime.datetime.now(datetime.UTC) + relativedelta(months=7)):
            data = self._get_widget_data()

        coloring = data["bodyMapColor"]
        self.assertColoring(coloring, location=BodyLocation.RIGHT_HIP, color=GREY)
        self.assertColoring(coloring, location=BodyLocation.LEFT_SHOULDER, color=GREY)
        self.assertColoring(coloring, location=BodyLocation.LEFT_WRIST, color=GREY)

        self.assertNotIn("tooltip", data)
        self.assertEqual(0, len(data["results"]))
        self.assertTrue(data["hasSubmissions"])

    def _complete_hemo_prerequisites(self, body: dict, code: int = 201, user: str = USER) -> dict:
        url = url_for("hemophilia.submit_prerequisites", user_id=user)
        rsp = self.flask_client.post(url, json=body, headers=self.get_headers_for_token(user))
        self.assertEqual(code, rsp.status_code)
        return rsp.json

    def _get_configuration(self, code: int = 200, as_: str = USER) -> dict:
        url = url_for("user_v1.retrieve_deployment_config", user_id=as_)
        rsp = self.flask_client.get(url, headers=self.get_headers_for_token(as_))
        self.assertEqual(code, rsp.status_code)
        return rsp.json

    def _get_widget_data(self, code: int = 200, as_: str = USER) -> dict:
        url = url_for(
            f"widgets.get_widget_data_{HemophiliaJournalWidget.endpoint_name()}", widget_id=WIDGET_ID, user_id=as_
        )
        rsp = self.flask_client.get(url, headers=self.get_headers_for_token(as_))
        self.assertEqual(code, rsp.status_code)
        return rsp.json

    def _create_journal_record(self, journal: dict, code: int = 201, as_: str = USER) -> dict:
        rsp = self.flask_client.post(
            url_for("submit_module.create_module_result_HemophiliaJournal", user_id=USER),
            json=[journal],
            headers=self.get_headers_for_token(as_),
        )
        self.assertEqual(code, rsp.status_code)
        return rsp.json

    def assertColoring(self, body_map: list[dict], location: BodyLocation, color: str):
        body_map_item = first(body_map, key=lambda b: b["location"] == location.name)
        self.assertEqual(color, body_map_item["color"])

    @property
    def __historical_data(self):
        return [
            {
                HemophiliaJournalDTO.USER_ID: USER,
                HemophiliaJournalDTO.DEPLOYMENT_ID: DEPLOYMENT_ID,
                HemophiliaJournalDTO.MODULE_ID: HemophiliaJournalModule.moduleId,
                HemophiliaJournalDTO.START_DATE_TIME: "2025-01-01T00:00:00Z",
                HemophiliaJournalDTO.DEVICE_NAME: "Android",
                HemophiliaJournalDTO.BODY_LOCATION: BodyLocation.RIGHT_HIP.name,
                HemophiliaJournalDTO.BODY_PART_INJURY: BodyPartInjury.JOINT_RIGHT_HIP.name,
                "type": "HemophiliaJournal",
                HemophiliaJournalDTO.EXTRA_DATA: {
                    "accidentDate": "2025-01-01",
                    "reason": InjuryReason.ACTIVITY.name,
                    "note": "I fell down while playing",
                    "severity": InjurySeverity.SEVERE.name,
                    "treatment": {"id": "treatmentId", "name": "Medication"},
                    "scale": 8,
                },
            },
            {
                HemophiliaJournalDTO.USER_ID: USER,
                HemophiliaJournalDTO.DEPLOYMENT_ID: DEPLOYMENT_ID,
                HemophiliaJournalDTO.MODULE_ID: HemophiliaJournalModule.moduleId,
                HemophiliaJournalDTO.START_DATE_TIME: "2025-02-01T00:00:00Z",
                HemophiliaJournalDTO.DEVICE_NAME: "Android",
                HemophiliaJournalDTO.BODY_LOCATION: BodyLocation.RIGHT_HIP.name,
                HemophiliaJournalDTO.BODY_PART_INJURY: BodyPartInjury.JOINT_RIGHT_HIP.name,
                "type": "HemophiliaJournal",
                HemophiliaJournalDTO.EXTRA_DATA: {
                    "accidentDate": "2025-02-01",
                    "reason": InjuryReason.ACTIVITY.name,
                    "note": "I fell down while playing",
                    "severity": InjurySeverity.SEVERE.name,
                    "treatment": {"id": "treatmentId", "name": "Medication"},
                    "scale": 8,
                },
            },
            {
                HemophiliaJournalDTO.USER_ID: USER,
                HemophiliaJournalDTO.DEPLOYMENT_ID: DEPLOYMENT_ID,
                HemophiliaJournalDTO.MODULE_ID: HemophiliaJournalModule.moduleId,
                HemophiliaJournalDTO.START_DATE_TIME: "2025-03-01T00:00:00Z",
                HemophiliaJournalDTO.DEVICE_NAME: "Android",
                HemophiliaJournalDTO.BODY_LOCATION: BodyLocation.RIGHT_HIP.name,
                HemophiliaJournalDTO.BODY_PART_INJURY: BodyPartInjury.SURFACE_RIGHT_LEG.name,
                "type": "HemophiliaJournal",
                HemophiliaJournalDTO.EXTRA_DATA: {
                    "accidentDate": "2025-03-01",
                    "reason": InjuryReason.ACTIVITY.name,
                    "note": "I fell down while playing",
                    "severity": InjurySeverity.SEVERE.name,
                    "treatment": {"id": "treatmentId", "name": "Medication"},
                    "scale": 8,
                },
            },
            {
                HemophiliaJournalDTO.USER_ID: USER,
                HemophiliaJournalDTO.DEPLOYMENT_ID: DEPLOYMENT_ID,
                HemophiliaJournalDTO.MODULE_ID: HemophiliaJournalModule.moduleId,
                HemophiliaJournalDTO.START_DATE_TIME: "2024-12-01T00:00:00Z",
                HemophiliaJournalDTO.DEVICE_NAME: "Android",
                HemophiliaJournalDTO.BODY_LOCATION: BodyLocation.LEFT_SHOULDER.name,
                HemophiliaJournalDTO.BODY_PART_INJURY: BodyPartInjury.MUSCLE_LEFT_ARM.name,
                "type": "HemophiliaJournal",
                HemophiliaJournalDTO.EXTRA_DATA: {
                    "accidentDate": "2024-12-01",
                    "reason": InjuryReason.ACTIVITY.name,
                    "note": "I fell down while playing",
                    "severity": InjurySeverity.SEVERE.name,
                    "treatment": {"id": "treatmentId", "name": "Medication"},
                    "scale": 8,
                },
            },
        ]
