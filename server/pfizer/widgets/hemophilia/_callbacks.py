import i18n

from huma_plugins.widgets.profile import ProfileWidgetLayout, Setting, SettingType
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.deployment.dtos.deployment import DeploymentDTO


def add_hemophilia_section(
    layout: ProfileWidgetLayout, authz_user: AuthorizedUser, deployment: DeploymentDTO
) -> ProfileWidgetLayout:
    from ._app_widget import HemophiliaJournalWidget

    hemo_widget = deployment.builder.widgets(HemophiliaJournalWidget.id)
    if not hemo_widget:
        return layout

    section = Setting(
        type=SettingType.MENU_TITLE, data={"title": i18n.t("Hemophilia.title", locale=authz_user.get_language())}
    )
    menu_item = Setting(type="HEMOPHILIA_PROFILE", data={})
    # Insert after the header and profile details sections, ref: ProfileSectionsManager._default_layout()
    layout.insert(2, section)
    layout.insert(3, menu_item)
    return layout
