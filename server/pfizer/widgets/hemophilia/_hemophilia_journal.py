import typing as t

import i18n
from boltons.iterutils import first

from huma_plugins.components.medication.module import MedicationsV2Module
from pfizer.components.hemophilia.dtos import body_map_tree
from pfizer.components.hemophilia.modules import HemophiliaJournalModule
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.dtos.role.role import RoleName
from sdk.common.adapter.email_adapter import PercentageTemplate
from sdk.common.localization import Localizable
from sdk.common.utils.convertible import ConvertibleClassValidationError, default_field, meta, required_field
from sdk.common.utils.fields import id_field
from sdk.common.utils.huconvertible import HuConvertible
from sdk.common.utils.validators import validate_len, validate_not_too_long, validate_not_too_long_or_empty
from sdk.deployment.dtos.builder import UIWidget
from sdk.deployment.models import Deployment
from sdk.module_result.dtos.module_config import ModuleConfig, ModuleInfo


class _HemophiliaJournalHeader(Localizable, HuConvertible):
    TITLE = "title"
    ICON = "icon"

    title: str = default_field(metadata=meta(validate_not_too_long_or_empty))
    icon: str = id_field()

    _localizableFields = (TITLE,)


class _HemophiliaJournalUIWidget(HuConvertible, Localizable):
    FORM = "form"
    HEADER = "header"
    DESCRIPTION = "description"

    modules: list[ModuleInfo] = required_field(metadata=meta(validate_len(2, 2)))
    form: dict = required_field()
    description: str = required_field(metadata=meta(validate_not_too_long))
    header: _HemophiliaJournalHeader = default_field()
    bodyMap: list[dict] = default_field(metadata=meta(dump_only=True))

    _localizableFields = (FORM, HEADER, DESCRIPTION)

    @classmethod
    def validate(cls, config: t.Self):
        hemo_module = first(config.modules, key=lambda module: module.moduleId == HemophiliaJournalModule.moduleId)
        medication_module = first(config.modules, key=lambda module: module.moduleId == MedicationsV2Module.moduleId)
        if not hemo_module:
            raise ConvertibleClassValidationError(f"Module {HemophiliaJournalModule.moduleId} is required")
        if not medication_module:
            raise ConvertibleClassValidationError(f"Module {MedicationsV2Module.moduleId} is required")


class HemophiliaJournalUIWidget(UIWidget, HuConvertible):
    CONFIG = "config"

    config: _HemophiliaJournalUIWidget = required_field()

    def is_valid_for(self, role: str) -> bool:
        return role == RoleName.USER

    def configure(self, **kwargs):
        authz_user: AuthorizedUser = kwargs.get("authz_user")
        deployment: Deployment = kwargs.get("deployment")

        language = authz_user.user.language or deployment.language
        self._configure_header(authz_user.user.givenName, language, deployment.iconFileId)
        self._configure_body_map(language)

    def has_config_dependency(self, config: ModuleConfig) -> bool:
        return bool(first(self.config.modules, key=lambda module: module.id == config.id))

    def _configure_header(self, name: str, language: str, icon_id: str):
        localization_text = i18n.t("Hemophilia.Widget.header", locale=language)
        self.config.header = self.config.header or _HemophiliaJournalHeader()

        title_template = self.config.header.title or localization_text
        self.config.header.title = PercentageTemplate(title_template).safe_substitute(username=name)
        self.config.header.icon = icon_id

    def _configure_body_map(self, language: str):
        body_map = [
            {
                "bleedType": bleed_type.name,
                "title": i18n.t(f"Hemophilia.{bleed_type.name}.title", locale=language),
                "description": i18n.t(f"Hemophilia.{bleed_type.name}.description", locale=language),
                "locations": [
                    {
                        "location": location.name,
                        "points": [
                            {"name": i18n.t(f"Hemophilia.body.{part.name}", locale=language), "value": part.name}
                            for part in parts
                        ],
                    }
                    for location, parts in locations.items()
                ],
            }
            for bleed_type, locations in body_map_tree.items()
        ]
        self.config.bodyMap = body_map
