from huma_plugins.widgets.profile import ProfileSectionsManager
from pfizer.components.hemophilia.component import HemophiliaComponent
from sdk.builder.widget import AppWidget
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils import inject
from sdk.deployment.dtos.builder import UIWidget
from ._callbacks import add_hemophilia_section
from ._hemophilia_journal import HemophiliaJournalUIWidget
from ._use_case import HemophiliaJournalUseCase


class HemophiliaJournalWidget(AppWidget):
    id = "com.pfizer.widget.hemophilia_journal"
    title = "Hemophilia journal"
    description = "A widget for tracking bleeds and pain events."

    dependencies = [HemophiliaComponent]

    @property
    def config_class(self) -> type[UIWidget]:
        return HemophiliaJournalUIWidget

    @property
    def use_case(self) -> UseCase:
        return HemophiliaJournalUseCase()

    def post_init(self):
        """To be called after all widgets are initialized"""
        if profile_manager := inject.instance(ProfileSectionsManager, safe=True):
            profile_manager.register_processor(add_hemophilia_section)
