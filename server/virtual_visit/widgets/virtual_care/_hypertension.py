from dataclasses import field

from sdk import convertibleclass
from sdk.builder.widget import AppWidget
from sdk.common.utils.convertible import default_field, required_field, meta
from sdk.common.utils.validators import not_empty, validate_object_id
from sdk.deployment.dtos.builder import UIWidget
from sdk.module_result.component import ModuleResultComponent
from virtual_visit.widgets.virtual_care import HypertensionWidgetUseCase


@convertibleclass
class _HypertensionConfig:
    moduleId: str = required_field(metadata=meta(not_empty))
    moduleConfigId: str = required_field(metadata=meta(validate_object_id))
    articleTags: list[str] = default_field()
    medicationTags: list[str] = default_field()
    allowManualReadings: bool = field(default=True)


@convertibleclass
class HypertensionUIWidget(UIWidget):
    CONFIG = "config"

    config: _HypertensionConfig = required_field()


class HypertensionWidget(AppWidget):
    id = "com.huma.widget.virtual_care.hypertension"
    dependencies = [ModuleResultComponent]

    @property
    def config_class(self) -> type[HypertensionUIWidget]:
        return HypertensionUIWidget

    @property
    def use_case(self) -> HypertensionWidgetUseCase:
        return HypertensionWidgetUseCase()
