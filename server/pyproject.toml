[project]
name = "lifelight-plugins"
version = "1.0.0"
description = "A collection of plugins for Lifelight"
requires-python = ">=3.11"
dependencies = [
    "framework",
    "huma_plugins",
    "sdk",
]

[dependecy-groups]
dev = [
    "black",
    "coverage",
    "freezegun",
    "packaging",
    "pre-commit",
    "pytest-cov",
    "pytest-django",
    "yamllint",
]

[tool.uv.sources]
framework = { git = "https://github.com/huma-engineering/huma-server-platform.git", rev = "v2.4.4" }
huma_plugins = { path = "../../huma-server-plugins" }
sdk = { path = "../../huma-server-sdk" }
