FROM python:3.11-bullseye
COPY --from=ghcr.io/astral-sh/uv:0.4.30 /uv /uvx /bin/

RUN apt-get -y update && \
    apt-get install -y ffmpeg make && \
    apt-get clean

WORKDIR .

# Copy dependency source directories first
COPY ../huma-server-sdk ./libs/huma-server-sdk
COPY ../huma-server-plugins ./libs/huma-server-plugins

# Copy project files
COPY pyproject.toml uv.lock ./

# Update paths in project files to match container structure
RUN sed -i 's|../huma-server-sdk|libs/huma-server-sdk|g' pyproject.toml && sed -i 's|../huma-server-sdk|libs/huma-server-sdk|g' uv.lock
RUN sed -i 's|../huma-server-plugins|libs/huma-server-plugins|g' pyproject.toml && sed -i 's|../huma-server-plugins|libs/huma-server-plugins|g' uv.lock

# Install dependencies
ENV UV_PROJECT_ENVIRONMENT="/usr/local/"
RUN uv sync --no-install-project --dev --locked

# Copy additional files needed for testing
COPY Makefile .pytest.ini ./

# Create a minimal manage.py for Django commands
RUN echo '#!/usr/bin/env python3\n\nif __name__ == "__main__":\n    from framework.commands import cli\n\n    cli()' > manage.py && chmod +x manage.py

CMD ["make", "billing/tests/run"]
